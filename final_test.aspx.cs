using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.Script.Serialization;

public partial class final_test : System.Web.UI.Page
{
	dbconnection db = new dbconnection();
	SqlCommand cmd = new SqlCommand();
	SqlDataAdapter da = new SqlDataAdapter();
	DataSet ds = new DataSet();

	private static int testStep = 0;

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			lblOverallStatus.Text = "✅ 頁面載入完成，準備開始測試";
			UpdateOverallStatus();
		}
	}

	protected void btnSetupSession_Click(object sender, EventArgs e)
	{
		try
		{
			// 設定完整的測試 Session
			Session["tech_no"] = "TEST001";
			Session["user"] = "測試管理者";
			Session["master_single"] = "3";
			Session["office"] = "TEST";
			Session["ysem"] = "113";

			lblSessionStatus.Text = "✅ Session 設定成功 - 管理者權限已啟用";
			lblSessionStatus.CssClass = "success";

			testStep = Math.Max(testStep, 1);
			UpdateOverallStatus();

			ScriptManager.RegisterStartupScript(this, GetType(), "sessionSuccess",
				"updateTestLog('✅ Session 設定完成');", true);
		}
		catch (Exception ex)
		{
			lblSessionStatus.Text = "❌ Session 設定失敗: " + ex.Message;
			lblSessionStatus.CssClass = "error";

			ScriptManager.RegisterStartupScript(this, GetType(), "sessionError",
				"updateTestLog('❌ Session 設定錯誤: " + ex.Message + "');", true);
		}
	}

	protected void btnTestDatabase_Click(object sender, EventArgs e)
	{
		try
		{
			ScriptManager.RegisterStartupScript(this, GetType(), "dbTestStart",
				"showLoading(); updateTestLog('🔍 開始測試資料庫連線');", true);

			// 測試資料庫連線
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			// 測試查詢各個視圖
			string[] views = { "V_recowish1", "V_recowish3", "V_recowish5" };
			string[] viewNames = { "日間部", "碩士班", "進修部" };
			string results = "資料庫連線測試結果：<br/>";

			for (int i = 0; i < views.Length; i++)
			{
				try
				{
					cmd.Connection = db.conn;
					cmd.CommandText = $"SELECT COUNT(*) FROM {views[i]}";

					int count = (int)cmd.ExecuteScalar();
					results += $"• {viewNames[i]} ({views[i]}): {count} 筆記錄<br/>";
				}
				catch (Exception viewEx)
				{
					results += $"• {viewNames[i]} ({views[i]}): ❌ 錯誤 - {viewEx.Message}<br/>";
				}
			}

			lblDatabaseStatus.Text = "✅ " + results;
			lblDatabaseStatus.CssClass = "success";

			testStep = Math.Max(testStep, 2);
			UpdateOverallStatus();

			ScriptManager.RegisterStartupScript(this, GetType(), "dbTestComplete",
				"hideLoading(); updateTestLog('✅ 資料庫連線測試完成');", true);
		}
		catch (Exception ex)
		{
			lblDatabaseStatus.Text = "❌ 資料庫連線失敗: " + ex.Message;
			lblDatabaseStatus.CssClass = "error";

			ScriptManager.RegisterStartupScript(this, GetType(), "dbTestError",
				"hideLoading(); updateTestLog('❌ 資料庫連線錯誤: " + ex.Message + "');", true);
		}
		finally
		{
			if (db.conn.State == ConnectionState.Open)
			{
				db.conn.Close();
			}
		}
	}

	protected void btnTestLoading_Click(object sender, EventArgs e)
	{
		try
		{
			lblLoadingStatus.Text = "🔄 正在測試載入效果...";

			ScriptManager.RegisterStartupScript(this, GetType(), "loadingTest",
				@"showLoading();
                  updateTestLog('🔄 顯示載入遮罩');
                  setTimeout(function() {
                      hideLoading();
                      updateTestLog('✅ 載入遮罩隱藏成功');
                  }, 3000);", true);

			// 延遲更新狀態
			ScriptManager.RegisterStartupScript(this, GetType(), "loadingTestComplete",
				@"setTimeout(function() {
                      var statusLabel = document.getElementById('" + lblLoadingStatus.ClientID + @"');
                      if (statusLabel) {
                          statusLabel.innerHTML = '✅ 載入效果測試完成 - 遮罩顯示和隱藏正常';
                          statusLabel.className = 'success';
                      }
                  }, 3500);", true);

			testStep = Math.Max(testStep, 3);
			UpdateOverallStatus();
		}
		catch (Exception ex)
		{
			lblLoadingStatus.Text = "❌ 載入效果測試失敗: " + ex.Message;
			lblLoadingStatus.CssClass = "error";
		}
	}

	protected void btnFullAnalysis_Click(object sender, EventArgs e)
	{
		try
		{
			// 檢查 Session
			if (Session["tech_no"] == null || Session["master_single"].ToString() != "3")
			{
				lblAnalysisResult.Text = "❌ 請先設定 Session 才能執行分析";
				lblAnalysisResult.CssClass = "error";
				return;
			}

			// 檢查選項
			if (string.IsNullOrEmpty(RadioButtonList1.SelectedValue) || string.IsNullOrEmpty(RadioButtonList2.SelectedValue))
			{
				lblAnalysisResult.Text = "❌ 請選擇分析項目和查詢類型";
				lblAnalysisResult.CssClass = "warning";
				return;
			}

			lblAnalysisResult.Text = "🔄 正在執行完整分析...";

			ScriptManager.RegisterStartupScript(this, GetType(), "analysisStart",
				"showLoading(); updateTestLog('🚀 開始執行完整分析');", true);

			// 模擬分析流程
			string selectedType = RadioButtonList1.SelectedValue;
			string queryType = RadioButtonList2.SelectedValue;
			string viewName = "", typeName = "", queryName = "";

			// 設定視圖名稱
			switch (selectedType)
			{
				case "1": viewName = "V_recowish1"; typeName = "日間部"; break;
				case "3": viewName = "V_recowish3"; typeName = "碩士班"; break;
				case "5": viewName = "V_recowish5"; typeName = "進修部"; break;
			}

			// 設定查詢類型
			switch (queryType)
			{
				case "1": queryName = "滿意度"; break;
				case "2": queryName = "參與度"; break;
				case "3": queryName = "完成度"; break;
			}

			// 執行資料庫查詢
			cmd.Connection = db.conn;
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			cmd.CommandText = $"SELECT TOP 10 * FROM {viewName}";
			da.SelectCommand = cmd;
			DataSet analysisDs = new DataSet();
			da.Fill(analysisDs, "analysisData");

			DataTable resultTable = analysisDs.Tables["analysisData"];

			if (resultTable.Rows.Count > 0)
			{
				lblAnalysisResult.Text = $"✅ 分析完成！{typeName} {queryName} 數據: {resultTable.Rows.Count} 筆記錄";
				lblAnalysisResult.CssClass = "success";

				// 生成測試圖表數據
				GenerateTestChart(resultTable, $"{typeName} {queryName} 統計分析");

				testStep = Math.Max(testStep, 4);
				UpdateOverallStatus();

				ScriptManager.RegisterStartupScript(this, GetType(), "analysisComplete",
					"hideLoading(); updateTestLog('✅ 完整分析執行成功');", true);
			}
			else
			{
				lblAnalysisResult.Text = $"⚠️ {typeName} 數據表為空";
				lblAnalysisResult.CssClass = "warning";

				ScriptManager.RegisterStartupScript(this, GetType(), "analysisNoData",
					"hideLoading(); updateTestLog('⚠️ 分析執行完成但無數據');", true);
			}
		}
		catch (Exception ex)
		{
			lblAnalysisResult.Text = "❌ 分析執行失敗: " + ex.Message;
			lblAnalysisResult.CssClass = "error";

			ScriptManager.RegisterStartupScript(this, GetType(), "analysisError",
				"hideLoading(); updateTestLog('❌ 分析執行錯誤: " + ex.Message + "');", true);
		}
		finally
		{
			if (db.conn.State == ConnectionState.Open)
			{
				db.conn.Close();
			}
		}
	}

	protected void btnRunAllTests_Click(object sender, EventArgs e)
	{
		// 自動執行所有測試
		ScriptManager.RegisterStartupScript(this, GetType(), "runAllTests",
			@"showLoading();
              updateTestLog('🎯 開始執行所有測試');
              setTimeout(function() { document.getElementById('" + btnSetupSession.ClientID + @"').click(); }, 1000);
              setTimeout(function() { document.getElementById('" + btnTestDatabase.ClientID + @"').click(); }, 2000);
              setTimeout(function() { document.getElementById('" + btnTestLoading.ClientID + @"').click(); }, 3000);
              setTimeout(function() { 
                  // 自動選擇選項
                  var radio1 = document.querySelector('input[value=\"1\"][name*=\"RadioButtonList1\"]');

				  var radio2 = document.querySelector('input[value=\"1\"][name*=\"RadioButtonList2\"]');
		if (radio1) radio1.checked = true;
		if (radio2) radio2.checked = true;
		setTimeout(function() {
			document.getElementById('" + btnFullAnalysis.ClientID + @"').click();
			hideLoading();
		}, 1000);
		}, 4000); ", true);

	}


	protected void btnReset_Click(object sender, EventArgs e)
	{
		// 重置所有測試狀態
		Session.Clear();
		testStep = 0;

		lblSessionStatus.Text = "Session 尚未設定";
		lblSessionStatus.CssClass = "";

		lblDatabaseStatus.Text = "資料庫尚未測試";
		lblDatabaseStatus.CssClass = "";

		lblLoadingStatus.Text = "載入效果尚未測試";
		lblLoadingStatus.CssClass = "";

		lblAnalysisResult.Text = "分析尚未執行";
		lblAnalysisResult.CssClass = "";

		RadioButtonList1.ClearSelection();
		RadioButtonList2.ClearSelection();

		UpdateOverallStatus();

		ScriptManager.RegisterStartupScript(this, GetType(), "resetComplete",
			"updateTestLog('🔄 測試狀態已重置');", true);
	}

	private void GenerateTestChart(DataTable data, string title)
	{
		try
		{
			var chartData = new
			{
				labels = new[] { "優秀", "良好", "普通", "待改善" },
				datasets = new[]
				{
					new
					{
						label = title,
						data = new[] {
							Math.Min(data.Rows.Count * 0.4, 50),
							Math.Min(data.Rows.Count * 0.3, 30),
							Math.Min(data.Rows.Count * 0.2, 20),
							Math.Min(data.Rows.Count * 0.1, 10)
						},
						backgroundColor = new[] {
							"rgba(75, 192, 192, 0.8)",
							"rgba(54, 162, 235, 0.8)",
							"rgba(255, 206, 86, 0.8)",
							"rgba(255, 99, 132, 0.8)"
						},
						borderWidth = 2
					}
				}
			};

			JavaScriptSerializer serializer = new JavaScriptSerializer();
			string chartDataJson = serializer.Serialize(chartData);

			ScriptManager.RegisterStartupScript(this, GetType(), "drawTestChart",
				$"drawTestChart({chartDataJson}, '{title}');", true);
		}
		catch (Exception ex)
		{
			ScriptManager.RegisterStartupScript(this, GetType(), "chartError",
				$"updateTestLog('❌ 圖表生成錯誤: {ex.Message}');", true);
		}
	}

	private void UpdateOverallStatus()
	{
		string status = "🏆 測試進度：";
		string[] steps = {
			"Session 設定",
			"資料庫連線",
			"載入效果",
			"完整分析"
		};

		for (int i = 0; i < steps.Length; i++)
		{
			if (i < testStep)
			{
				status += $" ✅ {steps[i]}";
			}
			else if (i == testStep)
			{
				status += $" 🔄 {steps[i]}";
			}
			else
			{
				status += $" ⏳ {steps[i]}";
			}
		}

		if (testStep >= 4)
		{
			status += "<br/><strong style='color: green;'>🎉 所有測試完成！系統功能正常運作。</strong>";
		}

		lblOverallStatus.Text = status;
	}
}
