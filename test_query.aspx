<%@ Page Language="C#" AutoEventWireup="true" CodeFile="test_query.aspx.cs" Inherits="test_query" %>

<!DOCTYPE html>
<html>

<head runat="server">
	<title>SQL 查詢測試</title>
	<style>
		body {
			font-family: 'Microsoft JhengHei', sans-serif;
			margin: 20px;
		}

		.container {
			max-width: 1000px;
			margin: 0 auto;
		}

		.test-section {
			margin: 20px 0;
			padding: 15px;
			border: 1px solid #ddd;
			border-radius: 5px;
		}

		.test-section h3 {
			margin-top: 0;
			color: #333;
		}

		.btn {
			padding: 10px 20px;
			margin: 5px;
			background: #007bff;
			color: white;
			border: none;
			border-radius: 3px;
			cursor: pointer;
		}

		.btn:hover {
			background: #0056b3;
		}

		.result {
			margin: 10px 0;
			padding: 10px;
			background: #f8f9fa;
			border-radius: 3px;
		}

		.error {
			background: #f8d7da;
			color: #721c24;
		}

		.success {
			background: #d4edda;
			color: #155724;
		}

		table {
			width: 100%;
			border-collapse: collapse;
			margin: 10px 0;
		}

		th,
		td {
			border: 1px solid #ddd;
			padding: 8px;
			text-align: left;
		}

		th {
			background: #f2f2f2;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<div class="container">
			<h2>📊 SQL 查詢測試工具</h2>

			<div class="test-section">
				<h3>🔗 資料庫連線測試</h3>
				<asp:Button ID="btnTestConnection" runat="server" Text="測試連線" CssClass="btn" OnClick="btnTestConnection_Click" />
				<div class="result">
					<asp:Label ID="lblConnectionResult" runat="server" Text="點擊按鈕測試資料庫連線"></asp:Label>
				</div>
			</div>

			<div class="test-section">
				<h3>📋 視圖資料測試</h3>
				<asp:Button ID="btnTestV1" runat="server" Text="測試 V_recowish1 (日間部)" CssClass="btn" OnClick="btnTestV1_Click" />
				<asp:Button ID="btnTestV3" runat="server" Text="測試 V_recowish3 (碩士班)" CssClass="btn" OnClick="btnTestV3_Click" />
				<asp:Button ID="btnTestV5" runat="server" Text="測試 V_recowish5 (進修部)" CssClass="btn" OnClick="btnTestV5_Click" />
				<div class="result">
					<asp:Label ID="lblQueryResult" runat="server" Text="選擇一個視圖進行測試"></asp:Label>
				</div>
				<asp:GridView ID="gvTestData" runat="server" AutoGenerateColumns="true" CssClass="table"></asp:GridView>
			</div>

			<div class="test-section">
				<h3>🔧 調試資訊</h3>
				<div id="debugInfo">
					<p><strong>伺服器時間:</strong> <%= DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") %></p>
					<p><strong>應用程式路徑:</strong> <%= Request.ApplicationPath %></p>
				</div>
			</div>
		</div>
	</form>
</body>

</html>