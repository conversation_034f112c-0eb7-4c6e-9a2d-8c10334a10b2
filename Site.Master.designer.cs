//------------------------------------------------------------------------------
// <auto-generated>
//     此程式碼是由工具產生的。
//
//     變更此檔案可能會造成不正確的行為，而且如果
//     重新產生程式碼，所做的變更將會遺失。
// </auto-generated>
//------------------------------------------------------------------------------

/// <summary>
/// Site 類別。
/// </summary>
/// <remarks>
/// 自動產生的類別。
/// </remarks>
public partial class Site
{

	/// <summary>
	/// head 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移到程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.ContentPlaceHolder head;

	/// <summary>
	/// labsys 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移到程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Label labsys;

	/// <summary>
	/// form1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移到程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.HtmlControls.HtmlForm form1;

	/// <summary>
	/// ScriptManager1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移到程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.ScriptManager ScriptManager1;

	/// <summary>
	/// LoginName1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移到程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.LoginName LoginName1;

	/// <summary>
	/// adminMenuControl 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移到程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.HtmlControls.HtmlGenericControl adminMenuControl;

	/// <summary>
	/// teacherMenu 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移到程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.HtmlControls.HtmlGenericControl teacherMenu;

	/// <summary>
	/// MainContent 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移到程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.ContentPlaceHolder MainContent;
}
