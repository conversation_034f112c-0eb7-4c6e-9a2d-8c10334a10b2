## About

Provides additional types for efficiently representing asynchronous operations.

## Main Types

The main types provided by this library are:

- System.Threading.Tasks.ValueTask
- System.Threading.Tasks.ValueTask&lt;TResult&gt;

## Additional Documentation

- API reference can be found in: https://learn.microsoft.com/en-us/dotnet/api/system.threading.tasks
- https://devblogs.microsoft.com/dotnet/understanding-the-whys-whats-and-whens-of-valuetask/

## Related Packages

All of the types provided in this library are part of the .NET Core shared framework.

## License

System.Threading.Tasks.Extensions is released as open source under the [MIT license](https://licenses.nuget.org/MIT).
