﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="PIS.aspx.cs" Inherits="PIS" %>

    <!DOCTYPE html>
    <html lang="zh-TW">

    <head runat="server">
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>個人資料保護告知</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
        <style>
            /* 自訂動畫 */
            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }

                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .animate-fadeIn {
                animation: fadeIn 0.5s ease-out forwards;
            }

            /* 按鈕禁用時的樣式 (Tailwind 預設的 opacity-50 cursor-not-allowed 已經很好) */
            /* .agree-button:disabled { } */
        </style>
    </head>

    <body class="bg-gray-100 flex items-center justify-center min-h-screen">
        <form id="form1" runat="server">
            <div class="bg-white p-8 md:p-12 rounded-lg shadow-xl max-w-2xl w-full mx-4 animate-fadeIn">
                <div class="text-center mb-6">
                    <i class="fas fa-shield-alt fa-3x text-blue-500 mb-3"></i>
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-700">個人資料保護告知</h1>
                </div>

                <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6 rounded">
                    <div class="flex">
                        <div class="py-1"><i class="fas fa-exclamation-triangle fa-lg text-red-400 mr-3"></i></div>
                        <div>
                            <p class="text-sm md:text-base text-red-700">
                                <span
                                    class="font-bold">請注意</span>，各單位處理含個資檔案時，應依據「個人資料保護法」及相關規定審慎處理，不可違反個資法，蒐集、洩漏個資或進行非公務以外之用途。
                                個人資料之蒐集、處理或利用，應尊重當事人之權益，依誠實及信用方法為之，不得逾越特定目的之必要範圍，並應與蒐集之目的具有正當合理之關聯。
                            </p>
                        </div>
                    </div>
                </div>

                <div class="text-center mb-8">
                    <p class="text-lg text-gray-600">
                        按鈕啟用倒數：<span id="timer" class="font-bold text-xl text-blue-600">5</span> 秒
                    </p>
                </div>

                <asp:Button ID="agreeButton" runat="server" Text="我已閱讀並同意上述事項" OnClick="RedirectToHomePage"
                    Enabled="false"
                    CssClass="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75 transition-all duration-150 ease-in-out opacity-50 cursor-not-allowed" />
            </div>
        </form>
        <script type="text/javascript">
            let timer = 5;
            const timerElement = document.getElementById('timer');
            const agreeButton = document.getElementById('<%=agreeButton.ClientID%>'); // 使用 ClientID

            // 確保按鈕初始狀態符合 Enabled="false"
            if (agreeButton) {
                agreeButton.disabled = true;
                // Tailwind class 'opacity-50 cursor-not-allowed' 應該由 CssClass 控制
            }

            const countdown = setInterval(() => {
                timer--;
                if (timerElement) {
                    timerElement.textContent = timer;
                }

                if (timer <= 0) {
                    clearInterval(countdown);
                    if (agreeButton) {
                        agreeButton.disabled = false;
                        // 移除 Tailwind 的禁用樣式，並添加啟用樣式 (如果需要的話)
                        // 通常按鈕的 CSS class 會處理 :disabled 狀態
                        // 這裡確保移除 opacity 和 cursor-not-allowed (如果它們是直接加的)
                        // 但由於是透過 CssClass 控制，ASP.NET 可能會處理好
                        // 為了保險，可以手動調整 class
                        agreeButton.classList.remove('opacity-50', 'cursor-not-allowed');
                        // 如果有特定的啟用 class，可以在這裡添加
                    }
                }
            }, 1000);
        </script>
    </body>

    </html>