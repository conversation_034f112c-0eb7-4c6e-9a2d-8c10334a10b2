﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle.Cryptography" version="2.6.1" targetFramework="net48" />
  <package id="ClosedXML" version="0.105.0" targetFramework="net48" />
  <package id="ClosedXML.Parser" version="2.0.0" targetFramework="net48" />
  <package id="DocumentFormat.OpenXml" version="3.3.0" targetFramework="net48" />
  <package id="DocumentFormat.OpenXml.Framework" version="3.3.0" targetFramework="net48" />
  <package id="ExcelDataReader" version="3.7.0" targetFramework="net48" />
  <package id="ExcelDataReader.DataSet" version="3.7.0" targetFramework="net48" />
  <package id="ExcelNumberFormat" version="1.1.0" targetFramework="net48" />
  <package id="itext" version="9.2.0" targetFramework="net48" />
  <package id="itext.commons" version="9.2.0" targetFramework="net48" />
  <package id="itext7" version="9.2.0" targetFramework="net48" />
  <package id="iTextSharp" version="5.5.13.4" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.5" targetFramework="net48" />
  <package id="Microsoft.Bcl.HashCode" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="4.1.0" targetFramework="net48" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection" version="9.0.5" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="9.0.5" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging" version="9.0.5" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="9.0.5" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options" version="9.0.5" targetFramework="net48" />
  <package id="Microsoft.Extensions.Primitives" version="9.0.5" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="RBush.Signed" version="4.0.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.6.1" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.5" targetFramework="net48" />
  <package id="System.Drawing.Common" version="9.0.5" targetFramework="net48" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.IO.Packaging" version="9.0.5" targetFramework="net48" />
  <package id="System.Memory" version="4.6.3" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.6.1" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.2" targetFramework="net48" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.3" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.6.1" targetFramework="net48" />
</packages>