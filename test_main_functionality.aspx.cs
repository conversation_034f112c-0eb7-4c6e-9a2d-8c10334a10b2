using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.Script.Serialization;

public partial class test_main_functionality : System.Web.UI.Page
{
	dbconnection db = new dbconnection();
	SqlCommand cmd = new SqlCommand();
	SqlDataAdapter da = new SqlDataAdapter();
	DataSet ds = new DataSet();

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			lblStatus.Text = "✅ 頁面載入完成，請選擇分析類型並點擊執行";

			// 註冊頁面載入完成的調試信息
			ScriptManager.RegisterStartupScript(this, GetType(), "pageLoadComplete",
				"updateDebugLog('✅ 後端頁面載入完成');", true);
		}
	}

	protected void btnExecute_Click(object sender, EventArgs e)
	{
		try
		{
			// 顯示開始處理
			lblStatus.Text = "🔄 開始處理請求...";

			ScriptManager.RegisterStartupScript(this, GetType(), "startProcess",
				"updateDebugLog('🔄 開始執行分析');", true);

			// 檢查是否選擇了分析類型
			if (string.IsNullOrEmpty(RadioButtonList1.SelectedValue))
			{
				lblResult.Text = "❌ 請先選擇分析類型！";
				lblResult.CssClass = "error";

				ScriptManager.RegisterStartupScript(this, GetType(), "noSelection",
					"updateDebugLog('❌ 未選擇分析類型'); hideLoading();", true);
				return;
			}

			string selectedType = RadioButtonList1.SelectedValue;
			string viewName = "";
			string typeName = "";

			// 根據選擇設定視圖名稱
			switch (selectedType)
			{
				case "1":
					viewName = "V_recowish1";
					typeName = "日間部";
					break;
				case "3":
					viewName = "V_recowish3";
					typeName = "碩士班";
					break;
				case "5":
					viewName = "V_recowish5";
					typeName = "進修部";
					break;
				default:
					lblResult.Text = "❌ 無效的分析類型！";
					lblResult.CssClass = "error";
					ScriptManager.RegisterStartupScript(this, GetType(), "invalidType",
						"updateDebugLog('❌ 無效的分析類型: " + selectedType + "'); hideLoading();", true);
					return;
			}

			lblStatus.Text = $"📊 正在查詢 {typeName} 數據...";

			// 執行資料庫查詢
			string sqlStr = $"SELECT * FROM {viewName}";

			ScriptManager.RegisterStartupScript(this, GetType(), "sqlQuery",
				$"updateDebugLog('📊 執行SQL: {sqlStr}');", true);

			cmd.Connection = db.conn;
			cmd.CommandText = sqlStr;
			da.SelectCommand = cmd;

			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			da.Fill(ds, "data");
			DataTable resultTable = ds.Tables["data"];

			if (resultTable.Rows.Count > 0)
			{
				lblResult.Text = $"✅ 成功查詢到 {resultTable.Rows.Count} 筆 {typeName} 數據";
				lblResult.CssClass = "success";

				// 生成簡單的圖表數據
				GenerateChart(resultTable, typeName);

				ScriptManager.RegisterStartupScript(this, GetType(), "querySuccess",
					$"updateDebugLog('✅ 查詢成功: {resultTable.Rows.Count} 筆記錄'); hideLoading();", true);
			}
			else
			{
				lblResult.Text = $"⚠️ {typeName} 數據表為空";
				lblResult.CssClass = "error";

				ScriptManager.RegisterStartupScript(this, GetType(), "noData",
					$"updateDebugLog('⚠️ 無數據: {viewName}'); hideLoading();", true);
			}

			db.conn.Close();
		}
		catch (Exception ex)
		{
			lblResult.Text = $"❌ 執行錯誤: {ex.Message}";
			lblResult.CssClass = "error";

			ScriptManager.RegisterStartupScript(this, GetType(), "executeError",
				$"updateDebugLog('❌ 執行錯誤: {ex.Message}'); hideLoading();", true);
		}
	}

	private void GenerateChart(DataTable data, string typeName)
	{
		try
		{
			// 模擬圖表數據生成
			var chartData = new
			{
				labels = new[] { "非常滿意", "滿意", "普通", "不滿意", "非常不滿意" },
				datasets = new[]
				{
					new
					{
						label = typeName + " 滿意度分布",
						data = new[] {
							Math.Min(data.Rows.Count * 0.4, data.Rows.Count),  // 40% 非常滿意
                            Math.Min(data.Rows.Count * 0.3, data.Rows.Count),  // 30% 滿意
                            Math.Min(data.Rows.Count * 0.2, data.Rows.Count),  // 20% 普通
                            Math.Min(data.Rows.Count * 0.08, data.Rows.Count), // 8% 不滿意
                            Math.Min(data.Rows.Count * 0.02, data.Rows.Count)  // 2% 非常不滿意
                        },
						backgroundColor = new[]
						{
							"rgba(75, 192, 192, 0.6)",
							"rgba(54, 162, 235, 0.6)",
							"rgba(255, 206, 86, 0.6)",
							"rgba(255, 99, 132, 0.6)",
							"rgba(153, 102, 255, 0.6)"
						},
						borderColor = new[]
						{
							"rgba(75, 192, 192, 1)",
							"rgba(54, 162, 235, 1)",
							"rgba(255, 206, 86, 1)",
							"rgba(255, 99, 132, 1)",
							"rgba(153, 102, 255, 1)"
						},
						borderWidth = 1
					}
				}
			};

			string chartTitle = $"{typeName} 學生滿意度統計 (總樣本: {data.Rows.Count})";

			JavaScriptSerializer serializer = new JavaScriptSerializer();
			string chartDataJson = serializer.Serialize(chartData);

			ScriptManager.RegisterStartupScript(this, GetType(), "drawChart",
				$"drawChart({chartDataJson}, '{chartTitle}');", true);

			ScriptManager.RegisterStartupScript(this, GetType(), "chartGenerated",
				"updateDebugLog('📈 圖表數據已生成並傳送到前端');", true);
		}
		catch (Exception ex)
		{
			ScriptManager.RegisterStartupScript(this, GetType(), "chartError",
				$"updateDebugLog('❌ 圖表生成錯誤: {ex.Message}');", true);
		}
	}
}
