﻿using iTextSharp.text;
using iTextSharp.text.pdf;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml.Linq;

public class App_Func
{
    DataSet ds = new DataSet();
    DataTable dT = new DataTable();
    DBSQL dbsql = new DBSQL();
    public string sqlStr;

    //取得系統之最大學年期
    public string get_ysem_Max()
    {
        string Strysem = "";
        sqlStr = @"select pr_ysem from ssm_prereg group by pr_ysem order by pr_ysem desc";

        ds = dbsql.DB_Select(sqlStr);

        if (ds.Tables[0].Rows.Count > 0) //有
        {
            //DataTable Tysem = ds.Tables[0];
            Strysem = ds.Tables[0].Rows[0][0].ToString().Trim();
            ds.Clear();
        }
        return Strysem;
    }

    //取得目前之學年期
    public string get_ysem()
    {
        //string Strysem = "";
        //'ysem_year 學年度 ysem_sem學期
        Int32 ysem_year, int_month, ysem_sem;
        ysem_sem = 1; //'上學期
        ysem_year = Int32.Parse(DateTime.Now.Year.ToString()) - 1911 - 1; //(Year(Now()) - 1911) - 1;
        int_month = Int32.Parse(DateTime.Now.Month.ToString());

        if ((int_month >= 8 && int_month <= 12) || int_month == 1)  //189,10,11,12
        {
            if (int_month != 1)
            {
                ysem_year = ysem_year + 1;
            }
        }
        else if ((int_month >= 2 && int_month <= 7)) //234567
        {
            //'否則 假如 現在月份>=2 且 現在月分<=7 代表為第二學期
            ysem_sem = 2; //下學期
        }
        return ysem_year.ToString() + ysem_sem.ToString();
    }

    public DataSet get_ALLysem(string _sqloption)
    {
        //string Strysem = "";
        if (_sqloption == "ysem")
        {
            sqlStr = @"select pr_ysem as ysem , Left(pr_ysem,3) as year from ssm_prereg group by pr_ysem order by pr_ysem desc";
        }
        else
        {//year
            sqlStr = @"select  Left(pr_ysem,3) as year from ssm_prereg group by Left(pr_ysem,3) order by Left(pr_ysem,3) desc";
        }
        ds.Clear();
        ds = dbsql.DB_Select(sqlStr);
        //DataTable Tysem = new DataTable;

        //if (ds.Tables[0].Rows.Count > 0) //有
        //{
        //     dT = ds.Tables[0];
        //    //Strysem = ds.Tables[0].Rows[0][0].ToString().Trim();
        //    ds.Clear();
        //}
        return ds;
    }

    //取得學生的導師的DATA
    public DataSet get_TeachOfStudData(string _sqloption, string _StudNo)
    {
        //string Strysem = "";
        if (_sqloption == "1")
        {
            //sqlStr = @"select pr_ysem as ysem , Left(pr_ysem,3) as year from ssm_prereg group by pr_ysem order by pr_ysem desc";
        }
        else
        {//year
            sqlStr = @"select tech_no,tech_cname,tech_email from S12_teacher
                       where  tech_no in (
                        select Rtrim(tech_no) as class_techno from View_SSM_All_Student
                        where stud_no = '" + _StudNo + "')";
        }
        ds.Clear();
        ds = dbsql.DB_Select(sqlStr);
        return ds;
    }

    //檢查學生編號存在
    public Int32 CHK_Student(string _StudNo)
    {
        Int32 rtncnt = 0;
        sqlStr = @"select stud_no from student where stud_status IN ('0', 'D', 'E', 'F', 'G', 'H', 'N', 'C')
                   and stud_no='" + _StudNo + "'";

        ds = dbsql.DB_Select(sqlStr);

        if (ds.Tables[0].Rows.Count > 0) //有
        {
            //DataTable Tysem = ds.Tables[0];
            //rtncnt = (int)(ds.Tables[0].Rows[0][0]);
            rtncnt = ds.Tables[0].Rows.Count;
            ds.Clear();
        }
        return rtncnt;
    }

    //檢查老師或職員編號存在
    public Int32 CHK_Teacher(string _TeachNo, string _type)
    {
        Int32 rtncnt = 0;
        switch (_type)
        {
            case "emp": //員工
                sqlStr = @"select s1.tech_no as id_no,s1.tech_cname as cname from S12_teacher as s1 where s1.tech_duty='1' and s1.tech_no='" + _TeachNo + "'";
                break;
            case "teach":  //班級導師
                sqlStr = @"select cl.tech_no as id_no,s12t.tech_cname as cnam from class as cl join S12_teacher as s12t on s12t.tech_no=cl.tech_no where cl.tech_no='" + _TeachNo + "'";
                break;
            case "2":
                break;
        }

        ds = dbsql.DB_Select(sqlStr);

        if (ds.Tables[0].Rows.Count > 0) //有
        {
            //DataTable Tysem = ds.Tables[0];
            //rtncnt = (int)(ds.Tables[0].Rows[0][0]);
            rtncnt = ds.Tables[0].Rows.Count;
            ds.Clear();
        }
        return rtncnt;
    }

    //檢查班級編號存在
    public Int32 CHK_Class(string _ClassNo)
    {
        Int32 rtncnt = 0;
        sqlStr = @"select cl.class_code,class_name from class as cl join S12_teacher as s12t on s12t.tech_no=cl.tech_no where cl.class_code='" + _ClassNo + "'";

        ds = dbsql.DB_Select(sqlStr);

        if (ds.Tables[0].Rows.Count > 0) //有
        {
            //DataTable Tysem = ds.Tables[0];
            //rtncnt = (int)(ds.Tables[0].Rows[0][0]);
            rtncnt = ds.Tables[0].Rows.Count;
            ds.Clear();
        }
        return rtncnt;
    }

    #region 驗證身分證字號
    public Boolean CHK_IDNO(string _IDNo)
    {
        int[] uid = new int[10]; //數字陣列存放身分證字號用
        int chkTotal; //計算總和用


        _IDNo = _IDNo.ToUpper(); //將身分證字號英文改為大寫

        //將輸入的值存入陣列中
        for (int i = 1; i < _IDNo.Length; i++)
        {
            uid[i] = Convert.ToInt32(_IDNo.Substring(i, 1));
        }
        //將開頭字母轉換為對應的數值
        switch (_IDNo.Substring(0, 1).ToUpper())
        {
            case "A": uid[0] = 10; break;
            case "B": uid[0] = 11; break;
            case "C": uid[0] = 12; break;
            case "D": uid[0] = 13; break;
            case "E": uid[0] = 14; break;
            case "F": uid[0] = 15; break;
            case "G": uid[0] = 16; break;
            case "H": uid[0] = 17; break;
            case "I": uid[0] = 34; break;
            case "J": uid[0] = 18; break;
            case "K": uid[0] = 19; break;
            case "L": uid[0] = 20; break;
            case "M": uid[0] = 21; break;
            case "N": uid[0] = 22; break;
            case "O": uid[0] = 35; break;
            case "P": uid[0] = 23; break;
            case "Q": uid[0] = 24; break;
            case "R": uid[0] = 25; break;
            case "S": uid[0] = 26; break;
            case "T": uid[0] = 27; break;
            case "U": uid[0] = 28; break;
            case "V": uid[0] = 29; break;
            case "W": uid[0] = 32; break;
            case "X": uid[0] = 30; break;
            case "Y": uid[0] = 31; break;
            case "Z": uid[0] = 33; break;
        }

        //檢查第一個數值是否為1.2(判斷性別)
        if (uid[1] == 1 || uid[1] == 2)
        {
            chkTotal = (uid[0] / 10 * 1) + (uid[0] % 10 * 9);

            int k = 8;
            for (int j = 1; j < 9; j++)
            {
                chkTotal += uid[j] * k;
                k--;
            }

            chkTotal += uid[9];

            if (chkTotal % 10 != 0)
            {
                return false;
                //Response.Write("身分證字號錯誤");
            }
        }
        else
        {
            return false;
            //Response.Write("身分證字號錯誤");
        }

        return true;
    }
    #endregion

    //手機號碼驗證
    public Boolean CHK_mphone(string _mphoneNo)
    {
        if (_mphoneNo.Length != 10)
        {
            //Response.Write("<script>alert('輸入手機號碼非10碼！必須連續10碼數字')</script>");
            return false;
        }
        else
        {
            if (!f_IsNUmber(_mphoneNo))
            {
                //Response.Write("<script>alert('輸入手機號碼錯誤！')</script>");
                //mphoneCheck = 1;
                return false;
            }
            //else
            //{
            //    return true;
            //}
        }

        return true;
    }

    //數字驗證函式
    public bool f_IsNUmber(string p_strVaule)
    {
        if (p_strVaule == "")
        {
            return false;
        }
        else
        {
            Regex m_regex = new System.Text.RegularExpressions.Regex("^(-?[0-9]*[.]*[0-9]{0,3})$");
            return m_regex.IsMatch(p_strVaule);
        }
    }

    //check_session
    public bool f_pageload_check_session()
    {
        if (HttpContext.Current.Session["uid"] == null)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
    public bool f_pageload_studcheck_session()
    {
        if (HttpContext.Current.Session["stud_no"] == null)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
    public bool f_pageload_techcheck_session()
    {
        if (HttpContext.Current.Session["tech_no"] == null)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
    public string FilterGdName(string orgString)
    {
        //正則表達式

        //只允許使用的是 ASCII 範圍，並不包含 Unicode 字符。
        MatchCollection matches = Regex.Matches(orgString, @"[^\W'-]+", RegexOptions.IgnoreCase);
        //只允許使用正整數
        //MatchCollection matches = Regex.Matches(orgString, @"^\d+$", RegexOptions.IgnoreCase);
        //只允許使用字母和數字
        //MatchCollection matches = Regex.Matches(orgString, @"^[a-zA-Z0-9]+$", RegexOptions.IgnoreCase);

        // @"[^\W']+"：
        // \W：匹配所有非字母或數字的字元（例如符號、空格）。使用的是 ASCII 範圍，並不包含 Unicode 字符
        // ^\W：表示「非 \W」，即匹配字母、數字或底線。使用的是 ASCII 範圍，並不包含 Unicode 字符
        // +：表示匹配一個或多個上述字元。
        // '-: (單引號)或連字元 (-) 被排除，意味著它不屬於有效的單詞字元。
        // 總結：匹配的內容為由字母、數字或底線組成的連續單詞，忽略大小寫 (RegexOptions.IgnoreCase)。

        //允許Unicode 字符。
        //MatchCollection matches = Regex.Matches(orgString, @"(?<!['-])[\p{L}\p{N}_]+(?!['-])", RegexOptions.IgnoreCase);
        //使用 RegexOptions.Compiled 加速多次匹配的效能
        //start RegexOptions.Compiled
        //Regex regex = new Regex(@"(?<!['-])[\p{L}\p{N}_]+(?!['-])", RegexOptions.IgnoreCase | RegexOptions.Compiled);
        //MatchCollection matches = regex.Matches(orgString);
        //end RegexOptions.Compiled
        // (?<!['-])：負向先行判斷，表示「確保匹配的內容前面不是單引號 (') 或連字元 (-)」。
        // [\p{L}\p{N}_]+：匹配一個或多個 Unicode。 \p{L}：匹配所有 Unicode 的字母。\p{N}：匹配所有 Unicode 的數字。（_）允許使用底線。
        // (?!['-])：負向後行判斷，表示「確保匹配的內容後面不是單引號 (') 或連字元 (-)」。
        //如果需要過濾更多符號，可以在負向判斷中擴展條件。

        //string newString = string.Empty;   //舊寫法 字串拼接使用 += 效率較低，因為每次都會產生新字串。
        //foreach (Match match in matches)
        //{
        //    newString += match.Value;
        //}
        //return newString;
        StringBuilder sb = new StringBuilder(); //使用 StringBuilder 來替代+=
        foreach (Match match in matches)
        {
            sb.Append(match.Value);
        }
        return sb.ToString();
    }
    public static bool IsNumeric(string input)
    {
        // 允許正負號、小數點、科學計數法
        return Regex.IsMatch(input, @"^-?\d+(\.\d+)?(e[+-]?\d+)?$");
    }
    bool IsAllowed(string input)
    {
        // 允許字母和數字
        return Regex.IsMatch(input, @"^[a-zA-Z0-9]+$");
    }
    public class Whitelist
    {
        private HashSet<char> allowedChars = new HashSet<char> { 'a', 'b', 'c', '1', '2', '3' };

        public bool IsAllowed(string input)
        {
            return input.All(c => allowedChars.Contains(c));
        }
    }
    public void MsgBox(string Msg)
    {
        ScriptManager.RegisterStartupScript(((Page)HttpContext.Current.Handler), typeof(string), "_msgbox", "MsgBox('" + Msg + "');", true);
        //((Page)HttpContext.Current.Handler).ClientScript.RegisterStartupScript(GetType(), "message", "<script>alert('第一種方式，無白屏！');</script>");
    }

    public class PdfPageEventHelper : iTextSharp.text.pdf.PdfPageEventHelper
    {
        private BaseFont bfChinese;
        private iTextSharp.text.Font fontChinese;

        public PdfPageEventHelper()
        {
            string chKaiuFontPath = HttpContext.Current.Server.MapPath("~/fonts/kaiu.ttf");
            bfChinese = BaseFont.CreateFont(chKaiuFontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            fontChinese = new iTextSharp.text.Font(bfChinese, 10, iTextSharp.text.Font.NORMAL);
        }

        public override void OnEndPage(PdfWriter writer, Document document)
        {
            PdfPTable footer = new PdfPTable(1);
            footer.TotalWidth = 300;
            footer.HorizontalAlignment = Element.ALIGN_CENTER;

            PdfPCell cell = new PdfPCell(new Phrase("頁碼: " + writer.PageNumber, fontChinese));
            cell.Border = 0;
            cell.HorizontalAlignment = Element.ALIGN_CENTER;
            footer.AddCell(cell);

            footer.WriteSelectedRows(0, -1, (document.PageSize.Width - footer.TotalWidth) / 2, 30, writer.DirectContent);
        }
    }

}