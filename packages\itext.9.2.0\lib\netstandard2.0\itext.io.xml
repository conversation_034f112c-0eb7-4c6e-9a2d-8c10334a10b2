<?xml version="1.0"?>
<doc>
    <assembly>
        <name>itext.io</name>
    </assembly>
    <members>
        <member name="T:iText.IO.Codec.BitFile">
            <summary>Came from GIFEncoder initially.</summary>
            <remarks>
            Came from GIFEncoder initially.
            Modified - to allow for output compressed data without the block counts
            which breakup the compressed data stream for GIF.
            </remarks>
        </member>
        <member name="F:iText.IO.Codec.BitFile.blocks">
            <summary>note this also indicates gif format BITFile.</summary>
        </member>
        <member name="M:iText.IO.Codec.BitFile.#ctor(System.IO.Stream,System.Boolean)">
            <param name="output">destination for output data</param>
            <param name="blocks">GIF LZW requires block counts for output data</param>
        </member>
        <member name="T:iText.IO.Codec.Brotli.Dec.BitReader">
            <summary>Bit reading helpers.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.BitReader.Capacity">
            <summary>
            Input byte buffer, consist of a ring-buffer and a "slack" region where bytes from the start of
            the ring-buffer are copied.
            </summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.BitReader.endOfStreamReached">
            <summary>Input stream is finished.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.BitReader.accumulator">
            <summary>Pre-fetched bits.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.BitReader.bitOffset">
            <summary>Current bit-reading position in accumulator.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.BitReader.intOffset">
            <summary>Offset of next item in intBuffer.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.IO.Codec.Brotli.Dec.BitReader.ReadMoreInput(iText.IO.Codec.Brotli.Dec.BitReader)" -->
        <member name="M:iText.IO.Codec.Brotli.Dec.BitReader.FillBitWindow(iText.IO.Codec.Brotli.Dec.BitReader)">
            <summary>Advances the Read buffer by 5 bytes to make room for reading next 24 bits.</summary>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.BitReader.ReadBits(iText.IO.Codec.Brotli.Dec.BitReader,System.Int32)">
            <summary>Reads the specified number of bits from Read Buffer.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.IO.Codec.Brotli.Dec.BitReader.Init(iText.IO.Codec.Brotli.Dec.BitReader,System.IO.Stream)" -->
        <!-- Badly formed XML comment ignored for member "T:iText.IO.Codec.Brotli.Dec.BrotliInputStream" -->
        <member name="F:iText.IO.Codec.Brotli.Dec.BrotliInputStream.buffer">
            <summary>Internal buffer used for efficient byte-by-byte reading.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.BrotliInputStream.remainingBufferBytes">
            <summary>Number of decoded but still unused bytes in internal buffer.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.BrotliInputStream.bufferOffset">
            <summary>Next unused byte offset.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.BrotliInputStream.state">
            <summary>Decoder state.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.IO.Codec.Brotli.Dec.BrotliInputStream.#ctor(System.IO.Stream)" -->
        <!-- Badly formed XML comment ignored for member "M:iText.IO.Codec.Brotli.Dec.BrotliInputStream.#ctor(System.IO.Stream,System.Int32)" -->
        <!-- Badly formed XML comment ignored for member "M:iText.IO.Codec.Brotli.Dec.BrotliInputStream.#ctor(System.IO.Stream,System.Int32,System.Byte[])" -->
        <member name="M:iText.IO.Codec.Brotli.Dec.BrotliInputStream.ReadByte">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.BrotliInputStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.IO.Codec.Brotli.Dec.BrotliRuntimeException">
            <summary>Unchecked exception used internally.</summary>
        </member>
        <member name="T:iText.IO.Codec.Brotli.Dec.Context">
            <summary>Common context lookup table for all context modes.</summary>
        </member>
        <member name="T:iText.IO.Codec.Brotli.Dec.Decode">
            <summary>API for Brotli decompression.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.Decode.FixedTable">
            <summary>Static Huffman code for the code length code lengths.</summary>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.Decode.DecodeVarLenUnsignedByte(iText.IO.Codec.Brotli.Dec.BitReader)">
            <summary>Decodes a number in the range [0..255], by reading 1 - 11 bits.</summary>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.Decode.ReadSymbol(System.Int32[],System.Int32,iText.IO.Codec.Brotli.Dec.BitReader)">
            <summary>Decodes the next Huffman code from bit-stream.</summary>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.Decode.ReadMetablockInfo(iText.IO.Codec.Brotli.Dec.State)">
            <summary>Reads next metablock header.</summary>
            <param name="state">decoding state</param>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.Decode.Decompress(iText.IO.Codec.Brotli.Dec.State)">
            <summary>Actual decompress implementation.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:iText.IO.Codec.Brotli.Dec.Dictionary" -->
        <!-- Badly formed XML comment ignored for member "T:iText.IO.Codec.Brotli.Dec.Dictionary.DataHolder0" -->
        <member name="T:iText.IO.Codec.Brotli.Dec.Huffman">
            <summary>Utilities for building Huffman decoding tables.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.Huffman.HuffmanMaxTableSize">
            <summary>
            Maximum possible Huffman table size for an alphabet size of 704, max code length 15 and root
            table bits 8.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.IO.Codec.Brotli.Dec.Huffman.GetNextKey(System.Int32,System.Int32)" -->
        <!-- Badly formed XML comment ignored for member "M:iText.IO.Codec.Brotli.Dec.Huffman.ReplicateValue(System.Int32[],System.Int32,System.Int32,System.Int32,System.Int32)" -->
        <member name="M:iText.IO.Codec.Brotli.Dec.Huffman.NextTableBitSize(System.Int32[],System.Int32,System.Int32)">
            <param name="count">histogram of bit lengths for the remaining symbols,</param>
            <param name="len">code length of the next processed symbol.</param>
            <returns>table width of the next 2nd level table.</returns>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.Huffman.BuildHuffmanTable(System.Int32[],System.Int32,System.Int32,System.Int32[],System.Int32)">
            <summary>Builds Huffman lookup table assuming code lengths are in symbol order.</summary>
        </member>
        <member name="T:iText.IO.Codec.Brotli.Dec.HuffmanTreeGroup">
            <summary>Contains a collection of huffman trees with the same alphabet size.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.HuffmanTreeGroup.alphabetSize">
            <summary>The maximal alphabet size in this group.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.HuffmanTreeGroup.codes">
            <summary>Storage for Huffman lookup tables.</summary>
        </member>
        <member name="F:iText.IO.Codec.Brotli.Dec.HuffmanTreeGroup.trees">
            <summary>
            Offsets of distinct lookup tables in
            <see cref="F:iText.IO.Codec.Brotli.Dec.HuffmanTreeGroup.codes"/>
            storage.
            </summary>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.HuffmanTreeGroup.Init(iText.IO.Codec.Brotli.Dec.HuffmanTreeGroup,System.Int32,System.Int32)">
            <summary>Initializes the Huffman tree group.</summary>
            <param name="group">POJO to be initialised</param>
            <param name="alphabetSize">the maximal alphabet size in this group</param>
            <param name="n">number of Huffman codes</param>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.HuffmanTreeGroup.Decode(iText.IO.Codec.Brotli.Dec.HuffmanTreeGroup,iText.IO.Codec.Brotli.Dec.BitReader)">
            <summary>Decodes Huffman trees from input stream and constructs lookup tables.</summary>
            <param name="group">target POJO</param>
            <param name="br">data source</param>
        </member>
        <member name="T:iText.IO.Codec.Brotli.Dec.IntReader">
            <summary>Byte-to-int conversion magic.</summary>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.IntReader.Convert(iText.IO.Codec.Brotli.Dec.IntReader,System.Int32)">
            <summary>Translates bytes to ints.</summary>
            <remarks>
            Translates bytes to ints.
            NB: intLen == 4 * byteSize!
            NB: intLen should be less or equal to intBuffer length.
            </remarks>
        </member>
        <!-- Badly formed XML comment ignored for member "T:iText.IO.Codec.Brotli.Dec.Prefix" -->
        <member name="T:iText.IO.Codec.Brotli.Dec.RunningState">
            <summary>Enumeration of decoding state-machine.</summary>
        </member>
        <member name="M:iText.IO.Codec.Brotli.Dec.State.SetInput(iText.IO.Codec.Brotli.Dec.State,System.IO.Stream)">
            <summary>Associate input with decoder state.</summary>
            <param name="state">uninitialized state without associated input</param>
            <param name="input">compressed data source</param>
        </member>
        <member name="T:iText.IO.Codec.Brotli.Dec.Transform">
            <summary>Transformations on dictionary words.</summary>
        </member>
        <member name="T:iText.IO.Codec.Brotli.Dec.Utils">
            <summary>A set of utility methods.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.IO.Codec.Brotli.Dec.Utils.FillWithZeroes(System.Byte[],System.Int32,System.Int32)" -->
        <!-- Badly formed XML comment ignored for member "M:iText.IO.Codec.Brotli.Dec.Utils.FillWithZeroes(System.Int32[],System.Int32,System.Int32)" -->
        <!-- Badly formed XML comment ignored for member "T:iText.IO.Codec.Brotli.Dec.WordTransformType" -->
        <member name="T:iText.IO.Codec.CCITTG4Encoder">
            <summary>Encodes data in the CCITT G4 FAX format.</summary>
        </member>
        <member name="M:iText.IO.Codec.CCITTG4Encoder.#ctor(System.Int32)">
            <summary>Creates a new encoder.</summary>
            <param name="width">the line width</param>
        </member>
        <member name="M:iText.IO.Codec.CCITTG4Encoder.Fax4Encode(System.Byte[],System.Int32,System.Int32)">
            <summary>Encodes a number of lines.</summary>
            <param name="data">the data to be encoded</param>
            <param name="offset">the offset into the data</param>
            <param name="size">the size of the data to be encoded</param>
        </member>
        <member name="M:iText.IO.Codec.CCITTG4Encoder.Compress(System.Byte[],System.Int32,System.Int32)">
            <summary>Encodes a full image.</summary>
            <param name="data">the data to encode</param>
            <param name="width">the image width</param>
            <param name="height">the image height</param>
            <returns>the encoded image</returns>
        </member>
        <member name="M:iText.IO.Codec.CCITTG4Encoder.Fax4Encode(System.Byte[],System.Int32)">
            <summary>Encodes a number of lines.</summary>
            <param name="data">the data to be encoded</param>
            <param name="height">the number of lines to encode</param>
        </member>
        <member name="M:iText.IO.Codec.CCITTG4Encoder.Close">
            <summary>Closes the encoder and returns the encoded data.</summary>
            <returns>the encoded data</returns>
        </member>
        <member name="T:iText.IO.Codec.Jbig2SegmentReader">
            <summary>
            Class to read a JBIG2 file at a basic level: understand all the segments,
            understand what segments belong to which pages, how many pages there are,
            what the width and height of each page is, and global segments if there
            are any.
            </summary>
            <remarks>
            Class to read a JBIG2 file at a basic level: understand all the segments,
            understand what segments belong to which pages, how many pages there are,
            what the width and height of each page is, and global segments if there
            are any.  Or: the minimum required to be able to take a normal sequential
            or random-access organized file, and be able to embed JBIG2 pages as images
            in a PDF.
            </remarks>
        </member>
        <member name="T:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment">
            <summary>Inner class that holds information about a JBIG2 segment.</summary>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetDataLength">
            <summary>Retrieves the data length of a JBig2Segment object.</summary>
            <returns>data length value</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetDataLength(System.Int64)">
            <summary>Sets the data length of a JBig2Segment object.</summary>
            <param name="dataLength">data length value</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetPage">
            <summary>Retrieves the page number of a JBig2Segment object.</summary>
            <returns>page number</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetPage(System.Int32)">
            <summary>Sets the page number of a JBig2Segment object.</summary>
            <param name="page">page number</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetReferredToSegmentNumbers">
            <summary>Retrieves the referred-to segment numbers of a JBig2Segment object.</summary>
            <returns>Every referred-to segment number</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetReferredToSegmentNumbers(System.Int32[])">
            <summary>Sets the referred-to segment numbers of a JBig2Segment object.</summary>
            <param name="referredToSegmentNumbers">Referred-to segment numbers</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetSegmentRetentionFlags">
            <summary>Retrieves segment retention flags of a JBig2Segment object.</summary>
            <returns>Every segment retention flag value</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetSegmentRetentionFlags(System.Boolean[])">
            <summary>Sets segment retention flags of a JBig2Segment object.</summary>
            <param name="segmentRetentionFlags">Segment retention flag values</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetType">
            <summary>Retrieves type of the JBig2Segment object.</summary>
            <returns>Type value</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetType(System.Int32)">
            <summary>Sets type of the JBig2Segment object.</summary>
            <param name="type">Type value</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.IsDeferredNonRetain">
            <summary>Retrieves whether the object is deferred without retention.</summary>
            <remarks>
            Retrieves whether the object is deferred without retention.
            Default value is false.
            </remarks>
            <returns>true if deferred without retention, false otherwise</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetDeferredNonRetain(System.Boolean)">
            <summary>Sets whether the JBig2Segments object is deferred without retention.</summary>
            <param name="deferredNonRetain">true for deferred without retention, false otherwise</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetCountOfReferredToSegments">
            <summary>Retrieves the count of the referred-to segments.</summary>
            <returns>count of referred-to segments</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetCountOfReferredToSegments(System.Int32)">
            <summary>Sets the count of referred-to segments of the JBig2Segment object.</summary>
            <param name="countOfReferredToSegments">count of referred segments</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetData">
            <summary>Retrieves data of the JBig2Segment object.</summary>
            <returns>data bytes</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetData(System.Byte[])">
            <summary>Sets data of the JBig2Segment object.</summary>
            <param name="data">data bytes</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetHeaderData">
            <summary>Retrieves header data of the JBig2Segment object.</summary>
            <returns>header data bytes</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetHeaderData(System.Byte[])">
            <summary>Sets header data of the JBig2Segment object.</summary>
            <param name="headerData">header date bytes</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.IsPageAssociationSize">
            <summary>Retrieves page association size of the JBig2Segment object.</summary>
            <returns>page association size value</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetPageAssociationSize(System.Boolean)">
            <summary>Sets page association size of the JBig2Segment object.</summary>
            <param name="pageAssociationSize">page association size</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetPageAssociationOffset">
            <summary>Retrieves the page association offset of the JBig2Segment object.</summary>
            <returns>page association offset value</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.SetPageAssociationOffset(System.Int32)">
            <summary>Sets page association offset of the JBig2Segment object.</summary>
            <param name="pageAssociationOffset">page association offset</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Segment.GetSegmentNumber">
            <summary>Retrieves the segment number of the JBig2Segment object.</summary>
            <returns>segment number</returns>
        </member>
        <member name="T:iText.IO.Codec.Jbig2SegmentReader.Jbig2Page">
            <summary>Inner class that holds information about a JBIG2 page.</summary>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Page.GetPage">
            <summary>Retrieves the page number of the Jbig2Page object.</summary>
            <returns>page number</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Page.GetPageBitmapWidth">
            <summary>Retrieves page bitmap width of the Jbig2Page object.</summary>
            <returns>width of page bitmap</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Page.SetPageBitmapWidth(System.Int32)">
            <summary>Sets page bitmap width of the JBig2Page object.</summary>
            <param name="pageBitmapWidth">page bitmap width</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Page.GetPageBitmapHeight">
            <summary>Retrieves page bitmap height of the JBig2Page object.</summary>
            <returns>height of the page bitmap</returns>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Page.SetPageBitmapHeight(System.Int32)">
            <summary>Sets the height of the page bitmap of a Jbig2Page object.</summary>
            <param name="pageBitmapHeight">height of the page bitmap</param>
        </member>
        <member name="M:iText.IO.Codec.Jbig2SegmentReader.Jbig2Page.GetData(System.Boolean)">
            <summary>
            return as a single byte array the header-data for each segment in segment number
            order, EMBEDDED organization, but I am putting the needed segments in SEQUENTIAL organization.
            </summary>
            <remarks>
            return as a single byte array the header-data for each segment in segment number
            order, EMBEDDED organization, but I am putting the needed segments in SEQUENTIAL organization.
            if for_embedding, skip the segment types that are known to be not for acrobat.
            </remarks>
            <param name="for_embedding">True if the bytes represents embedded data, false otherwise</param>
            <returns>a byte array</returns>
        </member>
        <member name="T:iText.IO.Codec.LZWCompressor">
            <summary>
            Modified from original LZWCompressor to change interface to passing a
            buffer of data to be compressed.
            </summary>
        </member>
        <member name="F:iText.IO.Codec.LZWCompressor.codeSize_">
            <summary>base underlying code size of data being compressed 8 for TIFF, 1 to 8 for GIF</summary>
        </member>
        <member name="F:iText.IO.Codec.LZWCompressor.clearCode_">
            <summary>reserved clear code based on code size</summary>
        </member>
        <member name="F:iText.IO.Codec.LZWCompressor.endOfInfo_">
            <summary>reserved end of data code based on code size</summary>
        </member>
        <member name="F:iText.IO.Codec.LZWCompressor.numBits_">
            <summary>current number bits output for each code</summary>
        </member>
        <member name="F:iText.IO.Codec.LZWCompressor.limit_">
            <summary>limit at which current number of bits code size has to be increased</summary>
        </member>
        <member name="F:iText.IO.Codec.LZWCompressor.prefix_">
            <summary>the prefix code which represents the predecessor string to current input point</summary>
        </member>
        <member name="F:iText.IO.Codec.LZWCompressor.bf_">
            <summary>output destination for bit codes</summary>
        </member>
        <member name="F:iText.IO.Codec.LZWCompressor.lzss_">
            <summary>general purpose LZW string table</summary>
        </member>
        <member name="F:iText.IO.Codec.LZWCompressor.tiffFudge_">
            <summary>modify the limits of the code values in LZW encoding due to TIFF bug / feature</summary>
        </member>
        <member name="M:iText.IO.Codec.LZWCompressor.#ctor(System.IO.Stream,System.Int32,System.Boolean)">
            <param name="outputStream">destination for compressed data</param>
            <param name="codeSize">the initial code size for the LZW compressor</param>
            <param name="TIFF">flag indicating that TIFF lzw fudge needs to be applied</param>
        </member>
        <member name="M:iText.IO.Codec.LZWCompressor.Compress(System.Byte[],System.Int32,System.Int32)">
            <param name="buf">The data to be compressed to output stream</param>
            <param name="offset">The offset at which the data starts</param>
            <param name="length">The length of the data being compressed</param>
        </member>
        <member name="M:iText.IO.Codec.LZWCompressor.Flush">
            <summary>
            Indicate to compressor that no more data to go so write out
            any remaining buffered data.
            </summary>
        </member>
        <member name="T:iText.IO.Codec.LZWStringTable">
            <summary>General purpose LZW String Table.</summary>
            <remarks>
            General purpose LZW String Table.
            Extracted from GIFEncoder by Adam Doppelt
            Comments added by Robin Luiten
            <c>expandCode</c> added by Robin Luiten
            The strLen_ table to give quick access to the lenght of an expanded
            code for use by the <c>expandCode</c> method added by Robin.
            </remarks>
        </member>
        <member name="F:iText.IO.Codec.LZWStringTable.RES_CODES">
            <summary>codesize + Reserved Codes</summary>
        </member>
        <member name="F:iText.IO.Codec.LZWStringTable.strLen_">
            <summary>
            each entry corresponds to a code and contains the length of data
            that the code expands to when decoded.
            </summary>
        </member>
        <member name="M:iText.IO.Codec.LZWStringTable.#ctor">
            <summary>Constructor allocate memory for string store data</summary>
        </member>
        <member name="M:iText.IO.Codec.LZWStringTable.AddCharString(System.Int16,System.Byte)">
            <param name="index">value of -1 indicates no predecessor [used in initialization]</param>
            <param name="b">
            the byte [character] to add to the string store which follows
            the predecessor string specified the index.
            </param>
            <returns>
            0xFFFF if no space in table left for addition of predecessor
            index and byte b. Else return the code allocated for combination index + b.
            </returns>
        </member>
        <member name="M:iText.IO.Codec.LZWStringTable.FindCharString(System.Int16,System.Byte)">
            <param name="index">index to prefix string</param>
            <param name="b">the character that follws the index prefix</param>
            <returns>
            b if param index is HASH_FREE. Else return the code
            for this prefix and byte successor
            </returns>
        </member>
        <member name="M:iText.IO.Codec.LZWStringTable.ClearTable(System.Int32)">
            <param name="codesize">
            the size of code to be preallocated for the
            string store.
            </param>
        </member>
        <member name="M:iText.IO.Codec.LZWStringTable.ExpandCode(System.Byte[],System.Int32,System.Int16,System.Int32)">
            <summary>
            If expanded data doesn't fit into array only what will fit is written
            to buf and the return value indicates how much of the expanded code has
            been written to the buf.
            </summary>
            <remarks>
            If expanded data doesn't fit into array only what will fit is written
            to buf and the return value indicates how much of the expanded code has
            been written to the buf. The next call to expandCode() should be with
            the same code and have the skip parameter set the negated value of the
            previous return. Successive negative return values should be negated and
            added together for next skip parameter value with same code.
            </remarks>
            <param name="buf">buffer to place expanded data into</param>
            <param name="offset">offset to place expanded data</param>
            <param name="code">
            the code to expand to the byte array it represents.
            PRECONDITION This code must already be in the LZSS
            </param>
            <param name="skipHead">
            is the number of bytes at the start of the expanded code to
            be skipped before data is written to buf. It is possible that skipHead is
            equal to codeLen.
            </param>
            <returns>
            the length of data expanded into buf. If the expanded code is longer
            than space left in buf then the value returned is a negative number which when
            negated is equal to the number of bytes that were used of the code being expanded.
            This negative value also indicates the buffer is full.
            </returns>
        </member>
        <member name="T:iText.IO.Codec.PngWriter">
            <summary>Writes a PNG image.</summary>
        </member>
        <member name="T:iText.IO.Codec.TIFFConstants">
            <summary>A list of constants used in class TIFFImage.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_SUBFILETYPE">
            <summary>subfile data descriptor</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.FILETYPE_REDUCEDIMAGE">
            <summary>reduced resolution version</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.FILETYPE_PAGE">
            <summary>one page of many</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.FILETYPE_MASK">
            <summary>transparency mask</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_OSUBFILETYPE">
            <summary>+kind of data in subfile</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.OFILETYPE_IMAGE">
            <summary>full resolution image data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.OFILETYPE_REDUCEDIMAGE">
            <summary>reduced size image data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.OFILETYPE_PAGE">
            <summary>one page of many</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IMAGEWIDTH">
            <summary>image width in pixels</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IMAGELENGTH">
            <summary>image height in pixels</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_BITSPERSAMPLE">
            <summary>bits per channel (sample)</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_COMPRESSION">
            <summary>data compression technique</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_NONE">
            <summary>dump mode</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_CCITTRLE">
            <summary>CCITT modified Huffman RLE</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_CCITTFAX3">
            <summary>CCITT Group 3 fax encoding</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_CCITTFAX4">
            <summary>CCITT Group 4 fax encoding</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_LZW">
            <summary>Lempel-Ziv &amp; Welch</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_OJPEG">
            <summary>!6.0 JPEG</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_JPEG">
            <summary>%JPEG DCT compression</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_NEXT">
            <summary>NeXT 2-bit RLE</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_CCITTRLEW">
            <summary>#1 w/ word alignment</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_PACKBITS">
            <summary>Macintosh RLE</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_THUNDERSCAN">
            <summary>ThunderScan RLE</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_IT8CTPAD">
            <summary>IT8 CT w/padding</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_IT8LW">
            <summary>IT8 Linework RLE</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_IT8MP">
            <summary>IT8 Monochrome picture</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_IT8BL">
            <summary>IT8 Binary line art</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_PIXARFILM">
            <summary>Pixar companded 10bit LZW</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_PIXARLOG">
            <summary>Pixar companded 11bit ZIP</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_DEFLATE">
            <summary>Deflate compression</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_ADOBE_DEFLATE">
            <summary>Deflate compression, as recognized by Adobe</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_DCS">
            <summary>Kodak DCS encoding</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_JBIG">
            <summary>ISO JBIG</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_SGILOG">
            <summary>SGI Log Luminance RLE</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COMPRESSION_SGILOG24">
            <summary>SGI Log 24-bit packed</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PHOTOMETRIC">
            <summary>photometric interpretation</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_MINISWHITE">
            <summary>min value is white</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_MINISBLACK">
            <summary>min value is black</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_RGB">
            <summary>RGB color model</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_PALETTE">
            <summary>color map indexed</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_MASK">
            <summary>$holdout mask</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_SEPARATED">
            <summary>!color separations</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_YCBCR">
            <summary>!CCIR 601</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_CIELAB">
            <summary>!1976 CIE L*a*b</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_LOGL">
            <summary>CIE Log2(L)</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PHOTOMETRIC_LOGLUV">
            <summary>CIE Log2(L) (u',v')</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_THRESHHOLDING">
            <summary>+thresholding used on data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.THRESHHOLD_BILEVEL">
            <summary>b&amp;w art scan</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.THRESHHOLD_HALFTONE">
            <summary>or dithered scan</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.THRESHHOLD_ERRORDIFFUSE">
            <summary>usually floyd-steinberg</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_CELLWIDTH">
            <summary>+dithering matrix width</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_CELLLENGTH">
            <summary>+dithering matrix height</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_FILLORDER">
            <summary>data order within a byte</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.FILLORDER_MSB2LSB">
            <summary>most significant -&gt; least</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.FILLORDER_LSB2MSB">
            <summary>least significant -&gt; most</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_DOCUMENTNAME">
            <summary>name of doc.</summary>
            <remarks>name of doc. image is from</remarks>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IMAGEDESCRIPTION">
            <summary>info about image</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_MAKE">
            <summary>scanner manufacturer name</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_MODEL">
            <summary>scanner model name/number</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_STRIPOFFSETS">
            <summary>offsets to data strips</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_ORIENTATION">
            <summary>+image orientation</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.ORIENTATION_TOPLEFT">
            <summary>row 0 top, col 0 lhs</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.ORIENTATION_TOPRIGHT">
            <summary>row 0 top, col 0 rhs</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.ORIENTATION_BOTRIGHT">
            <summary>row 0 bottom, col 0 rhs</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.ORIENTATION_BOTLEFT">
            <summary>row 0 bottom, col 0 lhs</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.ORIENTATION_LEFTTOP">
            <summary>row 0 lhs, col 0 top</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.ORIENTATION_RIGHTTOP">
            <summary>row 0 rhs, col 0 top</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.ORIENTATION_RIGHTBOT">
            <summary>row 0 rhs, col 0 bottom</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.ORIENTATION_LEFTBOT">
            <summary>row 0 lhs, col 0 bottom</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_SAMPLESPERPIXEL">
            <summary>samples per pixel</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_ROWSPERSTRIP">
            <summary>rows per strip of data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_STRIPBYTECOUNTS">
            <summary>bytes counts for strips</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_MINSAMPLEVALUE">
            <summary>+minimum sample value</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_MAXSAMPLEVALUE">
            <summary>+maximum sample value</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_XRESOLUTION">
            <summary>pixels/resolution in x</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_YRESOLUTION">
            <summary>pixels/resolution in y</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PLANARCONFIG">
            <summary>storage organization</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PLANARCONFIG_CONTIG">
            <summary>single image plane</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PLANARCONFIG_SEPARATE">
            <summary>separate planes of data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PAGENAME">
            <summary>page name image is from</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_XPOSITION">
            <summary>x page offset of image lhs</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_YPOSITION">
            <summary>y page offset of image lhs</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_FREEOFFSETS">
            <summary>+byte offset to free block</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_FREEBYTECOUNTS">
            <summary>+sizes of free blocks</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_GRAYRESPONSEUNIT">
            <summary>$gray scale curve accuracy</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GRAYRESPONSEUNIT_10S">
            <summary>tenths of a unit</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GRAYRESPONSEUNIT_100S">
            <summary>hundredths of a unit</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GRAYRESPONSEUNIT_1000S">
            <summary>thousandths of a unit</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GRAYRESPONSEUNIT_10000S">
            <summary>ten-thousandths of a unit</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GRAYRESPONSEUNIT_100000S">
            <summary>hundred-thousandths</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_GRAYRESPONSECURVE">
            <summary>$gray scale response curve</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_GROUP3OPTIONS">
            <summary>32 flag bits</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GROUP3OPT_2DENCODING">
            <summary>2-dimensional coding</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GROUP3OPT_UNCOMPRESSED">
            <summary>data not compressed</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GROUP3OPT_FILLBITS">
            <summary>fill to byte boundary</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_GROUP4OPTIONS">
            <summary>32 flag bits</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GROUP4OPT_UNCOMPRESSED">
            <summary>data not compressed</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.GROUP4OPT_FILLBITS">
            <summary>fill to byte boundary</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_RESOLUTIONUNIT">
            <summary>units of resolutions</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.RESUNIT_NONE">
            <summary>no meaningful units</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.RESUNIT_INCH">
            <summary>english</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.RESUNIT_CENTIMETER">
            <summary>metric</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PAGENUMBER">
            <summary>page numbers of multi-page</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_COLORRESPONSEUNIT">
            <summary>$color curve accuracy</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COLORRESPONSEUNIT_10S">
            <summary>tenths of a unit</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COLORRESPONSEUNIT_100S">
            <summary>hundredths of a unit</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COLORRESPONSEUNIT_1000S">
            <summary>thousandths of a unit</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COLORRESPONSEUNIT_10000S">
            <summary>ten-thousandths of a unit</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.COLORRESPONSEUNIT_100000S">
            <summary>hundred-thousandths</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_TRANSFERFUNCTION">
            <summary>!colorimetry info</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_SOFTWARE">
            <summary>name and release</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_DATETIME">
            <summary>creation date and time</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_ARTIST">
            <summary>creator of image</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_HOSTCOMPUTER">
            <summary>machine where created</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PREDICTOR">
            <summary>prediction scheme w/ LZW</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PREDICTOR_NONE">
            <summary>no predictor</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.PREDICTOR_HORIZONTAL_DIFFERENCING">
            <summary>horizontal differencing</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_WHITEPOINT">
            <summary>image white point</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PRIMARYCHROMATICITIES">
            <summary>!primary chromaticities</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_COLORMAP">
            <summary>RGB map for pallette image</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_HALFTONEHINTS">
            <summary>!highlight+shadow info</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_TILEWIDTH">
            <summary>!rows/data tile</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_TILELENGTH">
            <summary>!cols/data tile</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_TILEOFFSETS">
            <summary>!offsets to data tiles</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_TILEBYTECOUNTS">
            <summary>!byte counts for tiles</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_BADFAXLINES">
            <summary>lines w/ wrong pixel count</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_CLEANFAXDATA">
            <summary>regenerated line info</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.CLEANFAXDATA_CLEAN">
            <summary>no errors detected</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.CLEANFAXDATA_REGENERATED">
            <summary>receiver regenerated lines</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.CLEANFAXDATA_UNCLEAN">
            <summary>uncorrected errors exist</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_CONSECUTIVEBADFAXLINES">
            <summary>max consecutive bad lines</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_SUBIFD">
            <summary>subimage descriptors</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_INKSET">
            <summary>!inks in separated image</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.INKSET_CMYK">
            <summary>!cyan-magenta-yellow-black</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_INKNAMES">
            <summary>!ascii names of inks</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_NUMBEROFINKS">
            <summary>!number of inks</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_DOTRANGE">
            <summary>!0% and 100% dot codes</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_TARGETPRINTER">
            <summary>!separation target</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_EXTRASAMPLES">
            <summary>!info about extra samples</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.EXTRASAMPLE_UNSPECIFIED">
            <summary>!unspecified data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.EXTRASAMPLE_ASSOCALPHA">
            <summary>!associated alpha data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.EXTRASAMPLE_UNASSALPHA">
            <summary>!unassociated alpha data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_SAMPLEFORMAT">
            <summary>!data sample format</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.SAMPLEFORMAT_UINT">
            <summary>!unsigned integer data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.SAMPLEFORMAT_INT">
            <summary>!signed integer data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.SAMPLEFORMAT_IEEEFP">
            <summary>!IEEE floating point data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.SAMPLEFORMAT_VOID">
            <summary>!untyped data</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.SAMPLEFORMAT_COMPLEXINT">
            <summary>!complex signed int</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.SAMPLEFORMAT_COMPLEXIEEEFP">
            <summary>!complex ieee floating</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_SMINSAMPLEVALUE">
            <summary>!variable MinSampleValue</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_SMAXSAMPLEVALUE">
            <summary>!variable MaxSampleValue</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGTABLES">
            <summary>%JPEG table stream</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGPROC">
            <summary>!JPEG processing algorithm</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.JPEGPROC_BASELINE">
            <summary>!baseline sequential</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.JPEGPROC_LOSSLESS">
            <summary>!Huffman coded lossless</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGIFOFFSET">
            <summary>!pointer to SOI marker</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGIFBYTECOUNT">
            <summary>!JFIF stream length</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGRESTARTINTERVAL">
            <summary>!restart interval length</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGLOSSLESSPREDICTORS">
            <summary>!lossless proc predictor</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGPOINTTRANSFORM">
            <summary>!lossless point transform</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGQTABLES">
            <summary>!Q matrice offsets</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGDCTABLES">
            <summary>!DCT table offsets</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JPEGACTABLES">
            <summary>!AC coefficient offsets</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_YCBCRCOEFFICIENTS">
            <summary>!RGB -&gt; YCbCr transform</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_YCBCRSUBSAMPLING">
            <summary>!YCbCr subsampling factors</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_YCBCRPOSITIONING">
            <summary>!subsample positioning</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.YCBCRPOSITION_CENTERED">
            <summary>!as in PostScript Level 2</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.YCBCRPOSITION_COSITED">
            <summary>!as in CCIR 601-1</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_REFERENCEBLACKWHITE">
            <summary>!colorimetry info</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_REFPTS">
            <summary>image reference points</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_REGIONTACKPOINT">
            <summary>region-xform tack point</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_REGIONWARPCORNERS">
            <summary>warp quadrilateral</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_REGIONAFFINE">
            <summary>affine transformation mat</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_MATTEING">
            <summary>$use ExtraSamples</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_DATATYPE">
            <summary>$use SampleFormat</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IMAGEDEPTH">
            <summary>z depth of image</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_TILEDEPTH">
            <summary>z depth/data tile</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PIXAR_IMAGEFULLWIDTH">
            <summary>full image size in x</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PIXAR_IMAGEFULLLENGTH">
            <summary>full image size in y</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PIXAR_TEXTUREFORMAT">
            <summary>texture map format</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PIXAR_WRAPMODES">
            <summary>s &amp; t wrap modes</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PIXAR_FOVCOT">
            <summary>cotan(fov) for env.</summary>
            <remarks>cotan(fov) for env. maps</remarks>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PIXAR_MATRIX_WORLDTOSCREEN">
            <summary>W2S</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PIXAR_MATRIX_WORLDTOCAMERA">
            <summary>W2C</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_WRITERSERIALNUMBER">
            <summary>
            device serial number
            tag 33405 is a private tag registered to Eastman Kodak
            </summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_COPYRIGHT">
            <summary>tag 33432 is listed in the 6.0 spec w/ unknown ownership</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_RICHTIFFIPTC">
            <summary>IPTC TAG from RichTIFF specifications</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8SITE">
            <summary>site name</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8COLORSEQUENCE">
            <summary>color seq.</summary>
            <remarks>color seq. [RGB,CMYK,etc]</remarks>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8HEADER">
            <summary>DDES Header</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8RASTERPADDING">
            <summary>raster scanline padding</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8BITSPERRUNLENGTH">
            <summary># of bits in short run</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8BITSPEREXTENDEDRUNLENGTH">
            <summary># of bits in long run</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8COLORTABLE">
            <summary>LW colortable</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8IMAGECOLORINDICATOR">
            <summary>BP/BL image color switch</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8BKGCOLORINDICATOR">
            <summary>BP/BL bg color switch</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8IMAGECOLORVALUE">
            <summary>BP/BL image color value</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8BKGCOLORVALUE">
            <summary>BP/BL bg color value</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8PIXELINTENSITYRANGE">
            <summary>MP pixel intensity value</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8TRANSPARENCYINDICATOR">
            <summary>HC transparency switch</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_IT8COLORCHARACTERIZATION">
            <summary>color character.</summary>
            <remarks>color character. table</remarks>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_FRAMECOUNT">
            <summary>Sequence Frame Count</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_ICCPROFILE">
            <summary>
            ICC profile data
            tag 34750 is a private tag registered to Adobe?
            </summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_PHOTOSHOP">
            <summary>tag 34377 is private tag registered to Adobe for PhotoShop</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_JBIGOPTIONS">
            <summary>
            JBIG options
            tag 34750 is a private tag registered to Pixel Magic
            </summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_FAXRECVPARAMS">
            <summary>encoded Class 2 ses.</summary>
            <remarks>encoded Class 2 ses. parms</remarks>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_FAXSUBADDRESS">
            <summary>received SubAddr string</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_FAXRECVTIME">
            <summary>receive time (secs)</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_STONITS">
            <summary>Sample value to Nits</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_FEDEX_EDR">
            <summary>
            unknown use
            tag 34929 is a private tag registered to FedEx
            </summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFConstants.TIFFTAG_DCSHUESHIFTVALUES">
            <summary>
            hue shift correction data
            tag 65535 is an undefined tag used by Eastman Kodak
            </summary>
        </member>
        <member name="T:iText.IO.Codec.TIFFDirectory">
            <summary>
            A class representing an Image File Directory (IFD) from a TIFF 6.0
            stream.
            </summary>
            <remarks>
            A class representing an Image File Directory (IFD) from a TIFF 6.0
            stream.  The TIFF file format is described in more detail in the
            comments for the TIFFDescriptor class.
            <br />
            <para /> A TIFF IFD consists of a set of TIFFField tags.  Methods are
            provided to query the set of tags and to obtain the raw field
            array.  In addition, convenience methods are provided for acquiring
            the values of tags that contain a single value that fits into a
            byte, int, long, float, or double.
            <br />
            <para /> Every TIFF file is made up of one or more public IFDs that are
            joined in a linked list, rooted in the file header.  A file may
            also contain so-called private IFDs that are referenced from
            tag data and do not appear in the main list.
            <br />
            <para /><b> This class is not a committed part of the JAI API.  It may
            be removed or changed in future releases of JAI.</b>
            </remarks>
            <seealso cref="T:iText.IO.Codec.TIFFField"/>
        </member>
        <member name="F:iText.IO.Codec.TIFFDirectory.isBigEndian">
            <summary>A boolean storing the endianness of the stream.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFDirectory.numEntries">
            <summary>The number of entries in the IFD.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFDirectory.fields">
            <summary>An array of TIFFFields.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFDirectory.fieldIndex">
            <summary>A Hashtable indexing the fields by tag number.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFDirectory.IFDOffset">
            <summary>The offset of this IFD.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFDirectory.nextIFDOffset">
            <summary>The offset of the next IFD.</summary>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.#ctor">
            <summary>The default constructor.</summary>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.#ctor(iText.IO.Source.RandomAccessFileOrArray,System.Int32)">
            <summary>Constructs a TIFFDirectory from a SeekableStream.</summary>
            <remarks>
            Constructs a TIFFDirectory from a SeekableStream.
            The directory parameter specifies which directory to read from
            the linked list present in the stream; directory 0 is normally
            read but it is possible to store multiple images in a single
            TIFF file by maintaining multiple directories.
            </remarks>
            <param name="stream">a SeekableStream to read from.</param>
            <param name="directory">the index of the directory to read.</param>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.#ctor(iText.IO.Source.RandomAccessFileOrArray,System.Int64,System.Int32)">
            <summary>Constructs a TIFFDirectory by reading a SeekableStream.</summary>
            <remarks>
            Constructs a TIFFDirectory by reading a SeekableStream.
            The ifd_offset parameter specifies the stream offset from which
            to begin reading; this mechanism is sometimes used to store
            private IFDs within a TIFF file that are not part of the normal
            sequence of IFDs.
            </remarks>
            <param name="stream">a SeekableStream to read from.</param>
            <param name="ifd_offset">the long byte offset of the directory.</param>
            <param name="directory">
            the index of the directory to read beyond the
            one at the current stream offset; zero indicates the IFD
            at the current offset.
            </param>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetNumEntries">
            <summary>Returns the number of directory entries.</summary>
            <returns>The number of directory entries</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetField(System.Int32)">
            <summary>
            Returns the value of a given tag as a TIFFField,
            or null if the tag is not present.
            </summary>
            <param name="tag">The tag</param>
            <returns>The value of the given tag as a TIFFField or null</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.IsTagPresent(System.Int32)">
            <summary>Returns true if a tag appears in the directory.</summary>
            <param name="tag">The tag</param>
            <returns>True if the tag appears in the directory, false otherwise</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetTags">
            <summary>
            Returns an ordered array of integers indicating the tags
            values.
            </summary>
            <returns>an ordered array of integers indicating the tags</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetFields">
            <summary>
            Returns an array of TIFFFields containing all the fields
            in this directory.
            </summary>
            <returns>an array of TIFFFields containing all the fields in this directory</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetFieldAsByte(System.Int32,System.Int32)">
            <summary>
            Returns the value of a particular index of a given tag as a
            byte.
            </summary>
            <remarks>
            Returns the value of a particular index of a given tag as a
            byte.  The caller is responsible for ensuring that the tag is
            present and has type TIFFField.TIFF_SBYTE, TIFF_BYTE, or
            TIFF_UNDEFINED.
            </remarks>
            <param name="tag">The tag</param>
            <param name="index">The index</param>
            <returns>the value of a particular index of a given tag as a byte</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetFieldAsByte(System.Int32)">
            <summary>
            Returns the value of index 0 of a given tag as a
            byte.
            </summary>
            <remarks>
            Returns the value of index 0 of a given tag as a
            byte.  The caller is responsible for ensuring that the tag is
            present and has  type TIFFField.TIFF_SBYTE, TIFF_BYTE, or
            TIFF_UNDEFINED.
            </remarks>
            <param name="tag">The tag</param>
            <returns>The value of index 0 of the given tag as a byte</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetFieldAsLong(System.Int32,System.Int32)">
            <summary>
            Returns the value of a particular index of a given tag as a
            long.
            </summary>
            <remarks>
            Returns the value of a particular index of a given tag as a
            long.  The caller is responsible for ensuring that the tag is
            present and has type TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED,
            TIFF_SHORT, TIFF_SSHORT, TIFF_SLONG or TIFF_LONG.
            </remarks>
            <param name="tag">The tag</param>
            <param name="index">The index</param>
            <returns>The value of the given index of the given tag as a long</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetFieldAsLong(System.Int32)">
            <summary>
            Returns the value of index 0 of a given tag as a
            long.
            </summary>
            <remarks>
            Returns the value of index 0 of a given tag as a
            long.  The caller is responsible for ensuring that the tag is
            present and has type TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED,
            TIFF_SHORT, TIFF_SSHORT, TIFF_SLONG or TIFF_LONG.
            </remarks>
            <param name="tag">The tag</param>
            <returns>The value of index 0 of the given tag as a long</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetFieldAsFloat(System.Int32,System.Int32)">
            <summary>
            Returns the value of a particular index of a given tag as a
            float.
            </summary>
            <remarks>
            Returns the value of a particular index of a given tag as a
            float.  The caller is responsible for ensuring that the tag is
            present and has numeric type (all but TIFF_UNDEFINED and
            TIFF_ASCII).
            </remarks>
            <param name="tag">The tag</param>
            <param name="index">The index</param>
            <returns>The value of the given index of the given tag as a float</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetFieldAsFloat(System.Int32)">
            <summary>Returns the value of index 0 of a given tag as a float.</summary>
            <remarks>
            Returns the value of index 0 of a given tag as a float.  The
            caller is responsible for ensuring that the tag is present and
            has numeric type (all but TIFF_UNDEFINED and TIFF_ASCII).
            </remarks>
            <param name="tag">The tag</param>
            <returns>The value of index 0 of the given tag as a float</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetFieldAsDouble(System.Int32,System.Int32)">
            <summary>
            Returns the value of a particular index of a given tag as a
            double.
            </summary>
            <remarks>
            Returns the value of a particular index of a given tag as a
            double.  The caller is responsible for ensuring that the tag is
            present and has numeric type (all but TIFF_UNDEFINED and
            TIFF_ASCII).
            </remarks>
            <param name="tag">The tag</param>
            <param name="index">The index</param>
            <returns>The value of the given index of the given tag as a double</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetFieldAsDouble(System.Int32)">
            <summary>Returns the value of index 0 of a given tag as a double.</summary>
            <remarks>
            Returns the value of index 0 of a given tag as a double.  The
            caller is responsible for ensuring that the tag is present and
            has numeric type (all but TIFF_UNDEFINED and TIFF_ASCII).
            </remarks>
            <param name="tag">The tag</param>
            <returns>The value of index 0 of the given tag as a double</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetNumDirectories(iText.IO.Source.RandomAccessFileOrArray)">
            <summary>
            Returns the number of image directories (subimages) stored in a
            given TIFF file, represented by a <c>SeekableStream</c>.
            </summary>
            <param name="stream">RandomAccessFileOrArray</param>
            <returns>
            The number of image directories (subimages) stored
            in a given TIFF file
            </returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.IsBigEndian">
            <summary>
            Returns a boolean indicating whether the byte order used in the
            TIFF file is big-endian (i.e. whether the byte order is from
            the most significant to the least significant)
            </summary>
            <returns>
            
            <see langword="true"/>
            if the byte order used in the TIFF file is big-endian
            </returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetIFDOffset">
            <summary>Returns the offset of the IFD corresponding to this <c>TIFFDirectory</c>.</summary>
            <returns>the offset of the IFD corresponding to this <c>TIFFDirectory</c>.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFDirectory.GetNextIFDOffset">
            <summary>
            Returns the offset of the next IFD after the IFD corresponding to this
            <c>TIFFDirectory</c>.
            </summary>
            <returns>
            the offset of the next IFD after the IFD corresponding to this
            <c>TIFFDirectory</c>.
            </returns>
        </member>
        <member name="T:iText.IO.Codec.TIFFFaxDecoder">
            <summary>Class that can decode TIFF files.</summary>
        </member>
        <member name="M:iText.IO.Codec.TIFFFaxDecoder.#ctor(System.Int32,System.Int32,System.Int32)">
            <param name="fillOrder">The fill order of the compressed data bytes.</param>
            <param name="w">width</param>
            <param name="h">height</param>
        </member>
        <member name="M:iText.IO.Codec.TIFFFaxDecoder.ReverseBits(System.Byte[])">
            <summary>Reverses the bits in the array</summary>
            <param name="b">the bits to reverse</param>
        </member>
        <member name="T:iText.IO.Codec.TIFFFaxDecompressor">
            <summary>Class that can decompress TIFF files.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFFaxDecompressor.fillOrder">
            <summary>The logical order of bits within a byte.</summary>
            <remarks>
            The logical order of bits within a byte.
            <pre>
            1 = MSB-to-LSB
            2 = LSB-to-MSB (flipped)
            </pre>
            </remarks>
        </member>
        <member name="F:iText.IO.Codec.TIFFFaxDecompressor.uncompressedMode">
            <summary>Uncompressed mode flag: 1 if uncompressed, 0 if not.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFFaxDecompressor.fillBits">
            <summary>
            EOL padding flag: 1 if fill bits have been added before an EOL such
            that the EOL ends on a byte boundary, 0 otherwise.
            </summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFFaxDecompressor.oneD">
            <summary>Coding dimensionality: 1 for 2-dimensional, 0 for 1-dimensional.</summary>
        </member>
        <member name="M:iText.IO.Codec.TIFFFaxDecompressor.SetOptions(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Invokes the superclass method and then sets instance variables on
            the basis of the metadata set on this decompressor.
            </summary>
            <param name="fillOrder">The fill order</param>
            <param name="compression">The compression algorithm</param>
            <param name="t4Options">The T4 options</param>
            <param name="t6Options">The T6 options</param>
        </member>
        <member name="T:iText.IO.Codec.TIFFField">
            <summary>A class representing a field in a TIFF 6.0 Image File Directory.</summary>
            <remarks>
            A class representing a field in a TIFF 6.0 Image File Directory.
            <para /> The TIFF file format is described in more detail in the
            comments for the TIFFDescriptor class.
            <para /> A field in a TIFF Image File Directory (IFD).  A field is defined
            as a sequence of values of identical data type.  TIFF 6.0 defines
            12 data types, which are mapped internally onto the Java data types
            byte, int, long, float, and double.
            <para /><b> This class is not a committed part of the JAI API.  It may
            be removed or changed in future releases of JAI.</b>
            </remarks>
            <seealso cref="T:iText.IO.Codec.TIFFDirectory"/>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_BYTE">
            <summary>Flag for 8 bit unsigned integers.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_ASCII">
            <summary>Flag for null-terminated ASCII strings.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_SHORT">
            <summary>Flag for 16 bit unsigned integers.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_LONG">
            <summary>Flag for 32 bit unsigned integers.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_RATIONAL">
            <summary>Flag for pairs of 32 bit unsigned integers.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_SBYTE">
            <summary>Flag for 8 bit signed integers.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_UNDEFINED">
            <summary>Flag for 8 bit uninterpreted bytes.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_SSHORT">
            <summary>Flag for 16 bit signed integers.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_SLONG">
            <summary>Flag for 32 bit signed integers.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_SRATIONAL">
            <summary>Flag for pairs of 32 bit signed integers.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_FLOAT">
            <summary>Flag for 32 bit IEEE floats.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.TIFF_DOUBLE">
            <summary>Flag for 64 bit IEEE doubles.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.tag">
            <summary>The tag number.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.type">
            <summary>The tag type.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.count">
            <summary>The number of data items present in the field.</summary>
        </member>
        <member name="F:iText.IO.Codec.TIFFField.data">
            <summary>The field data.</summary>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.#ctor">
            <summary>The default constructor.</summary>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.#ctor(System.Int32,System.Int32,System.Int32,System.Object)">
            <summary>Constructs a TIFFField with arbitrary data.</summary>
            <remarks>
            Constructs a TIFFField with arbitrary data.  The data
            parameter must be an array of a Java type appropriate for the
            type of the TIFF field.  Since there is no available 32-bit
            unsigned data type, long is used. The mapping between types is
            as follows:
            <table border="1" summary="tifffield data">
            <tr>
            <th> TIFF type </th> <th> Java type </th>
            </tr>
            <tr>
            <td><tt>TIFF_BYTE</tt></td>      <td><tt>byte</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_ASCII</tt></td>     <td><tt>String</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_SHORT</tt></td>     <td><tt>char</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_LONG</tt></td>      <td><tt>long</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_RATIONAL</tt></td>  <td><tt>long[2]</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_SBYTE</tt></td>     <td><tt>byte</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_UNDEFINED</tt></td> <td><tt>byte</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_SSHORT</tt></td>    <td><tt>short</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_SLONG</tt></td>     <td><tt>int</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_SRATIONAL</tt></td> <td><tt>int[2]</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_FLOAT</tt></td>     <td><tt>float</tt></td>
            </tr>
            <tr>
            <td><tt>TIFF_DOUBLE</tt></td>    <td><tt>double</tt></td>
            </tr>
            </table>
            </remarks>
            <param name="tag">the tag number</param>
            <param name="type">the tag type</param>
            <param name="count">the number of data items present in the field</param>
            <param name="data">the field data</param>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetTag">
            <summary>Returns the tag number</summary>
            <returns>the tag number, between 0 and 65535.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetFieldType">
            <summary>Returns the type of the data stored in the IFD.</summary>
            <remarks>
            Returns the type of the data stored in the IFD.
            For a TIFF6.0 file, the value will equal one of the
            TIFF_ constants defined in this class.  For future
            revisions of TIFF, higher values are possible.
            </remarks>
            <returns>The type of the data stored in the IFD</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetCount">
            <summary>Returns the number of elements in the IFD.</summary>
            <returns>The number of elements in the IFD</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsBytes">
            <summary>Returns the data as an uninterpreted array of bytes.</summary>
            <remarks>
            Returns the data as an uninterpreted array of bytes.
            The type of the field must be one of TIFF_BYTE, TIFF_SBYTE,
            or TIFF_UNDEFINED;
            <para /> For data in TIFF_BYTE format, the application must take
            care when promoting the data to longer integral types
            to avoid sign extension.
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_BYTE, TIFF_SBYTE, or TIFF_UNDEFINED.
            </remarks>
            <returns>the data as an uninterpreted array of bytes</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsChars">
            <summary>
            Returns TIFF_SHORT data as an array of chars (unsigned 16-bit
            integers).
            </summary>
            <remarks>
            Returns TIFF_SHORT data as an array of chars (unsigned 16-bit
            integers).
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_SHORT.
            </remarks>
            <returns>TIFF_SHORT data as an array of chars</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsShorts">
            <summary>
            Returns TIFF_SSHORT data as an array of shorts (signed 16-bit
            integers).
            </summary>
            <remarks>
            Returns TIFF_SSHORT data as an array of shorts (signed 16-bit
            integers).
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_SSHORT.
            </remarks>
            <returns>TIFF_SSHORT data as an array of shorts (signed 16-bit integers).</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsInts">
            <summary>
            Returns TIFF_SLONG data as an array of ints (signed 32-bit
            integers).
            </summary>
            <remarks>
            Returns TIFF_SLONG data as an array of ints (signed 32-bit
            integers).
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_SLONG.
            </remarks>
            <returns>TIFF_SLONG data as an array of ints (signed 32-bit integers).</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsLongs">
            <summary>
            Returns TIFF_LONG data as an array of longs (signed 64-bit
            integers).
            </summary>
            <remarks>
            Returns TIFF_LONG data as an array of longs (signed 64-bit
            integers).
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_LONG.
            </remarks>
            <returns>TIFF_LONG data as an array of longs (signed 64-bit integers).</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsFloats">
            <summary>Returns TIFF_FLOAT data as an array of floats.</summary>
            <remarks>
            Returns TIFF_FLOAT data as an array of floats.
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_FLOAT.
            </remarks>
            <returns>TIFF_FLOAT data as an array of floats.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsDoubles">
            <summary>Returns TIFF_DOUBLE data as an array of doubles.</summary>
            <remarks>
            Returns TIFF_DOUBLE data as an array of doubles.
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_DOUBLE.
            </remarks>
            <returns>TIFF_DOUBLE data as an array of doubles.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsStrings">
            <summary>Returns TIFF_ASCII data as an array of strings.</summary>
            <remarks>
            Returns TIFF_ASCII data as an array of strings.
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_ASCII.
            </remarks>
            <returns>TIFF_ASCII data as an array of strings.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsSRationals">
            <summary>Returns TIFF_SRATIONAL data as an array of 2-element arrays of ints.</summary>
            <remarks>
            Returns TIFF_SRATIONAL data as an array of 2-element arrays of ints.
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_SRATIONAL.
            </remarks>
            <returns>TIFF_SRATIONAL data as an array of 2-element arrays of ints.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsRationals">
            <summary>Returns TIFF_RATIONAL data as an array of 2-element arrays of longs.</summary>
            <remarks>
            Returns TIFF_RATIONAL data as an array of 2-element arrays of longs.
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_RATTIONAL.
            </remarks>
            <returns>TIFF_RATIONAL data as an array of 2-element arrays of longs.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsInt(System.Int32)">
            <summary>
            Returns data in TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED, TIFF_SHORT,
            TIFF_SSHORT, or TIFF_SLONG format as an int.
            </summary>
            <remarks>
            Returns data in TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED, TIFF_SHORT,
            TIFF_SSHORT, or TIFF_SLONG format as an int.
            <para /> TIFF_BYTE and TIFF_UNDEFINED data are treated as unsigned;
            that is, no sign extension will take place and the returned
            value will be in the range [0, 255].  TIFF_SBYTE data will
            be returned in the range [-128, 127].
            <para /> A ClassCastException will be thrown if the field is not of
            type TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED, TIFF_SHORT,
            TIFF_SSHORT, or TIFF_SLONG.
            </remarks>
            <param name="index">The index</param>
            <returns>
            data in TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED, TIFF_SHORT, TIFF_SSHORT,
            or TIFF_SLONG format as an int.
            </returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsLong(System.Int32)">
            <summary>
            Returns data in TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED, TIFF_SHORT,
            TIFF_SSHORT, TIFF_SLONG, or TIFF_LONG format as a long.
            </summary>
            <remarks>
            Returns data in TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED, TIFF_SHORT,
            TIFF_SSHORT, TIFF_SLONG, or TIFF_LONG format as a long.
            <para /> TIFF_BYTE and TIFF_UNDEFINED data are treated as unsigned;
            that is, no sign extension will take place and the returned
            value will be in the range [0, 255].  TIFF_SBYTE data will
            be returned in the range [-128, 127].
            <para /> A ClassCastException will be thrown if the field is not of
            type TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED, TIFF_SHORT,
            TIFF_SSHORT, TIFF_SLONG, or TIFF_LONG.
            </remarks>
            <param name="index">The index</param>
            <returns>
            data in TIFF_BYTE, TIFF_SBYTE, TIFF_UNDEFINED, TIFF_SHORT, TIFF_SSHORT, TIFF_SLONG,
            or TIFF_LONG format as a long.
            </returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsFloat(System.Int32)">
            <summary>Returns data in any numerical format as a float.</summary>
            <remarks>
            Returns data in any numerical format as a float. Data in
            TIFF_SRATIONAL or TIFF_RATIONAL format are evaluated by
            dividing the numerator into the denominator using
            double-precision arithmetic and then truncating to single
            precision. Data in TIFF_SLONG, TIFF_LONG, or TIFF_DOUBLE
            format may suffer from truncation.
            <para /> A ClassCastException will be thrown if the field is
            of type TIFF_UNDEFINED or TIFF_ASCII.
            </remarks>
            <param name="index">The index</param>
            <returns>data in any numerical format as a float.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsDouble(System.Int32)">
            <summary>Returns data in any numerical format as a double.</summary>
            <remarks>
            Returns data in any numerical format as a double. Data in
            TIFF_SRATIONAL or TIFF_RATIONAL format are evaluated by
            dividing the numerator into the denominator using
            double-precision arithmetic.
            <para /> A ClassCastException will be thrown if the field is of
            type TIFF_UNDEFINED or TIFF_ASCII.
            </remarks>
            <param name="index">The index</param>
            <returns>data in any numerical format as a double.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsString(System.Int32)">
            <summary>Returns a TIFF_ASCII data item as a String.</summary>
            <remarks>
            Returns a TIFF_ASCII data item as a String.
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_ASCII.
            </remarks>
            <param name="index">The index</param>
            <returns>a TIFF_ASCII data item as a String.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsSRational(System.Int32)">
            <summary>
            Returns a TIFF_SRATIONAL data item as a two-element array
            of ints.
            </summary>
            <remarks>
            Returns a TIFF_SRATIONAL data item as a two-element array
            of ints.
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_SRATIONAL.
            </remarks>
            <param name="index">The index</param>
            <returns>a TIFF_SRATIONAL data item as a two-element array of ints.</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.GetAsRational(System.Int32)">
            <summary>
            Returns a TIFF_RATIONAL data item as a two-element array
            of ints.
            </summary>
            <remarks>
            Returns a TIFF_RATIONAL data item as a two-element array
            of ints.
            <para /> A ClassCastException will be thrown if the field is not
            of type TIFF_RATIONAL.
            </remarks>
            <param name="index">The index</param>
            <returns>a TIFF_RATIONAL data item as a two-element array of ints</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFField.CompareTo(iText.IO.Codec.TIFFField)">
            <summary>
            Compares this <c>TIFFField</c> with another
            <c>TIFFField</c> by comparing the tags.
            </summary>
            <remarks>
            Compares this <c>TIFFField</c> with another
            <c>TIFFField</c> by comparing the tags.
            <para /><b>Note: this class has a natural ordering that is inconsistent
            with <c>equals()</c>.</b>
            </remarks>
        </member>
        <member name="T:iText.IO.Codec.TIFFLZWDecoder">
            <summary>A class for performing LZW decoding.</summary>
        </member>
        <member name="M:iText.IO.Codec.TIFFLZWDecoder.Decode(System.Byte[],System.Byte[],System.Int32)">
            <summary>Method to decode LZW compressed data.</summary>
            <param name="data">The compressed data</param>
            <param name="uncompData">Array to return the uncompressed data in</param>
            <param name="h">The number of rows the compressed data contains</param>
            <returns>The decoded data</returns>
        </member>
        <member name="M:iText.IO.Codec.TIFFLZWDecoder.InitializeStringTable">
            <summary>Initialize the string table.</summary>
        </member>
        <member name="M:iText.IO.Codec.TIFFLZWDecoder.WriteString(System.Byte[])">
            <summary>Write out the string just uncompressed.</summary>
            <param name="str">the byte string for uncompressed write out</param>
        </member>
        <member name="M:iText.IO.Codec.TIFFLZWDecoder.AddStringToTable(System.Byte[],System.Byte)">
            <summary>Add a new string to the string table.</summary>
            <param name="oldString">
            the byte string at the end of which the new string
            will be written and which will be added to the string table
            </param>
            <param name="newString">the byte to be written to the end of the old string</param>
        </member>
        <member name="M:iText.IO.Codec.TIFFLZWDecoder.AddStringToTable(System.Byte[])">
            <summary>Add a new string to the string table.</summary>
            <param name="str">the byte string which will be added to the string table</param>
        </member>
        <member name="M:iText.IO.Codec.TIFFLZWDecoder.ComposeString(System.Byte[],System.Byte)">
            <summary>Append <c>newString</c> to the end of <c>oldString</c>.</summary>
            <param name="oldString">the byte string at the end of which the new string will be written</param>
            <param name="newString">the byte to be written to the end of the old string</param>
            <returns>the byte string which is the sum of the new string and the old string</returns>
        </member>
        <member name="T:iText.IO.Codec.TiffWriter">
            <summary>Exports images as TIFF.</summary>
        </member>
        <member name="T:iText.IO.Codec.TiffWriter.FieldBase">
            <summary>Inner class class containing information about a field.</summary>
        </member>
        <member name="T:iText.IO.Codec.TiffWriter.FieldShort">
            <summary>Inner class containing info about a field.</summary>
        </member>
        <member name="T:iText.IO.Codec.TiffWriter.FieldLong">
            <summary>Inner class containing info about a field.</summary>
        </member>
        <member name="T:iText.IO.Codec.TiffWriter.FieldRational">
            <summary>Inner class containing info about a field.</summary>
        </member>
        <member name="T:iText.IO.Codec.TiffWriter.FieldByte">
            <summary>Inner class containing info about a field.</summary>
        </member>
        <member name="T:iText.IO.Codec.TiffWriter.FieldUndefined">
            <summary>Inner class containing info about a field.</summary>
        </member>
        <member name="T:iText.IO.Codec.TiffWriter.FieldImage">
            <summary>Inner class containing info about a field.</summary>
        </member>
        <member name="T:iText.IO.Codec.TiffWriter.FieldAscii">
            <summary>Inner class containing info about an ASCII field.</summary>
        </member>
        <member name="T:iText.IO.Colors.IccProfile">
            <summary>Class used to represent the International Color Consortium profile</summary>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.#ctor">
            <summary>Creates a new, empty icc profile.</summary>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetInstance(System.Byte[],System.Int32)">
            <summary>Construct an icc profile from the passed byte[], using the passed number of components.</summary>
            <param name="data">byte[] containing the raw icc profile data</param>
            <param name="numComponents">number of components the profile contains</param>
            <returns>IccProfile constructed from the data</returns>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetInstance(System.Byte[])">
            <summary>Construct an icc profile from the passed byte[], using the passed number of components.</summary>
            <param name="data">byte[] containing the raw icc profile data</param>
            <returns>IccProfile constructed from the data</returns>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetInstance(iText.IO.Source.RandomAccessFileOrArray)">
            <summary>Construct an icc profile from the passed random-access file or array.</summary>
            <param name="file">random-access file or array containing the profile</param>
            <returns>IccProfile constructed from the data</returns>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetInstance(System.IO.Stream)">
            <summary>Construct an icc profile from the passed InputStream.</summary>
            <param name="stream">inputstream containing the profile</param>
            <returns>IccProfile constructed from the data</returns>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetInstance(System.String)">
            <summary>Construct an icc profile from the file found at the passed path</summary>
            <param name="filename">path to the file contaning the profile</param>
            <returns>IccProfile constructed from the data</returns>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetIccColorSpaceName(System.Byte[])">
            <summary>Get the Color space name of the icc profile found in the data.</summary>
            <param name="data">byte[] containing the icc profile</param>
            <returns>String containing the color space of the profile</returns>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetIccDeviceClass(System.Byte[])">
            <summary>Get the device class of the icc profile found in the data.</summary>
            <param name="data">byte[] containing the icc profile</param>
            <returns>String containing the device class of the profile</returns>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetIccNumberOfComponents(System.Byte[])">
            <summary>Get the number of color components of the icc profile found in the data.</summary>
            <param name="data">byte[] containing the icc profile</param>
            <returns>Number of color components</returns>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetData">
            <summary>Get the icc color profile data.</summary>
            <returns>byte[] containing the data</returns>
        </member>
        <member name="M:iText.IO.Colors.IccProfile.GetNumComponents">
            <summary>Get the number of color components in the profile.</summary>
            <returns>number of components</returns>
        </member>
        <member name="T:iText.IO.Exceptions.ExceptionUtil">
            <summary>This file is a helper class for internal usage only.</summary>
            <remarks>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </remarks>
        </member>
        <member name="T:iText.IO.Exceptions.FontCompressionException">
            <summary>General compressed font parsing exception.</summary>
        </member>
        <member name="M:iText.IO.Exceptions.FontCompressionException.#ctor">
            <summary>
            Creates a new
            <see cref="T:iText.IO.Exceptions.FontCompressionException"/>.
            </summary>
        </member>
        <member name="M:iText.IO.Exceptions.FontCompressionException.#ctor(System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.IO.Exceptions.FontCompressionException"/>.
            </summary>
            <param name="message">the detail message</param>
        </member>
        <member name="M:iText.IO.Exceptions.FontCompressionException.#ctor(System.String,System.Exception)">
            <summary>
            Creates a new
            <see cref="T:iText.IO.Exceptions.FontCompressionException"/>.
            </summary>
            <param name="message">the detail message</param>
            <param name="cause">
            the cause (which is saved for later retrieval by
            <see cref="P:System.Exception.InnerException"/>
            method)
            </param>
        </member>
        <member name="T:iText.IO.Exceptions.IOException">
            <summary>Exception class for exceptions in io module.</summary>
        </member>
        <member name="F:iText.IO.Exceptions.IOException.obj">
            <summary>Object for more details</summary>
        </member>
        <member name="M:iText.IO.Exceptions.IOException.#ctor(System.String)">
            <summary>Creates a new IOException.</summary>
            <param name="message">the detail message.</param>
        </member>
        <member name="M:iText.IO.Exceptions.IOException.#ctor(System.Exception)">
            <summary>Creates a new IOException.</summary>
            <param name="cause">
            the cause (which is saved for later retrieval by
            <see cref="P:System.Exception.InnerException"/>
            method).
            </param>
        </member>
        <member name="M:iText.IO.Exceptions.IOException.#ctor(System.String,System.Object)">
            <summary>Creates a new IOException.</summary>
            <param name="message">the detail message.</param>
            <param name="obj">an object for more details.</param>
        </member>
        <member name="M:iText.IO.Exceptions.IOException.#ctor(System.String,System.Exception)">
            <summary>Creates a new IOException.</summary>
            <param name="message">the detail message.</param>
            <param name="cause">
            the cause (which is saved for later retrieval by
            <see cref="P:System.Exception.InnerException"/>
            method).
            </param>
        </member>
        <member name="M:iText.IO.Exceptions.IOException.#ctor(System.String,System.Exception,System.Object)">
            <summary>Creates a new instance of IOException.</summary>
            <param name="message">the detail message.</param>
            <param name="cause">
            the cause (which is saved for later retrieval by
            <see cref="P:System.Exception.InnerException"/>
            method).
            </param>
            <param name="obj">an object for more details.</param>
        </member>
        <member name="P:iText.IO.Exceptions.IOException.Message">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Exceptions.IOException.GetMessageParams">
            <summary>Gets additional params for Exception message.</summary>
            <returns>params for exception message.</returns>
        </member>
        <member name="M:iText.IO.Exceptions.IOException.SetMessageParams(System.Object[])">
            <summary>Sets additional params for Exception message.</summary>
            <param name="messageParams">additional params.</param>
            <returns>object itself.</returns>
        </member>
        <member name="T:iText.IO.Exceptions.IoExceptionMessageConstant">
            <summary>Class containing constants to be used in exceptions in the IO module.</summary>
        </member>
        <member name="T:iText.IO.Font.CFFFont.Item">
            <summary>List items for the linked list that builds the new CID font.</summary>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Item.Increment(System.Int32[])">
            <summary>Remember the current offset and increment by item's size in bytes.</summary>
            <param name="currentOffset">increment offset by item's size</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Item.Emit(System.Byte[])">
            <summary>Emit the byte stream for this item.</summary>
            <param name="buffer">byte array</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Item.Xref">
            <summary>Fix up cross references to this item (applies only to markers).</summary>
        </member>
        <member name="M:iText.IO.Font.CFFFont.OffsetItem.GetOffset">
            <summary>Retrieves offset of an OffsetItem object.</summary>
            <returns>offset value</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.OffsetItem.SetOffset(System.Int32)">
            <summary>Set the value of an offset item that was initially unknown.</summary>
            <remarks>
            Set the value of an offset item that was initially unknown.
            It will be fixed up latex by a call to xref on some marker.
            </remarks>
            <param name="offset">offset to set</param>
        </member>
        <member name="T:iText.IO.Font.CFFFont.RangeItem">
            <summary>A range item.</summary>
        </member>
        <member name="T:iText.IO.Font.CFFFont.IndexOffsetItem">
            <summary>An index-offset item for the list.</summary>
            <remarks>
            An index-offset item for the list.
            The size denotes the required size in the CFF. A positive
            value means that we need a specific size in bytes (for offset arrays)
            and a negative value means that this is a dict item that uses a
            variable-size representation.
            </remarks>
        </member>
        <member name="T:iText.IO.Font.CFFFont.DictOffsetItem">
            <summary>an unknown offset in a dictionary for the list.</summary>
            <remarks>
            an unknown offset in a dictionary for the list.
            We will fix up the offset later; for now, assume it's large.
            </remarks>
        </member>
        <member name="T:iText.IO.Font.CFFFont.UInt24Item">
            <summary>Card24 item.</summary>
        </member>
        <member name="T:iText.IO.Font.CFFFont.UInt32Item">
            <summary>Card32 item.</summary>
        </member>
        <member name="T:iText.IO.Font.CFFFont.UInt16Item">
            <summary>A SID or Card16 item.</summary>
        </member>
        <member name="T:iText.IO.Font.CFFFont.UInt8Item">
            <summary>A Card8 item.</summary>
        </member>
        <member name="T:iText.IO.Font.CFFFont.DictNumberItem">
            <summary>A dictionary number on the list.</summary>
            <remarks>
            A dictionary number on the list.
            This implementation is inefficient: it doesn't use the variable-length
            representation.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.CFFFont.DictNumberItem.GetSize">
            <summary>Retrieves the size of a DictNumberItem.</summary>
            <returns>size value</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.DictNumberItem.SetSize(System.Int32)">
            <summary>Sets the size of a DictNumberItem.</summary>
            <param name="size">size value</param>
        </member>
        <member name="T:iText.IO.Font.CFFFont.MarkerItem">
            <summary>An offset-marker item for the list.</summary>
            <remarks>
            An offset-marker item for the list.
            It is used to mark an offset and to set the offset list item.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.CFFFont.GetEntireIndexRange(System.Int32)">
            <summary>a utility that creates a range item for an entire index</summary>
            <param name="indexOffset">where the index is</param>
            <returns>a range item representing the entire index</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.GetCID(System.String)">
            <summary>get a single CID font.</summary>
            <remarks>
            get a single CID font. The PDF architecture (1.4)
            supports 16-bit strings only with CID CFF fonts, not
            in Type-1 CFF fonts, so we convert the font to CID if
            it is in the Type-1 format.
            Two other tasks that we need to do are to select
            only a single font from the CFF package (this again is
            a PDF restriction) and to subset the CharStrings glyph
            description.
            </remarks>
            <param name="fontName">name of the font</param>
            <returns>byte array represents the CID font</returns>
        </member>
        <member name="F:iText.IO.Font.CFFFont.buf">
            <summary>A random Access File or an array</summary>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetName">
            <summary>Retrieves the name of the font.</summary>
            <returns>font name</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetName(System.String)">
            <summary>Sets the name of the font.</summary>
            <param name="name">font name</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFullName">
            <summary>Retrieves the full name of the font.</summary>
            <returns>full font name</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFullName(System.String)">
            <summary>Sets the full name of the font.</summary>
            <param name="fullName">full font name</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.IsCID">
            <summary>Retrieves whether the font is a CID font.</summary>
            <returns>true if font is CID font, false otherwise</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetCID(System.Boolean)">
            <summary>Sets if font is CID font.</summary>
            <param name="CID">true if font is CID font, false otherwise</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetPrivateOffset">
            <summary>Retrieves the private offset of the font.</summary>
            <returns>private offset value</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetPrivateOffset(System.Int32)">
            <summary>Sets the private offset of the font.</summary>
            <param name="privateOffset">private offset value</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetPrivateLength">
            <summary>Retrieves the private length of the font.</summary>
            <returns>private length value</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetPrivateLength(System.Int32)">
            <summary>Sets the private length of the font.</summary>
            <param name="privateLength">private length value</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetPrivateSubrs">
            <summary>Retrieves the private subrs of the font.</summary>
            <returns>private subrs value</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetPrivateSubrs(System.Int32)">
            <summary>Sets the private subrs of the font.</summary>
            <param name="privateSubrs">private subrs value</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetCharstringsOffset">
            <summary>Retrieves the char string offset of the font.</summary>
            <returns>char string offset</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetCharstringsOffset(System.Int32)">
            <summary>Sets the char string offset of the font.</summary>
            <param name="charstringsOffset">char string offset</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetEncodingOffset">
            <summary>Retrieves the encoding offset of the font.</summary>
            <returns>encoding offset</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetEncodingOffset(System.Int32)">
            <summary>Sets the encoding offset of the font.</summary>
            <param name="encodingOffset">encoding offset</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetCharsetOffset">
            <summary>Retrieves the charset offset of the font.</summary>
            <returns>charset offset</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetCharsetOffset(System.Int32)">
            <summary>Sets the charset offset of the font.</summary>
            <param name="charsetOffset">charset offset</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFdarrayOffset">
            <summary>Retrieves the font dictionary array offset of the object.</summary>
            <returns>FD array offset</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFdarrayOffset(System.Int32)">
            <summary>Sets the font dictionary array offset of the object.</summary>
            <param name="fdarrayOffset">FD array offset</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFdselectOffset">
            <summary>Retrieves the font dictionary select offset of the object.</summary>
            <returns>FD select offset</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFdselectOffset(System.Int32)">
            <summary>Sets the font dictionary select offset of the object.</summary>
            <param name="fdselectOffset">FD select offset</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFdprivateOffsets">
            <summary>Retrieves the font dictionary private offsets of the object.</summary>
            <returns>FD private offsets</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFdprivateOffsets(System.Int32[])">
            <summary>Sets the font dictionary private offsets of the object.</summary>
            <param name="fdprivateOffsets">FD private offsets</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFdprivateLengths">
            <summary>Retrieves the font dictionary private lengths of the object.</summary>
            <returns>FD private lengths</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFdprivateLengths(System.Int32[])">
            <summary>Sets the font dictionary private lengths of the object.</summary>
            <param name="fdprivateLengths">FD private lengths</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFdprivateSubrs">
            <summary>Retrieves the font dictionary private subrs of the object.</summary>
            <returns>FD private subrs</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFdprivateSubrs(System.Int32[])">
            <summary>Sets the font dictionary private subrs of the object.</summary>
            <param name="fdprivateSubrs">FD private subrs</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetNglyphs">
            <summary>Retrieves the number of glyphs of the font.</summary>
            <returns>number of glyphs</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetNglyphs(System.Int32)">
            <summary>Sets the number of glyphs of the font.</summary>
            <param name="nglyphs">number of glyphs</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetNstrings">
            <summary>Retrieves the number of strings of the font.</summary>
            <returns>number of strings</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetNstrings(System.Int32)">
            <summary>Sets the number of strings of the font.</summary>
            <param name="nstrings">number of strings</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetCharsetLength">
            <summary>Retrieves the charset length of the font.</summary>
            <returns>charset length</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetCharsetLength(System.Int32)">
            <summary>Sets the charset length of the font.</summary>
            <param name="charsetLength">charset length</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetCharstringsOffsets">
            <summary>Retrieves the char strings offsets of the font.</summary>
            <returns>char strings offsets</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetCharstringsOffsets(System.Int32[])">
            <summary>Sets the char strings offsets of the font.</summary>
            <param name="charstringsOffsets">char strings offsets</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetCharset">
            <summary>Retrieves the charset of the font.</summary>
            <returns>charset</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetCharset(System.Int32[])">
            <summary>Sets the charset of the font.</summary>
            <param name="charset">charset</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFDSelect">
            <summary>Retrieves the font dictionary select of the object.</summary>
            <returns>FD select</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFDSelect(System.Int32[])">
            <summary>Sets the font dictionary select of the object.</summary>
            <param name="FDSelect">FD select</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFDSelectLength">
            <summary>Retrieves the font dictionary select length of the object.</summary>
            <returns>FD select length</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFDSelectLength(System.Int32)">
            <summary>Sets the font dictionary select length of the object.</summary>
            <param name="FDSelectLength">FD select length</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFDSelectFormat">
            <summary>Retrieves the font dictionary select format of the object.</summary>
            <returns>FD select format</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFDSelectFormat(System.Int32)">
            <summary>Sets the font dictionary select format of the object.</summary>
            <param name="FDSelectFormat">FD select format</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetCharstringType">
            <summary>Retrieves the char string type of the font.</summary>
            <returns>char string type</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetCharstringType(System.Int32)">
            <summary>Sets the char string type of the font.</summary>
            <param name="charstringType">char string type</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFDArrayCount">
            <summary>Retrieves the font dictionary array count of the object.</summary>
            <returns>FD array count</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFDArrayCount(System.Int32)">
            <summary>Sets the font dictionary array count of the object.</summary>
            <param name="FDArrayCount">FD array count</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFDArrayOffsize">
            <summary>Retrieves the font dictionary array offsize of the object.</summary>
            <returns>FD array offsize</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFDArrayOffsize(System.Int32)">
            <summary>Sets the font dictionary array offsize of the object.</summary>
            <param name="FDArrayOffsize">FD array offsize</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetFDArrayOffsets">
            <summary>Retrieves the font dictionary array offsets of the object.</summary>
            <returns>FD array offsets</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetFDArrayOffsets(System.Int32[])">
            <summary>Sets the font dictionary array offsets of the object.</summary>
            <param name="FDArrayOffsets">FD array offsets</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetPrivateSubrsOffset">
            <summary>Retrieves the private subrs offset of the font.</summary>
            <returns>private subrs offset</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetPrivateSubrsOffset(System.Int32[])">
            <summary>Set the private subrs offset of the font</summary>
            <param name="privateSubrsOffset">private subrs offset</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetPrivateSubrsOffsetsArray">
            <summary>Retrieves the private subrs offsets array of the font.</summary>
            <returns>private subrs offsets array</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetPrivateSubrsOffsetsArray(System.Int32[][])">
            <summary>Sets the private subrs offsets array of the font.</summary>
            <param name="privateSubrsOffsetsArray">private subrs offsets array</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetSubrsOffsets">
            <summary>Retrieves the subrs offsets of the font.</summary>
            <returns>subrs offsets</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetSubrsOffsets(System.Int32[])">
            <summary>Sets the subrs offsets of the font.</summary>
            <param name="subrsOffsets">subrs offsets</param>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.GetGidToCid">
            <summary>Retrieves the glyphs to character id array of the font.</summary>
            <returns>glyphs to character id array</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFont.Font.SetGidToCid(System.Int32[])">
            <summary>Sets the glyphs to character id array of the font.</summary>
            <param name="gidToCid">glyphs to character id array</param>
        </member>
        <member name="T:iText.IO.Font.CFFFontSubset">
            <summary>This Class subsets a CFF Type Font.</summary>
            <remarks>
            This Class subsets a CFF Type Font. The subset is preformed for CID fonts and NON CID fonts.
            The Charstring is subsetted for both types. For CID fonts only the FDArray which are used are embedded.
            The Lsubroutines of the FDArrays used are subsetted as well. The Subroutine subset supports both Type1 and Type2
            formatting although only tested on Type2 Format.
            For Non CID the Lsubroutines are subsetted. On both types the Gsubroutines is subsetted.
            A font which was not of CID type is transformed into CID as a part of the subset process.
            The CID synthetic creation was written by Sivan Toledo (<EMAIL>)
            </remarks>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.SubrsFunctions">
            <summary>The Strings in this array represent Type1/Type2 operator names</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.SubrsEscapeFuncs">
            <summary>The Strings in this array represent Type1/Type2 escape operator names</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.ENDCHAR_OP">
            <summary>Operator codes for unused  CharStrings and unused local and global Subrs</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.GlyphsUsed">
            <summary>
            A Map containing the glyphs used in the text after being converted
            to glyph number by the CMap
            </summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.glyphsInList">
            <summary>The GlyphsUsed keys as an list</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.FDArrayUsed">
            <summary>A Set for keeping the FDArrays being used by the font</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.hSubrsUsed">
            <summary>A Maps array for keeping the subroutines used in each FontDict</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.lSubrsUsed">
            <summary>The SubroutinesUsed Maps as lists</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.hGSubrsUsed">
            <summary>A Map for keeping the Global subroutines used in the font</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.lGSubrsUsed">
            <summary>The Global SubroutinesUsed Maps as lists</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.hSubrsUsedNonCID">
            <summary>A Map for keeping the subroutines used in a non-cid font</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.lSubrsUsedNonCID">
            <summary>The SubroutinesUsed Map as list</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.NewLSubrsIndex">
            <summary>An array of the new Indexes for the local Subr.</summary>
            <remarks>An array of the new Indexes for the local Subr. One index for each FontDict</remarks>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.NewSubrsIndexNonCID">
            <summary>The new subroutines index for a non-cid font</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.NewGSubrsIndex">
            <summary>The new global subroutines index of the font</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.NewCharStringsIndex">
            <summary>The new CharString of the font</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.GBias">
            <summary>The bias for the global subroutines</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.OutputList">
            <summary>The linked list for generating the new font stream</summary>
        </member>
        <member name="F:iText.IO.Font.CFFFontSubset.NumOfHints">
            <summary>Number of arguments to the stem operators in a subroutine calculated recursively</summary>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.#ctor(System.Byte[])">
            <summary>C'tor for CFFFontSubset</summary>
            <param name="cff">- The font file</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CountCharset(System.Int32,System.Int32)">
            <summary>Calculates the length of the charset according to its format</summary>
            <param name="Offset">The Charset Offset</param>
            <param name="NumofGlyphs">Number of glyphs in the font</param>
            <returns>the length of the Charset</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CountRange(System.Int32,System.Int32)">
            <summary>Function calculates the number of ranges in the Charset</summary>
            <param name="NumofGlyphs">The number of glyphs in the font</param>
            <param name="Type">The format of the Charset</param>
            <returns>The number of ranges in the Charset data structure</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.ReadFDSelect(System.Int32)">
            <summary>Read the FDSelect of the font and compute the array and its length</summary>
            <param name="Font">The index of the font being processed</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildFDArrayUsed(System.Int32)">
            <summary>Function reads the FDSelect and builds the FDArrayUsed Map According to the glyphs used</summary>
            <param name="Font">the Number of font being processed</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.ReadFDArray(System.Int32)">
            <summary>Read the FDArray count, offsize and Offset array</summary>
            <param name="Font">the Number of font being processed</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.Process(System.String)">
            <summary>
            The Process function extracts one font out of the CFF file and returns a
            subset version of the original.
            </summary>
            <param name="fontName">- The name of the font to be taken out of the CFF</param>
            <returns>The new font stream</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.Process">
            <summary>
            The Process function extracts one font out of the CFF file and returns a
            subset version of the original with the first name.
            </summary>
            <returns>The new font stream</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CalcBias(System.Int32,System.Int32)">
            <summary>
            Function calcs bias according to the CharString type and the count
            of the subrs
            </summary>
            <param name="Offset">The offset to the relevant subrs index</param>
            <param name="Font">the font</param>
            <returns>The calculated Bias</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildNewCharString(System.Int32)">
            <summary>Function uses BuildNewIndex to create the new index of the subset charstrings.</summary>
            <param name="FontIndex">the font</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildNewLGSubrs(System.Int32)">
            <summary>Function builds the new local and global subsrs indices.</summary>
            <remarks>
            Function builds the new local and global subsrs indices. IF CID then All of
            the FD Array lsubrs will be subsetted.
            </remarks>
            <param name="Font">the font</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildFDSubrsOffsets(System.Int32,System.Int32)">
            <summary>
            The function finds for the FD array processed the local subr offset and its
            offset array.
            </summary>
            <param name="Font">the font</param>
            <param name="FD">The FDARRAY processed</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildSubrUsed(System.Int32,System.Int32,System.Int32,System.Int32[],System.Collections.Generic.ICollection{System.Int32},System.Collections.Generic.IList{System.Int32})">
            <summary>Function uses ReadAsubr on the glyph used to build the LSubr and Gsubr Map.</summary>
            <remarks>
            Function uses ReadAsubr on the glyph used to build the LSubr and Gsubr Map.
            The Map (of the lsubr only) is then scanned recursively for Lsubr and Gsubrs
            calls.
            </remarks>
            <param name="Font">the font</param>
            <param name="FD">FD array processed. 0 indicates function was called by non CID font</param>
            <param name="SubrOffset">the offset to the subr index to calc the bias</param>
            <param name="SubrsOffsets">the offset array of the subr index</param>
            <param name="hSubr">Map of the subrs used</param>
            <param name="lSubr">list of the subrs used</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildGSubrsUsed(System.Int32)">
            <summary>
            Function scans the Glsubr used list to find recursive calls
            to Gsubrs and adds to Map and list
            </summary>
            <param name="Font">the font</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.ReadASubr(System.Int32,System.Int32,System.Int32,System.Int32,System.Collections.Generic.ICollection{System.Int32},System.Collections.Generic.IList{System.Int32},System.Int32[])">
            <summary>The function reads a subrs (glyph info) between begin and end.</summary>
            <remarks>
            The function reads a subrs (glyph info) between begin and end.
            Adds calls to a Lsubr to the hSubr and lSubrs.
            Adds calls to a Gsubr to the hGSubr and lGSubrs.
            </remarks>
            <param name="begin">the start point of the subr</param>
            <param name="end">the end point of the subr</param>
            <param name="GBias">the bias of the Global Subrs</param>
            <param name="LBias">the bias of the Local Subrs</param>
            <param name="hSubr">the subroutines used as set</param>
            <param name="lSubr">the subroutines used as list</param>
            <param name="LSubrsOffsets">the offsets array of the subroutines</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.HandelStack">
            <summary>
            Function Checks how the current operator effects the run time stack after being run
            An operator may increase or decrease the stack size
            </summary>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.StackOpp">
            <summary>Function checks the key and return the change to the stack after the operator</summary>
            <returns>The change in the stack. 2-&gt; flush the stack</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.EmptyStack">
            <summary>Empty the Type2 Stack</summary>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.PopStack">
            <summary>Pop one element from the stack</summary>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.PushStack">
            <summary>Add an item to the stack</summary>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.ReadCommand">
            <summary>The function reads the next command after the file pointer is set</summary>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CalcHints(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32[])">
            <summary>The function reads the subroutine and returns the number of the hint in it.</summary>
            <remarks>
            The function reads the subroutine and returns the number of the hint in it.
            If a call to another subroutine is found the function calls recursively.
            </remarks>
            <param name="begin">the start point of the subr</param>
            <param name="end">the end point of the subr</param>
            <param name="LBias">the bias of the Local Subrs</param>
            <param name="GBias">the bias of the Global Subrs</param>
            <param name="LSubrsOffsets">The Offsets array of the subroutines</param>
            <returns>The number of hints in the subroutine read.</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildNewIndex(System.Int32[],System.Collections.Generic.ICollection{System.Int32},System.Byte)">
            <summary>Function builds the new offset array, object array and assembles the index.</summary>
            <remarks>
            Function builds the new offset array, object array and assembles the index.
            used for creating the glyph and subrs subsetted index
            </remarks>
            <param name="Offsets">the offset array of the original index</param>
            <param name="Used">the Map of the used objects</param>
            <param name="OperatorForUnusedEntries">the operator inserted into the data stream for unused entries</param>
            <returns>the new index subset version</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildNewIndexAndCopyAllGSubrs(System.Int32[],System.Byte)">
            <summary>Function builds the new offset array, object array and assembles the index.</summary>
            <remarks>
            Function builds the new offset array, object array and assembles the index.
            used for creating the glyph and subrs subsetted index
            </remarks>
            <param name="Offsets">the offset array of the original index</param>
            <param name="OperatorForUnusedEntries">the operator inserted into the data stream for unused entries</param>
            <returns>the new index subset version</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.AssembleIndex(System.Int32[],System.Byte[])">
            <summary>
            Function creates the new index, inserting the count,offsetsize,offset array
            and object array.
            </summary>
            <param name="NewOffsets">the subsetted offset array</param>
            <param name="NewObjects">the subsetted object array</param>
            <returns>the new index created</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildNewFile(System.Int32)">
            <summary>The function builds the new output stream according to the subset process</summary>
            <param name="Font">the font</param>
            <returns>the subsetted font stream</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CopyHeader">
            <summary>Function Copies the header from the original fileto the output list</summary>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.BuildIndexHeader(System.Int32,System.Int32,System.Int32)">
            <summary>Function Build the header of an index</summary>
            <param name="Count">the count field of the index</param>
            <param name="Offsize">the offsize field of the index</param>
            <param name="First">the first offset of the index</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CreateKeys(iText.IO.Font.CFFFont.OffsetItem,iText.IO.Font.CFFFont.OffsetItem,iText.IO.Font.CFFFont.OffsetItem,iText.IO.Font.CFFFont.OffsetItem)">
            <summary>Function adds the keys into the TopDict</summary>
            <param name="fdarrayRef">OffsetItem for the FDArray</param>
            <param name="fdselectRef">OffsetItem for the FDSelect</param>
            <param name="charsetRef">OffsetItem for the CharSet</param>
            <param name="charstringsRef">OffsetItem for the CharString</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CreateNewStringIndex(System.Int32)">
            <summary>
            Function takes the original string item and adds the new strings
            to accommodate the CID rules
            </summary>
            <param name="Font">the font</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CreateFDSelect(iText.IO.Font.CFFFont.OffsetItem,System.Int32)">
            <summary>Function creates new FDSelect for non-CID fonts.</summary>
            <remarks>
            Function creates new FDSelect for non-CID fonts.
            The FDSelect built uses a single range for all glyphs
            </remarks>
            <param name="fdselectRef">OffsetItem for the FDSelect</param>
            <param name="nglyphs">the number of glyphs in the font</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CreateCharset(iText.IO.Font.CFFFont.OffsetItem,System.Int32)">
            <summary>Function creates new CharSet for non-CID fonts.</summary>
            <remarks>
            Function creates new CharSet for non-CID fonts.
            The CharSet built uses a single range for all glyphs
            </remarks>
            <param name="charsetRef">OffsetItem for the CharSet</param>
            <param name="nglyphs">the number of glyphs in the font</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CreateFDArray(iText.IO.Font.CFFFont.OffsetItem,iText.IO.Font.CFFFont.OffsetItem,System.Int32)">
            <summary>Function creates new FDArray for non-CID fonts.</summary>
            <remarks>
            Function creates new FDArray for non-CID fonts.
            The FDArray built has only the "Private" operator that points to the font's
            original private dict
            </remarks>
            <param name="fdarrayRef">OffsetItem for the FDArray</param>
            <param name="privateRef">OffsetItem for the Private Dict</param>
            <param name="Font">the font</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.Reconstruct(System.Int32)">
            <summary>Function reconstructs the FDArray, PrivateDict and LSubr for CID fonts</summary>
            <param name="Font">the font</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.ReconstructFDArray(System.Int32,iText.IO.Font.CFFFont.OffsetItem[])">
            <summary>Function subsets the FDArray and builds the new one with new offsets</summary>
            <param name="Font">The font</param>
            <param name="fdPrivate">OffsetItem Array (one for each FDArray)</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.ReconstructPrivateDict(System.Int32,iText.IO.Font.CFFFont.OffsetItem[],iText.IO.Font.CFFFont.IndexBaseItem[],iText.IO.Font.CFFFont.OffsetItem[])">
            <summary>Function Adds the new private dicts (only for the FDs used) to the list</summary>
            <param name="Font">the font</param>
            <param name="fdPrivate">OffsetItem array one element for each private</param>
            <param name="fdPrivateBase">IndexBaseItem array one element for each private</param>
            <param name="fdSubrs">OffsetItem array one element for each private</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.ReconstructPrivateSubrs(System.Int32,iText.IO.Font.CFFFont.IndexBaseItem[],iText.IO.Font.CFFFont.OffsetItem[])">
            <summary>Function Adds the new LSubrs dicts (only for the FDs used) to the list</summary>
            <param name="Font">The index of the font</param>
            <param name="fdPrivateBase">The IndexBaseItem array for the linked list</param>
            <param name="fdSubrs">OffsetItem array for the linked list</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CalcSubrOffsetSize(System.Int32,System.Int32)">
            <summary>
            Calculates how many byte it took to write the offset for the subrs in a specific
            private dict.
            </summary>
            <param name="Offset">The Offset for the private dict</param>
            <param name="Size">The size of the private dict</param>
            <returns>The size of the offset of the subrs in the private dict</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CountEntireIndexRange(System.Int32)">
            <summary>Function computes the size of an index</summary>
            <param name="indexOffset">The offset for the computed index</param>
            <returns>The size of the index</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CreateNonCIDPrivate(System.Int32,iText.IO.Font.CFFFont.OffsetItem)">
            <summary>
            The function creates a private dict for a font that was not CID
            All the keys are copied as is except for the subrs key
            </summary>
            <param name="Font">the font</param>
            <param name="Subr">The OffsetItem for the subrs of the private</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.CreateNonCIDSubrs(System.Int32,iText.IO.Font.CFFFont.IndexBaseItem,iText.IO.Font.CFFFont.OffsetItem)">
            <summary>
            the function marks the beginning of the subrs index and adds the subsetted subrs
            index to the output list.
            </summary>
            <param name="Font">the font</param>
            <param name="PrivateBase">IndexBaseItem for the private that's referencing to the subrs</param>
            <param name="Subrs">OffsetItem for the subrs</param>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.GetCidForGlyphId(System.Int32)">
            <summary>Returns the CID to which specified GID is mapped.</summary>
            <param name="gid">glyph identifier</param>
            <returns>CID value</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.GetCidForGlyphId(System.Int32,System.Int32)">
            <summary>Returns the CID to which specified GID is mapped.</summary>
            <param name="fontIndex">index of font for which cid-gid mapping is to be identified</param>
            <param name="gid">glyph identifier</param>
            <returns>CID value</returns>
        </member>
        <member name="M:iText.IO.Font.CFFFontSubset.InitGlyphIdToCharacterIdArray(System.Int32,System.Int32,System.Int32)">
            <summary>Creates glyph-to-character id array.</summary>
            <param name="fontIndex">index of font for which charsets data is to be parsed</param>
            <param name="numOfGlyphs">number of glyphs in the font</param>
            <param name="offset">the offset to charsets data</param>
        </member>
        <member name="M:iText.IO.Font.CidFontProperties.IsCidFont(System.String,System.String)">
            <summary>Checks if its a valid CJKFont font.</summary>
            <param name="fontName">the font name.</param>
            <param name="enc">the encoding.</param>
            <returns>
            
            <see langword="true"/>
            if it is CJKFont.
            </returns>
        </member>
        <member name="T:iText.IO.Font.CjkResourceLoader">
            <summary>This class is responsible for loading and handling CJK fonts and CMaps from font-asian package.</summary>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.IsPredefinedCidFont(System.String)">
            <summary>
            Checks if the font with the given name and encoding is one
            of the predefined CID fonts.
            </summary>
            <param name="fontName">the font name.</param>
            <returns>
            
            <see langword="true"/>
            if it is CJKFont.
            </returns>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.GetCompatibleCidFont(System.String)">
            <summary>Finds a CJK font family which is compatible to the given CMap.</summary>
            <param name="cmap">a name of the CMap for which compatible font is searched.</param>
            <returns>a CJK font name if there's known compatible font for the given cmap name, or null otherwise.</returns>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.GetCompatibleCmaps(System.String)">
            <summary>
            Finds all CMap names that belong to the same registry to which a given
            font belongs.
            </summary>
            <param name="fontName">a name of the font for which CMap's are searched.</param>
            <returns>a set of CMap names corresponding to the given font.</returns>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.GetAllPredefinedCidFonts">
            <summary>Get all loaded predefined CID fonts.</summary>
            <returns>predefined CID fonts.</returns>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.GetRegistryNames">
            <summary>Get all loaded CJK registry names mapped to a set of compatible cmaps.</summary>
            <returns>CJK registry names mapped to a set of compatible cmaps.</returns>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.GetCid2UniCmap(System.String)">
            <summary>Parses CMap with a given name producing it in a form of cid to unicode mapping.</summary>
            <param name="uniMap">a CMap name. It is expected that CMap identified by this name defines unicode to cid mapping.
                </param>
            <returns>
            an object for convenient mapping from cid to unicode. If no CMap was found for provided name
            an exception is thrown.
            </returns>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.GetUni2CidCmap(System.String)">
            <summary>Parses CMap with a given name producing it in a form of unicode to cid mapping.</summary>
            <param name="uniMap">a CMap name. It is expected that CMap identified by this name defines unicode to cid mapping.
                </param>
            <returns>
            an object for convenient mapping from unicode to cid. If no CMap was found for provided name
            an exception is thrown.
            </returns>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.GetByte2CidCmap(System.String)">
            <summary>Parses CMap with a given name producing it in a form of byte to cid mapping.</summary>
            <param name="cmap">a CMap name. It is expected that CMap identified by this name defines byte to cid mapping.
                </param>
            <returns>
            an object for convenient mapping from byte to cid. If no CMap was found for provided name
            an exception is thrown.
            </returns>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.GetCidToCodepointCmap(System.String)">
            <summary>Parses CMap with a given name producing it in a form of cid to code point mapping.</summary>
            <param name="cmap">a CMap name. It is expected that CMap identified by this name defines code point to cid mapping.
                </param>
            <returns>
            an object for convenient mapping from cid to code point. If no CMap was found for provided name
            an exception is thrown.
            </returns>
        </member>
        <member name="M:iText.IO.Font.CjkResourceLoader.GetCodepointToCidCmap(System.String)">
            <summary>Parses CMap with a given name producing it in a form of code point to cid mapping.</summary>
            <param name="uniMap">a CMap name. It is expected that CMap identified by this name defines code point to cid mapping.
                </param>
            <returns>
            an object for convenient mapping from code point to cid. If no CMap was found for provided name
            an exception is thrown.
            </returns>
        </member>
        <member name="M:iText.IO.Font.CMapEncoding.#ctor(System.String)">
            <param name="cmap">CMap name.</param>
        </member>
        <member name="M:iText.IO.Font.CMapEncoding.#ctor(System.String,System.String)">
            <param name="cmap">CMap name.</param>
            <param name="uniMap">CMap to convert Unicode value to CID.</param>
        </member>
        <member name="M:iText.IO.Font.CMapEncoding.IsBuiltWith(System.String)">
            <summary>
            Checks whether the
            <see cref="T:iText.IO.Font.CMapEncoding"/>
            was built with corresponding cmap name.
            </summary>
            <param name="cmap">a CMAP</param>
            <returns>true, if the CMapEncoding was built with the cmap. Otherwise false.</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapByteCid.Cursor.GetOffset">
            <summary>Retrieves the offset of the object.</summary>
            <returns>offset value</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapByteCid.Cursor.SetOffset(System.Int32)">
            <summary>Sets the offset of the object.</summary>
            <param name="offset">offset value</param>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapByteCid.Cursor.GetLength">
            <summary>Retrieves the length of the object.</summary>
            <returns>length value</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapByteCid.Cursor.SetLength(System.Int32)">
            <summary>Sets the length value of the object.</summary>
            <param name="length">length value</param>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapByteCid.DecodeSequence(System.Byte[],System.Int32,System.Int32)">
            <summary>Decode byte sequence.</summary>
            <param name="cidBytes">byteCodeBytes</param>
            <param name="offset">number of bytes to skip before starting to return chars from the sequence</param>
            <param name="length">number of bytes to process</param>
            <returns>string that contains decoded representation of the given sequence</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapCidToCodepoint.GetCodeSpaceRanges">
            <summary>
            Returns a list containing sequential pairs of code space beginning and endings:
            (begincodespacerange1, endcodespacerange1, begincodespacerange2, endcodespacerange1, ...)
            </summary>
            <returns>
            list of
            <c>byte[]</c>
            that contain code space ranges
            </returns>
        </member>
        <member name="T:iText.IO.Font.Cmap.CMapCodepointToCid">
            <summary>Class represents real codepoint-CID mapping without any additional manipulation.</summary>
            <remarks>
            Class represents real codepoint-CID mapping without any additional manipulation.
            <para />
            See
            <see cref="T:iText.IO.Font.Cmap.CMapCidToCodepoint"/>
            for CID-codepoint representation.
            </remarks>
        </member>
        <member name="F:iText.IO.Font.Cmap.CMapContentParser.COMMAND_TYPE">
            <summary>Commands have this type.</summary>
        </member>
        <member name="F:iText.IO.Font.Cmap.CMapContentParser.tokeniser">
            <summary>Holds value of property tokeniser.</summary>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapContentParser.#ctor(iText.IO.Source.PdfTokenizer)">
            <summary>Creates a new instance of PdfContentParser</summary>
            <param name="tokeniser">the tokeniser with the content</param>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapContentParser.Parse(System.Collections.Generic.IList{iText.IO.Font.Cmap.CMapObject})">
            <summary>Parses a single command from the content.</summary>
            <remarks>
            Parses a single command from the content. Each command is output as an array of arguments
            having the command itself as the last element. The returned array will be empty if the
            end of content was reached.
            </remarks>
            <param name="ls">
            an
            <c>ArrayList</c>
            to use. It will be cleared before using.
            </param>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapContentParser.ReadDictionary">
            <summary>Reads a dictionary.</summary>
            <remarks>
            Reads a dictionary. The tokeniser must be positioned past the
            <c>"&lt;&lt;"</c>
            token.
            </remarks>
            <returns>the dictionary</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapContentParser.ReadArray">
            <summary>Reads an array.</summary>
            <remarks>Reads an array. The tokeniser must be positioned past the "[" token.</remarks>
            <returns>an array</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapContentParser.ReadObject">
            <summary>Reads a pdf object.</summary>
            <returns>the pdf object</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapContentParser.NextValidToken">
            <summary>Reads the next token skipping over the comments.</summary>
            <returns>
            
            <see langword="true"/>
            if a token was read,
            <see langword="false"/>
            if the end of content was reached.
            </returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapContentParser.ToHex(System.Int32)">
            <summary>Gets an hex string in the format "&lt;HHHH&gt;".</summary>
            <param name="n">the number</param>
            <returns>the hex string</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapLocationResource.GetLocationPath">
            <summary>Retrieve base folder path where CMaps are located.</summary>
            <returns>CMaps location path.</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapObject.ToString">
            <summary>
            Return String representation of
            <c>value</c>
            field.
            </summary>
        </member>
        <member name="T:iText.IO.Font.Cmap.CMapToUnicode">
            <summary>This class represents a CMap file.</summary>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapToUnicode.#ctor">
            <summary>Creates a new instance of CMap.</summary>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapToUnicode.HasByteMappings">
            <summary>This will tell if this cmap has any two byte mappings.</summary>
            <returns>true If there are any two byte mappings, false otherwise.</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapToUnicode.Lookup(System.Byte[],System.Int32,System.Int32)">
            <summary>This will perform a lookup into the map.</summary>
            <param name="code">The code used to lookup.</param>
            <param name="offset">The offset into the byte array.</param>
            <param name="length">The length of the data we are getting.</param>
            <returns>The string that matches the lookup.</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapToUnicode.GetCodeSpaceRanges">
            <summary>
            Returns a list containing sequential pairs of code space beginning and endings:
            (begincodespacerange1, endcodespacerange1, begincodespacerange2, endcodespacerange1, ...)
            </summary>
            <returns>
            list of
            <c>byte[]</c>
            that contain code space ranges
            </returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.CMapUniCid.GetCodePoints">
            <summary>Returns all mapped code points.</summary>
            <returns>an array containing all mapped code points</returns>
        </member>
        <member name="M:iText.IO.Font.Cmap.StandardCMapCharsets.DisableCharsetEncoders">
            <summary>Charset encoders are disabled.</summary>
        </member>
        <member name="M:iText.IO.Font.Cmap.StandardCMapCharsets.EnableCharsetEncoders">
            <summary>Charset encoders are enabled (default).</summary>
        </member>
        <member name="T:iText.IO.Font.Constants.FontDescriptorFlags">
            <summary>Font descriptor flags</summary>
        </member>
        <member name="T:iText.IO.Font.Constants.FontMacStyleFlags">
            <summary>Represents Open Type head.macStyle bits.</summary>
            <remarks>
            Represents Open Type head.macStyle bits.
            <para />
            https://www.microsoft.com/typography/otspec/head.htm
            </remarks>
        </member>
        <member name="T:iText.IO.Font.Constants.FontResources">
            <summary>Font resources paths.</summary>
        </member>
        <member name="T:iText.IO.Font.Constants.FontStretches">
            <summary>Font stretch constants and ids</summary>
        </member>
        <member name="M:iText.IO.Font.Constants.FontStretches.FromOpenTypeWidthClass(System.Int32)">
            <summary>Convert from Open Type font width class notation.</summary>
            <remarks>
            Convert from Open Type font width class notation.
            <para />
            https://www.microsoft.com/typography/otspec/os2.htm#wdc
            </remarks>
            <param name="fontWidth">Open Type font width.</param>
            <returns>
            one of the
            <see cref="T:iText.IO.Font.Constants.FontStretches"/>
            constants.
            </returns>
        </member>
        <member name="T:iText.IO.Font.Constants.FontStyles">
            <summary>Font styles ids</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.FontStyles.UNDEFINED">
            <summary>Undefined font style.</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.FontStyles.NORMAL">
            <summary>Normal font style.</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.FontStyles.BOLD">
            <summary>Bold font style.</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.FontStyles.ITALIC">
            <summary>Italic font style.</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.FontStyles.BOLDITALIC">
            <summary>Bold-Italic font style.</summary>
        </member>
        <member name="T:iText.IO.Font.Constants.FontWeights">
            <summary>Font weight values and utility methods</summary>
        </member>
        <member name="M:iText.IO.Font.Constants.FontWeights.FromType1FontWeight(System.String)">
            <summary>Parses font weight constant to corresponding value.</summary>
            <param name="weight">weight constant</param>
            <returns>corresponding weight int value</returns>
        </member>
        <member name="M:iText.IO.Font.Constants.FontWeights.NormalizeFontWeight(System.Int32)">
            <summary>
            Normalize font weight to either
            <see cref="F:iText.IO.Font.Constants.FontWeights.THIN"/>
            or
            <see cref="F:iText.IO.Font.Constants.FontWeights.BLACK"/>.
            </summary>
            <param name="fontWeight">font weight int value</param>
            <returns>
            either
            <see cref="F:iText.IO.Font.Constants.FontWeights.THIN"/>
            or
            <see cref="F:iText.IO.Font.Constants.FontWeights.BLACK"/>
            based on a given weight value
            </returns>
        </member>
        <member name="T:iText.IO.Font.Constants.StandardFontFamilies">
            <summary>
            Class containing families for
            <see cref="T:iText.IO.Font.Constants.StandardFonts"/>.
            </summary>
            <remarks>
            Class containing families for
            <see cref="T:iText.IO.Font.Constants.StandardFonts"/>.
            This class was made for com.itextpdf.io.font.FontRegisterProvider.
            </remarks>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFontFamilies.COURIER">
            <summary>
            Font family for
            <see cref="F:iText.IO.Font.Constants.StandardFonts.COURIER"/>
            ,
            <see cref="F:iText.IO.Font.Constants.StandardFonts.COURIER_BOLD"/>
            ,
            <see cref="F:iText.IO.Font.Constants.StandardFonts.COURIER_OBLIQUE"/>
            and
            <see cref="F:iText.IO.Font.Constants.StandardFonts.COURIER_BOLDOBLIQUE"/>.
            </summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFontFamilies.HELVETICA">
            <summary>
            Font family for
            <see cref="F:iText.IO.Font.Constants.StandardFonts.HELVETICA"/>
            ,
            <see cref="F:iText.IO.Font.Constants.StandardFonts.HELVETICA_BOLD"/>
            ,
            <see cref="F:iText.IO.Font.Constants.StandardFonts.HELVETICA_OBLIQUE"/>
            and
            <see cref="F:iText.IO.Font.Constants.StandardFonts.HELVETICA_BOLDOBLIQUE"/>.
            </summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFontFamilies.SYMBOL">
            <summary>
            Font family for
            <see cref="F:iText.IO.Font.Constants.StandardFonts.SYMBOL"/>.
            </summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFontFamilies.ZAPFDINGBATS">
            <summary>
            Font family for
            <see cref="F:iText.IO.Font.Constants.StandardFonts.ZAPFDINGBATS"/>.
            </summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFontFamilies.TIMES">
            <summary>
            Font family for
            <see cref="F:iText.IO.Font.Constants.StandardFonts.TIMES_ROMAN"/>
            ,
            <see cref="F:iText.IO.Font.Constants.StandardFonts.TIMES_BOLD"/>
            ,
            <see cref="F:iText.IO.Font.Constants.StandardFonts.TIMES_ITALIC"/>
            and
            <see cref="F:iText.IO.Font.Constants.StandardFonts.TIMES_BOLDITALIC"/>.
            </summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.COURIER">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.COURIER_BOLD">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.COURIER_OBLIQUE">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.COURIER_BOLDOBLIQUE">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.HELVETICA">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.HELVETICA_BOLD">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.HELVETICA_OBLIQUE">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.HELVETICA_BOLDOBLIQUE">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.SYMBOL">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.TIMES_ROMAN">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.TIMES_BOLD">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.TIMES_ITALIC">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.TIMES_BOLDITALIC">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="F:iText.IO.Font.Constants.StandardFonts.ZAPFDINGBATS">
            <summary>This is a possible value of a base 14 type 1 font</summary>
        </member>
        <member name="T:iText.IO.Font.Constants.TrueTypeCodePages">
            <summary>The code pages possible for a True Type font.</summary>
        </member>
        <member name="M:iText.IO.Font.Constants.TrueTypeCodePages.Get(System.Int32)">
            <summary>Gets code page description based on ulCodePageRange bit settings (OS/2 table).</summary>
            <remarks>
            Gets code page description based on ulCodePageRange bit settings (OS/2 table).
            See https://www.microsoft.com/typography/unicode/ulcp.htm for more details.
            </remarks>
            <param name="bit">index from ulCodePageRange bit settings (OS/2 table). From 0 to 63.</param>
            <returns>code bage description.</returns>
        </member>
        <member name="M:iText.IO.Font.FontCache.ClearSavedFonts">
            <summary>
            Clears the cache by removing fonts that were added via
            <see cref="M:iText.IO.Font.FontCache.SaveFont(iText.IO.Font.FontProgram,System.String)"/>.
            </summary>
            <remarks>
            Clears the cache by removing fonts that were added via
            <see cref="M:iText.IO.Font.FontCache.SaveFont(iText.IO.Font.FontProgram,System.String)"/>.
            <para />
            Be aware that in multithreading environment this method call will affect the result of
            <see cref="M:iText.IO.Font.FontCache.GetFont(System.String)"/>.
            This in its turn affects creation of fonts via factories when
            <c>cached</c>
            argument is set to true (which is by default).
            </remarks>
        </member>
        <member name="F:iText.IO.Font.FontEncoding.NOTDEF">
            <summary>A not defined character in a custom PDF encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.FontEncoding.baseEncoding">
            <summary>Base font encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.FontEncoding.fontSpecific">
            <summary>
            <see langword="true"/>
            if the font must use its built in encoding.
            </summary>
            <remarks>
            <see langword="true"/>
            if the font must use its built in encoding. In that case
            the
            <c>encoding</c>
            is only used to map a char to the position inside the font, not to the expected char name.
            </remarks>
        </member>
        <member name="F:iText.IO.Font.FontEncoding.unicodeToCode">
            <summary>Mapping map from unicode to simple code according to the encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.FontEncoding.differences">
            <summary>Encoding names.</summary>
        </member>
        <member name="F:iText.IO.Font.FontEncoding.unicodeDifferences">
            <summary>Encodings unicode differences</summary>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.CreateFontSpecificEncoding">
            <summary>This encoding will base on font encoding (FontSpecific encoding in Type 1 terminology)</summary>
            <returns>created font specific encoding</returns>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.FillFontEncoding(iText.IO.Font.FontEncoding)">
            <summary>
            Fill
            <see cref="T:iText.IO.Font.FontEncoding"/>
            object with default data.
            </summary>
            <param name="encoding">
            
            <see cref="T:iText.IO.Font.FontEncoding"/>
            to fill.
            </param>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.GetUnicode(System.Int32)">
            <summary>Gets unicode value for corresponding font's char code.</summary>
            <param name="index">font's char code</param>
            <returns>-1, if the char code unsupported or valid unicode.</returns>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.SetDifference(System.Int32,System.String)">
            <summary>Sets a new value in the differences array.</summary>
            <remarks>
            Sets a new value in the differences array.
            See
            <see cref="F:iText.IO.Font.FontEncoding.differences"/>.
            </remarks>
            <param name="index">position to replace</param>
            <param name="difference">new difference value</param>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.ConvertToBytes(System.String)">
            <summary>
            Converts a
            <c>String</c>
            to a
            <c>byte</c>
            array according to the encoding.
            </summary>
            <remarks>
            Converts a
            <c>String</c>
            to a
            <c>byte</c>
            array according to the encoding.
            String could contain a unicode symbols or font specific codes.
            </remarks>
            <param name="text">
            the
            <c>String</c>
            to be converted.
            </param>
            <returns>
            an array of
            <c>byte</c>
            representing the conversion according to the encoding
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.ConvertToByte(System.Int32)">
            <summary>
            Converts a unicode symbol or font specific code
            to
            <c>byte</c>
            according to the encoding.
            </summary>
            <param name="unicode">a unicode symbol or FontSpecif code to be converted.</param>
            <returns>
            a
            <c>byte</c>
            representing the conversion according to the encoding
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.CanEncode(System.Int32)">
            <summary>
            Check whether a unicode symbol or font specific code can be converted
            to
            <c>byte</c>
            according to the encoding.
            </summary>
            <param name="unicode">a unicode symbol or font specific code to be checked.</param>
            <returns>
            
            <see langword="true"/>
            if
            <c>ch</c>
            could be encoded.
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.CanDecode(System.Int32)">
            <summary>
            Check whether a
            <c>byte</c>
            code can be converted
            to unicode symbol according to the encoding.
            </summary>
            <param name="code">a byte code to be checked.</param>
            <returns>
            
            <see langword="true"/>
            if
            <paramref name="code"/>
            could be decoded.
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.IsBuiltWith(System.String)">
            <summary>
            Checks whether the
            <see cref="T:iText.IO.Font.FontEncoding"/>
            was built with corresponding encoding.
            </summary>
            <param name="encoding">an encoding</param>
            <returns>true, if the FontEncoding was built with the encoding. Otherwise false.</returns>
        </member>
        <member name="M:iText.IO.Font.FontEncoding.NormalizeEncoding(System.String)">
            <summary>Normalize the encoding names.</summary>
            <remarks>
            Normalize the encoding names. "winansi" is changed to "Cp1252" and
            "macroman" is changed to "MacRoman".
            </remarks>
            <param name="enc">the encoding to be normalized</param>
            <returns>the normalized encoding</returns>
        </member>
        <member name="M:iText.IO.Font.FontMetrics.GetTypoAscender">
            <summary>Gets typo (a.k.a. sTypo or OS/2) vertical metric corresponding to ascender.</summary>
            <remarks>
            Gets typo (a.k.a. sTypo or OS/2) vertical metric corresponding to ascender.
            <para />
            Typo vertical metrics are the primary source for iText ascender/descender calculations.
            </remarks>
            <returns>typo ascender value in normalized 1000-units</returns>
        </member>
        <member name="M:iText.IO.Font.FontMetrics.GetTypoDescender">
            <summary>Gets typo (a.k.a. sTypo or OS/2) vertical metric corresponding to descender.</summary>
            <remarks>
            Gets typo (a.k.a. sTypo or OS/2) vertical metric corresponding to descender.
            <para />
            Typo vertical metrics are the primary source for iText ascender/descender calculations.
            </remarks>
            <returns>typo descender value in normalized 1000-units</returns>
        </member>
        <member name="M:iText.IO.Font.FontMetrics.GetCapHeight">
            <summary>Gets the capital letters height.</summary>
            <remarks>
            Gets the capital letters height.
            <para />
            This property defines the vertical coordinate of the top of flat capital letters,
            measured from the baseline.
            </remarks>
            <returns>cap height in 1000-units</returns>
        </member>
        <member name="M:iText.IO.Font.FontMetrics.SetTypoAscender(System.Int32)">
            <summary>Sets typo (a.k.a. sTypo or OS/2) vertical metric corresponding to ascender.</summary>
            <remarks>
            Sets typo (a.k.a. sTypo or OS/2) vertical metric corresponding to ascender.
            <para />
            Typo vertical metrics are the primary source for iText ascender/descender calculations.
            </remarks>
            <param name="typoAscender">typo ascender value in normalized 1000-units</param>
        </member>
        <member name="M:iText.IO.Font.FontMetrics.SetTypoDescender(System.Int32)">
            <summary>Sets typo (a.k.a. sTypo or OS/2) vertical metric corresponding to descender.</summary>
            <remarks>
            Sets typo (a.k.a. sTypo or OS/2) vertical metric corresponding to descender.
            <para />
            Typo vertical metrics are the primary source for iText ascender/descender calculations.
            </remarks>
            <param name="typoDescender">typo descender value in normalized 1000-units</param>
        </member>
        <member name="M:iText.IO.Font.FontMetrics.SetCapHeight(System.Int32)">
            <summary>Sets the capital letters height.</summary>
            <remarks>
            Sets the capital letters height.
            <para />
            This property defines the vertical coordinate of the top of flat capital letters,
            measured from the baseline.
            </remarks>
            <param name="capHeight">cap height in 1000-units</param>
        </member>
        <member name="M:iText.IO.Font.FontNames.GetNames(System.Int32)">
            <summary>Extracts the names of the font in all the languages available.</summary>
            <param name="id">the name id to retrieve in OpenType notation</param>
            <returns>
            not empty
            <c>String[][]</c>
            if any names exists, otherwise
            <see langword="null"/>.
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontNames.GetFamilyName2">
            <summary>Get extra family name if exists.</summary>
            <returns>
            extra family name if exists in the font,
            <see langword="null"/>
            otherwise.
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontNames.SetFontWeight(System.Int32)">
            <summary>Sets font weight.</summary>
            <param name="weight">
            integer form 100 to 900. See
            <see cref="T:iText.IO.Font.Constants.FontWeights"/>.
            </param>
        </member>
        <member name="M:iText.IO.Font.FontNames.GetFontStretch">
            <summary>Gets font stretch in css notation (font-stretch property).</summary>
            <returns>
            One of
            <see cref="T:iText.IO.Font.Constants.FontStretches"/>
            values.
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontNames.SetFontStretch(System.String)">
            <summary>Sets font stretch in css notation (font-stretch property).</summary>
            <param name="fontStretch">
            
            <see cref="T:iText.IO.Font.Constants.FontStretches"/>.
            </param>
        </member>
        <member name="M:iText.IO.Font.FontNames.SetFamilyName2(System.String[][])">
            <summary>Set extra family name used for better fonts match.</summary>
            <param name="familyName2">family name to set.</param>
        </member>
        <member name="M:iText.IO.Font.FontNames.SetMacStyle(System.Int32)">
            <summary>Sets Open Type head.macStyle.</summary>
            <remarks>
            Sets Open Type head.macStyle.
            <para />
            <see cref="T:iText.IO.Font.Constants.FontMacStyleFlags"/>
            </remarks>
            <param name="macStyle">macStyle flag</param>
        </member>
        <member name="F:iText.IO.Font.FontProgram.encodingScheme">
            <summary>The font's encoding name.</summary>
            <remarks>
            The font's encoding name. This encoding is 'StandardEncoding' or 'AdobeStandardEncoding' for a font
            that can be totally encoded according to the characters names. For all other names the font is treated as
            symbolic.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.FontProgram.GetWidth(System.Int32)">
            <summary>Get glyph's width.</summary>
            <param name="unicode">a unicode symbol or FontSpecif code.</param>
            <returns>Gets width in normalized 1000 units.</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgram.GetCharBBox(System.Int32)">
            <summary>Get glyph's bbox.</summary>
            <param name="unicode">a unicode symbol or FontSpecif code.</param>
            <returns>Gets bbox in normalized 1000 units.</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgram.GetKerning(System.Int32,System.Int32)">
            <summary>Gets the kerning between two glyphs.</summary>
            <param name="first">the first unicode value</param>
            <param name="second">the second unicode value</param>
            <returns>the kerning to be applied</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgram.GetKerning(iText.IO.Font.Otf.Glyph,iText.IO.Font.Otf.Glyph)">
            <summary>Gets the kerning between two glyphs.</summary>
            <param name="first">the first glyph</param>
            <param name="second">the second glyph</param>
            <returns>the kerning to be applied</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgram.IsBuiltWith(System.String)">
            <summary>
            Checks whether the
            <see cref="T:iText.IO.Font.FontProgram"/>
            was built with corresponding fontName.
            </summary>
            <remarks>
            Checks whether the
            <see cref="T:iText.IO.Font.FontProgram"/>
            was built with corresponding fontName.
            Default value is false unless overridden.
            </remarks>
            <param name="fontName">a font name or path to a font program</param>
            <returns>true, if the FontProgram was built with the fontProgram. Otherwise false.</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgram.TrimFontStyle(System.String)">
            <summary>Gets the name without the modifiers Bold, Italic or BoldItalic.</summary>
            <param name="name">the full name of the font</param>
            <returns>the name without the modifiers Bold, Italic or BoldItalic</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgram.SetTypoAscender(System.Int32)">
            <summary>Sets typo ascender.</summary>
            <remarks>
            Sets typo ascender. See also
            <see cref="M:iText.IO.Font.FontMetrics.SetTypoAscender(System.Int32)"/>.
            </remarks>
            <param name="ascender">typo ascender value in 1000-units</param>
        </member>
        <member name="M:iText.IO.Font.FontProgram.SetTypoDescender(System.Int32)">
            <summary>Sets typo descender.</summary>
            <remarks>
            Sets typo descender. See also
            <see cref="M:iText.IO.Font.FontMetrics.SetTypoDescender(System.Int32)"/>.
            </remarks>
            <param name="descender">typo descender value in 1000-units</param>
        </member>
        <member name="M:iText.IO.Font.FontProgram.SetCapHeight(System.Int32)">
            <summary>Sets the capital letters height.</summary>
            <remarks>
            Sets the capital letters height. See also
            <see cref="M:iText.IO.Font.FontMetrics.SetCapHeight(System.Int32)"/>.
            </remarks>
            <param name="capHeight">cap height in 1000-units</param>
        </member>
        <member name="M:iText.IO.Font.FontProgram.SetItalicAngle(System.Int32)">
            <summary>Sets the PostScript italic angle.</summary>
            <remarks>
            Sets the PostScript italic angle.
            <para />
            Italic angle in counter-clockwise degrees from the vertical. Zero for upright text, negative for text that leans
            to the right (forward).
            </remarks>
            <param name="italicAngle">in counter-clockwise degrees from the vertical</param>
        </member>
        <member name="M:iText.IO.Font.FontProgram.SetFontWeight(System.Int32)">
            <summary>Sets font weight.</summary>
            <param name="fontWeight">
            integer form 100 to 900. See
            <see cref="T:iText.IO.Font.Constants.FontWeights"/>.
            </param>
        </member>
        <member name="M:iText.IO.Font.FontProgram.SetFontStretch(System.String)">
            <summary>Sets font width in css notation (font-stretch property)</summary>
            <param name="fontWidth">
            
            <see cref="T:iText.IO.Font.Constants.FontStretches"/>.
            </param>
        </member>
        <member name="M:iText.IO.Font.FontProgram.SetFontFamily(System.String)">
            <summary>Sets a preferred font family name.</summary>
            <param name="fontFamily">a preferred font family name.</param>
        </member>
        <member name="M:iText.IO.Font.FontProgram.SetFontName(System.String)">
            <summary>Sets the PostScript name of the font.</summary>
            <remarks>
            Sets the PostScript name of the font.
            <para />
            If full name is null, it will be set as well.
            </remarks>
            <param name="fontName">the PostScript name of the font, shall not be null or empty.</param>
        </member>
        <member name="T:iText.IO.Font.FontProgramDescriptor">
            <summary>Base font descriptor.</summary>
        </member>
        <member name="M:iText.IO.Font.FontProgramDescriptor.GetFamilyName2LowerCase">
            <summary>Get extra family name if exists.</summary>
            <returns>
            extra family name if exists in the font,
            <see langword="null"/>
            otherwise.
            </returns>
        </member>
        <member name="T:iText.IO.Font.FontProgramFactory">
            <summary>Provides methods for creating various types of fonts.</summary>
        </member>
        <member name="F:iText.IO.Font.FontProgramFactory.DEFAULT_CACHED">
            <summary>This is the default value of the <var>cached</var> variable.</summary>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateFont">
            <summary>Creates a new standard Helvetica font program file.</summary>
            <returns>
            a
            <see cref="T:iText.IO.Font.FontProgram"/>
            object with Helvetica font description
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateFont(System.String)">
            <summary>Creates a new font program.</summary>
            <remarks>
            Creates a new font program. This font program can be one of the 14 built in fonts,
            a Type1 font referred to by an AFM or PFM file, a TrueType font or
            a CJK font from the Adobe Asian Font Pack.
            Fonts in TrueType Collections are addressed by index such as "msgothic.ttc,1".
            This would get the second font (indexes start at 0), in this case "MS PGothic".
            <para />
            The fonts are cached and if they already exist they are extracted from the cache,
            not parsed again.
            <para />
            </remarks>
            <param name="fontProgram">the name of the font or its location on file</param>
            <returns>
            returns a new
            <see cref="T:iText.IO.Font.FontProgram"/>
            . This font program may come from the cache
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateFont(System.String,System.Boolean)">
            <summary>Creates a new font program.</summary>
            <remarks>
            Creates a new font program. This font program can be one of the 14 built in fonts,
            a Type1 font referred to by an AFM or PFM file, a TrueType font or
            a CJK font from the Adobe Asian Font Pack.
            Fonts in TrueType Collections are addressed by index such as "msgothic.ttc,1".
            This would get the second font (indexes start at 0), in this case "MS PGothic".
            <para />
            The fonts are cached and if they already exist they are extracted from the cache,
            not parsed again.
            <para />
            </remarks>
            <param name="fontProgram">the name of the font or its location on file</param>
            <param name="cached">whether to to cache this font program after it has been loaded</param>
            <returns>
            returns a new
            <see cref="T:iText.IO.Font.FontProgram"/>
            . This font program may come from the cache
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateFont(System.String,System.String,System.Boolean)">
            <summary>Creates a new font program.</summary>
            <remarks>
            Creates a new font program. This font program can be one of the 14 built in fonts,
            a Type1 font referred to by an AFM or PFM file, a TrueType font or
            a CJK font from the Adobe Asian Font Pack.
            Fonts in TrueType Collections are addressed by index such as "msgothic.ttc,1".
            This would get the second font (indexes start at 0), in this case "MS PGothic".
            <para />
            The fonts are cached and if they already exist they are extracted from the cache,
            not parsed again.
            <para />
            </remarks>
            <param name="fontProgram">the name of the font or its location on file</param>
            <param name="cmap">CMap to convert Unicode value to CID if CJK font is used</param>
            <param name="cached">whether to cache this font program after it has been loaded</param>
            <returns>
            returns a new
            <see cref="T:iText.IO.Font.FontProgram"/>
            . This font program may come from the cache
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateFont(System.Byte[])">
            <summary>Creates a new font program.</summary>
            <remarks>
            Creates a new font program.
            The fonts are cached and if they already exist they are extracted from the cache,
            not parsed again.
            <para />
            </remarks>
            <param name="fontProgram">the byte contents of the font program</param>
            <returns>
            returns a new
            <see cref="T:iText.IO.Font.FontProgram"/>
            . This font program may come from the cache
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateFont(System.Byte[],System.Boolean)">
            <summary>Creates a new font program.</summary>
            <remarks>
            Creates a new font program.
            The fonts are cached and if they already exist they are extracted from the cache,
            not parsed again.
            <para />
            </remarks>
            <param name="fontProgram">the byte contents of the font program</param>
            <param name="cached">whether to to cache this font program</param>
            <returns>
            returns a new
            <see cref="T:iText.IO.Font.FontProgram"/>
            . This font program may come from the cache
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateType1Font(System.Byte[],System.Byte[])">
            <summary>Creates a new Type 1 font by the byte contents of the corresponding AFM/PFM and PFB files</summary>
            <param name="afm">the contents of the AFM or PFM metrics file</param>
            <param name="pfb">the contents of the PFB file</param>
            <returns>
            created
            <see cref="T:iText.IO.Font.FontProgram"/>
            instance
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateType1Font(System.Byte[],System.Byte[],System.Boolean)">
            <summary>Creates a new Type 1 font by the byte contents of the corresponding AFM/PFM and PFB files</summary>
            <param name="afm">the contents of the AFM or PFM metrics file</param>
            <param name="pfb">the contents of the PFB file</param>
            <param name="cached">
            specifies whether to cache the created
            <see cref="T:iText.IO.Font.FontProgram"/>
            or not
            </param>
            <returns>
            created
            <see cref="T:iText.IO.Font.FontProgram"/>
            instance
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateType1Font(System.String,System.String)">
            <summary>Creates a new Type 1 font by the corresponding AFM/PFM and PFB files</summary>
            <param name="metricsPath">path to the AFM or PFM metrics file</param>
            <param name="binaryPath">path to the contents of the PFB file</param>
            <returns>
            created
            <see cref="T:iText.IO.Font.FontProgram"/>
            instance
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateType1Font(System.String,System.String,System.Boolean)">
            <summary>Creates a new Type 1 font by the corresponding AFM/PFM and PFB files</summary>
            <param name="metricsPath">path to the AFM or PFM metrics file</param>
            <param name="binaryPath">path to the contents of the PFB file</param>
            <param name="cached">
            specifies whether to cache the created
            <see cref="T:iText.IO.Font.FontProgram"/>
            or not
            </param>
            <returns>
            created
            <see cref="T:iText.IO.Font.FontProgram"/>
            instance
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateFont(System.String,System.Int32,System.Boolean)">
            <summary>Creates a new TrueType font program from ttc (TrueType Collection) file.</summary>
            <param name="ttc">location  of TrueType Collection file (*.ttc)</param>
            <param name="ttcIndex">the index of the font file from the collection to be read</param>
            <param name="cached">
            true if the font comes from the cache or is added to
            the cache if new, false if the font is always created new
            </param>
            <returns>
            returns a new
            <see cref="T:iText.IO.Font.FontProgram"/>
            instance. This font may come from the cache but only if cached
            is true, otherwise it will always be created new
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateFont(System.Byte[],System.Int32,System.Boolean)">
            <summary>Creates a new TrueType font program from ttc (TrueType Collection) file bytes.</summary>
            <param name="ttc">the content of a TrueType Collection file (*.ttc)</param>
            <param name="ttcIndex">the index of the font file from the collection to be read</param>
            <param name="cached">
            true if the font comes from the cache or is added to
            the cache if new, false if the font is always created new
            </param>
            <returns>
            returns a new
            <see cref="T:iText.IO.Font.FontProgram"/>
            instance. This font may come from the cache but only if cached
            is true, otherwise it will always be created new
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateRegisteredFont(System.String,System.Int32,System.Boolean)">
            <summary>Creates a FontProgram from the font file that has been previously registered.</summary>
            <param name="fontName">
            either a font alias, if the font file has been registered with an alias,
            or just a font name otherwise
            </param>
            <param name="style">
            the style of the font to look for. Possible values are listed in
            <see cref="T:iText.IO.Font.Constants.FontStyles"/>.
            See
            <see cref="F:iText.IO.Font.Constants.FontStyles.BOLD"/>
            ,
            <see cref="F:iText.IO.Font.Constants.FontStyles.ITALIC"/>
            ,
            <see cref="F:iText.IO.Font.Constants.FontStyles.NORMAL"/>
            ,
            <see cref="F:iText.IO.Font.Constants.FontStyles.BOLDITALIC"/>
            ,
            <see cref="F:iText.IO.Font.Constants.FontStyles.UNDEFINED"/>
            </param>
            <param name="cached">whether to try to get the font program from cache</param>
            <returns>
            created
            <see cref="T:iText.IO.Font.FontProgram"/>
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateRegisteredFont(System.String,System.Int32)">
            <summary>Creates a FontProgram from the font file that has been previously registered.</summary>
            <param name="fontName">
            either a font alias, if the font file has been registered with an alias,
            or just a font name otherwise
            </param>
            <param name="style">
            the style of the font to look for. Possible values are listed in
            <see cref="T:iText.IO.Font.Constants.FontStyles"/>.
            See
            <see cref="F:iText.IO.Font.Constants.FontStyles.BOLD"/>
            ,
            <see cref="F:iText.IO.Font.Constants.FontStyles.ITALIC"/>
            ,
            <see cref="F:iText.IO.Font.Constants.FontStyles.NORMAL"/>
            ,
            <see cref="F:iText.IO.Font.Constants.FontStyles.BOLDITALIC"/>
            ,
            <see cref="F:iText.IO.Font.Constants.FontStyles.UNDEFINED"/>
            </param>
            <returns>
            created
            <see cref="T:iText.IO.Font.FontProgram"/>
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.CreateRegisteredFont(System.String)">
            <summary>Creates a FontProgram from the font file that has been previously registered.</summary>
            <param name="fontName">
            either a font alias, if the font file has been registered with an alias,
            or just a font name otherwise
            </param>
            <returns>
            created
            <see cref="T:iText.IO.Font.FontProgram"/>
            </returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.RegisterFontFamily(System.String,System.String,System.String)">
            <summary>Register a font by giving explicitly the font family and name.</summary>
            <param name="familyName">the font family</param>
            <param name="fullName">the font name</param>
            <param name="path">the font path</param>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.RegisterFont(System.String)">
            <summary>Registers a .ttf, .otf, .afm, .pfm, or a .ttc font file.</summary>
            <remarks>
            Registers a .ttf, .otf, .afm, .pfm, or a .ttc font file.
            In case if TrueType Collection (.ttc), an additional parameter may be specified defining the index of the font
            to be registered, e.g. "path/to/font/collection.ttc,0". The index is zero-based.
            </remarks>
            <param name="path">the path to a font file</param>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.RegisterFont(System.String,System.String)">
            <summary>Register a font file and use an alias for the font contained in it.</summary>
            <param name="path">the path to a font file</param>
            <param name="alias">the alias you want to use for the font</param>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.RegisterFontDirectory(System.String)">
            <summary>Register all the fonts in a directory.</summary>
            <param name="dir">the directory</param>
            <returns>the number of fonts registered</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.RegisterFontDirectoryRecursively(System.String)">
            <summary>Register all the fonts in a directory recursively.</summary>
            <param name="dir">the directory</param>
            <returns>the number of fonts registered</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.RegisterSystemFontDirectories">
            <summary>Register fonts in some probable directories.</summary>
            <remarks>
            Register fonts in some probable directories. It usually works in Windows,
            Linux and Solaris.
            </remarks>
            <returns>the number of fonts registered</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.GetRegisteredFonts">
            <summary>Gets a set of registered font names.</summary>
            <returns>a set of registered fonts</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.GetRegisteredFontFamilies">
            <summary>Gets a set of registered font names.</summary>
            <returns>a set of registered font families</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.IsRegisteredFont(System.String)">
            <summary>Checks if a certain font is registered.</summary>
            <param name="fontName">the name of the font that has to be checked.</param>
            <returns>true if the font is found</returns>
        </member>
        <member name="M:iText.IO.Font.FontProgramFactory.ClearRegisteredFontFamilies">
            <summary>Clears registered font cache</summary>
        </member>
        <member name="T:iText.IO.Font.FontRegisterProvider">
            <summary>
            If you are using True Type fonts, you can declare the paths of the different ttf- and ttc-files
            to this class first and then create fonts in your code using one of the getFont method
            without having to enter a path as parameter.
            </summary>
        </member>
        <member name="F:iText.IO.Font.FontRegisterProvider.fontNames">
            <summary>This is a map of postscriptfontnames of fonts and the path of their font file.</summary>
        </member>
        <member name="F:iText.IO.Font.FontRegisterProvider.fontFamilies">
            <summary>This is a map of fontfamilies.</summary>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.#ctor">
            <summary>Creates new FontRegisterProvider</summary>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.GetFont(System.String,System.Int32)">
            <summary>Constructs a <c>Font</c>-object.</summary>
            <param name="fontName">the name of the font</param>
            <param name="style">the style of this font</param>
            <returns>the Font constructed based on the parameters</returns>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.GetFont(System.String,System.Int32,System.Boolean)">
            <summary>Constructs a <c>Font</c>-object.</summary>
            <param name="fontName">the name of the font</param>
            <param name="style">the style of this font</param>
            <param name="cached">
            true if the font comes from the cache or is added to
            the cache if new, false if the font is always created new
            </param>
            <returns>the Font constructed based on the parameters</returns>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.RegisterFontFamily(System.String,System.String,System.String)">
            <summary>Register a font by giving explicitly the font family and name.</summary>
            <param name="familyName">the font family</param>
            <param name="fullName">the font name</param>
            <param name="path">the font path</param>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.RegisterFont(System.String)">
            <summary>Register a font file, either .ttf or .otf, .afm or a font from TrueType Collection.</summary>
            <remarks>
            Register a font file, either .ttf or .otf, .afm or a font from TrueType Collection.
            If a TrueType Collection is registered, an additional index of the font program can be specified
            </remarks>
            <param name="path">the path to a ttf- or ttc-file</param>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.RegisterFont(System.String,System.String)">
            <summary>Register a font file and use an alias for the font contained in it.</summary>
            <param name="path">the path to a font file</param>
            <param name="alias">the alias you want to use for the font</param>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.RegisterFontDirectory(System.String)">
            <summary>Register all the fonts in a directory.</summary>
            <param name="dir">the directory</param>
            <returns>the number of fonts registered</returns>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.RegisterFontDirectory(System.String,System.Boolean)">
            <summary>Register all the fonts in a directory and possibly its subdirectories.</summary>
            <param name="dir">the directory</param>
            <param name="scanSubdirectories">recursively scan subdirectories if <c>true</c></param>
            <returns>the number of fonts registered</returns>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.RegisterSystemFontDirectories">
            <summary>Register fonts in some probable directories.</summary>
            <remarks>
            Register fonts in some probable directories. It usually works in Windows,
            Linux and Solaris.
            </remarks>
            <returns>the number of fonts registered</returns>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.GetRegisteredFonts">
            <summary>Gets a set of registered font names.</summary>
            <returns>a set of registered fonts</returns>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.GetRegisteredFontFamilies">
            <summary>Gets a set of registered font names.</summary>
            <returns>a set of registered font families</returns>
        </member>
        <member name="M:iText.IO.Font.FontRegisterProvider.IsRegisteredFont(System.String)">
            <summary>Checks if a certain font is registered.</summary>
            <param name="fontname">the name of the font that has to be checked.</param>
            <returns>true if the font is found</returns>
        </member>
        <member name="T:iText.IO.Font.IExtraEncoding">
            <summary>
            Classes implementing this interface can create custom encodings or
            replace existing ones.
            </summary>
            <remarks>
            Classes implementing this interface can create custom encodings or
            replace existing ones. It is used in the context of <c>PdfEncoding</c>.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.IExtraEncoding.CharToByte(System.String,System.String)">
            <summary>Converts an Unicode string to a byte array according to some encoding.</summary>
            <param name="text">the Unicode string</param>
            <param name="encoding">
            the requested encoding. It's mainly of use if the same class
            supports more than one encoding.
            </param>
            <returns>the conversion or <c>null</c> if no conversion is supported</returns>
        </member>
        <member name="M:iText.IO.Font.IExtraEncoding.CharToByte(System.Char,System.String)">
            <summary>Converts an Unicode char to a byte array according to some encoding.</summary>
            <param name="char1">the Unicode char</param>
            <param name="encoding">
            the requested encoding. It's mainly of use if the same class
            supports more than one encoding.
            </param>
            <returns>the conversion or <c>null</c> if no conversion is supported</returns>
        </member>
        <member name="M:iText.IO.Font.IExtraEncoding.ByteToChar(System.Byte[],System.String)">
            <summary>Converts a byte array to an Unicode string according to some encoding.</summary>
            <param name="b">the input byte array</param>
            <param name="encoding">
            the requested encoding. It's mainly of use if the same class
            supports more than one encoding.
            </param>
            <returns>the conversion or <c>null</c> if no conversion is supported</returns>
        </member>
        <member name="T:iText.IO.Font.OpenTypeParser.HeaderTable">
            <summary>The components of table 'head'.</summary>
        </member>
        <member name="T:iText.IO.Font.OpenTypeParser.HorizontalHeader">
            <summary>The components of table 'hhea'.</summary>
        </member>
        <member name="T:iText.IO.Font.OpenTypeParser.WindowsMetrics">
            <summary>The components of table 'OS/2'.</summary>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.PostTable.italicAngle">
            <summary>The italic angle.</summary>
            <remarks>
            The italic angle. It is usually extracted from the 'post' table or in it's
            absence with the code:
            <pre>
            <c>-Math.atan2(hhea.caretSlopeRun, hhea.caretSlopeRise) * 180 / Math.PI</c>
            </pre>
            </remarks>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.PostTable.isFixedPitch">
            <summary><c>true</c> if all the glyphs have the same width.</summary>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.CmapTable.cmapEncodings">
            <summary>
            Collection of the pairs representing Platform ID and Encoding ID of the “cmap” subtables
            present in the font program.
            </summary>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.CmapTable.cmap10">
            <summary>The map containing the code information for the table 'cmap', encoding 1.0.</summary>
            <remarks>
            The map containing the code information for the table 'cmap', encoding 1.0.
            The key is the code and the value is an
            <c>int[2]</c>
            where position 0
            is the glyph number and position 1 is the glyph width normalized to 1000 units.
            </remarks>
            <seealso cref="F:iText.IO.Font.FontProgram.UNITS_NORMALIZATION"/>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.CmapTable.cmap31">
            <summary>The map containing the code information for the table 'cmap', encoding 3.1 in Unicode.</summary>
            <remarks>
            The map containing the code information for the table 'cmap', encoding 3.1 in Unicode.
            The key is the code and the value is an
            <c>int[2]</c>
            where position 0
            is the glyph number and position 1 is the glyph width normalized to 1000 units.
            </remarks>
            <seealso cref="F:iText.IO.Font.FontProgram.UNITS_NORMALIZATION"/>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.fileName">
            <summary>The file name.</summary>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.raf">
            <summary>The file in use.</summary>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.ttcIndex">
            <summary>The index for the TTC font.</summary>
            <remarks>
            The index for the TTC font. It is -1
            <c>int</c>
            for a TTF file.
            </remarks>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.directoryOffset">
            <summary>The offset from the start of the file to the table directory.</summary>
            <remarks>
            The offset from the start of the file to the table directory.
            It is 0 for TTF and may vary for TTC depending on the chosen font.
            </remarks>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.fontName">
            <summary>The font name.</summary>
            <remarks>The font name. This name is usually extracted from the table 'name' with the 'Name ID' 6.</remarks>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.allNameEntries">
            <summary>All the names of the Names-Table.</summary>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.cff">
            <summary>Indicate, that the font contains 'CFF ' table.</summary>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.cffOffset">
            <summary>Offset to 'CFF ' table.</summary>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.cffLength">
            <summary>Length of 'CFF ' table.</summary>
        </member>
        <member name="F:iText.IO.Font.OpenTypeParser.tables">
            <summary>Contains the location of the several tables.</summary>
            <remarks>
            Contains the location of the several tables. The key is the name of
            the table and the value is an <c>int[2]</c> where position 0
            is the offset from the start of the file and position 1 is the length
            of the table.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.GetPsFontName">
            <summary>Gets the Postscript font name.</summary>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadCffFont">
            <summary>
            If this font file is using the Compact Font File Format, then this method
            will return the raw bytes needed for the font stream.
            </summary>
            <remarks>
            If this font file is using the Compact Font File Format, then this method
            will return the raw bytes needed for the font stream. If this method is
            ever made public: make sure to add a test if (cff == true).
            </remarks>
            <returns>a byte array</returns>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.LoadTables(System.Boolean)">
            <summary>Reads the font data.</summary>
            <param name="all">
            if
            <see langword="true"/>
            , all tables will be read, otherwise only 'head', 'name', and 'os/2'
            </param>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.GetTTCName(System.String)">
            <summary>Gets the name from a composed TTC file name.</summary>
            <remarks>
            Gets the name from a composed TTC file name.
            If I have for input "myfont.ttc,2" the return will
            be "myfont.ttc".
            </remarks>
            <param name="name">the full name</param>
            <returns>the simple file name</returns>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadGlyphWidths">
            <summary>Reads the glyphs widths.</summary>
            <remarks>
            Reads the glyphs widths. The widths are extracted from the table 'hmtx'.
            The glyphs are normalized to 1000 units (TrueTypeFont.UNITS_NORMALIZATION).
            Depends on
            <see cref="F:iText.IO.Font.OpenTypeParser.HorizontalHeader.numberOfHMetrics"/>
            and
            <see cref="F:iText.IO.Font.OpenTypeParser.HeaderTable.unitsPerEm"/>.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadKerning(System.Int32)">
            <summary>Reads the kerning information from the 'kern' table.</summary>
            <param name="unitsPerEm">
            
            <see cref="F:iText.IO.Font.OpenTypeParser.HeaderTable.unitsPerEm"/>.
            </param>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadBbox(System.Int32)">
            <summary>Read the glyf bboxes from 'glyf' table.</summary>
            <param name="unitsPerEm">
            
            <see cref="F:iText.IO.Font.OpenTypeParser.HeaderTable.unitsPerEm"/>
            </param>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadNameTable">
            <summary>Extracts the names of the font in all the languages available.</summary>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadHheaTable">
            <summary>Read horizontal header, table 'hhea'.</summary>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadHeadTable">
            <summary>Read font header, table 'head'.</summary>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadOs_2Table">
            <summary>Reads the windows metrics table.</summary>
            <remarks>
            Reads the windows metrics table. The metrics are extracted from the table 'OS/2'.
            Depends on
            <see cref="F:iText.IO.Font.OpenTypeParser.HeaderTable.unitsPerEm"/>
            property.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadCmapTable">
            <summary>Reads the several maps from the table 'cmap'.</summary>
            <remarks>
            Reads the several maps from the table 'cmap'. The maps of interest are 1.0 for symbolic
            fonts and 3.1 for all others. A symbolic font is defined as having the map 3.0.
            Depends from
            <c>readGlyphWidths()</c>.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadStandardString(System.Int32)">
            <summary>
            Reads a <c>String</c> from the font file as bytes using the Cp1252
            encoding.
            </summary>
            <param name="length">the length of bytes to read</param>
            <returns>the <c>String</c> read</returns>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadUnicodeString(System.Int32)">
            <summary>Reads a Unicode <c>String</c> from the font file.</summary>
            <remarks>Reads a Unicode <c>String</c> from the font file. Each character is represented by two bytes.</remarks>
            <param name="length">the length of bytes to read. The <c>String</c> will have <c>length</c>/2 characters.</param>
            <returns>the <c>String</c> read.</returns>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.GetGlyphWidth(System.Int32)">
            <summary>Gets a glyph width.</summary>
            <param name="glyph">the glyph to get the width of</param>
            <returns>the width of the glyph in normalized 1000 units (TrueTypeFont.UNITS_NORMALIZATION)</returns>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadFormat0">
            <summary>The information in the maps of the table 'cmap' is coded in several formats.</summary>
            <remarks>
            The information in the maps of the table 'cmap' is coded in several formats.
            Format 0 is the Apple standard character to glyph index mapping table.
            </remarks>
            <returns>a <c>HashMap</c> representing this map</returns>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadFormat4(System.Boolean)">
            <summary>The information in the maps of the table 'cmap' is coded in several formats.</summary>
            <remarks>
            The information in the maps of the table 'cmap' is coded in several formats.
            Format 4 is the Microsoft standard character to glyph index mapping table.
            </remarks>
            <returns>a <c>HashMap</c> representing this map</returns>
        </member>
        <member name="M:iText.IO.Font.OpenTypeParser.ReadFormat6">
            <summary>The information in the maps of the table 'cmap' is coded in several formats.</summary>
            <remarks>
            The information in the maps of the table 'cmap' is coded in several formats.
            Format 6 is a trimmed table mapping. It is similar to format 0 but can have
            less than 256 entries.
            </remarks>
            <returns>a <c>HashMap</c> representing this map</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ChainingContextualTable`1.CheckIfLookaheadContextMatch(iText.IO.Font.Otf.GlyphLine,`0,System.Int32)">
            <summary>Checks if given glyph line at the given position matches given rule.</summary>
            <param name="line">glyph line to be checked</param>
            <param name="rule">rule to be compared with a given line</param>
            <param name="startIdx">glyph line position</param>
            <returns>true if given glyph line at the given position matches given rule</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ChainingContextualTable`1.CheckIfBacktrackContextMatch(iText.IO.Font.Otf.GlyphLine,`0)">
            <summary>Checks if given glyph line at the given position matches given rule.</summary>
            <param name="line">glyph line to be checked</param>
            <param name="rule">rule to be compared with a given line</param>
            <returns>true if given glyph line matches given rule</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualPositionRule.GetPosLookupRecords">
            <summary>Retrieves the position lookup records.</summary>
            <remarks>
            Retrieves the position lookup records. Each record specifies a position in the context glyph
            sequence and a LookupListIndex to the position lookup that is applied at that position.
            </remarks>
            <returns>
            an array of
            <see cref="T:iText.IO.Font.Otf.PosLookupRecord"/>.
            </returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualRule.GetContextLength">
            <summary>Gets the length of the context glyph sequence defined by this rule</summary>
            <returns>length of the context</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualRule.IsGlyphMatchesInput(System.Int32,System.Int32)">
            <summary>Checks if glyph line element matches element from input sequence of the rule.</summary>
            <remarks>
            Checks if glyph line element matches element from input sequence of the rule.
            <br /><br />
            NOTE: rules do not contain the first element of the input sequence, the first element is defined by rule
            position in substitution table. Therefore atIdx shall not be 0.
            </remarks>
            <param name="glyphId">glyph code id</param>
            <param name="atIdx">
            index in the rule sequence. Shall be: 0 &lt; atIdx &lt;
            <see cref="M:iText.IO.Font.Otf.ContextualRule.GetContextLength"/>
            </param>
            <returns>
            
            <see langword="true"/>
            if glyph matches element
            </returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualRule.GetLookaheadContextLength">
            <summary>Gets the length of the lookahead context glyph sequence defined by this rule</summary>
            <returns>length of the lookahead context</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualRule.GetBacktrackContextLength">
            <summary>Gets the length of the backtrack context glyph sequence defined by this rule</summary>
            <returns>length of the backtrack context</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualRule.IsGlyphMatchesLookahead(System.Int32,System.Int32)">
            <summary>Checks if glyph line element matches element from lookahead sequence of the rule.</summary>
            <param name="glyphId">glyph code id</param>
            <param name="atIdx">
            index in rule sequence. Shall be: 0 &lt;= atIdx &lt;
            <see cref="M:iText.IO.Font.Otf.ContextualRule.GetLookaheadContextLength"/>
            </param>
            <returns>
            
            <see langword="true"/>
            if glyph matches element from lookahead sequence
            </returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualRule.IsGlyphMatchesBacktrack(System.Int32,System.Int32)">
            <summary>Checks if glyph line element matches element from backtrack sequence of the rule.</summary>
            <param name="glyphId">glyph code id</param>
            <param name="atIdx">
            index in rule sequence. Shall be: 0 &lt;= atIdx &lt;
            <see cref="M:iText.IO.Font.Otf.ContextualRule.GetBacktrackContextLength"/>
            </param>
            <returns>
            
            <see langword="true"/>
            if glyph matches element from backtrack sequence
            </returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualSubstRule.GetSubstLookupRecords">
            <summary>Retrieves the substitution lookup records.</summary>
            <remarks>
            Retrieves the substitution lookup records. Each record specifies a position in the context glyph
            sequence and a LookupListIndex to the substitution lookup that is applied at that position.
            </remarks>
            <returns>
            an array of
            <see cref="T:iText.IO.Font.Otf.SubstLookupRecord"/>.
            </returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualTable`1.GetMatchingContextRule(iText.IO.Font.Otf.GlyphLine)">
            <summary>Gets a most preferable context rule that matches the line at current position.</summary>
            <remarks>
            Gets a most preferable context rule that matches the line at current position. If no matching context rule is
            found, it returns <c>null</c>.
            <br /><br />
            NOTE: if matching context rule is found, the <c>GlyphLine.start</c> and <c>GlyphLine.end</c>
            will be changed in such way that they will point at start and end of the matching context glyph sequence
            inside the glyph line.
            </remarks>
            <param name="line">a line, which is to be checked if it matches some context.</param>
            <returns>matching context rule or null, if none was found.</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualTable`1.GetSetOfRulesForStartGlyph(System.Int32)">
            <summary>Gets a set of rules, which start with given glyph id.</summary>
            <param name="startId">id of the first glyph in the sequence</param>
            <returns>
            a list of
            <see cref="T:iText.IO.Font.Otf.ContextualSubstRule"/>
            instances. The list will be empty if there are no rules
            that start with a given glyph id
            </returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ContextualTable`1.CheckIfContextMatch(iText.IO.Font.Otf.GlyphLine,`0)">
            <summary>Checks if given glyph line matches given rule.</summary>
            <param name="line">glyph line to be checked</param>
            <param name="rule">rule to be compared with a given glyph line</param>
            <returns>
            either index which corresponds to the last glyph of the matching context inside the glyph line
            if context matches, or -1 if context doesn't match
            </returns>
        </member>
        <member name="M:iText.IO.Font.Otf.FeatureRecord.GetTag">
            <summary>Retrieves the tag of the feature record.</summary>
            <returns>tag</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.FeatureRecord.SetTag(System.String)">
            <summary>Sets the tag of the feature record.</summary>
            <param name="tag">tag</param>
        </member>
        <member name="M:iText.IO.Font.Otf.FeatureRecord.GetLookups">
            <summary>Retrieves the lookups of the feature record.</summary>
            <returns>lookups</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.FeatureRecord.SetLookups(System.Int32[])">
            <summary>Sets the lookups of the feature record.</summary>
            <param name="lookups">lookups</param>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>Construct a non-mark Glyph, retrieving characters from unicode.</summary>
            <param name="code">code representation of the glyph in the font file</param>
            <param name="width">normalized width of the glyph</param>
            <param name="unicode">utf-32 representation of glyph if appears. Correct value is &gt; -1</param>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.#ctor(System.Int32,System.Int32,System.Char[])">
            <summary>Construct a non-mark Glyph, using the codepoint of the characters as unicode point.</summary>
            <param name="code">code representation of the glyph in the font file</param>
            <param name="width">normalized width of the glyph</param>
            <param name="chars">The Unicode text represented by this Glyph.</param>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.#ctor(System.Int32,System.Int32,System.Int32,System.Int32[])">
            <summary>Construct a non-mark Glyph, retrieving characters from unicode.</summary>
            <param name="code">code representation of the glyph in the font file</param>
            <param name="width">normalized width of the glyph</param>
            <param name="unicode">utf-32 representation of glyph if appears. Correct value is &gt; -1</param>
            <param name="bbox">The normalized bounding box of this Glyph.</param>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.#ctor(System.Int32,System.Int32)">
            <summary>Construct a non-mark Glyph object with id -1 and characters retrieved from unicode.</summary>
            <param name="width">normalized width of the glyph</param>
            <param name="unicode">utf-32 representation of glyph if appears. Correct value is &gt; -1</param>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.#ctor(System.Int32,System.Int32,System.Int32,System.Char[],System.Boolean)">
            <summary>Construct a glyph object form the passed arguments.</summary>
            <param name="code">code representation of the glyph in the font file</param>
            <param name="width">normalized width of the glyph</param>
            <param name="unicode">utf-32 representation of glyph if appears. Correct value is &gt; -1</param>
            <param name="chars">
            The Unicode text represented by this Glyph.
            if null is passed, the unicode value is used to retrieve the chars.
            </param>
            <param name="IsMark">True if the glyph is a Mark</param>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.#ctor(iText.IO.Font.Otf.Glyph)">
            <summary>Copy a Glyph.</summary>
            <param name="glyph">Glyph to copy</param>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.#ctor(iText.IO.Font.Otf.Glyph,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>Copy a Glyph and assign new placement and advance offsets and a new index delta to base glyph</summary>
            <param name="glyph">Glyph to copy</param>
            <param name="xPlacement">x - placement offset</param>
            <param name="yPlacement">y - placement offset</param>
            <param name="xAdvance">x - advance offset</param>
            <param name="yAdvance">y - advance offset</param>
            <param name="anchorDelta">Index delta to base glyph. If after a glyph there are several anchored glyphs we should know we to find base glyph.
                </param>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.#ctor(iText.IO.Font.Otf.Glyph,System.Int32)">
            <summary>Copy a glyph and assign the copied glyph a new unicode point and characters</summary>
            <param name="glyph">glyph to copy</param>
            <param name="unicode">new unicode point</param>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.Equals(System.Object)">
            <summary>Two Glyphs are equal if their unicode characters, code and normalized width are equal.</summary>
            <param name="obj">The object</param>
            <returns>True if this equals obj cast to Glyph, false otherwise.</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.GetUnicodeString">
            <summary>Gets a Unicode string corresponding to this glyph.</summary>
            <remarks>
            Gets a Unicode string corresponding to this glyph. In general case it might consist of many characters.
            If this glyph does not have a valid unicode (
            <see cref="M:iText.IO.Font.Otf.Glyph.HasValidUnicode"/>
            ), then a string consisting of a special
            Unicode '\ufffd' character is returned.
            </remarks>
            <returns>the Unicode string that corresponds to this glyph</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.Glyph.GetUnicodeChars">
            <summary>Gets Unicode char sequence corresponding to this glyph.</summary>
            <remarks>
            Gets Unicode char sequence corresponding to this glyph. In general case it might consist of many characters.
            If this glyph does not have a valid unicode (
            <see cref="M:iText.IO.Font.Otf.Glyph.HasValidUnicode"/>
            ), then a special
            Unicode '\ufffd' character is returned.
            </remarks>
            <returns>the Unicode char sequence that corresponds to this glyph</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.#ctor(System.Collections.Generic.IList{iText.IO.Font.Otf.Glyph})">
            <summary>Create a new line of Glyphs.</summary>
            <param name="glyphs">list containing the glyphs</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.#ctor(System.Collections.Generic.IList{iText.IO.Font.Otf.Glyph},System.Int32,System.Int32)">
            <summary>Create a new line of Glyphs from a slice of a List of Glyphs.</summary>
            <param name="glyphs">list of Glyphs to slice</param>
            <param name="start">starting index of the slice</param>
            <param name="end">terminating index of the slice</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.#ctor(System.Collections.Generic.IList{iText.IO.Font.Otf.Glyph},System.Collections.Generic.IList{iText.IO.Font.Otf.GlyphLine.ActualText},System.Int32,System.Int32)">
            <summary>Create a new line of Glyphs from a slice of a List of Glyphs, and add the actual text.</summary>
            <param name="glyphs">list of Glyphs to slice</param>
            <param name="actualText">corresponding list containing the actual text the glyphs represent</param>
            <param name="start">starting index of the slice</param>
            <param name="end">terminating index of the slice</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.#ctor(iText.IO.Font.Otf.GlyphLine)">
            <summary>Copy a line of Glyphs.</summary>
            <param name="other">line of Glyphs to copy</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.#ctor(iText.IO.Font.Otf.GlyphLine,System.Int32,System.Int32)">
            <summary>Copy a slice of a line of Glyphs</summary>
            <param name="other">line of Glyphs to copy</param>
            <param name="start">starting index of the slice</param>
            <param name="end">terminating index of the slice</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GetStart">
            <summary>Retrieves the start of the glyph line.</summary>
            <returns>start of glyph line</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.SetStart(System.Int32)">
            <summary>Sets the start of the glyph line.</summary>
            <param name="start">start of glyph line</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GetEnd">
            <summary>Retrieves the end of the glyph line.</summary>
            <returns>end of glyph line</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.SetEnd(System.Int32)">
            <summary>Sets the end of the glyph line.</summary>
            <param name="end">end of glyph line</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GetIdx">
            <summary>Retrieves the idx of the glyph line.</summary>
            <returns>idx of glyph line</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.SetIdx(System.Int32)">
            <summary>Sets the idx of the glyph line.</summary>
            <param name="idx">idx of glyph line</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.ToUnicodeString(System.Int32,System.Int32)">
            <summary>Get the unicode string representation of the GlyphLine slice.</summary>
            <param name="start">starting index of the slice</param>
            <param name="end">terminating index of the slice</param>
            <returns>String containing the unicode representation of the slice.</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.Copy(System.Int32,System.Int32)">
            <summary>Copy a slice of this Glyphline.</summary>
            <param name="left">leftmost index of the slice</param>
            <param name="right">rightmost index of the slice</param>
            <returns>new GlyphLine containing the copied slice</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.Add(iText.IO.Font.Otf.GlyphLine)">
            <summary>Add a line to the current one.</summary>
            <remarks>
            Add a line to the current one.
            The glyphs from the start till the end points will be copied.
            The same is true for the actual text.
            </remarks>
            <param name="other">the line that should be added to the current one</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.ReplaceContent(iText.IO.Font.Otf.GlyphLine)">
            <summary>Replaces the current content with the other line's content.</summary>
            <param name="other">the line with the content to be set to the current one</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.#ctor(System.Int32,System.Int32)">
            <summary>Creates a glyph line part object with given start and end values.</summary>
            <remarks>
            Creates a glyph line part object with given start and end values.
            Actual text is set to null.
            </remarks>
            <param name="start">start value of the glyph line part</param>
            <param name="end">end value of the glyph line part</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.#ctor(System.Int32,System.Int32,System.String)">
            <summary>Creates a glyph line part object with given start, end and actual text values.</summary>
            <param name="start">start value of the glyph line part</param>
            <param name="end">end value of the glyph line part</param>
            <param name="actualText">actual text</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.GetStart">
            <summary>Retrieves the start of the glyph line part.</summary>
            <returns>start value of glyph line part</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.SetStart(System.Int32)">
            <summary>Sets the start of the glyph line part.</summary>
            <param name="start">start of the glyph line part</param>
            <returns>Altered glyph line part object</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.GetEnd">
            <summary>Retrieves the end of the glyph line part.</summary>
            <returns>end value of glyph line part</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.SetEnd(System.Int32)">
            <summary>Sets the end of the glyph line part.</summary>
            <param name="end">end value of glyph line part</param>
            <returns>Altered glyph line part object</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.GetActualText">
            <summary>Retrieves the actual text of the glyph line part.</summary>
            <returns>Actual text of glyph line part</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.SetActualText(System.String)">
            <summary>Sets the actual text of the glyph line part.</summary>
            <param name="actualText">Actual text of glyph line part</param>
            <returns>Altered Glyph line part object</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.IsReversed">
            <summary>Retrieves whether the glyph line part is reversed.</summary>
            <returns>True if it is reversed, false otherwise.</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.GlyphLinePart.SetReversed(System.Boolean)">
            <summary>Sets whether the glyph line part is reversed.</summary>
            <param name="reversed">true if it should be reversed, false otherwise</param>
            <returns>Altered glyph line part object</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GlyphLine.ActualText.GetValue">
            <summary>Retrieves the value of the actual text.</summary>
            <returns>actual text value</returns>
        </member>
        <member name="T:iText.IO.Font.Otf.GlyphPositioningTableReader">
            <summary>Parses an OpenTypeFont file and reads the Glyph Substitution Table.</summary>
            <remarks>
            Parses an OpenTypeFont file and reads the Glyph Substitution Table. This table governs how two or more Glyphs should be merged
            to a single Glyph. This is especially useful for Asian languages like Bangla, Hindi, etc.
            <para />
            This has been written according to the OPenTypeFont specifications. This may be found <a href="http://www.microsoft.com/typography/otspec/gsub.htm">here</a>.
            </remarks>
        </member>
        <member name="T:iText.IO.Font.Otf.GlyphSubstitutionTableReader">
            <summary>Parses an OpenTypeFont file and reads the Glyph Substitution Table.</summary>
            <remarks>
            Parses an OpenTypeFont file and reads the Glyph Substitution Table. This table governs how two or more Glyphs should be merged
            to a single Glyph. This is especially useful for Asian languages like Bangla, Hindi, etc.
            <para />
            This has been written according to the OPenTypeFont specifications. This may be found <a href="http://www.microsoft.com/typography/otspec/gsub.htm">here</a>.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.Otf.GposAnchor.#ctor(iText.IO.Font.Otf.GposAnchor)">
            <summary>Creates a Gpos Anchor object based on a given Gpos Anchor object.</summary>
            <param name="other">other Gpos Anchor object</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GposAnchor.GetXCoordinate">
            <summary>Retrieves the X coordinate of the Gpos Anchor.</summary>
            <returns>X coordinate</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GposAnchor.SetXCoordinate(System.Int32)">
            <summary>Sets the x coordinate of the Gpos Anchor.</summary>
            <param name="xCoordinate">X coordinate</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GposAnchor.GetYCoordinate">
            <summary>Retrieves the Y coordinate of the Gpos Anchor.</summary>
            <returns>Y coordinate</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GposAnchor.SetYCoordinate(System.Int32)">
            <summary>Sets the Y coordinate of the Gpos Anchor.</summary>
            <param name="yCoordinate">Y coordinate</param>
        </member>
        <member name="T:iText.IO.Font.Otf.GposLookupType1">
            <summary>Lookup Type 1: Single Adjustment Positioning Subtable</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.GposLookupType2">
            <summary>
            Lookup Type 2:
            Pair Adjustment Positioning Subtable
            </summary>
        </member>
        <member name="M:iText.IO.Font.Otf.GposLookupType2.PairValueFormat.GetFirst">
            <summary>Retrieves the first object of the pair.</summary>
            <returns>first object</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GposLookupType2.PairValueFormat.SetFirst(iText.IO.Font.Otf.GposValueRecord)">
            <summary>Sets the first object of the pair.</summary>
            <param name="first">first object</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GposLookupType2.PairValueFormat.GetSecond">
            <summary>Retrieves the second object of the pair.</summary>
            <returns>second object</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GposLookupType2.PairValueFormat.SetSecond(iText.IO.Font.Otf.GposValueRecord)">
            <summary>Sets the second object of the pair.</summary>
            <param name="second">second object</param>
        </member>
        <member name="T:iText.IO.Font.Otf.GposLookupType4">
            <summary>
            Lookup Type 4:
            MarkToBase Attachment Positioning Subtable
            </summary>
        </member>
        <member name="T:iText.IO.Font.Otf.GposLookupType5">
            <summary>
            Lookup Type 5:
            MarkToLigature Attachment Positioning Subtable
            </summary>
        </member>
        <member name="T:iText.IO.Font.Otf.GposLookupType6">
            <summary>
            Lookup Type 6:
            MarkToMark Attachment Positioning Subtable
            </summary>
        </member>
        <member name="T:iText.IO.Font.Otf.GposLookupType7">
            <summary>
            Lookup Type 7:
            Contextual Positioning Subtables
            </summary>
        </member>
        <member name="T:iText.IO.Font.Otf.GposLookupType8">
            <summary>
            Lookup Type 8:
            Chaining Contextual Positioning Subtable
            </summary>
        </member>
        <member name="M:iText.IO.Font.Otf.GposValueRecord.GetXPlacement">
            <summary>Retrieves the X placement of the Gpos value record.</summary>
            <returns>X placement</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GposValueRecord.SetXPlacement(System.Int32)">
            <summary>Sets the X placement of the Gpos value record.</summary>
            <param name="xPlacement">X placement</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GposValueRecord.GetYPlacement">
            <summary>Retrieves the Y placement of the Gpos value record.</summary>
            <returns>Y placement</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GposValueRecord.SetYPlacement(System.Int32)">
            <summary>Sets the Y placement of the Gpos value record.</summary>
            <param name="yPlacement">Y placement</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GposValueRecord.GetXAdvance">
            <summary>Retrieves the X advance of the Gpos value record.</summary>
            <returns>x advance</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GposValueRecord.SetXAdvance(System.Int32)">
            <summary>Sets the X advance of the Gpos value record.</summary>
            <param name="xAdvance">X advance</param>
        </member>
        <member name="M:iText.IO.Font.Otf.GposValueRecord.GetYAdvance">
            <summary>Retrieves the Y advance of the Gpos value record.</summary>
            <returns>Y advance</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.GposValueRecord.SetYAdvance(System.Int32)">
            <summary>Sets the Y advance of the Gpos value record.</summary>
            <param name="yAdvance">Y advance</param>
        </member>
        <member name="T:iText.IO.Font.Otf.GsubLookupType2">
            <summary>LookupType 2: Multiple Substitution Subtable</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.GsubLookupType3">
            <summary>LookupType 3: Alternate Substitution Subtable</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.GsubLookupType4">
            <summary>LookupType 4: Ligature Substitution Subtable</summary>
        </member>
        <member name="F:iText.IO.Font.Otf.GsubLookupType4.ligatures">
            <summary>The key is the first character.</summary>
            <remarks>
            The key is the first character. The first element in the int array is the
            output ligature
            </remarks>
        </member>
        <member name="T:iText.IO.Font.Otf.GsubLookupType5">
            <summary>LookupType 5: Contextual Substitution Subtable</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.GsubLookupType6">
            <summary>LookupType 6: Chaining Contextual Substitution Subtable</summary>
        </member>
        <member name="M:iText.IO.Font.Otf.LanguageRecord.GetTag">
            <summary>Retrieves the tag of the language record.</summary>
            <returns>tag of record</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.LanguageRecord.SetTag(System.String)">
            <summary>Sets the tag of the language record.</summary>
            <param name="tag">tag of record</param>
        </member>
        <member name="M:iText.IO.Font.Otf.LanguageRecord.GetFeatureRequired">
            <summary>Retrieves the feature required of the language record.</summary>
            <returns>feature required</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.LanguageRecord.SetFeatureRequired(System.Int32)">
            <summary>Sets the feature required of the language record.</summary>
            <param name="featureRequired">feature required</param>
        </member>
        <member name="M:iText.IO.Font.Otf.LanguageRecord.GetFeatures">
            <summary>Retrieves the features of the language record.</summary>
            <returns>features</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.LanguageRecord.SetFeatures(System.Int32[])">
            <summary>Sets the features of the language record.</summary>
            <param name="features">features</param>
        </member>
        <member name="T:iText.IO.Font.Otf.LanguageTags">
            <summary>Constants corresponding to language tags in the OTF specification.</summary>
            <remarks>
            Constants corresponding to language tags in the OTF specification.
            Extracted from the specification, as published by Microsoft
            <a href="https://docs.microsoft.com/en-us/typography/opentype/spec/languagetags">here</a>.
            Note that tags in OTF always consist of exactly 4 bytes. Shorter
            identifiers are padded with spaces as necessary.
            </remarks>
        </member>
        <member name="T:iText.IO.Font.Otf.Lookuptype5.SubTableLookup5Format1">
            <summary>Contextual Substitution Subtable: Simple context glyph substitution</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.Lookuptype5.SubTableLookup5Format2">
            <summary>Contextual Substitution Subtable: Class-based context glyph substitution</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.Lookuptype5.SubTableLookup5Format3">
            <summary>Contextual Substitution Subtable: Coverage-based context glyph substitution</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.Lookuptype6.SubTableLookup6Format1">
            <summary>Chaining Contextual Substitution Subtable: Simple Chaining Context Glyph Substitution</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.Lookuptype6.SubTableLookup6Format2">
            <summary>Chaining Contextual Substitution Subtable: Class-based Chaining Context Glyph Substitution</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.Lookuptype6.SubTableLookup6Format3">
            <summary>Chaining Contextual Substitution Subtable: Coverage-based Chaining Context Glyph Substitution</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.Lookuptype8.PosTableLookup8Format1">
            <summary>Chaining Context Positioning Format 1: Simple Glyph Contexts</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.Lookuptype8.PosTableLookup8Format2">
            <summary>Chained Contexts Positioning Format 2: Class-based Glyph Contexts</summary>
        </member>
        <member name="T:iText.IO.Font.Otf.Lookuptype8.PosTableLookup8Format3">
            <summary>Chained Contexts Positioning Format 3: Coverage-based Glyph Contexts</summary>
        </member>
        <member name="M:iText.IO.Font.Otf.OpenTableLookup.GlyphIndexer.GetLine">
            <summary>Retrieves the glyph line of the object.</summary>
            <returns>glyph line</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.OpenTableLookup.GlyphIndexer.SetLine(iText.IO.Font.Otf.GlyphLine)">
            <summary>Sets the glyph line of the object.</summary>
            <param name="line">glyph line</param>
        </member>
        <member name="M:iText.IO.Font.Otf.OpenTableLookup.GlyphIndexer.GetGlyph">
            <summary>Retrieves the glyph of the object.</summary>
            <returns>glyph</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.OpenTableLookup.GlyphIndexer.SetGlyph(iText.IO.Font.Otf.Glyph)">
            <summary>Sets the glyph of the object.</summary>
            <param name="glyph">glyph</param>
        </member>
        <member name="M:iText.IO.Font.Otf.OpenTableLookup.GlyphIndexer.GetIdx">
            <summary>Retrieves the idx of the glyph indexer.</summary>
            <returns>idx</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.OpenTableLookup.GlyphIndexer.SetIdx(System.Int32)">
            <summary>Sets the idx of the glyph indexer.</summary>
            <param name="idx">idx</param>
        </member>
        <member name="M:iText.IO.Font.Otf.OpenTypeFontTableReader.StartReadingTable">
            <summary>This is the starting point of the class.</summary>
            <remarks>
            This is the starting point of the class. A sub-class must call this
            method to start getting call backs to the
            <see cref="M:iText.IO.Font.Otf.OpenTypeFontTableReader.ReadLookupTable(System.Int32,System.Int32,System.Int32[])"/>
            method.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.Otf.OtfMarkRecord.GetMarkClass">
            <summary>Retrieves the mark class of the OtfMarkRecord.</summary>
            <returns>mark class</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.OtfMarkRecord.SetMarkClass(System.Int32)">
            <summary>Sets the mark class of the OtfMarkRecord.</summary>
            <param name="markClass">mark class</param>
        </member>
        <member name="M:iText.IO.Font.Otf.OtfMarkRecord.GetAnchor">
            <summary>Retrieves the anchor of the OtfMarkRecord.</summary>
            <returns>anchor</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.OtfMarkRecord.SetAnchor(iText.IO.Font.Otf.GposAnchor)">
            <summary>Sets the anchor of the OtfMarkRecord.</summary>
            <param name="anchor">anchor</param>
        </member>
        <member name="M:iText.IO.Font.Otf.ScriptRecord.GetTag">
            <summary>Retrieves the tag of the Script Record.</summary>
            <returns>tag of record</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ScriptRecord.SetTag(System.String)">
            <summary>Sets the tag of the Script Record.</summary>
            <param name="tag">tag of record</param>
        </member>
        <member name="M:iText.IO.Font.Otf.ScriptRecord.GetDefaultLanguage">
            <summary>Retrieves the default language of the Script Record.</summary>
            <returns>default language</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ScriptRecord.SetDefaultLanguage(iText.IO.Font.Otf.LanguageRecord)">
            <summary>Sets the default language of the Script Record.</summary>
            <param name="defaultLanguage">default language</param>
        </member>
        <member name="M:iText.IO.Font.Otf.ScriptRecord.GetLanguages">
            <summary>Retrieves the languages of the Script Record.</summary>
            <returns>languages</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.ScriptRecord.SetLanguages(iText.IO.Font.Otf.LanguageRecord[])">
            <summary>Sets the languages of the Script Record.</summary>
            <param name="languages">languages</param>
        </member>
        <member name="M:iText.IO.Font.Otf.TagAndLocation.GetTag">
            <summary>Retrieves the tag of the object.</summary>
            <returns>tag</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.TagAndLocation.SetTag(System.String)">
            <summary>Sets the tag of the object.</summary>
            <param name="tag">tag</param>
        </member>
        <member name="M:iText.IO.Font.Otf.TagAndLocation.GetLocation">
            <summary>Retrieves the location of the object.</summary>
            <returns>location</returns>
        </member>
        <member name="M:iText.IO.Font.Otf.TagAndLocation.SetLocation(System.Int32)">
            <summary>Sets the location of the object.</summary>
            <param name="location">location</param>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.IDENTITY_H">
            <summary>The Unicode encoding with horizontal writing.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.IDENTITY_V">
            <summary>The Unicode encoding with vertical writing.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.CP1250">
            <summary>A possible encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.CP1252">
            <summary>A possible encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.CP1253">
            <summary>A possible encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.CP1257">
            <summary>A possible encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.WINANSI">
            <summary>A possible encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.MACROMAN">
            <summary>A possible encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.SYMBOL">
            <summary>A possible encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.ZAPFDINGBATS">
            <summary>A possible encoding.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.UNICODE_BIG">
            <summary>This is the encoding to be used to output text in Unicode.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.UNICODE_BIG_UNMARKED">
            <summary>This is the encoding to be used to output text for Identity-H/V CMaps.</summary>
        </member>
        <member name="F:iText.IO.Font.PdfEncodings.PDF_DOC_ENCODING">
            <summary>
            This is the default encoding to be used for converting Strings into
            bytes and vice versa.
            </summary>
            <remarks>
            This is the default encoding to be used for converting Strings into
            bytes and vice versa. The default encoding is PDF_DOC_ENCODING.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.PdfEncodings.ConvertToBytes(System.String,System.String)">
            <summary>
            Converts a
            <c>String</c>
            to a
            <c>byte</c>
            array according
            to the font's encoding.
            </summary>
            <param name="encoding">the encoding</param>
            <param name="text">
            the
            <c>String</c>
            to be converted
            </param>
            <returns>
            an array of
            <c>byte</c>
            representing the conversion according to the font's encoding
            </returns>
        </member>
        <member name="M:iText.IO.Font.PdfEncodings.ConvertToBytes(System.Char,System.String)">
            <summary>
            Converts a
            <c>char</c>
            to a
            <c>byte</c>
            array according
            to the font's encoding.
            </summary>
            <param name="encoding">the encoding</param>
            <param name="ch">
            the
            <c>char</c>
            to be converted
            </param>
            <returns>
            an array of
            <c>byte</c>
            representing the conversion according to the font's encoding
            </returns>
        </member>
        <member name="M:iText.IO.Font.PdfEncodings.ConvertToString(System.Byte[],System.String)">
            <summary>
            Converts a
            <c>byte</c>
            array to a
            <c>String</c>
            according
            to the some encoding.
            </summary>
            <param name="bytes">the bytes to convert</param>
            <param name="encoding">the encoding</param>
            <returns>
            the converted
            <c>String</c>
            </returns>
        </member>
        <member name="M:iText.IO.Font.PdfEncodings.IsPdfDocEncoding(System.String)">
            <summary>
            Checks is
            <paramref name="text"/>
            only has PDF_DOC_ENCODING characters.
            </summary>
            <param name="text">
            the
            <c>String</c>
            to test
            </param>
            <returns>
            
            <see langword="true"/>
            if only PDF_DOC_ENCODING characters are present
            </returns>
        </member>
        <member name="M:iText.IO.Font.PdfEncodings.AddExtraEncoding(System.String,iText.IO.Font.IExtraEncoding)">
            <summary>Adds an extra encoding.</summary>
            <param name="name">the name of the encoding. The encoding recognition is case insensitive</param>
            <param name="enc">the conversion class</param>
        </member>
        <member name="T:iText.IO.Font.Pfm2afm">
            <summary>Converts a PFM file into an AFM file.</summary>
        </member>
        <member name="M:iText.IO.Font.Pfm2afm.#ctor(iText.IO.Source.RandomAccessFileOrArray,System.IO.Stream)">
            <summary>Creates a new instance of Pfm2afm</summary>
        </member>
        <member name="M:iText.IO.Font.Pfm2afm.Convert(iText.IO.Source.RandomAccessFileOrArray,System.IO.Stream)">
            <summary>Converts a PFM file into an AFM file.</summary>
            <param name="input">the PFM file</param>
            <param name="output">the AFM file</param>
        </member>
        <member name="F:iText.IO.Font.Pfm2afm.Win2PSStd">
            <summary>Translate table from 1004 to psstd.</summary>
            <remarks>
            Translate table from 1004 to psstd.  1004 is an extension of the
            Windows translate table used in PM.
            </remarks>
        </member>
        <member name="F:iText.IO.Font.Pfm2afm.WinChars">
            <summary>Windows character names.</summary>
            <remarks>
            Windows character names.  Give a name to the used locations
            for when the all flag is specified.
            </remarks>
        </member>
        <member name="T:iText.IO.Font.TrueTypeCollection">
            <summary>Use this class for working with true type collection font (*.ttc)</summary>
        </member>
        <member name="M:iText.IO.Font.TrueTypeCollection.#ctor(System.Byte[])">
            <summary>
            Creates a new
            <see cref="T:iText.IO.Font.TrueTypeCollection"/>
            instance by its bytes.
            </summary>
            <param name="ttc">the byte contents of the collection</param>
        </member>
        <member name="M:iText.IO.Font.TrueTypeCollection.#ctor(System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.IO.Font.TrueTypeCollection"/>
            instance by its file path.
            </summary>
            <param name="ttcPath">the path of the collection</param>
        </member>
        <member name="M:iText.IO.Font.TrueTypeCollection.GetFontByTccIndex(System.Int32)">
            <summary>method return TrueTypeFont by ttc index</summary>
            <param name="ttcIndex">the index for the TTC font</param>
            <returns>TrueTypeFont</returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeCollection.GetTTCSize">
            <summary>returns the number of fonts in True Type Collection (file or bytes array)</summary>
            <returns>returns the number of fonts</returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeCollection.IsCached">
            <summary>
            Indicates if fonts created by the call to
            <see cref="M:iText.IO.Font.TrueTypeCollection.GetFontByTccIndex(System.Int32)"/>
            will be cached or not.
            </summary>
            <returns><c>true</c> if the created fonts will be cached, <c>false</c> otherwise</returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeCollection.SetCached(System.Boolean)">
            <summary>
            Sets if fonts created by the call to
            <see cref="M:iText.IO.Font.TrueTypeCollection.GetFontByTccIndex(System.Int32)"/>
            will be cached or not.
            </summary>
            <param name="cached"><c>true</c> if the created fonts will be cached, <c>false</c> otherwise</param>
        </member>
        <member name="F:iText.IO.Font.TrueTypeFont.kerning">
            <summary>The map containing the kerning information.</summary>
            <remarks>
            The map containing the kerning information. It represents the content of
            table 'kern'. The key is an <c>Integer</c> where the top 16 bits
            are the glyph number for the first character and the lower 16 bits are the
            glyph number for the second character. The value is the amount of kerning in
            normalized 1000 units as an <c>Integer</c>. This value is usually negative.
            </remarks>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFont.GetKerning(iText.IO.Font.Otf.Glyph,iText.IO.Font.Otf.Glyph)">
            <summary>Gets the kerning between two glyphs.</summary>
            <param name="first">the first glyph</param>
            <param name="second">the second glyph</param>
            <returns>the kerning to be applied</returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFont.GetDirectoryOffset">
            <summary>The offset from the start of the file to the table directory.</summary>
            <remarks>
            The offset from the start of the file to the table directory.
            It is 0 for TTF and may vary for TTC depending on the chosen font.
            </remarks>
            <returns>directory Offset</returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFont.MapGlyphsCidsToGids(System.Collections.Generic.ICollection{System.Int32})">
            <summary>
            Maps a set of glyph CIDs (as used in PDF file) to corresponding GID values
            (as a glyph primary identifier in the font file).
            </summary>
            <remarks>
            Maps a set of glyph CIDs (as used in PDF file) to corresponding GID values
            (as a glyph primary identifier in the font file).
            This call is only meaningful for fonts that return true for
            <see cref="M:iText.IO.Font.TrueTypeFont.IsCff"/>.
            For other types of fonts, GID and CID are always the same, so that call would essentially
            return a set of the same values.
            </remarks>
            <param name="glyphs">a set of glyph CIDs</param>
            <returns>a set of glyph ids corresponding to the passed glyph CIDs</returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFont.IsCmapPresent(System.Int32,System.Int32)">
            <summary>
            Checks whether current
            <see cref="T:iText.IO.Font.TrueTypeFont"/>
            program contains the “cmap” subtable
            with provided platform ID and encoding ID.
            </summary>
            <param name="platformID">platform ID</param>
            <param name="encodingID">encoding ID</param>
            <returns>
            
            <see langword="true"/>
            if “cmap” subtable with provided platform ID and encoding ID is present in the font program,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFont.GetNumberOfCmaps">
            <summary>
            Gets the number of the “cmap” subtables for the current
            <see cref="T:iText.IO.Font.TrueTypeFont"/>
            program.
            </summary>
            <returns>the number of the “cmap” subtables</returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFont.GetCodePagesSupported">
            <summary>Gets the code pages supported by the font.</summary>
            <returns>the code pages supported by the font</returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFont.UpdateUsedGlyphs(System.Collections.Generic.SortedSet{System.Int32},System.Boolean,System.Collections.Generic.IList{System.Int32[]})">
            <summary>The method will update usedGlyphs with additional range or with all glyphs if there is no subset.
                </summary>
            <remarks>
            The method will update usedGlyphs with additional range or with all glyphs if there is no subset.
            This set of used glyphs can be used for building width array and ToUnicode CMAP.
            </remarks>
            <param name="usedGlyphs">
            a set of integers, which are glyph ids that denote used glyphs.
            This set is updated inside of the method if needed.
            </param>
            <param name="subset">subset status</param>
            <param name="subsetRanges">additional subset ranges</param>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFont.ToCompactRange(System.Collections.Generic.IList{System.Int32[]})">
            <summary>
            Normalizes given ranges by making sure that first values in pairs are lower than second values and merges overlapping
            ranges in one.
            </summary>
            <param name="ranges">
            a
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            of integer arrays, which are constituted by pairs of ints that denote
            each range limits. Each integer array size shall be a multiple of two
            </param>
            <returns>single merged array consisting of pairs of integers, each of them denoting a range</returns>
        </member>
        <member name="T:iText.IO.Font.TrueTypeFontSubset">
            <summary>Subsets a True Type font by removing the unneeded glyphs from the font.</summary>
        </member>
        <member name="F:iText.IO.Font.TrueTypeFontSubset.tableDirectory">
            <summary>Contains the location of the several tables.</summary>
            <remarks>
            Contains the location of the several tables. The key is the name of
            the table and the value is an
            <c>int[3]</c>
            where position 0
            is the checksum, position 1 is the offset from the start of the file
            and position 2 is the length of the table.
            </remarks>
        </member>
        <member name="F:iText.IO.Font.TrueTypeFontSubset.rf">
            <summary>The file in use.</summary>
        </member>
        <member name="F:iText.IO.Font.TrueTypeFontSubset.fileName">
            <summary>The file name.</summary>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFontSubset.#ctor(System.String,iText.IO.Source.RandomAccessFileOrArray,System.Collections.Generic.ICollection{System.Int32},System.Int32,System.Boolean)">
            <summary>Creates a new TrueTypeFontSubSet</summary>
            <param name="directoryOffset">The offset from the start of the file to the table directory</param>
            <param name="fileName">the file name of the font</param>
            <param name="glyphsUsed">the glyphs used</param>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFontSubset.Process">
            <summary>Does the actual work of subsetting the font.</summary>
            <returns>the subset font</returns>
        </member>
        <member name="M:iText.IO.Font.TrueTypeFontSubset.ReadStandardString(System.Int32)">
            <summary>
            Reads a
            <c>String</c>
            from the font file as bytes using the Cp1252 encoding.
            </summary>
            <param name="length">the length of bytes to read</param>
            <returns>
            the
            <c>String</c>
            read
            </returns>
        </member>
        <member name="F:iText.IO.Font.Type1Font.kernPairs">
            <summary>Represents the section KernPairs in the AFM file.</summary>
            <remarks>
            Represents the section KernPairs in the AFM file.
            Key is uni1 &lt;&lt; 32 + uni2. Value is kerning value.
            </remarks>
        </member>
        <member name="F:iText.IO.Font.Type1Font.PFB_TYPES">
            <summary>Types of records in a PFB file.</summary>
            <remarks>Types of records in a PFB file. ASCII is 1 and BINARY is 2. They have to appear in the PFB file in this sequence.
                </remarks>
        </member>
        <member name="M:iText.IO.Font.Type1Font.InitializeGlyphs(iText.IO.Font.FontEncoding)">
            <summary>
            Fills missing character codes in
            <c>codeToGlyph</c>
            map.
            </summary>
            <param name="fontEncoding">to be used to map unicode values to character codes.</param>
        </member>
        <member name="M:iText.IO.Font.Type1Font.HasKernPairs">
            <summary>Checks if the font has any kerning pairs.</summary>
            <returns>
            
            <see langword="true"/>
            if the font has any kerning pairs.
            </returns>
        </member>
        <member name="M:iText.IO.Font.Type1Font.SetKerning(System.Int32,System.Int32,System.Int32)">
            <summary>Sets the kerning between two Unicode chars.</summary>
            <param name="first">the first unicode char.</param>
            <param name="second">the second unicode char.</param>
            <param name="kern">the kerning to apply in normalized 1000 units.</param>
            <returns>
            
            <see langword="true"/>
            if the kerning was applied,
            <see langword="false"/>
            otherwise.
            </returns>
        </member>
        <member name="M:iText.IO.Font.Type1Font.GetGlyph(System.String)">
            <summary>Find glyph by glyph name.</summary>
            <param name="name">Glyph name</param>
            <returns>Glyph instance if found, otherwise null.</returns>
        </member>
        <member name="M:iText.IO.Font.Type1Parser.#ctor(System.String,System.String,System.Byte[],System.Byte[])">
            <summary>Creates a new Type1 font file.</summary>
            <param name="afm">the AFM file if the input is made with a <c>byte</c> array</param>
            <param name="pfb">the PFB file if the input is made with a <c>byte</c> array</param>
            <param name="metricsPath">the name of one of the 14 built-in fonts or the location of an AFM file. The file must end in '.afm'
                </param>
        </member>
        <member name="T:iText.IO.Font.Woff2.JavaUnsignedUtil">
            <summary>Helper class to deal with unsigned primitives in java</summary>
        </member>
        <member name="T:iText.IO.Font.Woff2.Woff2Dec.Woff2FontInfo">
            <summary>Accumulates data we may need to reconstruct a single font.</summary>
            <remarks>
            Accumulates data we may need to reconstruct a single font. One per font
            created for a TTC.
            </remarks>
        </member>
        <member name="T:iText.IO.Font.Woff2.Woff2MemoryOut">
            <summary>Fixed memory block for woff2 out.</summary>
        </member>
        <member name="T:iText.IO.Font.Woff2.Woff2Out">
            <summary>Output interface for the woff2 decoding.</summary>
            <remarks>
            Output interface for the woff2 decoding.
            Writes to arbitrary offsets are supported to facilitate updating offset
            table and checksums after tables are ready. Reading the current size is
            supported so a 'loca' table can be built up while writing glyphs.
            By default limits size to kDefaultMaxSize.
            </remarks>
        </member>
        <member name="M:iText.IO.Image.BmpImageData.#ctor(System.Uri,System.Boolean)">
            <summary>
            Creates instance of
            <see cref="T:iText.IO.Image.BmpImageData"/>
            </summary>
            <param name="url">url of the image</param>
            <param name="noHeader">indicates that the source image does not have a header</param>
        </member>
        <member name="M:iText.IO.Image.BmpImageData.#ctor(System.Byte[],System.Boolean)">
            <summary>
            Creates instance of
            <see cref="T:iText.IO.Image.BmpImageData"/>
            </summary>
            <param name="bytes">contents of the image</param>
            <param name="noHeader">indicates that the source image does not have a header</param>
        </member>
        <member name="M:iText.IO.Image.BmpImageData.IsNoHeader">
            <returns>True if the bitmap image does not contain a header</returns>
        </member>
        <member name="M:iText.IO.Image.BmpImageHelper.ProcessImage(iText.IO.Image.ImageData)">
            <summary>Process the passed Image data as a BMP image.</summary>
            <remarks>
            Process the passed Image data as a BMP image.
            Image is loaded and all image attributes are initialized and/or updated
            </remarks>
            <param name="image">the image to process as a BMP image</param>
        </member>
        <member name="M:iText.IO.Image.GifImageData.LoadData">
            <summary>Load data by URL.</summary>
            <remarks>
            Load data by URL. url must be not null.
            Note, this method doesn't check if data or url is null.
            </remarks>
        </member>
        <member name="M:iText.IO.Image.GifImageHelper.ProcessImage(iText.IO.Image.GifImageData)">
            <summary>Reads image source and fills GifImage object with parameters (frames, width, height)</summary>
            <param name="image">GifImage</param>
        </member>
        <member name="M:iText.IO.Image.GifImageHelper.ProcessImage(iText.IO.Image.GifImageData,System.Int32)">
            <summary>Reads image source and fills GifImage object with parameters (frames, width, height)</summary>
            <param name="image">GifImage</param>
            <param name="lastFrameNumber">the last frame of the gif image should be read</param>
        </member>
        <member name="M:iText.IO.Image.GifImageHelper.ReadHeader(iText.IO.Image.GifImageHelper.GifParameters)">
            <summary>Reads GIF file header information.</summary>
        </member>
        <member name="M:iText.IO.Image.GifImageHelper.ReadLSD(iText.IO.Image.GifImageHelper.GifParameters)">
            <summary>Reads Logical Screen Descriptor</summary>
        </member>
        <member name="M:iText.IO.Image.GifImageHelper.ReadShort(iText.IO.Image.GifImageHelper.GifParameters)">
            <summary>Reads next 16-bit value, LSB first</summary>
        </member>
        <member name="M:iText.IO.Image.GifImageHelper.ReadBlock(iText.IO.Image.GifImageHelper.GifParameters)">
            <summary>Reads next variable length block from input.</summary>
            <returns>number of bytes stored in "buffer"</returns>
        </member>
        <member name="M:iText.IO.Image.GifImageHelper.ReadFrame(iText.IO.Image.GifImageHelper.GifParameters)">
            <summary>Reads next frame image</summary>
        </member>
        <member name="M:iText.IO.Image.GifImageHelper.ReadGraphicControlExt(iText.IO.Image.GifImageHelper.GifParameters)">
            <summary>Reads Graphics Control Extension values</summary>
        </member>
        <member name="M:iText.IO.Image.GifImageHelper.Skip(iText.IO.Image.GifImageHelper.GifParameters)">
            <summary>
            Skips variable length blocks up to and including
            next zero length block.
            </summary>
        </member>
        <member name="F:iText.IO.Image.ImageData.serialId">
            <summary>a static that is used for attributing a unique id to each image.</summary>
        </member>
        <member name="F:iText.IO.Image.ImageData.colorEncodingComponentsNumber">
            <summary>Is the number of components used to encode colorspace.</summary>
        </member>
        <member name="M:iText.IO.Image.ImageData.GetColorEncodingComponentsNumber">
            <summary>Gets the number of components used to encode colorspace.</summary>
            <returns>the number of components used to encode colorspace</returns>
        </member>
        <member name="M:iText.IO.Image.ImageData.SetColorEncodingComponentsNumber(System.Int32)">
            <summary>Sets the number of components used to encode colorspace.</summary>
            <param name="colorEncodingComponentsNumber">the number of components used to encode colorspace</param>
        </member>
        <member name="M:iText.IO.Image.ImageData.CanImageBeInline">
            <summary>Checks if image can be inline</summary>
            <returns>if the image can be inline</returns>
        </member>
        <member name="M:iText.IO.Image.ImageData.LoadData">
            <summary>Load data from URL.</summary>
            <remarks>
            Load data from URL. url must be not null.
            Note, this method doesn't check if data or url is null.
            </remarks>
        </member>
        <member name="M:iText.IO.Image.ImageData.GetSerialId">
            <summary>Creates a new serial id.</summary>
            <returns>the new serialId</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.Create(System.Byte[],System.Boolean)">
            <summary>Create an ImageData instance representing the image from the image bytes.</summary>
            <param name="bytes">byte representation of the image.</param>
            <param name="recoverImage">whether to recover from a image error (for TIFF-images)</param>
            <returns>The created ImageData object.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.Create(System.Byte[])">
            <summary>Create an ImageData instance representing the image from the image bytes.</summary>
            <param name="bytes">byte representation of the image.</param>
            <returns>The created ImageData object.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.Create(System.Uri,System.Boolean)">
            <summary>Create an ImageData instance representing the image from the file located at the specified url.</summary>
            <param name="url">location of the image</param>
            <param name="recoverImage">whether to recover from a image error (for TIFF-images)</param>
            <returns>The created ImageData object.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.Create(System.Uri)">
            <summary>Create an ImageData instance representing the image from the file located at the specified url.</summary>
            <param name="url">location of the image</param>
            <returns>The created ImageData object.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.Create(System.String,System.Boolean)">
            <summary>Create an ImageData instance representing the image from the specified file.</summary>
            <param name="filename">filename of the file containing the image</param>
            <param name="recoverImage">whether to recover from a image error (for TIFF-images)</param>
            <returns>The created ImageData object.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.Create(System.String)">
            <summary>Create an ImageData instance representing the image from the specified file.</summary>
            <param name="filename">filename of the file containing the image</param>
            <returns>The created ImageData object.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.Create(System.Int32,System.Int32,System.Boolean,System.Int32,System.Int32,System.Byte[],System.Int32[])">
            <summary>Create an ImageData instance from the passed parameters.</summary>
            <param name="width">width of the image in pixels</param>
            <param name="height">height of the image in pixels</param>
            <param name="reverseBits">whether to reverse the bits stored in data (TIFF images).</param>
            <param name="typeCCITT">Type of CCITT encoding</param>
            <param name="parameters">colour space parameters</param>
            <param name="data">array containing raw image data</param>
            <param name="transparency">array containing transparency information</param>
            <returns>created ImageData object.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.Create(System.Int32,System.Int32,System.Int32,System.Int32,System.Byte[],System.Int32[])">
            <summary>Create an ImageData instance from the passed parameters.</summary>
            <param name="width">width of the image in pixels</param>
            <param name="height">height of the image in pixels</param>
            <param name="components">colour space components</param>
            <param name="bpc">bits per colour.</param>
            <param name="data">array containing raw image data</param>
            <param name="transparency">array containing transparency information</param>
            <returns>created ImageData object.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateBmp(System.Uri,System.Boolean)">
            <summary>Get a bitmap ImageData instance from the specified url.</summary>
            <param name="url">location of the image.</param>
            <param name="noHeader">Whether the image contains a header.</param>
            <returns>created ImageData</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateBmp(System.Byte[],System.Boolean)">
            <summary>Get a bitmap ImageData instance from the provided bytes.</summary>
            <param name="bytes">array containing the raw image data</param>
            <param name="noHeader">Whether the image contains a header.</param>
            <returns>created ImageData</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateGif(System.Byte[])">
            <summary>Return a GifImage object.</summary>
            <remarks>Return a GifImage object. This object cannot be added to a document</remarks>
            <param name="bytes">array containing the raw image data</param>
            <returns>GifImageData instance.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateGifFrame(System.Uri,System.Int32)">
            <summary>Returns a specified frame of the gif image</summary>
            <param name="url">url of gif image</param>
            <param name="frame">number of frame to be returned, 1-based</param>
            <returns>GifImageData instance.</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateGifFrame(System.Byte[],System.Int32)">
            <summary>Returns a specified frame of the gif image</summary>
            <param name="bytes">byte array of gif image</param>
            <param name="frame">number of frame to be returned, 1-based</param>
            <returns>GifImageData instance</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateGifFrames(System.Byte[],System.Int32[])">
            <summary>Returns <c>List</c> of gif image frames</summary>
            <param name="bytes">byte array of gif image</param>
            <param name="frameNumbers">array of frame numbers of gif image, 1-based</param>
            <returns>all frames of gif image</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateGifFrames(System.Uri,System.Int32[])">
            <summary>Returns <c>List</c> of gif image frames</summary>
            <param name="url">url of gif image</param>
            <param name="frameNumbers">array of frame numbers of gif image, 1-based</param>
            <returns>all frames of gif image</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateGifFrames(System.Byte[])">
            <summary>Returns <c>List</c> of gif image frames</summary>
            <param name="bytes">byte array of gif image</param>
            <returns>all frames of gif image</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateGifFrames(System.Uri)">
            <summary>Returns <c>List</c> of gif image frames</summary>
            <param name="url">url of gif image</param>
            <returns>all frames of gif image</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.CreateJpeg(System.Uri)">
            <summary>
            Create an
            <see cref="T:iText.IO.Image.ImageData"/>
            instance from a Jpeg image url
            </summary>
            <param name="url">URL</param>
            <returns>the created JPEG image</returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.IsSupportedType(System.Byte[])">
            <summary>Checks if the type of image (based on first 8 bytes) is supported by factory.</summary>
            <remarks>
            Checks if the type of image (based on first 8 bytes) is supported by factory.
            <br />
            <b>Note:</b> if this method returns
            <see langword="true"/>
            it doesn't means that
            <see cref="M:iText.IO.Image.ImageDataFactory.Create(System.Byte[])"/>
            won't throw exception
            </remarks>
            <param name="source">image raw bytes</param>
            <returns>
            
            <see langword="true"/>
            if first eight bytes are recognised by factory as valid image type and
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.IsSupportedType(System.Uri)">
            <summary>Checks if the type of image (based on first 8 bytes) is supported by factory.</summary>
            <remarks>
            Checks if the type of image (based on first 8 bytes) is supported by factory.
            <br />
            <b>Note:</b> if this method returns
            <see langword="true"/>
            it doesn't means that
            <see cref="M:iText.IO.Image.ImageDataFactory.Create(System.Byte[])"/>
            won't throw exception
            </remarks>
            <param name="source">image URL</param>
            <returns>
            
            <see langword="true"/>
            if first eight bytes are recognised by factory as valid image type and
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.IO.Image.ImageDataFactory.IsSupportedType(iText.IO.Image.ImageType)">
            <summary>Checks if the type of image is supported by factory.</summary>
            <remarks>
            Checks if the type of image is supported by factory.
            <br />
            <b>Note:</b> if this method returns
            <see langword="true"/>
            it doesn't means that
            <see cref="M:iText.IO.Image.ImageDataFactory.Create(System.Byte[])"/>
            won't throw exception
            </remarks>
            <param name="imageType">image type</param>
            <returns>
            
            <see langword="true"/>
            if image type is supported and
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="T:iText.IO.Image.ImageTypeDetector">
            <summary>Helper class that detects image type by magic bytes</summary>
        </member>
        <member name="M:iText.IO.Image.ImageTypeDetector.DetectImageType(System.Byte[])">
            <summary>Detect image type by magic bytes given the byte array source.</summary>
            <param name="source">image bytes</param>
            <returns>
            detected image type, see
            <see cref="T:iText.IO.Image.ImageType"/>
            . Returns
            <see cref="F:iText.IO.Image.ImageType.NONE"/>
            if image type is unknown
            </returns>
        </member>
        <member name="M:iText.IO.Image.ImageTypeDetector.DetectImageType(System.Uri)">
            <summary>Detect image type by magic bytes given the source URL.</summary>
            <param name="source">image URL</param>
            <returns>
            detected image type, see
            <see cref="T:iText.IO.Image.ImageType"/>
            . Returns
            <see cref="F:iText.IO.Image.ImageType.NONE"/>
            if image type is unknown
            </returns>
        </member>
        <member name="M:iText.IO.Image.ImageTypeDetector.DetectImageType(System.IO.Stream)">
            <summary>Detect image type by magic bytes given the input stream.</summary>
            <param name="stream">image stream</param>
            <returns>
            detected image type, see
            <see cref="T:iText.IO.Image.ImageType"/>
            . Returns
            <see cref="F:iText.IO.Image.ImageType.NONE"/>
            if image type is unknown
            </returns>
        </member>
        <member name="M:iText.IO.Image.Jbig2ImageData.GetNumberOfPages(System.Byte[])">
            <summary>Gets the number of pages in a JBIG2 image.</summary>
            <param name="bytes">a byte array containing a JBIG2 image</param>
            <returns>the number of pages</returns>
        </member>
        <member name="M:iText.IO.Image.Jbig2ImageData.GetNumberOfPages(iText.IO.Source.RandomAccessFileOrArray)">
            <summary>Gets the number of pages in a JBIG2 image.</summary>
            <param name="raf">
            a
            <c>RandomAccessFileOrArray</c>
            containing a JBIG2 image
            </param>
            <returns>the number of pages</returns>
        </member>
        <member name="M:iText.IO.Image.Jbig2ImageHelper.GetGlobalSegment(iText.IO.Source.RandomAccessFileOrArray)">
            <summary>
            Gets a byte array that can be used as a /JBIG2Globals,
            or null if not applicable to the given jbig2.
            </summary>
            <param name="ra">an random access file or array</param>
            <returns>a byte array</returns>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.GetNumOfComps">
            <summary>Retrieves number of components of the object.</summary>
            <returns>number of components</returns>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.SetNumOfComps(System.Int32)">
            <summary>Sets number of components of the object.</summary>
            <param name="numOfComps">number of components</param>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.GetColorSpecBoxes">
            <summary>Retrieves the color spec boxes of the object.</summary>
            <returns>color spec boxes</returns>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.SetColorSpecBoxes(System.Collections.Generic.IList{iText.IO.Image.Jpeg2000ImageData.ColorSpecBox})">
            <summary>Sets the color spec boxes of the object.</summary>
            <param name="colorSpecBoxes">color spec boxes</param>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.IsJp2">
            <summary>Retrieves whether the object is a Jp2.</summary>
            <returns>true if it is a jp2, otherwise false</returns>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.SetJp2(System.Boolean)">
            <summary>Sets whether the object is a jp2.</summary>
            <param name="jp2">true is it is a jp2, otherwise false</param>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.IsJpxBaseline">
            <summary>Retrieves whether jpx is baseline.</summary>
            <returns>true if jpx is baseline, false otherwise</returns>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.SetJpxBaseline(System.Boolean)">
            <summary>Sets whether jpx is baseline.</summary>
            <param name="jpxBaseline">true if jpx is baseline, false otherwise</param>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.GetBpcBoxData">
            <summary>Retrieves the bits per component of the box data.</summary>
            <returns>bits per component</returns>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageData.Parameters.SetBpcBoxData(System.Byte[])">
            <summary>Sets the bits per component of the box data.</summary>
            <param name="bpcBoxData">bits per component</param>
        </member>
        <member name="M:iText.IO.Image.Jpeg2000ImageHelper.ProcessParameters(iText.IO.Image.Jpeg2000ImageData)">
            <summary>This method checks if the image is a valid JPEG and processes some parameters.</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.NOT_A_MARKER">
            <summary>This is a type of marker.</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.VALID_MARKER">
            <summary>This is a type of marker.</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.VALID_MARKERS">
            <summary>Acceptable Jpeg markers.</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.UNSUPPORTED_MARKER">
            <summary>This is a type of marker.</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.UNSUPPORTED_MARKERS">
            <summary>Unsupported Jpeg markers.</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.NOPARAM_MARKER">
            <summary>This is a type of marker.</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.NOPARAM_MARKERS">
            <summary>Jpeg markers without additional parameters.</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.M_APP0">
            <summary>Marker value</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.M_APP2">
            <summary>Marker value</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.M_APPE">
            <summary>Marker value</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.M_APPD">
            <summary>Marker value for Photoshop IRB</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.JFIF_ID">
            <summary>sequence that is used in all Jpeg files</summary>
        </member>
        <member name="F:iText.IO.Image.JpegImageHelper.PS_8BIM_RESO">
            <summary>sequence preceding Photoshop resolution data</summary>
        </member>
        <member name="M:iText.IO.Image.JpegImageHelper.ProcessImage(iText.IO.Image.ImageData)">
            <summary>Process the passed Image data as a JPEG image.</summary>
            <remarks>
            Process the passed Image data as a JPEG image.
            Image is loaded and all image attributes are initialized and/or updated.
            </remarks>
            <param name="image">the image to process as a JPEG image</param>
        </member>
        <member name="M:iText.IO.Image.JpegImageHelper.ProcessParameters(System.IO.Stream,System.String,iText.IO.Image.ImageData)">
            <summary>This method checks if the image is a valid JPEG and processes some parameters.</summary>
        </member>
        <member name="M:iText.IO.Image.JpegImageHelper.GetShort(System.IO.Stream)">
            <summary>Reads a short from the <c>InputStream</c>.</summary>
            <param name="jpegStream">the <c>InputStream</c></param>
            <returns>an int</returns>
        </member>
        <member name="M:iText.IO.Image.JpegImageHelper.Marker(System.Int32)">
            <summary>Returns a type of marker.</summary>
            <param name="marker">an int</param>
            <returns>a type: <var>VALID_MARKER</var>, <var>UNSUPPORTED_MARKER</var> or <var>NOPARAM_MARKER</var></returns>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.PNGID">
            <summary>Some PNG specific values.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.IHDR">
            <summary>A PNG marker.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.PLTE">
            <summary>A PNG marker.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.IDAT">
            <summary>A PNG marker.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.IEND">
            <summary>A PNG marker.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.tRNS">
            <summary>A PNG marker.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.pHYs">
            <summary>A PNG marker.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.gAMA">
            <summary>A PNG marker.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.cHRM">
            <summary>A PNG marker.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.sRGB">
            <summary>A PNG marker.</summary>
        </member>
        <member name="F:iText.IO.Image.PngImageHelper.iCCP">
            <summary>A PNG marker.</summary>
        </member>
        <member name="M:iText.IO.Image.PngImageHelper.GetInt(System.IO.Stream)">
            <summary>Gets an <c>int</c> from an <c>InputStream</c>.</summary>
            <param name="pngStream">an <c>InputStream</c></param>
            <returns>the value of an <c>int</c></returns>
        </member>
        <member name="M:iText.IO.Image.PngImageHelper.GetWord(System.IO.Stream)">
            <summary>Gets a <c>word</c> from an <c>InputStream</c>.</summary>
            <param name="pngStream">an <c>InputStream</c></param>
            <returns>the value of an <c>int</c></returns>
        </member>
        <member name="M:iText.IO.Image.PngImageHelper.GetString(System.IO.Stream)">
            <summary>Gets a <c>String</c> from an <c>InputStream</c>.</summary>
            <param name="pngStream">an <c>InputStream</c></param>
            <returns>the value of an <c>int</c></returns>
        </member>
        <member name="F:iText.IO.Image.RawImageData.CCITTG4">
            <summary>Pure two-dimensional encoding (Group 4)</summary>
        </member>
        <member name="F:iText.IO.Image.RawImageData.CCITTG3_1D">
            <summary>Pure one-dimensional encoding (Group 3, 1-D)</summary>
        </member>
        <member name="F:iText.IO.Image.RawImageData.CCITTG3_2D">
            <summary>Mixed one- and two-dimensional encoding (Group 3, 2-D)</summary>
        </member>
        <member name="F:iText.IO.Image.RawImageData.CCITT_BLACKIS1">
            <summary>
            A flag indicating whether 1-bits are to be interpreted as black pixels
            and 0-bits as white pixels,
            </summary>
        </member>
        <member name="F:iText.IO.Image.RawImageData.CCITT_ENCODEDBYTEALIGN">
            <summary>
            A flag indicating whether the filter expects extra 0-bits before each
            encoded line so that the line begins on a byte boundary.
            </summary>
        </member>
        <member name="F:iText.IO.Image.RawImageData.CCITT_ENDOFLINE">
            <summary>
            A flag indicating whether end-of-line bit patterns are required to be
            present in the encoding.
            </summary>
        </member>
        <member name="F:iText.IO.Image.RawImageData.CCITT_ENDOFBLOCK">
            <summary>
            A flag indicating whether the filter expects the encoded data to be
            terminated by an end-of-block pattern, overriding the Rows parameter.
            </summary>
            <remarks>
            A flag indicating whether the filter expects the encoded data to be
            terminated by an end-of-block pattern, overriding the Rows parameter. The
            use of this flag will set the key /EndOfBlock to false.
            </remarks>
        </member>
        <member name="M:iText.IO.Image.RawImageHelper.UpdateRawImageParameters(iText.IO.Image.RawImageData,System.Int32,System.Int32,System.Int32,System.Int32,System.Byte[])">
            <summary>Update original image with Raw Image parameters.</summary>
            <param name="image">to update its parameters with Raw Image parameters.</param>
            <param name="width">the exact width of the image</param>
            <param name="height">the exact height of the image</param>
            <param name="components">1,3 or 4 for GrayScale, RGB and CMYK</param>
            <param name="bpc">bits per component. Must be 1,2,4 or 8</param>
            <param name="data">the image data</param>
        </member>
        <member name="M:iText.IO.Image.TiffImageData.GetNumberOfPages(iText.IO.Source.RandomAccessFileOrArray)">
            <summary>Gets the number of pages the TIFF document has.</summary>
            <param name="raf">
            a
            <c>RandomAccessFileOrArray</c>
            containing a TIFF image.
            </param>
            <returns>the number of pages.</returns>
        </member>
        <member name="M:iText.IO.Image.TiffImageData.GetNumberOfPages(System.Byte[])">
            <summary>Gets the number of pages the TIFF document has.</summary>
            <param name="bytes">a byte array containing a TIFF image.</param>
            <returns>the number of pages.</returns>
        </member>
        <member name="M:iText.IO.Image.TiffImageHelper.ProcessImage(iText.IO.Image.ImageData)">
            <summary>Processes the ImageData as a TIFF image.</summary>
            <param name="image">image to process.</param>
        </member>
        <member name="T:iText.IO.Logs.IoLogMessageConstant">
            <summary>Class containing constants to be used in logging.</summary>
        </member>
        <member name="T:iText.IO.Source.ArrayRandomAccessSource">
            <summary>A RandomAccessSource that is based on an underlying byte array</summary>
        </member>
        <member name="M:iText.IO.Source.ByteBuffer.Prepend(System.Byte)">
            <summary>
            Fill
            <c>ByteBuffer</c>
            from the end.
            </summary>
            <remarks>
            Fill
            <c>ByteBuffer</c>
            from the end.
            Set byte at
            <c>capacity() - size() - 1</c>
            position.
            </remarks>
            <param name="b">
            
            <c>byte</c>.
            </param>
            <returns>
            
            <c>ByteBuffer</c>.
            </returns>
        </member>
        <member name="M:iText.IO.Source.ByteBuffer.Prepend(System.Byte[])">
            <summary>
            Fill
            <c>ByteBuffer</c>
            from the end.
            </summary>
            <remarks>
            Fill
            <c>ByteBuffer</c>
            from the end.
            Set bytes from
            <c>capacity() - size() - b.length</c>
            position.
            </remarks>
            <param name="b">
            
            <c>byte</c>.
            </param>
            <returns>
            
            <c>ByteBuffer</c>.
            </returns>
        </member>
        <member name="M:iText.IO.Source.GetBufferedRandomAccessSource.#ctor(iText.IO.Source.IRandomAccessSource)">
            <summary>Constructs a new OffsetRandomAccessSource</summary>
            <param name="source">the source</param>
        </member>
        <member name="M:iText.IO.Source.GetBufferedRandomAccessSource.Get(System.Int64)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.GetBufferedRandomAccessSource.Get(System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.GetBufferedRandomAccessSource.Length">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.GetBufferedRandomAccessSource.Close">
            <summary>Does nothing - the underlying source is not closed</summary>
        </member>
        <member name="T:iText.IO.Source.GroupedRandomAccessSource">
            <summary>
            A RandomAccessSource that is based on a set of underlying sources,
            treating the sources as if they were a contiguous block of data.
            </summary>
        </member>
        <member name="F:iText.IO.Source.GroupedRandomAccessSource.sources">
            <summary>The underlying sources (along with some meta data to quickly determine where each source begins and ends)
                </summary>
        </member>
        <member name="F:iText.IO.Source.GroupedRandomAccessSource.currentSourceEntry">
            <summary>Cached value to make multiple reads from the same underlying source more efficient</summary>
        </member>
        <member name="F:iText.IO.Source.GroupedRandomAccessSource.size">
            <summary>Cached size of the underlying channel</summary>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.#ctor(iText.IO.Source.IRandomAccessSource[])">
            <summary>
            Constructs a new
            <see cref="T:iText.IO.Source.GroupedRandomAccessSource"/>
            based on the specified set of sources
            </summary>
            <param name="sources">the sources used to build this group</param>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.GetStartingSourceIndex(System.Int64)">
            <summary>For a given offset, return the index of the source that contains the specified offset.</summary>
            <remarks>
            For a given offset, return the index of the source that contains the specified offset.
            This is an optimization feature to help optimize the access of the correct source without having to iterate
            through every single source each time.  It is safe to always return 0, in which case the full set of sources
            will be searched.
            Subclasses should override this method if they are able to compute the source index more efficiently
            (for example
            <see cref="!:FileChannelRandomAccessSource"/>
            takes advantage of fixed size page buffers to compute the index)
            </remarks>
            <param name="offset">the offset</param>
            <returns>the index of the input source that contains the specified offset, or 0 if unknown</returns>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.GetSourceEntryForOffset(System.Int64)">
            <summary>
            Returns the SourceEntry that contains the byte at the specified offset
            sourceReleased is called as a notification callback so subclasses can take care of cleanup
            when the source is no longer the active source
            </summary>
            <param name="offset">the offset of the byte to look for</param>
            <returns>the SourceEntry that contains the byte at the specified offset</returns>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.SourceReleased(iText.IO.Source.IRandomAccessSource)">
            <summary>Called when a given source is no longer the active source.</summary>
            <remarks>Called when a given source is no longer the active source.  This gives subclasses the abilty to release resources, if appropriate.
                </remarks>
            <param name="source">the source that is no longer the active source</param>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.SourceInUse(iText.IO.Source.IRandomAccessSource)">
            <summary>Called when a given source is about to become the active source.</summary>
            <remarks>Called when a given source is about to become the active source.  This gives subclasses the abilty to retrieve resources, if appropriate.
                </remarks>
            <param name="source">the source that is about to become the active source</param>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.Get(System.Int64)">
            <summary>
            <inheritDoc/>
            The source that contains the byte at position is retrieved, the correct offset into that source computed, then the value
            from that offset in the underlying source is returned.
            </summary>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.Get(System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.Length">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.Close">
            <summary>
            <inheritDoc/>
            <br/>
            Closes all of the underlying sources.
            </summary>
        </member>
        <member name="T:iText.IO.Source.GroupedRandomAccessSource.SourceEntry">
            <summary>Used to track each source, along with useful meta data</summary>
        </member>
        <member name="F:iText.IO.Source.GroupedRandomAccessSource.SourceEntry.source">
            <summary>The underlying source</summary>
        </member>
        <member name="F:iText.IO.Source.GroupedRandomAccessSource.SourceEntry.firstByte">
            <summary>The first byte (in the coordinates of the GroupedRandomAccessSource) that this source contains</summary>
        </member>
        <member name="F:iText.IO.Source.GroupedRandomAccessSource.SourceEntry.lastByte">
            <summary>The last byte (in the coordinates of the GroupedRandomAccessSource) that this source contains</summary>
        </member>
        <member name="F:iText.IO.Source.GroupedRandomAccessSource.SourceEntry.index">
            <summary>The index of this source in the GroupedRandomAccessSource</summary>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.SourceEntry.#ctor(System.Int32,iText.IO.Source.IRandomAccessSource,System.Int64)">
            <summary>Standard constructor</summary>
            <param name="index">the index</param>
            <param name="source">the source</param>
            <param name="offset">the offset of the source in the GroupedRandomAccessSource</param>
        </member>
        <member name="M:iText.IO.Source.GroupedRandomAccessSource.SourceEntry.OffsetN(System.Int64)">
            <summary>Given an absolute offset (in the GroupedRandomAccessSource coordinates), calculate the effective offset in the underlying source
                </summary>
            <param name="absoluteOffset">the offset in the parent GroupedRandomAccessSource</param>
            <returns>the effective offset in the underlying source</returns>
        </member>
        <member name="T:iText.IO.Source.IndependentRandomAccessSource">
            <summary>A RandomAccessSource that is wraps another RandomAccessSource but does not propagate close().</summary>
            <remarks>
            A RandomAccessSource that is wraps another RandomAccessSource but does not propagate close().  This is useful when
            passing a RandomAccessSource to a method that would normally close the source.
            </remarks>
        </member>
        <member name="F:iText.IO.Source.IndependentRandomAccessSource.source">
            <summary>The source</summary>
        </member>
        <member name="M:iText.IO.Source.IndependentRandomAccessSource.#ctor(iText.IO.Source.IRandomAccessSource)">
            <summary>Constructs a new IndependentRandomAccessSource object</summary>
            <param name="source">the source</param>
        </member>
        <member name="M:iText.IO.Source.IndependentRandomAccessSource.Get(System.Int64)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.IndependentRandomAccessSource.Get(System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.IndependentRandomAccessSource.Length">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.IndependentRandomAccessSource.Close">
            <summary>Does nothing - the underlying source is not closed</summary>
        </member>
        <member name="T:iText.IO.Source.IRandomAccessSource">
            <summary>Represents an abstract source that bytes can be read from.</summary>
            <remarks>
            Represents an abstract source that bytes can be read from.  This class forms the foundation for all byte input in iText.
            Implementations do not keep track of a current 'position', but rather provide absolute get methods.  Tracking position
            should be handled in classes that use RandomAccessSource internally (via composition).
            </remarks>
        </member>
        <member name="M:iText.IO.Source.IRandomAccessSource.Get(System.Int64)">
            <summary>Gets a byte at the specified position</summary>
            <param name="position">byte position</param>
            <returns>the byte, or -1 if EOF is reached</returns>
        </member>
        <member name="M:iText.IO.Source.IRandomAccessSource.Get(System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>Read an array of bytes of specified length from the specified position of source to the buffer applying the offset.
                </summary>
            <remarks>
            Read an array of bytes of specified length from the specified position of source to the buffer applying the offset.
            If the number of bytes requested cannot be read, all the possible bytes will be read to the buffer,
            and the number of actually read bytes will be returned.
            </remarks>
            <param name="position">the position in the RandomAccessSource to read from</param>
            <param name="bytes">output buffer</param>
            <param name="off">offset into the output buffer where results will be placed</param>
            <param name="len">the number of bytes to read</param>
            <returns>the number of bytes actually read, or -1 if the file is at EOF</returns>
        </member>
        <member name="M:iText.IO.Source.IRandomAccessSource.Length">
            <summary>Gets the length of the source</summary>
            <returns>the length of this source</returns>
        </member>
        <member name="M:iText.IO.Source.IRandomAccessSource.Close">
            <summary>Closes this source.</summary>
            <remarks>Closes this source. The underlying data structure or source (if any) will also be closed</remarks>
        </member>
        <member name="F:iText.IO.Source.PdfTokenizer.closeStream">
            <summary>Streams are closed automatically.</summary>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.#ctor(iText.IO.Source.RandomAccessFileOrArray)">
            <summary>
            Creates a PdfTokenizer for the specified
            <see cref="T:iText.IO.Source.RandomAccessFileOrArray"/>.
            </summary>
            <remarks>
            Creates a PdfTokenizer for the specified
            <see cref="T:iText.IO.Source.RandomAccessFileOrArray"/>.
            The beginning of the file is read to determine the location of the header, and the data source is adjusted
            as necessary to account for any junk that occurs in the byte source before the header
            </remarks>
            <param name="file">the source</param>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.Peek">
            <summary>Gets the next byte of pdf source without moving source position.</summary>
            <returns>the byte, or -1 if EOF is reached</returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.Peek(System.Byte[])">
            <summary>
            Gets the next
            <c>buffer.length</c>
            bytes of pdf source without moving source position.
            </summary>
            <param name="buffer">buffer to store read bytes</param>
            <returns>
            the number of read bytes. If it is less than
            <c>buffer.length</c>
            it means EOF has been reached.
            </returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.GetNextEof">
            <summary>Gets next %%EOF marker in current PDF file.</summary>
            <returns>next %%EOF marker position</returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.DecodeStringContent(System.Byte[],System.Int32,System.Int32,System.Boolean)">
            <summary>Resolve escape symbols or hexadecimal symbols.</summary>
            <remarks>
            Resolve escape symbols or hexadecimal symbols.
            <para />
            NOTE Due to PdfReference 1.7 part 3.2.3 String value contain ASCII characters,
            so we can convert it directly to byte array.
            </remarks>
            <param name="content">string bytes to be decoded</param>
            <param name="from">given start index</param>
            <param name="to">given end index</param>
            <param name="hexWriting">
            true if given string is hex-encoded, e.g. '&lt;69546578…&gt;'.
            False otherwise, e.g. '((iText( some version)…)'
            </param>
            <returns>
            byte[] for decrypting or for creating
            <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.DecodeStringContent(System.Byte[],System.Boolean)">
            <summary>Resolve escape symbols or hexadecimal symbols.</summary>
            <remarks>
            Resolve escape symbols or hexadecimal symbols.
            <br />
            NOTE Due to PdfReference 1.7 part 3.2.3 String value contain ASCII characters,
            so we can convert it directly to byte array.
            </remarks>
            <param name="content">string bytes to be decoded</param>
            <param name="hexWriting">
            true if given string is hex-encoded, e.g. '&lt;69546578…&gt;'.
            False otherwise, e.g. '((iText( some version)…)'
            </param>
            <returns>
            byte[] for decrypting or for creating
            <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.IsWhitespace(System.Int32)">
            <summary>Is a certain character a whitespace? Currently checks on the following: '0', '9', '10', '12', '13', '32'.
                </summary>
            <remarks>
            Is a certain character a whitespace? Currently checks on the following: '0', '9', '10', '12', '13', '32'.
            <br />
            The same as calling
            <see cref="M:iText.IO.Source.PdfTokenizer.IsWhitespace(System.Int32,System.Boolean)">isWhiteSpace(ch, true)</see>.
            </remarks>
            <param name="ch">int</param>
            <returns>boolean</returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.IsWhitespace(System.Int32,System.Boolean)">
            <summary>Checks whether a character is a whitespace.</summary>
            <remarks>Checks whether a character is a whitespace. Currently checks on the following: '0', '9', '10', '12', '13', '32'.
                </remarks>
            <param name="ch">int</param>
            <param name="isWhitespace">boolean</param>
            <returns>boolean</returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.ThrowError(System.String,System.Object[])">
            <summary>Helper method to handle content errors.</summary>
            <remarks>
            Helper method to handle content errors. Add file position to
            <c>PdfRuntimeException</c>.
            </remarks>
            <param name="error">message.</param>
            <param name="messageParams">error params.</param>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.CheckTrailer(iText.IO.Source.ByteBuffer)">
            <summary>
            Checks whether
            <paramref name="line"/>
            equals to 'trailer'.
            </summary>
            <param name="line">for check</param>
            <returns>true, if line is equals to 'trailer', otherwise false</returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.ReadLineSegment(iText.IO.Source.ByteBuffer)">
            <summary>Reads data into the provided byte[].</summary>
            <remarks>
            Reads data into the provided byte[]. Checks on leading whitespace.
            See
            <see cref="M:iText.IO.Source.PdfTokenizer.IsWhitespace(System.Int32)">isWhiteSpace(int)</see>
            or
            <see cref="M:iText.IO.Source.PdfTokenizer.IsWhitespace(System.Int32,System.Boolean)">isWhiteSpace(int, boolean)</see>
            for a list of whitespace characters.
            <br />
            The same as calling
            <see cref="M:iText.IO.Source.PdfTokenizer.ReadLineSegment(iText.IO.Source.ByteBuffer,System.Boolean)">readLineSegment(input, true)</see>.
            </remarks>
            <param name="buffer">
            a
            <see cref="T:iText.IO.Source.ByteBuffer"/>
            to which the result of reading will be saved
            </param>
            <returns>true, if something was read or if the end of the input stream is not reached</returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.ReadLineSegment(iText.IO.Source.ByteBuffer,System.Boolean)">
            <summary>Reads data into the provided byte[].</summary>
            <remarks>
            Reads data into the provided byte[]. Checks on leading whitespace.
            See
            <see cref="M:iText.IO.Source.PdfTokenizer.IsWhitespace(System.Int32)">isWhiteSpace(int)</see>
            or
            <see cref="M:iText.IO.Source.PdfTokenizer.IsWhitespace(System.Int32,System.Boolean)">isWhiteSpace(int, boolean)</see>
            for a list of whitespace characters.
            </remarks>
            <param name="buffer">
            a
            <see cref="T:iText.IO.Source.ByteBuffer"/>
            to which the result of reading will be saved
            </param>
            <param name="isNullWhitespace">
            boolean to indicate whether '0' is whitespace or not.
            If in doubt, use true or overloaded method
            <see cref="M:iText.IO.Source.PdfTokenizer.ReadLineSegment(iText.IO.Source.ByteBuffer)">readLineSegment(input)</see>
            </param>
            <returns>true, if something was read or if the end of the input stream is not reached</returns>
        </member>
        <member name="M:iText.IO.Source.PdfTokenizer.CheckObjectStart(iText.IO.Source.PdfTokenizer)">
            <summary>Check whether line starts with object declaration.</summary>
            <param name="lineTokenizer">tokenizer, built by single line.</param>
            <returns>object number and generation if check is successful, otherwise - null.</returns>
        </member>
        <member name="T:iText.IO.Source.RAFRandomAccessSource">
            <summary>
            A RandomAccessSource that uses a
            <see cref="T:System.IO.FileStream"/>
            as it's source
            Note: Unlike most of the RandomAccessSource implementations, this class is not thread safe
            </summary>
        </member>
        <member name="F:iText.IO.Source.RAFRandomAccessSource.raf">
            <summary>The source</summary>
        </member>
        <member name="F:iText.IO.Source.RAFRandomAccessSource.length">
            <summary>The length of the underling RAF.</summary>
            <remarks>
            The length of the underling RAF.  Note that the length is cached at construction time to avoid the possibility
            of
            <see cref="T:System.IO.IOException"/>
            s when reading the length.
            </remarks>
        </member>
        <member name="M:iText.IO.Source.RAFRandomAccessSource.#ctor(System.IO.FileStream)">
            <summary>Creates this object</summary>
            <param name="raf">the source for this RandomAccessSource</param>
        </member>
        <member name="M:iText.IO.Source.RAFRandomAccessSource.Get(System.Int64)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RAFRandomAccessSource.Get(System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RAFRandomAccessSource.Length">
            <summary>
            <inheritDoc/>
            Note: the length is determined when the
            <see cref="T:iText.IO.Source.RAFRandomAccessSource"/>
            is constructed.
            </summary>
            <remarks>
            <inheritDoc/>
            Note: the length is determined when the
            <see cref="T:iText.IO.Source.RAFRandomAccessSource"/>
            is constructed.  If the file length changes
            after construction, that change will not be reflected in this call.
            </remarks>
        </member>
        <member name="M:iText.IO.Source.RAFRandomAccessSource.Close">
            <summary>Closes the underlying RandomAccessFile</summary>
        </member>
        <member name="T:iText.IO.Source.RandomAccessFileOrArray">
            <summary>Class that is used to unify reading from random access files and arrays.</summary>
        </member>
        <member name="F:iText.IO.Source.RandomAccessFileOrArray.byteSource">
            <summary>The source that backs this object</summary>
        </member>
        <member name="F:iText.IO.Source.RandomAccessFileOrArray.byteSourcePosition">
            <summary>The physical location in the underlying byte source.</summary>
        </member>
        <member name="F:iText.IO.Source.RandomAccessFileOrArray.back">
            <summary>the pushed  back byte, if any</summary>
        </member>
        <member name="F:iText.IO.Source.RandomAccessFileOrArray.isBack">
            <summary>Whether there is a pushed back byte</summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.#ctor(iText.IO.Source.IRandomAccessSource)">
            <summary>Creates a RandomAccessFileOrArray that wraps the specified byte source.</summary>
            <remarks>
            Creates a RandomAccessFileOrArray that wraps the specified byte source.  The byte source will be closed when
            this RandomAccessFileOrArray is closed.
            </remarks>
            <param name="byteSource">the byte source to wrap</param>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.CreateView">
            <summary>Creates an independent view of this object (with it's own file pointer and push back queue).</summary>
            <remarks>
            Creates an independent view of this object (with it's own file pointer and push back queue).  Closing the new object will not close this object.
            Closing this object will have adverse effect on the view.
            </remarks>
            <returns>the new view</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.CreateSourceView">
            <summary>Creates the view of the byte source of this object.</summary>
            <remarks>
            Creates the view of the byte source of this object. Closing the view won't affect this object.
            Closing source will have adverse effect on the view.
            </remarks>
            <returns>the byte source view.</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.PushBack(System.Byte)">
            <summary>Pushes a byte back.</summary>
            <remarks>Pushes a byte back.  The next get() will return this byte instead of the value from the underlying data source
                </remarks>
            <param name="b">the byte to push</param>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.Read">
            <summary>Reads a single byte</summary>
            <returns>the byte, or -1 if EOF is reached</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.Peek">
            <summary>Gets the next byte without moving current position.</summary>
            <returns>the next byte, or -1 if EOF is reached</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.Peek(System.Byte[])">
            <summary>
            Gets the next
            <c>buffer.length</c>
            bytes without moving current position.
            </summary>
            <param name="buffer">buffer to store read bytes</param>
            <returns>
            the number of read bytes. If it is less than
            <c>buffer.length</c>
            it means EOF has been reached.
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>Reads the specified amount of bytes to the buffer applying the offset.</summary>
            <param name="b">destination buffer</param>
            <param name="off">offset at which to start storing characters</param>
            <param name="len">maximum number of characters to read</param>
            <returns>the number of bytes actually read or -1 in case of EOF</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.Read(System.Byte[])">
            <summary>Reads the bytes to the buffer.</summary>
            <remarks>Reads the bytes to the buffer. This method will try to read as many bytes as the buffer can hold.
                </remarks>
            <param name="b">the destination buffer</param>
            <returns>the number of bytes actually read</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadFully(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadFully(System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.Skip(System.Int64)">
            <summary>Make an attempt to skip the specified amount of bytes in source.</summary>
            <remarks>
            Make an attempt to skip the specified amount of bytes in source.
            However it may skip less amount of bytes. Possibly zero.
            </remarks>
            <param name="n">the number of bytes to skip</param>
            <returns>the actual number of bytes skipped</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.SkipBytes(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.Close">
            <summary>Closes the underlying source.</summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.Length">
            <summary>Gets the total amount of bytes in the source.</summary>
            <returns>source's size.</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.Seek(System.Int64)">
            <summary>Sets the current position in the source to the specified index.</summary>
            <param name="pos">the position to set</param>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.GetPosition">
            <summary>Gets the current position of the source considering the pushed byte to the source.</summary>
            <returns>
            the index of last read byte in the source in
            or the index of last read byte in source - 1 in case byte was pushed.
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadBoolean">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadByte">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadUnsignedByte">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadShort">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadShortLE">
            <summary>Reads a signed 16-bit number from this stream in little-endian order.</summary>
            <remarks>
            Reads a signed 16-bit number from this stream in little-endian order.
            The method reads two
            bytes from this stream, starting at the current stream pointer.
            If the two bytes read, in order, are
            <c>b1</c>
            and
            <c>b2</c>
            , where each of the two values is
            between
            <c>0</c>
            and
            <c>255</c>
            , inclusive, then the
            result is equal to:
            <blockquote><pre>
            (short)((b2 &lt;&lt; 8) | b1)
            </pre></blockquote>
            <para />
            This method blocks until the two bytes are read, the end of the
            stream is detected, or an exception is thrown.
            </remarks>
            <returns>
            the next two bytes of this stream, interpreted as a signed
            16-bit number.
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadUnsignedShort">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadUnsignedShortLE">
            <summary>Reads an unsigned 16-bit number from this stream in little-endian order.</summary>
            <remarks>
            Reads an unsigned 16-bit number from this stream in little-endian order.
            This method reads
            two bytes from the stream, starting at the current stream pointer.
            If the bytes read, in order, are
            <c>b1</c>
            and
            <c>b2</c>
            , where
            <c>0 &lt;= b1, b2 &lt;= 255</c>
            ,
            then the result is equal to:
            <blockquote><pre>
            (b2 &lt;&lt; 8) | b1
            </pre></blockquote>
            <para />
            This method blocks until the two bytes are read, the end of the
            stream is detected, or an exception is thrown.
            </remarks>
            <returns>
            the next two bytes of this stream, interpreted as an
            unsigned 16-bit integer.
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadChar">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadCharLE">
            <summary>Reads a Unicode character from this stream in little-endian order.</summary>
            <remarks>
            Reads a Unicode character from this stream in little-endian order.
            This method reads two
            bytes from the stream, starting at the current stream pointer.
            If the bytes read, in order, are
            <c>b1</c>
            and
            <c>b2</c>
            , where
            <c>0 &lt;= b1, b2 &lt;= 255</c>
            ,
            then the result is equal to:
            <blockquote><pre>
            (char)((b2 &lt;&lt; 8) | b1)
            </pre></blockquote>
            <para />
            This method blocks until the two bytes are read, the end of the
            stream is detected, or an exception is thrown.
            </remarks>
            <returns>the next two bytes of this stream as a Unicode character.</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadInt">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadIntLE">
            <summary>Reads a signed 32-bit integer from this stream in little-endian order.</summary>
            <remarks>
            Reads a signed 32-bit integer from this stream in little-endian order.
            This method reads 4
            bytes from the stream, starting at the current stream pointer.
            If the bytes read, in order, are
            <c>b1</c>
            ,
            <c>b2</c>
            ,
            <c>b3</c>
            , and
            <c>b4</c>
            , where
            <c>0 &lt;= b1, b2, b3, b4 &lt;= 255</c>
            ,
            then the result is equal to:
            <blockquote><pre>
            (b4 &lt;&lt; 24) | (b3 &lt;&lt; 16) + (b2 &lt;&lt; 8) + b1
            </pre></blockquote>
            <para />
            This method blocks until the four bytes are read, the end of the
            stream is detected, or an exception is thrown.
            </remarks>
            <returns>
            the next four bytes of this stream, interpreted as an
            <c>int</c>.
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadUnsignedInt">
            <summary>Reads an unsigned 32-bit integer from this stream.</summary>
            <remarks>
            Reads an unsigned 32-bit integer from this stream. This method reads 4
            bytes from the stream, starting at the current stream pointer.
            If the bytes read, in order, are
            <c>b1</c>
            ,
            <c>b2</c>
            ,
            <c>b3</c>
            , and
            <c>b4</c>
            , where
            <c>0 &lt;= b1, b2, b3, b4 &lt;= 255</c>
            ,
            then the result is equal to:
            <blockquote><pre>
            (b1 &lt;&lt; 24) | (b2 &lt;&lt; 16) + (b3 &lt;&lt; 8) + b4
            </pre></blockquote>
            <para />
            This method blocks until the four bytes are read, the end of the
            stream is detected, or an exception is thrown.
            </remarks>
            <returns>
            the next four bytes of this stream, interpreted as a
            <c>long</c>.
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadLong">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadFloat">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadDouble">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadLine">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessFileOrArray.ReadString(System.Int32,System.String)">
            <summary>
            Reads a
            <c>String</c>
            from the font file as bytes using the given encoding.
            </summary>
            <param name="length">the length of bytes to read</param>
            <param name="encoding">the given encoding</param>
            <returns>
            the
            <c>String</c>
            read
            </returns>
        </member>
        <member name="T:iText.IO.Source.RandomAccessSourceFactory">
            <summary>
            Factory to create
            <see cref="!:RandomAccessSource"/>
            objects based on various types of sources
            </summary>
        </member>
        <member name="F:iText.IO.Source.RandomAccessSourceFactory.forceReadDefaultValue">
            <summary>The default value for the forceRead flag
            	</summary>
        </member>
        <member name="F:iText.IO.Source.RandomAccessSourceFactory.forceRead">
            <summary>Whether the full content of the source should be read into memory at construction
            	</summary>
        </member>
        <member name="F:iText.IO.Source.RandomAccessSourceFactory.exclusivelyLockFile">
            <summary>Whether the underlying file should have a RW lock on it or just an R lock
            	</summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.#ctor">
            <summary>Creates a factory that will give preference to accessing the underling data source using memory mapped files
            	</summary>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.SetForceReadDefaultValue(System.Boolean)">
            <summary>Determines the default value for the forceRead flag
                </summary>
            <param name="forceRead">true if by default the full content will be read, false otherwise</param>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.SetForceRead(System.Boolean)">
            <summary>Determines whether the full content of the source will be read into memory
            	</summary>
            <param name="forceRead">true if the full content will be read, false otherwise</param>
            <returns>this object (this allows chaining of method calls)</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.SetUsePlainRandomAccess(System.Boolean)">
            <summary>
            Determines whether
            <see cref="T:System.IO.FileStream"/>
            should be used as the primary data access mechanism
            </summary>
            <param name="usePlainRandomAccess">
            whether
            <see cref="T:System.IO.FileStream"/>
            should be used as the primary data access mechanism
            </param>
            <returns>this object (this allows chaining of method calls)</returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.CreateSource(System.Byte[])">
            <summary>
            Creates a
            <see cref="!:RandomAccessSource"/>
            based on a byte array
            </summary>
            <param name="data">the byte array</param>
            <returns>
            the newly created
            <see cref="!:RandomAccessSource"/>
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.CreateSource(System.Uri)">
            <summary>
            Creates a
            <see cref="!:RandomAccessSource"/>
            based on a URL.  The data available at the URL is read into memory and used
            as the source for the
            <see cref="!:RandomAccessSource"/>
            </summary>
            <param name="url">the url to read from</param>
            <returns>
            the newly created
            <see cref="!:RandomAccessSource"/>
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.ExtractOrCreateSource(System.IO.Stream)">
            <summary>
            Creates or extracts a
            <see cref="!:RandomAccessSource"/>
            based on a
            <see cref="T:System.IO.Stream"/>
            <para/>
            If the InputStream is an instance of
            <see cref="T:iText.IO.Source.RASInputStream"/>
            then extracts the source from it.
            Otherwise The full content of the InputStream is read into memory and used
            as the source for the
            <see cref="!:RandomAccessSource"/>
            </summary>
            <param name="inputStream">the stream to read from</param>
            <returns>
            the newly created or extracted
            <see cref="!:RandomAccessSource"/>
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.CreateSource(System.IO.Stream)">
            <summary>
            Creates a
            <see cref="!:RandomAccessSource"/>
            based on an
            <see cref="T:System.IO.Stream"/>
            <para />
            The full content of the InputStream is read into memory and used
            as the source for the
            <see cref="!:RandomAccessSource"/>
            </summary>
            <param name="inputStream">the stream to read from</param>
            <returns>
            the newly created
            <see cref="!:RandomAccessSource"/>
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.CreateBestSource(System.String)">
            <summary>
            Creates a
            <see cref="!:RandomAccessSource"/>
            based on a filename string.
            If the filename describes a URL, a URL based source is created
            If the filename describes a file on disk, the contents may be read into memory (if
            <c>forceRead</c>
            is true),
            opened using memory mapped file channel (if usePlainRandomAccess is false), or
            opened using
            <see cref="T:System.IO.FileStream"/>
            access (if usePlainRandomAccess is true)
            This call will automatically fail over to using
            <see cref="T:System.IO.FileStream"/>
            if the memory map operation fails
            </summary>
            <param name="filename">
            the name of the file or resource to create the
            <see cref="!:RandomAccessSource"/>
            for
            </param>
            <returns>
            the newly created
            <see cref="!:RandomAccessSource"/>
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.CreateByReadingToMemory(System.String)">
            <summary>
            Creates a new
            <see cref="!:RandomAccessSource"/>
            by reading the specified file/resource into memory
            </summary>
            <param name="filename">the name of the resource to read</param>
            <returns>
            the newly created
            <see cref="!:RandomAccessSource"/>
            </returns>
        </member>
        <member name="M:iText.IO.Source.RandomAccessSourceFactory.CreateByReadingToMemory(System.IO.Stream)">
            <summary>
            Creates a new
            <see cref="!:RandomAccessSource"/>
            by reading the specified file/resource into memory
            </summary>
            <param name="stream">the name of the resource to read</param>
            <returns>
            the newly created
            <see cref="!:RandomAccessSource"/>
            </returns>
        </member>
        <member name="T:iText.IO.Source.RASInputStream">
            <summary>
            An input stream that uses a
            <see cref="!:RandomAccessSource"/>
            as
            its underlying source.
            </summary>
        </member>
        <member name="F:iText.IO.Source.RASInputStream.source">
            <summary>The source.</summary>
        </member>
        <member name="F:iText.IO.Source.RASInputStream.position">
            <summary>The current position in the source.</summary>
        </member>
        <member name="M:iText.IO.Source.RASInputStream.#ctor(iText.IO.Source.IRandomAccessSource)">
            <summary>Creates an input stream based on the source.</summary>
            <param name="source">The source.</param>
        </member>
        <member name="M:iText.IO.Source.RASInputStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.IO.Source.RASInputStream.ReadByte">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.IO.Source.WindowRandomAccessSource">
            <summary>
            A RandomAccessSource that wraps another RandomAccessSource and provides a window of it at a specific offset and over
            a specific length.
            </summary>
            <remarks>
            A RandomAccessSource that wraps another RandomAccessSource and provides a window of it at a specific offset and over
            a specific length.  Position 0 becomes the offset position in the underlying source.
            </remarks>
        </member>
        <member name="F:iText.IO.Source.WindowRandomAccessSource.source">
            <summary>The source</summary>
        </member>
        <member name="F:iText.IO.Source.WindowRandomAccessSource.offset">
            <summary>The amount to offset the source by</summary>
        </member>
        <member name="F:iText.IO.Source.WindowRandomAccessSource.length">
            <summary>The length</summary>
        </member>
        <member name="M:iText.IO.Source.WindowRandomAccessSource.#ctor(iText.IO.Source.IRandomAccessSource,System.Int64)">
            <summary>Constructs a new OffsetRandomAccessSource that extends to the end of the underlying source</summary>
            <param name="source">the source</param>
            <param name="offset">the amount of the offset to use</param>
        </member>
        <member name="M:iText.IO.Source.WindowRandomAccessSource.#ctor(iText.IO.Source.IRandomAccessSource,System.Int64,System.Int64)">
            <summary>Constructs a new OffsetRandomAccessSource with an explicit length</summary>
            <param name="source">the source</param>
            <param name="offset">the amount of the offset to use</param>
            <param name="length">the number of bytes to be included in this RAS</param>
        </member>
        <member name="M:iText.IO.Source.WindowRandomAccessSource.Get(System.Int64)">
            <summary>
            <inheritDoc/>
            Note that the position will be adjusted to read from the corrected location in the underlying source
            </summary>
        </member>
        <member name="M:iText.IO.Source.WindowRandomAccessSource.Get(System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
            <inheritDoc/>
            Note that the position will be adjusted to read from the corrected location in the underlying source
            </summary>
        </member>
        <member name="M:iText.IO.Source.WindowRandomAccessSource.Length">
            <summary>
            <inheritDoc/>
            Note that the length will be adjusted to read from the corrected location in the underlying source
            </summary>
        </member>
        <member name="M:iText.IO.Source.WindowRandomAccessSource.Close">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.IO.Util.ArrayUtil">
            <summary>This file is a helper class for internal usage only.</summary>
            <remarks>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in the future.
            </remarks>
        </member>
        <member name="M:iText.IO.Util.ArrayUtil.ShortenArray(System.Byte[],System.Int32)">
            <summary>Shortens byte array.</summary>
            <param name="src">the byte array</param>
            <param name="length">the new length of bytes array</param>
            <returns>the shortened byte array</returns>
        </member>
        <member name="M:iText.IO.Util.ArrayUtil.ToIntArray(System.Collections.Generic.ICollection{System.Int32})">
            <summary>Converts a collection to an int array.</summary>
            <param name="collection">the collection</param>
            <returns>the int array</returns>
        </member>
        <member name="M:iText.IO.Util.ArrayUtil.HashCode(System.Byte[])">
            <summary>Creates a hash of the given byte array.</summary>
            <param name="a">the byte array</param>
            <returns>the byte array</returns>
        </member>
        <member name="M:iText.IO.Util.ArrayUtil.FillWithValue(System.Int32[],System.Int32)">
            <summary>Fills an array with the given value.</summary>
            <param name="a">the int array</param>
            <param name="value">the number of a value</param>
            <returns>the int array</returns>
        </member>
        <member name="M:iText.IO.Util.ArrayUtil.FillWithValue(System.Single[],System.Single)">
            <summary>Fills an array with the given value.</summary>
            <param name="a">the float array</param>
            <param name="value">the number of a value</param>
            <returns>the float array</returns>
        </member>
        <member name="M:iText.IO.Util.ArrayUtil.FillWithValue``1(``0[],``0)">
            <summary>Fills an array with the given value.</summary>
            <param name="a">the array</param>
            <param name="value">the value of type</param>
            <typeparam name="T">the type of the implementation</typeparam>
        </member>
        <member name="M:iText.IO.Util.ArrayUtil.CloneArray(System.Int32[])">
            <summary>Clones int array.</summary>
            <param name="src">the int array</param>
            <returns>the int array</returns>
        </member>
        <member name="M:iText.IO.Util.ArrayUtil.IndexOf(System.Object[],System.Object)">
            <summary>Gets the index of object.</summary>
            <param name="a">the object array</param>
            <param name="key">the object key</param>
            <returns>the index of object</returns>
        </member>
        <member name="T:iText.IO.Util.AssemblyLoadContextUtil">
            <summary>This file is a helper class for internal usage only.</summary>
            <remarks>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            <br/>
            <br/>
            Ussage of this class may throw TypeInitializationException
            in .NET Standard enviroments that doesn't support System.Runtime.Loader
            </remarks>
        </member>
        <member name="M:iText.IO.Util.CliCommandUtil.IsVersionCommandExecutable(System.String,System.String)">
            <summary>
            Checks if the command, passed as parameter, is executable and the output version text contains
            expected text
            </summary>
            <param name="command">a string command to execute</param>
            <param name="versionText">an expected version text line</param>
            <returns>
            boolean result of checking: true - the required command is executable and the output version
            text is correct
            </returns>
        </member>
        <member name="T:iText.IO.Util.DecimalFormatUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="T:iText.IO.Util.EnumUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="T:iText.IO.Util.FilterUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="M:iText.IO.Util.FilterUtil.FlateDecode(System.Byte[],System.Boolean)">
            <summary>A helper to FlateDecode.</summary>
            <param name="input">the input data</param>
            <param name="strict">
            <CODE>true</CODE> to read a correct stream. <CODE>false</CODE>
            to try to read a corrupted stream
            </param>
            <returns>the decoded data</returns>
        </member>
        <member name="M:iText.IO.Util.FilterUtil.FlateDecode(System.Byte[])">
            <summary>Decodes a stream that has the FlateDecode filter.</summary>
            <param name="input">the input data</param>
            <returns>the decoded data</returns>
        </member>
        <member name="M:iText.IO.Util.FilterUtil.InflateData(System.Byte[],System.Byte[])">
            <summary>
            This method provides support for general purpose decompression using the
            popular ZLIB compression library.
            </summary>
            <param name="deflated">the input data bytes</param>
            <param name="inflated">the buffer for the uncompressed data</param>
        </member>
        <member name="T:iText.IO.Util.GhostscriptHelper">
            <summary>A utility class that is used as an interface to run 3rd-party tool Ghostscript.</summary>
            <remarks>
            A utility class that is used as an interface to run 3rd-party tool Ghostscript.
            Ghostscript is an interpreter for the PostScript language and PDF files, it allows to render them
            as images.
            <para />
            The Ghostscript needs to be installed independently on the system. This class provides a convenient
            way to run it by passing a terminal command. The command can either be specified explicitly or by a mean
            of environment variable
            <see cref="F:iText.IO.Util.GhostscriptHelper.GHOSTSCRIPT_ENVIRONMENT_VARIABLE"/>.
            </remarks>
        </member>
        <member name="F:iText.IO.Util.GhostscriptHelper.GHOSTSCRIPT_ENVIRONMENT_VARIABLE">
            <summary>The name of the environment variable with the command to execute Ghostscript operations.</summary>
        </member>
        <member name="M:iText.IO.Util.GhostscriptHelper.#ctor">
            <summary>
            Creates new instance that will rely on Ghostscript execution command defined by
            <see cref="F:iText.IO.Util.GhostscriptHelper.GHOSTSCRIPT_ENVIRONMENT_VARIABLE"/>
            environment variable.
            </summary>
        </member>
        <member name="M:iText.IO.Util.GhostscriptHelper.#ctor(System.String)">
            <summary>Creates new instance that will rely on Ghostscript execution command defined as passed argument.</summary>
            <param name="newGsExec">the Ghostscript execution command; if null - environment variables will be used instead
                </param>
        </member>
        <member name="M:iText.IO.Util.GhostscriptHelper.GetCliExecutionCommand">
            <summary>Returns a command that is used to run the utility.</summary>
            <remarks>
            Returns a command that is used to run the utility.
            This command doesn't contain command parameters. Parameters are added on specific
            methods invocation.
            </remarks>
            <returns>a string command</returns>
        </member>
        <member name="M:iText.IO.Util.GhostscriptHelper.RunGhostScriptImageGeneration(System.String,System.String,System.String)">
            <summary>Runs Ghostscript to render the PDF's pages as PNG images.</summary>
            <remarks>
            Runs Ghostscript to render the PDF's pages as PNG images.
            <para />
            Note, that this method  may create temporary directory and files.
            </remarks>
            <param name="pdf">Path to the PDF file to be rendered</param>
            <param name="outDir">Path to the output directory, in which the rendered pages will be stored</param>
            <param name="image">
            String which defines the name of the resultant images. This string will be
            concatenated with the number of the rendered page from the start of the
            PDF in "-%03d" format, e.g. "-011" for the eleventh rendered page and so on.
            This number may not correspond to the actual page number: for example,
            if the passed pageList equals to "5,3", then images with postfixes "-001.png"
            and "-002.png" will be created: the former for the third page, the latter
            for the fifth page. "%" sign in the passed name is prohibited.
            </param>
        </member>
        <member name="M:iText.IO.Util.GhostscriptHelper.RunGhostScriptImageGeneration(System.String,System.String,System.String,System.String)">
            <summary>Runs Ghostscript to render the PDF's pages as PNG images.</summary>
            <remarks>
            Runs Ghostscript to render the PDF's pages as PNG images.
            <para />
            Note, that this method  may create temporary directory and files.
            </remarks>
            <param name="pdf">Path to the PDF file to be rendered</param>
            <param name="outDir">Path to the output directory, in which the rendered pages will be stored</param>
            <param name="image">
            String which defines the name of the resultant images. This string will be
            concatenated with the number of the rendered page from the start of the
            PDF in "-%03d" format, e.g. "-011" for the eleventh rendered page and so on.
            This number may not correspond to the actual page number: for example,
            if the passed pageList equals to "5,3", then images with postfixes "-001.png"
            and "-002.png" will be created: the former for the third page, the latter
            for the fifth page. "%" sign in the passed name is prohibited.
            </param>
            <param name="pageList">
            String with numbers of the required pages to be rendered as images.
            This string should be formatted as a string with numbers, separated by commas,
            without whitespaces. Can be null, if it is required to render all the PDF's pages.
            </param>
        </member>
        <member name="T:iText.IO.Util.GhostscriptHelper.GhostscriptExecutionException">
            <summary>
            Exceptions thrown when errors occur during generation and comparison of images obtained on the basis of pdf
            files.
            </summary>
        </member>
        <member name="M:iText.IO.Util.GhostscriptHelper.GhostscriptExecutionException.#ctor(System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.IO.Util.GhostscriptHelper.GhostscriptExecutionException"/>.
            </summary>
            <param name="msg">the detail message.</param>
        </member>
        <member name="T:iText.IO.Util.HashCode">
            <summary>
            This class is a convenience method to sequentially calculate hash code of the
            object based on the field values.
            </summary>
            <remarks>
            This class is a convenience method to sequentially calculate hash code of the
            object based on the field values. The result depends on the order of elements
            appended. The exact formula is the same as for
            <see cref="!:System.Collections.IList&lt;E&gt;.GetHashCode()"/>.
            If you need order independent hash code just summate, multiply or XOR all
            elements.
            <para />
            Suppose we have class:
            <pre><c>
            class Thing {
            long id;
            String name;
            float weight;
            }
            </c></pre>
            The hash code calculation can be expressed in 2 forms.
            <para />
            For maximum performance:
            <pre><c>
            public int hashCode() {
            int hashCode = HashCode.EMPTY_HASH_CODE;
            hashCode = HashCode.combine(hashCode, id);
            hashCode = HashCode.combine(hashCode, name);
            hashCode = HashCode.combine(hashCode, weight);
            return hashCode;
            }
            </c></pre>
            <para />
            For convenience:
            <pre><c>
            public int hashCode() {
            return new HashCode().append(id).append(name).append(weight).hashCode();
            }
            </c></pre>
            </remarks>
            <seealso cref="!:System.Collections.IList&lt;E&gt;.GetHashCode()"/>
        </member>
        <member name="F:iText.IO.Util.HashCode.EMPTY_HASH_CODE">
            <summary>The hashCode value before any data is appended, equals to 1.</summary>
            <seealso cref="!:System.Collections.IList&lt;E&gt;.GetHashCode()"/>
        </member>
        <member name="M:iText.IO.Util.HashCode.GetHashCode">
            <summary>Returns accumulated hashCode</summary>
        </member>
        <member name="M:iText.IO.Util.HashCode.Combine(System.Int32,System.Boolean)">
            <summary>Combines hashCode of previous elements sequence and value's hashCode.</summary>
            <param name="hashCode">previous hashCode value</param>
            <param name="value">new element</param>
            <returns>combined hashCode</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Combine(System.Int32,System.Int64)">
            <summary>Combines hashCode of previous elements sequence and value's hashCode.</summary>
            <param name="hashCode">previous hashCode value</param>
            <param name="value">new element</param>
            <returns>combined hashCode</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Combine(System.Int32,System.Single)">
            <summary>Combines hashCode of previous elements sequence and value's hashCode.</summary>
            <param name="hashCode">previous hashCode value</param>
            <param name="value">new element</param>
            <returns>combined hashCode</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Combine(System.Int32,System.Double)">
            <summary>Combines hashCode of previous elements sequence and value's hashCode.</summary>
            <param name="hashCode">previous hashCode value</param>
            <param name="value">new element</param>
            <returns>combined hashCode</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Combine(System.Int32,System.Object)">
            <summary>Combines hashCode of previous elements sequence and value's hashCode.</summary>
            <param name="hashCode">previous hashCode value</param>
            <param name="value">new element</param>
            <returns>combined hashCode</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Combine(System.Int32,System.Int32)">
            <summary>Combines hashCode of previous elements sequence and value's hashCode.</summary>
            <param name="hashCode">previous hashCode value</param>
            <param name="value">new element</param>
            <returns>combined hashCode</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Append(System.Int32)">
            <summary>Appends value's hashCode to the current hashCode.</summary>
            <param name="value">new element</param>
            <returns>this</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Append(System.Int64)">
            <summary>Appends value's hashCode to the current hashCode.</summary>
            <param name="value">new element</param>
            <returns>this</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Append(System.Single)">
            <summary>Appends value's hashCode to the current hashCode.</summary>
            <param name="value">new element</param>
            <returns>this</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Append(System.Double)">
            <summary>Appends value's hashCode to the current hashCode.</summary>
            <param name="value">new element</param>
            <returns>this</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Append(System.Boolean)">
            <summary>Appends value's hashCode to the current hashCode.</summary>
            <param name="value">new element</param>
            <returns>this</returns>
        </member>
        <member name="M:iText.IO.Util.HashCode.Append(System.Object)">
            <summary>Appends value's hashCode to the current hashCode.</summary>
            <param name="value">new element</param>
            <returns>this</returns>
        </member>
        <member name="F:iText.IO.Util.ImageHandlerUtil.GHOSTSCRIPT_ENVIRONMENT_VARIABLE">
            <summary>The name of the environment variable with the command to execute Ghostscript operations.</summary>
        </member>
        <member name="F:iText.IO.Util.ImageHandlerUtil.MAGICK_COMPARE_ENVIRONMENT_VARIABLE">
            <summary>The name of the environment variable with the command to execute ImageMagic comparison operations.
                </summary>
        </member>
        <member name="M:iText.IO.Util.ImageHandlerUtil.RunGhostScriptImageGeneration(System.String,System.String,System.String)">
            <summary>Runs ghostscript to create images of pdfs.</summary>
            <param name="pdf">Path to the pdf file.</param>
            <param name="outDir">Path to the output directory</param>
            <param name="image">Path to the generated image</param>
        </member>
        <member name="M:iText.IO.Util.ImageHandlerUtil.RunGhostScriptImageGeneration(System.String,System.String,System.String,System.String)">
            <summary>Runs ghostscript to create images of specified pages of pdfs.</summary>
            <param name="pdf">Path to the pdf file.</param>
            <param name="outDir">Path to the output directory</param>
            <param name="image">Path to the generated image</param>
            <param name="pageNumber">
            Number of the required page of pdf to extract as image. Can be null,
            if it is required to extract all pages as images
            </param>
        </member>
        <member name="M:iText.IO.Util.ImageHandlerUtil.RunImageMagickImageCompare(System.String,System.String,System.String)">
            <summary>Runs imageMagick to visually compare images and generate difference output.</summary>
            <param name="outImageFilePath">Path to the output image file</param>
            <param name="cmpImageFilePath">Path to the cmp image file</param>
            <param name="diffImageName">Path to the difference output image file</param>
            <returns>boolean result of comparing: true - images are visually equal</returns>
        </member>
        <member name="M:iText.IO.Util.ImageHandlerUtil.RunImageMagickImageCompare(System.String,System.String,System.String,System.String)">
            <summary>Runs imageMagick to visually compare images with the specified fuzziness value and generate difference output.
                </summary>
            <param name="outImageFilePath">Path to the output image file</param>
            <param name="cmpImageFilePath">Path to the cmp image file</param>
            <param name="diffImageName">Path to the difference output image file</param>
            <param name="fuzzValue">fuzziness value to compare images. Can be null, if it is not required to use fuzziness
                </param>
            <returns>boolean result of comparing: true - images are visually equal</returns>
        </member>
        <member name="M:iText.IO.Util.ImageHandlerUtil.IsVersionCommandExecutable(System.String)">
            <summary>
            Checks if the specified by input parameter tool's system variable is correctly specified
            and the specified tool can be executed.
            </summary>
            <param name="keyWord">the keyword specifying the tool (GhostScript or ImageMagick)</param>
            <returns>
            boolean result of checking: true - System variable is correctly specified
            and the specified tool can be executed
            </returns>
        </member>
        <member name="T:iText.IO.Util.ImageHandlerUtil.ImageHandlerExecutionException">
            <summary>
            Exceptions thrown when errors occur during generation and comparison of images obtained on the basis of pdf
            files.
            </summary>
        </member>
        <member name="M:iText.IO.Util.ImageHandlerUtil.ImageHandlerExecutionException.#ctor(iText.IO.Util.ImageHandlerUtil,System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.IO.Util.ImageHandlerUtil.ImageHandlerExecutionException"/>.
            </summary>
            <param name="msg">the detail message.</param>
        </member>
        <member name="T:iText.IO.Util.ImageMagickCompareResult">
            <summary>
            A helper data class, which aggregates true/false result of ImageMagick comparing
            as well as the number of different pixels.
            </summary>
        </member>
        <member name="M:iText.IO.Util.ImageMagickCompareResult.#ctor(System.Boolean,System.Int64)">
            <summary>Creates an instance that contains ImageMagick comparing result information.</summary>
            <param name="result">true, if the compared images are equal.</param>
            <param name="diffPixels">number of different pixels.</param>
        </member>
        <member name="M:iText.IO.Util.ImageMagickCompareResult.IsComparingResultSuccessful">
            <summary>Returns image compare boolean value.</summary>
            <returns>true if the compared images are equal.</returns>
        </member>
        <member name="M:iText.IO.Util.ImageMagickCompareResult.GetDiffPixels">
            <summary>Getter for a different pixels count.</summary>
            <returns>Returns a a different pixels count.</returns>
        </member>
        <member name="T:iText.IO.Util.ImageMagickHelper">
            <summary>A utility class that is used as an interface to run 3rd-party tool ImageMagick.</summary>
            <remarks>
            A utility class that is used as an interface to run 3rd-party tool ImageMagick.
            ImageMagick among other things allows to compare images and this class provides means to utilize this feature.
            <para />
            The ImageMagick needs to be installed independently on the system. This class provides a convenient
            way to run it by passing a terminal command. The command can either be specified explicitly or by a mean
            of environment variable
            <see cref="F:iText.IO.Util.ImageMagickHelper.MAGICK_COMPARE_ENVIRONMENT_VARIABLE"/>.
            </remarks>
        </member>
        <member name="F:iText.IO.Util.ImageMagickHelper.MAGICK_COMPARE_ENVIRONMENT_VARIABLE">
            <summary>The name of the environment variable with the command to execute ImageMagic comparison operations.
                </summary>
        </member>
        <member name="M:iText.IO.Util.ImageMagickHelper.#ctor">
            <summary>
            Creates new instance that will rely on ImageMagick execution command defined by
            <see cref="F:iText.IO.Util.ImageMagickHelper.MAGICK_COMPARE_ENVIRONMENT_VARIABLE"/>
            environment variable.
            </summary>
        </member>
        <member name="M:iText.IO.Util.ImageMagickHelper.#ctor(System.String)">
            <summary>Creates new instance that will rely on ImageMagick execution command defined as passed argument.</summary>
            <param name="newCompareExec">the ImageMagick execution command; if null - environment variables will be used instead
                </param>
        </member>
        <member name="M:iText.IO.Util.ImageMagickHelper.GetCliExecutionCommand">
            <summary>Returns a command that is used to run the utility.</summary>
            <remarks>
            Returns a command that is used to run the utility.
            This command doesn't contain command parameters. Parameters are added on specific
            methods invocation.
            </remarks>
            <returns>a string command</returns>
        </member>
        <member name="M:iText.IO.Util.ImageMagickHelper.RunImageMagickImageCompare(System.String,System.String,System.String)">
            <summary>Runs imageMagick to visually compare images and generate difference output.</summary>
            <remarks>
            Runs imageMagick to visually compare images and generate difference output.
            <para />
            Note, that this method may create temporary files.
            </remarks>
            <param name="outImageFilePath">Path to the output image file</param>
            <param name="cmpImageFilePath">Path to the cmp image file</param>
            <param name="diffImageName">Path to the difference output image file</param>
            <returns>boolean result of comparing: true - images are visually equal</returns>
        </member>
        <member name="M:iText.IO.Util.ImageMagickHelper.RunImageMagickImageCompare(System.String,System.String,System.String,System.String)">
            <summary>Runs imageMagick to visually compare images with the specified fuzziness value and generate difference output.
                </summary>
            <remarks>
            Runs imageMagick to visually compare images with the specified fuzziness value and generate difference output.
            <para />
            Note, that this method may create temporary files.
            </remarks>
            <param name="outImageFilePath">Path to the output image file</param>
            <param name="cmpImageFilePath">Path to the cmp image file</param>
            <param name="diffImageName">Path to the difference output image file</param>
            <param name="fuzzValue">
            String fuzziness value to compare images. Should be formatted as string with integer
            or decimal number. Can be null, if it is not required to use fuzziness
            </param>
            <returns>boolean result of comparing: true - images are visually equal</returns>
        </member>
        <member name="M:iText.IO.Util.ImageMagickHelper.RunImageMagickImageCompareWithThreshold(System.String,System.String,System.String,System.String,System.Int64)">
            <summary>
            Runs imageMagick to visually compare images with the specified fuzziness value and given threshold
            and generate difference output.
            </summary>
            <remarks>
            Runs imageMagick to visually compare images with the specified fuzziness value and given threshold
            and generate difference output.
            <para />
            Note, that this method may create temporary files.
            </remarks>
            <param name="outImageFilePath">Path to the output image file</param>
            <param name="cmpImageFilePath">Path to the cmp image file</param>
            <param name="diffImageName">Path to the difference output image file</param>
            <param name="fuzzValue">
            String fuzziness value to compare images. Should be formatted as string with integer
            or decimal number. Can be null, if it is not required to use fuzziness
            </param>
            <param name="threshold">Long value of accepted threshold.</param>
            <returns>boolean result of comparing: true - images are visually equal</returns>
        </member>
        <member name="M:iText.IO.Util.ImageMagickHelper.RunImageMagickImageCompareAndGetResult(System.String,System.String,System.String,System.String)">
            <summary>Runs imageMagick to visually compare images with the specified fuzziness value and generate difference output.
                </summary>
            <remarks>
            Runs imageMagick to visually compare images with the specified fuzziness value and generate difference output.
            This method returns an object of
            <see cref="T:iText.IO.Util.ImageMagickCompareResult"/>
            , containing comparing result information,
            such as boolean result value and the number of different pixels.
            <para />
            Note, that this method may create temporary files.
            </remarks>
            <param name="outImageFilePath">Path to the output image file</param>
            <param name="cmpImageFilePath">Path to the cmp image file</param>
            <param name="diffImageName">Path to the difference output image file</param>
            <param name="fuzzValue">
            String fuzziness value to compare images. Should be formatted as string with integer
            or decimal number. Can be null, if it is not required to use fuzziness
            </param>
            <returns>
            an object of
            <see cref="T:iText.IO.Util.ImageMagickCompareResult"/>
            . containing comparing result information.
            </returns>
        </member>
        <member name="T:iText.IO.Util.IntHashtable">
            <summary>A hash map that uses primitive ints for the key rather than objects.</summary>
            <remarks>
            A hash map that uses primitive ints for the key rather than objects.
            <para />
            Note that this class is for internal optimization purposes only, and may
            not be supported in future releases of Jakarta Commons Lang.  Utilities of
            this sort may be included in future releases of Jakarta Commons Collections.
            </remarks>
        </member>
        <member name="F:iText.IO.Util.IntHashtable.count">
            <summary>The total number of entries in the hash table.</summary>
        </member>
        <member name="F:iText.IO.Util.IntHashtable.table">
            <summary>The hash table data.</summary>
        </member>
        <member name="F:iText.IO.Util.IntHashtable.threshold">
            <summary>The table is rehashed when its size exceeds this threshold.</summary>
            <remarks>
            The table is rehashed when its size exceeds this threshold.  (The
            value of this field is (int)(capacity * loadFactor).)
            </remarks>
            <serial/>
        </member>
        <member name="F:iText.IO.Util.IntHashtable.loadFactor">
            <summary>The load factor for the hashtable.</summary>
            <serial/>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.#ctor">
            <summary>
            Constructs a new, empty hashtable with a default capacity and load
            factor, which is <c>20</c> and <c>0.75</c> respectively.
            </summary>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.#ctor(System.Int32)">
            <summary>
            Constructs a new, empty hashtable with the specified initial capacity
            and default load factor, which is <c>0.75</c>.
            </summary>
            <param name="initialCapacity">the initial capacity of the hashtable.</param>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.#ctor(System.Int32,System.Single)">
            <summary>
            Constructs a new, empty hashtable with the specified initial
            capacity and the specified load factor.
            </summary>
            <param name="initialCapacity">the initial capacity of the hashtable.</param>
            <param name="loadFactor">the load factor of the hashtable.</param>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.Size">
            <summary>Returns the number of keys in this hashtable.</summary>
            <returns>the number of keys in this hashtable.</returns>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.IsEmpty">
            <summary>Tests if this hashtable maps no keys to values.</summary>
            <returns>
            <c>true</c> if this hashtable maps no keys to values;
            <c>false</c> otherwise.
            </returns>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.Contains(System.Int32)">
            <summary>Tests if some key maps into the specified value in this hashtable.</summary>
            <remarks>
            Tests if some key maps into the specified value in this hashtable.
            This operation is more expensive than the <c>containsKey</c>
            method.
            <para />
            Note that this method is identical in functionality to containsValue,
            (which is part of the Map interface in the collections framework).
            </remarks>
            <param name="value">a value to search for.</param>
            <returns>
            <c>true</c> if and only if some key maps to the
            <c>value</c> argument in this hashtable as
            determined by the <tt>equals</tt> method;
            <c>false</c> otherwise.
            </returns>
            <seealso cref="M:iText.IO.Util.IntHashtable.ContainsKey(System.Int32)"/>
            <seealso cref="M:iText.IO.Util.IntHashtable.ContainsValue(System.Int32)"/>
            <seealso cref="!:System.Collections.IDictionary&lt;K, V&gt;"/>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.ContainsValue(System.Int32)">
            <summary>
            Returns <c>true</c> if this HashMap maps one or more keys
            to this value.
            </summary>
            <remarks>
            Returns <c>true</c> if this HashMap maps one or more keys
            to this value.
            <para />
            Note that this method is identical in functionality to contains
            (which predates the Map interface).
            </remarks>
            <param name="value">value whose presence in this HashMap is to be tested.</param>
            <returns>boolean <c>true</c> if the value is contained</returns>
            <seealso cref="!:System.Collections.IDictionary&lt;K, V&gt;"/>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.ContainsKey(System.Int32)">
            <summary>Tests if the specified int is a key in this hashtable.</summary>
            <param name="key">possible key.</param>
            <returns>
            <c>true</c> if and only if the specified int is a
            key in this hashtable, as determined by the <tt>equals</tt>
            method; <c>false</c> otherwise.
            </returns>
            <seealso cref="M:iText.IO.Util.IntHashtable.Contains(System.Int32)"/>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.Get(System.Int32)">
            <summary>Returns the value to which the specified key is mapped in this map.</summary>
            <param name="key">a key in the hashtable.</param>
            <returns>
            the value to which the key is mapped in this hashtable;
            0 if the key is not mapped to any value in
            this hashtable.
            </returns>
            <seealso cref="M:iText.IO.Util.IntHashtable.Put(System.Int32,System.Int32)"/>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.Rehash">
            <summary>
            Increases the capacity of and internally reorganizes this
            hashtable, in order to accommodate and access its entries more
            efficiently.
            </summary>
            <remarks>
            Increases the capacity of and internally reorganizes this
            hashtable, in order to accommodate and access its entries more
            efficiently.
            <para />
            This method is called automatically when the number of keys
            in the hashtable exceeds this hashtable's capacity and load
            factor.
            </remarks>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.Put(System.Int32,System.Int32)">
            <summary>
            Maps the specified <c>key</c> to the specified
            <c>value</c> in this hashtable.
            </summary>
            <remarks>
            Maps the specified <c>key</c> to the specified
            <c>value</c> in this hashtable. The key cannot be
            <c>null</c>.
            <para />
            The value can be retrieved by calling the <c>get</c> method
            with a key that is equal to the original key.
            </remarks>
            <param name="key">the hashtable key.</param>
            <param name="value">the value.</param>
            <returns>
            the previous value of the specified key in this hashtable,
            or <c>null</c> if it did not have one.
            </returns>
            <seealso cref="M:iText.IO.Util.IntHashtable.Get(System.Int32)"/>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.Remove(System.Int32)">
            <summary>
            Removes the key (and its corresponding value) from this
            hashtable.
            </summary>
            <remarks>
            Removes the key (and its corresponding value) from this
            hashtable.
            <para />
            This method does nothing if the key is not present in the
            hashtable.
            </remarks>
            <param name="key">the key that needs to be removed.</param>
            <returns>
            the value to which the key had been mapped in this hashtable,
            or <c>null</c> if the key did not have a mapping.
            </returns>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.Clear">
            <summary>Clears this hashtable so that it contains no keys.</summary>
        </member>
        <member name="T:iText.IO.Util.IntHashtable.Entry">
            <summary>
            Innerclass that acts as a datastructure to create a new entry in the
            table.
            </summary>
        </member>
        <member name="M:iText.IO.Util.IntHashtable.Entry.#ctor(System.Int32,System.Int32,iText.IO.Util.IntHashtable.Entry)">
            <summary>Create a new entry with the given values.</summary>
            <param name="key">The key used to enter this in the table</param>
            <param name="value">The value for this key</param>
            <param name="next">A reference to the next entry in the table</param>
        </member>
        <member name="T:iText.IO.Util.PdfNameUtil">
            <summary>This file is a helper class for internal usage only.</summary>
            <remarks>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </remarks>
        </member>
        <member name="T:iText.IO.Util.ResourceUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="M:iText.IO.Util.ResourceUtil.GetResourceStream(System.String)">
            <summary>Gets the resource's inputstream.</summary>
            <param name="key">the full name of the resource.</param>
            <returns>
            the
            <c>InputStream</c>
            to get the resource or
            <see langword="null"/>
            if not found.
            </returns>
        </member>
        <member name="T:iText.IO.Util.StreamUtil">
            <summary>This file is a helper class for internal usage only.</summary>
            <remarks>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </remarks>
        </member>
        <member name="M:iText.IO.Util.StreamUtil.Skip(System.IO.Stream,System.Int64)">
            <summary>
            This method is an alternative for the
            <c>InputStream.skip()</c>
            -method that doesn't seem to work properly for big values of
            <paramref name="size"/>.
            </summary>
            <param name="stream">
            the
            <c>InputStream</c>
            </param>
            <param name="size">the number of bytes to skip</param>
        </member>
        <member name="M:iText.IO.Util.StreamUtil.CreateEscapedString(System.Byte[])">
            <summary>
            Escapes a
            <c>byte</c>
            array according to the PDF conventions.
            </summary>
            <param name="bytes">
            the
            <c>byte</c>
            array to escape
            </param>
            <returns>
            an escaped
            <c>byte</c>
            array
            </returns>
        </member>
        <member name="M:iText.IO.Util.StreamUtil.WriteEscapedString(System.IO.Stream,System.Byte[])">
            <summary>
            Escapes a
            <c>byte</c>
            array according to the PDF conventions.
            </summary>
            <param name="outputStream">
            the
            <c>OutputStream</c>
            an escaped
            <c>byte</c>
            array write to.
            </param>
            <param name="bytes">
            the
            <c>byte</c>
            array to escape.
            </param>
        </member>
        <member name="M:iText.IO.Util.StreamUtil.InputStreamToArray(System.IO.Stream)">
            <summary>Reads the full content of a stream and returns them in a byte array</summary>
            <param name="stream">the stream to read</param>
            <returns>a byte array containing all of the bytes from the stream</returns>
        </member>
        <member name="M:iText.IO.Util.StreamUtil.CopyBytes(iText.IO.Source.IRandomAccessSource,System.Int64,System.Int64,System.IO.Stream)">
            <summary>
            Copy bytes from the
            <c>RandomAccessSource</c>
            to
            <c>OutputStream</c>.
            </summary>
            <param name="source">
            the
            <c>RandomAccessSource</c>
            copy from.
            </param>
            <param name="start">start position of source copy from.</param>
            <param name="length">length copy to.</param>
            <param name="output">
            the
            <c>OutputStream</c>
            copy to.
            </param>
        </member>
        <member name="M:iText.IO.Util.StreamUtil.ReadFully(System.IO.Stream,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads
            <paramref name="len"/>
            bytes from an input stream.
            </summary>
            <param name="input">the stream to read</param>
            <param name="b">the buffer into which the data is read.</param>
            <param name="off">an int specifying the offset into the data.</param>
            <param name="len">an int specifying the number of bytes to read.</param>
        </member>
        <member name="T:iText.IO.Util.TextUtil">
            <summary>This file is a helper class for internal usage only.</summary>
            <remarks>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </remarks>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsDiacritic(System.Int32)">
            <summary>
            Checks if the passed code point corresponds to diacritic.
            </summary>
            <param name="codePoint">the code point to check</param>
            <returns>true if passed code point is diacritic, false otherwise</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsSurrogateHigh(System.Char)">
            <summary>
            Check if the value of a character belongs to a certain interval
            that indicates it's the higher part of a surrogate pair.
            </summary>
            <param name="c">the character</param>
            <returns>true if the character belongs to the interval</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsSurrogateLow(System.Char)">
            <summary>
            Check if the value of a character belongs to a certain interval
            that indicates it's the lower part of a surrogate pair.
            </summary>
            <param name="c">the character</param>
            <returns>true if the character belongs to the interval</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsSurrogatePair(System.String,System.Int32)">
            <summary>
            Checks if two subsequent characters in a String are
            are the higher and the lower character in a surrogate
            pair (and therefore eligible for conversion to a UTF 32 character).
            </summary>
            <param name="text">the String with the high and low surrogate characters</param>
            <param name="idx">the index of the 'high' character in the pair</param>
            <returns>true if the characters are surrogate pairs</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsSurrogatePair(System.Char[],System.Int32)">
            <summary>
            Checks if two subsequent characters in a character array are
            are the higher and the lower character in a surrogate
            pair (and therefore eligible for conversion to a UTF 32 character).
            </summary>
            <param name="text">the character array with the high and low surrogate characters</param>
            <param name="idx">the index of the 'high' character in the pair</param>
            <returns>true if the characters are surrogate pairs</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.ConvertToUtf32(System.Char,System.Char)">
            <summary>
            Returns the code point of a UTF32 character corresponding with
            a high and a low surrogate value.
            </summary>
            <param name="highSurrogate">the high surrogate value</param>
            <param name="lowSurrogate">the low surrogate value</param>
            <returns>a code point value</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.ConvertToUtf32(System.Char[],System.Int32)">
            <summary>Converts a unicode character in a character array to a UTF 32 code point value.</summary>
            <param name="text">a character array that has the unicode character(s)</param>
            <param name="idx">the index of the 'high' character</param>
            <returns>the code point value</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.ConvertToUtf32(System.String,System.Int32)">
            <summary>Converts a unicode character in a String to a UTF32 code point value</summary>
            <param name="text">a String that has the unicode character(s)</param>
            <param name="idx">the index of the 'high' character</param>
            <returns>the codepoint value</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.ConvertFromUtf32(System.Int32)">
            <summary>Converts a UTF32 code point value to a char array with the corresponding character(s).</summary>
            <param name="codePoint">a Unicode value</param>
            <returns>the corresponding char array</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.ConvertFromUtf32(System.Int32[],System.Int32,System.Int32)">
            <summary>
            /
            Converts a UTF32 code point sequence to a String with the corresponding character(s).
            </summary>
            <param name="text">a Unicode text sequence</param>
            <param name="startPos">start position of text to convert, inclusive</param>
            <param name="endPos">end position of txt to convert, exclusive</param>
            <returns>the corresponding characters in a String</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.ConvertFromUtf32ToCharArray(System.Int32)">
            <summary>Converts a UTF32 code point value to a char array with the corresponding character(s).</summary>
            <param name="codePoint">a Unicode value</param>
            <returns>the corresponding characters in a char array</returns>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsIdentifierIgnorable(System.Int32)">
            <summary>
            Determines if the specified character (Unicode code point) should be regarded
            as an ignorable character in a Java identifier or a Unicode identifier.
            </summary>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsNewLine(iText.IO.Font.Otf.Glyph)">
            <summary>
            Determines if represented Glyph is '\n' or '\r' character.
            </summary>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsNewLine(System.Char)">
            <summary>
            Determines if represented Glyph is '\n' or '\r' character.
            </summary>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsNewLine(System.Int32)">
            <summary>
            Determines if represented Glyph is '\n' or '\r' character.
            </summary>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsSpaceOrWhitespace(iText.IO.Font.Otf.Glyph)">
            <summary>
            Determines if represented Glyph is space or whitespace character.
            </summary>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsWhitespace(iText.IO.Font.Otf.Glyph)">
            <summary>
            Determines if represented Glyph is whitespace character.
            </summary>
        </member>
        <member name="M:iText.IO.Util.TextUtil.IsUni0020(iText.IO.Font.Otf.Glyph)">
            <summary>
            Determines if represented Glyph is ' ' (SPACE) character.
            </summary>
        </member>
        <member name="T:iText.IO.Util.UrlUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="M:iText.IO.Util.UrlUtil.ToURL(System.String)">
            <summary>This method makes a valid URL from a given filename.</summary>
            <param name="filename">a given filename</param>
            <returns>a valid URL</returns>
        </member>
        <member name="M:iText.IO.Util.UrlUtil.OpenStream(System.Uri,System.Int32,System.Int32)">
            <summary>
            Gets the input stream of connection related to last redirected url. You should manually close input stream after
            calling this method to not hold any open resources.
            </summary>
            <param name="url">an initial URL.</param>
            <param name="connectTimeout">a connect timeout in milliseconds</param>
            <param name="readTimeout">a read timeout in milliseconds</param>
            
            <returns>an input stream of connection related to the last redirected url.</returns>
        </member>
        <member name="M:iText.IO.Util.UrlUtil.ToNormalizedURI(System.String)">
            <summary>
            This method makes a normalized URI from a given filename. 
            </summary>
            <param name="filename">a given filename</param>
            <returns>a valid Uri</returns>
        </member>
        <member name="M:iText.IO.Util.UrlUtil.ToNormalizedURI(System.IO.FileInfo)">
            <summary>
            This method makes a normalized URI from a given file. 
            </summary>
            <param name="file">a given file</param>
            <returns>a valid Uri</returns>
        </member>
        <member name="M:iText.IO.Util.UrlUtil.ToAbsoluteURI(System.Uri)">
            <summary>Get the entire URI string which is properly encoded.</summary>
            <param name="uri">URI which convert to encoded string</param>
            <returns>URI string representation</returns>
        </member>
        <member name="M:iText.IO.Util.UrlUtil.GetFileUriString(System.String)">
            <summary>
            This method gets uri string from a file.
            </summary>
            <param name="filename">a given filename</param>
            <returns>a uri string</returns>
        </member>
        <member name="M:iText.IO.Util.UrlUtil.GetNormalizedFileUriString(System.String)">
            <summary>
            This method gets normalized uri string from a file.
            </summary>
            <param name="filename">a given filename</param>
            <returns>a normalized uri string</returns>
        </member>
        <member name="M:iText.IO.Util.UrlUtil.GetInputStreamOfFinalConnection(System.Uri)">
            <summary>
            Gets the input stream of connection related to last redirected url. You should manually close input stream after
            calling this method to not hold any open resources.
            </summary>
            <param name="url">an initial URL.</param>
            
            <returns>an input stream of connection related to the last redirected url.</returns>
        </member>
        <member name="M:iText.IO.Util.UrlUtil.GetInputStreamOfFinalConnection(System.Uri,System.Int32,System.Int32)">
            <summary>
            Gets the input stream of connection related to last redirected url. You should manually close input stream after
            calling this method to not hold any open resources.
            </summary>
            <param name="url">an initial URL.</param>
            <param name="connectTimeout">a connect timeout in milliseconds</param>
            <param name="readTimeout">a read timeout in milliseconds</param>
            
            <returns>an input stream of connection related to the last redirected url.</returns>
        </member>
        <member name="T:iText.IO.Util.XmlUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in the future.
            </summary>
        </member>
        <member name="M:iText.IO.Util.XmlUtil.InitNewXmlDocument">
            <summary>
            This method creates a new empty Xml document.
            </summary>
            <returns>a new Xml document</returns>
        </member>
        <member name="M:iText.IO.Util.XmlUtil.InitXmlDocument(System.IO.Stream)">
            <summary>
            This method creates a new Xml document from input stream.
            </summary>
            <param name="inputStream">to parse</param>
            <returns>parsed Xml document</returns>
        </member>
        <member name="T:System.util.zlib.ZDeflaterOutputStream">
            <summary>
            Summary description for DeflaterOutputStream.
            </summary>
        </member>
        <member name="T:System.util.zlib.ZInflaterInputStream">
            <summary>
            Summary description for DeflaterOutputStream.
            </summary>
        </member>
    </members>
</doc>
