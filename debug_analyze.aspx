<%@ Page Language="C#" AutoEventWireup="true" CodeFile="debug_analyze.aspx.cs" Inherits="debug_analyze" %>

<!DOCTYPE html>
<html>

<head runat="server">
	<title>診斷工具 - 分析頁面問題</title>
	<style>
		body {
			font-family: 'Microsoft JhengHei', sans-serif;
			margin: 20px;
			background: #f5f5f5;
		}

		.container {
			max-width: 1200px;
			margin: 0 auto;
		}

		.test-section {
			margin: 20px 0;
			padding: 20px;
			background: white;
			border-radius: 8px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		}

		.test-section h3 {
			margin-top: 0;
			color: #333;
			border-bottom: 2px solid #007bff;
			padding-bottom: 10px;
		}

		.btn {
			padding: 12px 24px;
			margin: 8px;
			background: #007bff;
			color: white;
			border: none;
			border-radius: 5px;
			cursor: pointer;
		}

		.btn:hover {
			background: #0056b3;
		}

		.btn-success {
			background: #28a745;
		}

		.btn-danger {
			background: #dc3545;
		}

		.result {
			margin: 15px 0;
			padding: 15px;
			border-radius: 5px;
			white-space: pre-wrap;
		}

		.error {
			background: #f8d7da;
			color: #721c24;
			border: 1px solid #f5c6cb;
		}

		.success {
			background: #d4edda;
			color: #155724;
			border: 1px solid #c3e6cb;
		}

		.info {
			background: #d1ecf1;
			color: #0c5460;
			border: 1px solid #bee5eb;
		}

		.debug-log {
			font-family: monospace;
			font-size: 12px;
			max-height: 400px;
			overflow-y: auto;
		}

		table {
			width: 100%;
			border-collapse: collapse;
			margin: 10px 0;
		}

		th,
		td {
			border: 1px solid #ddd;
			padding: 8px;
			text-align: left;
		}

		th {
			background: #f2f2f2;
			font-weight: bold;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<asp:ScriptManager ID="ScriptManager1" runat="server" EnablePageMethods="true"></asp:ScriptManager>

		<div class="container">
			<h1>🔧 診斷工具 - 分析頁面問題排查</h1>

			<div class="test-section">
				<h3>🔗 步驟 1: 資料庫連線測試</h3>
				<asp:Button ID="btnTestConnection" runat="server" Text="測試資料庫連線" CssClass="btn" OnClick="btnTestConnection_Click" />
				<div class="result">
					<asp:Label ID="lblConnectionResult" runat="server" Text="點擊按鈕測試資料庫連線"></asp:Label>
				</div>
			</div>

			<div class="test-section">
				<h3>📊 步驟 2: 模擬主頁面查詢邏輯</h3>
				<p>模擬 Analyze_recowish01.aspx 的查詢邏輯:</p>

				<label>選擇學制類型:</label><br />
				<asp:RadioButtonList ID="RadioButtonList1" runat="server" RepeatDirection="Horizontal">
					<asp:ListItem Value="1" Selected="True">日間部</asp:ListItem>
					<asp:ListItem Value="2">碩士班</asp:ListItem>
					<asp:ListItem Value="3">進修部</asp:ListItem>
				</asp:RadioButtonList>

				<br /><label>選擇查詢項目:</label><br />
				<asp:RadioButtonList ID="RadioButtonList2" runat="server" RepeatDirection="Horizontal">
					<asp:ListItem Value="1" Selected="True">實名制人數</asp:ListItem>
					<asp:ListItem Value="2">報到人數</asp:ListItem>
					<asp:ListItem Value="3">繳交畢業證書人數</asp:ListItem>
					<asp:ListItem Value="4">繳費人數</asp:ListItem>
				</asp:RadioButtonList>

				<br />
				<asp:Button ID="btnSimulateQuery" runat="server" Text="🔍 執行模擬查詢" CssClass="btn btn-success" OnClick="btnSimulateQuery_Click" />

				<div class="result">
					<asp:Label ID="lblQueryResult" runat="server" Text="選擇選項後點擊執行查詢"></asp:Label>
				</div>

				<asp:GridView ID="gvResults" runat="server" AutoGenerateColumns="true" CssClass="table"></asp:GridView>
			</div>

			<div class="test-section">
				<h3>🐛 步驟 3: JavaScript 圖表測試</h3>
				<asp:Button ID="btnTestChart" runat="server" Text="測試圖表 JavaScript" CssClass="btn" OnClick="btnTestChart_Click" />
				<div id="debugChart" style="width: 100%; height: 300px; border: 1px solid #ddd; margin: 10px 0;"></div>
				<div class="result debug-log">
					<asp:Label ID="lblChartResult" runat="server" Text="點擊按鈕測試圖表功能"></asp:Label>
				</div>
			</div>

			<div class="test-section">
				<h3>📋 步驟 4: 詳細調試日誌</h3>
				<div id="debugLog" class="result debug-log info">
					<strong>系統狀態:</strong><br />
					伺服器時間: <%= DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") %><br />
					應用程式路徑: <%= Request.ApplicationPath %><br />
					頁面是否回傳: <%= IsPostBack %><br />
					<br />
					<strong>調試日誌將在這裡顯示...</strong>
				</div>
			</div>
		</div>
	</form>

	<script src="https://www.gstatic.com/charts/loader.js"></script>
	<script>
		google.charts.load('current', { 'packages': ['corechart'] });

		function testChart(chartData, chartTitle) {
			console.log('testChart 被調用:', { chartData, chartTitle });

			if (!chartData || !chartTitle) {
				document.getElementById('debugChart').innerHTML = '<p style="color: red;">❌ 圖表資料或標題為空</p>';
				return;
			}

			try {
				var data = google.visualization.arrayToDataTable(chartData);
				var options = {
					title: chartTitle,
					pieHole: 0.4,
					width: '100%',
					height: 300
				};
				var chart = new google.visualization.PieChart(document.getElementById('debugChart'));
				chart.draw(data, options);

				document.getElementById('<%= lblChartResult.ClientID %>').innerHTML = '✅ 圖表渲染成功!';
			} catch (e) {
				console.error('圖表渲染錯誤:', e);
				document.getElementById('<%= lblChartResult.ClientID %>').innerHTML = '❌ 圖表渲染錯誤: ' + e.message;
			}
		}

		function updateDebugLog(message) {
			var log = document.getElementById('debugLog');
			log.innerHTML += '<br/>' + new Date().toLocaleTimeString() + ': ' + message;
			log.scrollTop = log.scrollHeight;
		}
	</script>
</body>

</html>