<?xml version="1.0"?>
<doc>
    <assembly>
        <name>itext.forms</name>
    </assembly>
    <members>
        <member name="T:iText.Forms.Exceptions.AttributeNotFoundException">
            <summary>
            This class represents iText exception that should be thrown when the attribute with given name
            is not found in the object attributes list.
            </summary>
        </member>
        <member name="M:iText.Forms.Exceptions.AttributeNotFoundException.#ctor(System.String)">
            <summary>The exception thrown when the attribute with given name is not found in the object attributes list.
                </summary>
            <param name="attribute">the name of missing attribute.</param>
        </member>
        <member name="T:iText.Forms.Exceptions.FormsExceptionMessageConstant">
            <summary>Class that bundles all the error message templates as constants.</summary>
        </member>
        <member name="T:iText.Forms.Exceptions.XfdfException">
            <summary>
            This class represents iText exception that should be thrown when some errors occur while working with
            XFDF objects (XFDF file is XML-based Acrobat Forms Data Format).
            </summary>
        </member>
        <member name="M:iText.Forms.Exceptions.XfdfException.#ctor(System.String)">
            <summary>The exception thrown when some errors occur while working with XFDF objects.</summary>
            <param name="message">exception message.</param>
        </member>
        <member name="F:iText.Forms.Exceptions.XfdfException.ATTRIBUTE_NAME_OR_VALUE_MISSING">
            <summary>Message in case one tries to add attribute with null name or value.</summary>
        </member>
        <member name="F:iText.Forms.Exceptions.XfdfException.PAGE_IS_MISSING">
            <summary>Message in case one tries to add annotation without indicating the page it belongs to.</summary>
        </member>
        <member name="T:iText.Forms.Fields.AbstractPdfFormField">
            <summary>
            This class represents a single field or field group in an
            <see cref="T:iText.Forms.PdfAcroForm">AcroForm</see>.
            </summary>
            <remarks>
            This class represents a single field or field group in an
            <see cref="T:iText.Forms.PdfAcroForm">AcroForm</see>.
            <para />
            To be able to be wrapped with this
            <see cref="T:iText.Kernel.Pdf.PdfObjectWrapper`1"/>
            the
            <see cref="T:iText.Kernel.Pdf.PdfObject"/>
            must be indirect.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.AbstractPdfFormField.DEFAULT_FONT_SIZE">
            <summary>Size of text in form fields when font size is not explicitly set.</summary>
        </member>
        <member name="F:iText.Forms.Fields.AbstractPdfFormField.MIN_FONT_SIZE">
            <summary>Minimal size of text in form fields.</summary>
        </member>
        <member name="F:iText.Forms.Fields.AbstractPdfFormField.DA_FONT">
            <summary>Index of font value in default appearance element.</summary>
        </member>
        <member name="F:iText.Forms.Fields.AbstractPdfFormField.DA_SIZE">
            <summary>Index of font size value in default appearance element.</summary>
        </member>
        <member name="F:iText.Forms.Fields.AbstractPdfFormField.DA_COLOR">
            <summary>Index of color value in default appearance element.</summary>
        </member>
        <member name="F:iText.Forms.Fields.AbstractPdfFormField.parent">
            <summary>Parent form field.</summary>
        </member>
        <member name="F:iText.Forms.Fields.AbstractPdfFormField.enableFieldRegeneration">
            <summary>Indicates if the form field appearance stream regeneration is enabled.</summary>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="pdfObject">the dictionary to be wrapped, must have an indirect reference.</param>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetPdfObject">
            <summary>Gets the wrapped dictionary.</summary>
            <returns>the wrapped dictionary.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.SetParent(iText.Forms.Fields.PdfFormField)">
            <summary>
            Sets a parent
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            for the current object.
            </summary>
            <param name="parent">another form field that this field belongs to, usually a group field.</param>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetParent">
            <summary>Gets the parent dictionary.</summary>
            <returns>another form field that this field belongs to.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetParentField">
            <summary>Gets the parent field.</summary>
            <returns>another form field that this field belongs to.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetFieldName">
            <summary>Gets the current field name.</summary>
            <returns>
            the current field name, as a
            <see cref="T:iText.Kernel.Pdf.PdfString"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetDefaultAppearance">
            <summary>
            Gets default appearance string containing a sequence of valid page-content graphics or text state operators that
            define such properties as the field's text size and color.
            </summary>
            <returns>
            the default appearance graphics, as a
            <see cref="T:iText.Kernel.Pdf.PdfString"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetFontSize">
            <summary>Gets the current fontSize of the form field.</summary>
            <returns>the current fontSize.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetFont">
            <summary>Gets the current font of the form field.</summary>
            <returns>
            the current
            <see cref="T:iText.Kernel.Font.PdfFont">font</see>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetColor">
            <summary>Gets the current color of the form field.</summary>
            <returns>
            the current
            <see cref="T:iText.Kernel.Colors.Color">color</see>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetPdfConformance">
            <summary>Gets the declared conformance.</summary>
            <returns>
            the
            <see cref="T:iText.Kernel.Pdf.PdfConformance"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.RegenerateField">
            <summary>This method regenerates appearance stream of the field.</summary>
            <remarks>
            This method regenerates appearance stream of the field. Use it if you
            changed any field parameters and didn't use setValue method which
            generates appearance by itself.
            </remarks>
            <returns>whether or not the regeneration was successful.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.DisableFieldRegeneration">
            <summary>This method disables regeneration of the field and its children appearance stream.</summary>
            <remarks>
            This method disables regeneration of the field and its children appearance stream. So all of its children
            in the hierarchy will also not be regenerated.
            <para />
            Note that after this method is called field will be regenerated
            only during
            <see cref="M:iText.Forms.Fields.AbstractPdfFormField.EnableFieldRegeneration"/>
            call.
            </remarks>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.EnableFieldRegeneration">
            <summary>This method enables regeneration of the field appearance stream.</summary>
            <remarks>
            This method enables regeneration of the field appearance stream. Please note that this method enables
            regeneration for the children of the field. Also, appearance will be regenerated during this method call.
            <para />
            Should be called after
            <see cref="M:iText.Forms.Fields.AbstractPdfFormField.DisableFieldRegeneration"/>
            method call.
            </remarks>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.DisableCurrentFieldRegeneration">
            <summary>This method disables regeneration of the current field appearance stream.</summary>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.EnableCurrentFieldRegeneration">
            <summary>This method enables regeneration of the current field appearance stream and regenerates it.</summary>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.IsFieldRegenerationEnabled">
            <summary>This method checks if field appearance stream regeneration is enabled.</summary>
            <returns>true if regeneration is enabled for this field (and all of its ancestors), false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.SetColorNoRegenerate(iText.Kernel.Colors.Color)">
            <summary>Sets the text color and does not regenerate appearance stream.</summary>
            <param name="color">the new value for the Color.</param>
            <returns>the edited field.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetAppearanceStates">
            <summary>Gets the appearance state names.</summary>
            <returns>an array of Strings containing the names of the appearance states.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.Put(iText.Kernel.Pdf.PdfName,iText.Kernel.Pdf.PdfObject)">
            <summary>
            Inserts the value into the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            of this field and associates it with the specified key.
            </summary>
            <remarks>
            Inserts the value into the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            of this field and associates it with the specified key.
            If the key is already present in this field dictionary,
            this method will override the old value with the specified one.
            </remarks>
            <param name="key">key to insert or to override.</param>
            <param name="value">the value to associate with the specified key.</param>
            <returns>the edited field.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.Remove(iText.Kernel.Pdf.PdfName)">
            <summary>
            Removes the specified key from the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            of this field.
            </summary>
            <param name="key">key to be removed.</param>
            <returns>the edited field.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.Release">
            <summary>Releases underlying pdf object and other pdf entities used by wrapper.</summary>
            <remarks>
            Releases underlying pdf object and other pdf entities used by wrapper.
            This method should be called instead of direct call to
            <see cref="M:iText.Kernel.Pdf.PdfObject.Release"/>
            if the wrapper is used.
            </remarks>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetDocument">
            <summary>
            Gets the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            that owns that form field.
            </summary>
            <returns>
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            that owns that form field.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.IsWrappedObjectMustBeIndirect">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.SetColor(iText.Kernel.Colors.Color)">
            <summary>Sets the text color and regenerates appearance stream.</summary>
            <param name="color">the new value for the Color.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.SetFont(iText.Kernel.Font.PdfFont)">
            <summary>Basic setter for the <c>font</c> property.</summary>
            <remarks>
            Basic setter for the <c>font</c> property. Regenerates the field
            appearance after setting the new value.
            Note that the font will be added to the document so ensure that the font is embedded
            if it's a pdf/a document.
            </remarks>
            <param name="font">The new font to be set.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.SetFontSize(System.Single)">
            <summary>Basic setter for the <c>fontSize</c> property.</summary>
            <remarks>
            Basic setter for the <c>fontSize</c> property. Regenerates the
            field appearance after setting the new value.
            </remarks>
            <param name="fontSize">The new font size to be set.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.SetFontSize(System.Int32)">
            <summary>Basic setter for the <c>fontSize</c> property.</summary>
            <remarks>
            Basic setter for the <c>fontSize</c> property. Regenerates the
            field appearance after setting the new value.
            </remarks>
            <param name="fontSize">The new font size to be set.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.SetFontSizeAutoScale">
            <summary>Sets zero font size which will be interpreted as auto-size according to ISO 32000-1, 12.7.3.3.</summary>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.SetFontAndSize(iText.Kernel.Font.PdfFont,System.Single)">
            <summary>
            Combined setter for the <c>font</c> and <c>fontSize</c>
            properties.
            </summary>
            <remarks>
            Combined setter for the <c>font</c> and <c>fontSize</c>
            properties. Regenerates the field appearance after setting the new value.
            </remarks>
            <param name="font">The new font to be set.</param>
            <param name="fontSize">The new font size to be set.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.IsTerminalFormField">
            <summary>Determines whether current form field is terminal or not.</summary>
            <returns>true if this form field is a terminal one, false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetFieldName(System.Collections.Generic.ICollection{iText.Forms.Fields.PdfFormField})">
            <summary>Gets the current field name.</summary>
            <param name="visited">list of visited parents which is used to determine cycle references</param>
            <returns>
            the current field name, as a
            <see cref="T:iText.Kernel.Pdf.PdfString"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.Equals(System.Object)">
            <summary>Indicate whether some other object is "equal to" this one.</summary>
            <remarks>Indicate whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Forms.Fields.AbstractPdfFormField.GetHashCode">
            <summary>Generate a hash code for this object.</summary>
        </member>
        <member name="F:iText.Forms.Fields.Borders.AbstractFormBorder.FORM_UNDERLINE">
            <summary>The form underline border.</summary>
            <seealso cref="T:iText.Forms.Fields.Borders.UnderlineBorder"/>
        </member>
        <member name="F:iText.Forms.Fields.Borders.AbstractFormBorder.FORM_BEVELED">
            <summary>The form beveled border.</summary>
            <seealso cref="T:iText.Forms.Fields.Borders.BeveledBorder"/>
        </member>
        <member name="F:iText.Forms.Fields.Borders.AbstractFormBorder.FORM_INSET">
            <summary>The form inset border.</summary>
            <seealso cref="T:iText.Forms.Fields.Borders.InsetBorder"/>
        </member>
        <member name="M:iText.Forms.Fields.Borders.BeveledBorder.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,iText.Layout.Borders.Border.Side,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Fields.Borders.BeveledBorder.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Fields.Borders.BeveledBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Fields.Borders.FormBorderFactory">
            <summary>
            A factory for creating
            <see cref="T:iText.Forms.Fields.Borders.AbstractFormBorder"/>
            implementations.
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.Borders.FormBorderFactory.GetBorder(iText.Kernel.Pdf.PdfDictionary,System.Single,iText.Kernel.Colors.Color,iText.Kernel.Colors.Color)">
            <summary>
            Returns
            <see cref="T:iText.Layout.Borders.Border"/>
            for specific borderStyle.
            </summary>
            <param name="borderStyle">border style dictionary. ISO 32000-1 12.5.4</param>
            <param name="borderWidth">width of the border</param>
            <param name="borderColor">color of the border</param>
            <param name="backgroundColor">element background color. This param used for drawing beveled border type</param>
            <returns>
            
            <see cref="T:iText.Layout.Borders.Border"/>
            implementation or
            <see langword="null"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.Borders.InsetBorder.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,iText.Layout.Borders.Border.Side,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Fields.Borders.InsetBorder.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Fields.Borders.InsetBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Fields.Borders.UnderlineBorder.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,iText.Layout.Borders.Border.Side,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Fields.Borders.UnderlineBorder.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Fields.Borders.UnderlineBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Fields.CheckBoxFormFieldBuilder">
            <summary>Builder for checkbox form field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.CheckBoxFormFieldBuilder.#ctor(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>
            Creates builder for
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            creation.
            </summary>
            <param name="document">document to be used for form field creation</param>
            <param name="formFieldName">name of the form field</param>
        </member>
        <member name="M:iText.Forms.Fields.CheckBoxFormFieldBuilder.GetCheckType">
            <summary>Gets check type for checkbox form field.</summary>
            <returns>check type to be set for checkbox form field</returns>
        </member>
        <member name="M:iText.Forms.Fields.CheckBoxFormFieldBuilder.SetCheckType(iText.Forms.Fields.Properties.CheckBoxType)">
            <summary>Sets check type for checkbox form field.</summary>
            <remarks>
            Sets check type for checkbox form field. Default value is
            <see cref="F:iText.Forms.Fields.Properties.CheckBoxType.CROSS"/>.
            </remarks>
            <param name="checkType">check type to be set for checkbox form field</param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.CheckBoxFormFieldBuilder.CreateCheckBox">
            <summary>Creates checkbox form field based on provided parameters.</summary>
            <returns>
            new
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.CheckBoxFormFieldBuilder.GetThis">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Fields.ChoiceFormFieldBuilder">
            <summary>Builder for choice form field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.#ctor(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>
            Creates builder for
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            creation.
            </summary>
            <param name="document">document to be used for form field creation</param>
            <param name="formFieldName">name of the form field</param>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.GetOptions">
            <summary>Gets options for choice form field.</summary>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of choice form field options
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.SetOptions(iText.Kernel.Pdf.PdfArray)">
            <summary>Sets options for choice form field.</summary>
            <param name="options">
            
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of choice form field options
            </param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.SetOptions(System.String[])">
            <summary>Sets options for choice form field.</summary>
            <param name="options">
            array of
            <see cref="T:System.String"/>
            options
            </param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.SetOptions(System.String[][])">
            <summary>Sets options for choice form field.</summary>
            <param name="options">
            two-dimensional array of
            <see cref="T:System.String"/>
            options. Every inner array shall have two elements.
            </param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.CreateList">
            <summary>Creates list form field based on provided parameters.</summary>
            <returns>
            new
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.CreateComboBox">
            <summary>Creates combobox form field base on provided parameters.</summary>
            <returns>
            new
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.GetThis">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.ProcessOptions(System.String[][])">
            <summary>
            Convert
            <see cref="T:System.String"/>
            multidimensional array of combo box or list options to
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>.
            </summary>
            <param name="options">Two-dimensional array of options.</param>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            that contains all the options.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.ChoiceFormFieldBuilder.ProcessOptions(System.String[])">
            <summary>
            Convert
            <see cref="T:System.String"/>
            array of combo box or list options to
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>.
            </summary>
            <param name="options">array of options.</param>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            that contains all the options.
            </returns>
        </member>
        <member name="T:iText.Forms.Fields.FormFieldBuilder`1">
            <summary>Builder for form field.</summary>
            <typeparam name="T">specific form field builder which extends this class.</typeparam>
        </member>
        <member name="F:iText.Forms.Fields.FormFieldBuilder`1.document">
            <summary>Document to be used for form field creation.</summary>
        </member>
        <member name="F:iText.Forms.Fields.FormFieldBuilder`1.formFieldName">
            <summary>Name of the form field.</summary>
        </member>
        <member name="F:iText.Forms.Fields.FormFieldBuilder`1.conformance">
            <summary>Conformance of the form field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.FormFieldBuilder`1.#ctor(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>
            Creates builder for
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            creation.
            </summary>
            <param name="document">document to be used for form field creation</param>
            <param name="formFieldName">name of the form field</param>
        </member>
        <member name="M:iText.Forms.Fields.FormFieldBuilder`1.GetDocument">
            <summary>Gets document to be used for form field creation.</summary>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.FormFieldBuilder`1.GetFormFieldName">
            <summary>Gets name of the form field.</summary>
            <returns>name to be used for form field creation</returns>
        </member>
        <member name="M:iText.Forms.Fields.FormFieldBuilder`1.GetConformance">
            <summary>Gets conformance for form field creation.</summary>
            <returns>
            instance of
            <see cref="T:iText.Kernel.Pdf.PdfConformance"/>
            to be used for form field creation
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.FormFieldBuilder`1.SetConformance(iText.Kernel.Pdf.PdfConformance)">
            <summary>Sets conformance for form field creation.</summary>
            <param name="conformance">
            Instance of
            <see cref="T:iText.Kernel.Pdf.PdfConformance"/>
            to be used for form field creation.
            </param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.FormFieldBuilder`1.GetThis">
            <summary>Returns this builder object.</summary>
            <remarks>Returns this builder object. Required for superclass methods.</remarks>
            <returns>this builder</returns>
        </member>
        <member name="T:iText.Forms.Fields.FormsMetaInfoStaticContainer">
            <summary>Class to store meta info that will be used in forms module in static context.</summary>
        </member>
        <member name="M:iText.Forms.Fields.FormsMetaInfoStaticContainer.UseMetaInfoDuringTheAction(iText.Layout.Renderer.MetaInfoContainer,System.Action)">
            <summary>Sets meta info related to forms into static context, executes the action and then cleans meta info.
                </summary>
            <remarks>
            Sets meta info related to forms into static context, executes the action and then cleans meta info.
            <para />
            Keep in mind that this instance will only be accessible from the same thread.
            </remarks>
            <param name="metaInfoContainer">instance to be set.</param>
            <param name="action">action which will be executed while meta info is set to static context.</param>
        </member>
        <member name="M:iText.Forms.Fields.FormsMetaInfoStaticContainer.GetMetaInfoForLayout">
            <summary>Gets meta info which was set previously.</summary>
            <remarks>
            Gets meta info which was set previously.
            <para />
            Keep in mind that this operation will return meta info instance which was set previously from the same thread.
            </remarks>
            <returns>meta info instance.</returns>
        </member>
        <member name="T:iText.Forms.Fields.Merging.AddIndexStrategy">
            <summary>
            A
            <see cref="T:iText.Forms.Fields.Merging.OnDuplicateFormFieldNameStrategy"/>
            implementation that adds an index to the field name of the second field
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.Merging.AddIndexStrategy.#ctor(System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Fields.Merging.AddIndexStrategy"/>
            instance.
            </summary>
            <param name="separator">the separator that will be used to separate the original field name and the index</param>
        </member>
        <member name="M:iText.Forms.Fields.Merging.AddIndexStrategy.Execute(iText.Forms.Fields.PdfFormField,iText.Forms.Fields.PdfFormField,System.Boolean)">
            <summary>Renames the second field by adding an index to its name.</summary>
            <param name="firstField">the first field</param>
            <param name="secondField">the second field</param>
            <param name="throwExceptionOnError">if true, an exception will be thrown</param>
            <returns>
            returns
            <see langword="false"/>
            value, since
            <see cref="T:iText.Forms.Fields.Merging.AddIndexStrategy"/>
            never merges fields.
            </returns>
        </member>
        <member name="T:iText.Forms.Fields.Merging.AlwaysThrowExceptionStrategy">
            <summary>
            A
            <see cref="T:iText.Forms.Fields.Merging.OnDuplicateFormFieldNameStrategy"/>
            implementation that throws an exception if the second field has the same
            name as the first field.
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.Merging.AlwaysThrowExceptionStrategy.#ctor">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Fields.Merging.AlwaysThrowExceptionStrategy"/>
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.Merging.AlwaysThrowExceptionStrategy.Execute(iText.Forms.Fields.PdfFormField,iText.Forms.Fields.PdfFormField,System.Boolean)">
            <summary>executes the strategy.</summary>
            <param name="firstField">the first field</param>
            <param name="secondField">the second field</param>
            <param name="throwExceptionOnError">if true, an exception will be thrown</param>
            <returns>true if the second field was renamed successfully, false otherwise</returns>
        </member>
        <member name="T:iText.Forms.Fields.Merging.MergeFieldsStrategy">
            <summary>
            A
            <see cref="T:iText.Forms.Fields.Merging.OnDuplicateFormFieldNameStrategy"/>
            implementation that merges the second field into the first field if the
            second field has the same name as the first field.
            </summary>
            <remarks>
            A
            <see cref="T:iText.Forms.Fields.Merging.OnDuplicateFormFieldNameStrategy"/>
            implementation that merges the second field into the first field if the
            second field has the same name as the first field.
            This strategy is used by default.
            </remarks>
        </member>
        <member name="M:iText.Forms.Fields.Merging.MergeFieldsStrategy.#ctor">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Fields.Merging.MergeFieldsStrategy"/>
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.Merging.MergeFieldsStrategy.Execute(iText.Forms.Fields.PdfFormField,iText.Forms.Fields.PdfFormField,System.Boolean)">
            <summary>executes the strategy.</summary>
            <param name="firstField">the first field</param>
            <param name="secondField">the second field</param>
            <param name="throwExceptionOnError">if true, an exception will be thrown</param>
            <returns>true if the second field was merged successfully, false otherwise</returns>
        </member>
        <member name="T:iText.Forms.Fields.Merging.OnDuplicateFormFieldNameStrategy">
            <summary>
            A
            <see cref="T:iText.Forms.Fields.Merging.OnDuplicateFormFieldNameStrategy"/>
            implementation that throws an exception if the second field has the same
            name as the first field.
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.Merging.OnDuplicateFormFieldNameStrategy.Execute(iText.Forms.Fields.PdfFormField,iText.Forms.Fields.PdfFormField,System.Boolean)">
            <summary>executes the strategy.</summary>
            <param name="firstField">the first field</param>
            <param name="secondField">the second field</param>
            <param name="throwExceptionOnError">if true, an exception will be thrown</param>
            <returns>true if the second field was merged successfully, false otherwise</returns>
        </member>
        <member name="T:iText.Forms.Fields.NonTerminalFormFieldBuilder">
            <summary>Builder for non-terminal form field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.NonTerminalFormFieldBuilder.#ctor(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>
            Creates builder for non-terminal
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            creation.
            </summary>
            <param name="document">document to be used for form field creation</param>
            <param name="formFieldName">name of the form field</param>
        </member>
        <member name="M:iText.Forms.Fields.NonTerminalFormFieldBuilder.CreateNonTerminalFormField">
            <summary>Creates non-terminal form field based on provided parameters.</summary>
            <returns>
            new
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.NonTerminalFormFieldBuilder.GetThis">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Fields.PdfButtonFormField">
            <summary>An interactive control on the screen that raises events and/or can retain data.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfButtonFormField.FF_NO_TOGGLE_TO_OFF">
            <summary>If true, clicking the selected button deselects it, leaving no button selected.</summary>
            <remarks>
            If true, clicking the selected button deselects it, leaving no button selected.
            If false, exactly one radio button shall be selected at all times.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.PdfButtonFormField.FF_RADIO">
            <summary>If true, the field is a set of radio buttons.</summary>
            <remarks>
            If true, the field is a set of radio buttons.
            If false, the field is a check box.
            This flag should be set only if the
            <see cref="F:iText.Forms.Fields.PdfButtonFormField.FF_PUSH_BUTTON"/>
            flag is set to false.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.PdfButtonFormField.FF_PUSH_BUTTON">
            <summary>If true, the field is a push button that does not retain a permanent value.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfButtonFormField.FF_RADIOS_IN_UNISON">
            <summary>
            If true, a group of radio buttons within a radio button field,
            that use the same value for the on state will turn on and off in unison.
            </summary>
            <remarks>
            If true, a group of radio buttons within a radio button field,
            that use the same value for the on state will turn on and off in unison.
            That is if one is checked, they are all checked.
            If false, the buttons are mutually exclusive.
            </remarks>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a minimal
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>.
            </summary>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.#ctor(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a button form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>.
            </param>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a button form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a button form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="pdfObject">the dictionary to be wrapped, must have an indirect reference.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.GetFormType">
            <summary>Returns <c>Btn</c>, the form type for choice form fields.</summary>
            <returns>
            the form type, as a
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.IsRadio">
            <summary>
            If true, the field is a set of radio buttons; if false, the field is a
            check box.
            </summary>
            <remarks>
            If true, the field is a set of radio buttons; if false, the field is a
            check box. This flag only works if the Pushbutton flag is set to false.
            </remarks>
            <returns>whether the field is currently radio buttons or a checkbox</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.SetRadio(System.Boolean)">
            <summary>
            If true, the field is a set of radio buttons; if false, the field is a
            check box.
            </summary>
            <remarks>
            If true, the field is a set of radio buttons; if false, the field is a
            check box. This flag should be set only if the Pushbutton flag is set to false.
            </remarks>
            <param name="radio">whether the field should be radio buttons or a checkbox</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.IsToggleOff">
            <summary>
            If true, clicking the selected button deselects it, leaving no button
            selected.
            </summary>
            <remarks>
            If true, clicking the selected button deselects it, leaving no button
            selected. If false, exactly one radio button shall be selected at all
            times. Only valid for radio buttons.
            </remarks>
            <returns>whether a radio button currently allows to choose no options</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.SetToggleOff(System.Boolean)">
            <summary>If true, clicking the selected button deselects it, leaving no button selected.</summary>
            <remarks>
            If true, clicking the selected button deselects it, leaving no button selected.
            If false, exactly one radio button shall be selected at all times.
            </remarks>
            <param name="toggleOff">whether a radio button may allow to choose no options</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.IsPushButton">
            <summary>If true, the field is a pushbutton that does not retain a permanent value.</summary>
            <returns>whether or not the field is currently a pushbutton</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.SetPushButton(System.Boolean)">
            <summary>If true, the field is a pushbutton that does not retain a permanent value.</summary>
            <param name="pushButton">whether or not to set the field to a pushbutton</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.IsRadiosInUnison">
            <summary>
            If true, a group of radio buttons within a radio button field that use
            the same value for the on state will turn on and off in unison;
            that is if one is checked, they are all checked.
            </summary>
            <remarks>
            If true, a group of radio buttons within a radio button field that use
            the same value for the on state will turn on and off in unison;
            that is if one is checked, they are all checked.
            If false, the buttons are mutually exclusive
            </remarks>
            <returns>whether or not buttons are turned off in unison</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.SetRadiosInUnison(System.Boolean)">
            <summary>
            If true, a group of radio buttons within a radio button field that use
            the same value for the on state will turn on and off in unison; that is
            if one is checked, they are all checked.
            </summary>
            <remarks>
            If true, a group of radio buttons within a radio button field that use
            the same value for the on state will turn on and off in unison; that is
            if one is checked, they are all checked.
            If false, the buttons are mutually exclusive
            </remarks>
            <param name="radiosInUnison">whether or not buttons should turn off in unison</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.SetImage(System.String)">
            <summary>Set image to be used as a background content in a push button.</summary>
            <param name="image">path to the image to be used.</param>
            <returns>
            this
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.SetImageAsForm(iText.Kernel.Pdf.Xobject.PdfFormXObject)">
            <summary>
            Set image to be used as a background content in a push button as
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject"/>.
            </summary>
            <param name="form">
            
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject"/>
            to be used as an image
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfButtonFormField.AddKid(iText.Forms.Fields.AbstractPdfFormField)">
            <summary><inheritDoc/></summary>
            <param name="kid">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Forms.Fields.PdfChoiceFormField">
            <summary>An AcroForm field type representing any type of choice field.</summary>
            <remarks>
            An AcroForm field type representing any type of choice field. Choice fields
            are to be represented by a viewer as a list box or a combo box.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.PdfChoiceFormField.FF_COMBO">
            <summary>If true, the field is a combo box.</summary>
            <remarks>
            If true, the field is a combo box.
            If false, the field is a list box.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.PdfChoiceFormField.FF_EDIT">
            <summary>If true, the combo box shall include an editable text box as well as a drop-down list.</summary>
            <remarks>
            If true, the combo box shall include an editable text box as well as a drop-down list.
            If false, it shall include only a drop-down list.
            This flag shall be used only if the Combo flag is true.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.PdfChoiceFormField.FF_SORT">
            <summary>If true, the field's option items shall be sorted alphabetically.</summary>
            <remarks>
            If true, the field's option items shall be sorted alphabetically.
            This flag is intended for use by writers, not by readers.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.PdfChoiceFormField.FF_MULTI_SELECT">
            <summary>If true, more than one of the field's option items may be selected simultaneously.</summary>
            <remarks>
            If true, more than one of the field's option items may be selected simultaneously.
            If false, at most one item shall be selected.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.PdfChoiceFormField.FF_DO_NOT_SPELL_CHECK">
            <summary>If true, text entered in the field shall be spell-checked.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfChoiceFormField.FF_COMMIT_ON_SEL_CHANGE">
            <summary>If true, the new value shall be committed as soon as a selection is made (commonly with the pointing device).
                </summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a minimal
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>.
            </summary>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.#ctor(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a choice form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>.
            </param>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a choice form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a choice form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="pdfObject">the dictionary to be wrapped, must have an indirect reference.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.GetFormType">
            <summary>Returns <c>Ch</c>, the form type for choice form fields.</summary>
            <returns>
            the form type, as a
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetTopIndex(System.Int32)">
            <summary>Sets the index of the first visible option in a scrollable list.</summary>
            <param name="index">the index of the first option</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.GetTopIndex">
            <summary>Gets the current index of the first option in a scrollable list.</summary>
            <returns>
            the index of the first option, as a
            <see cref="T:iText.Kernel.Pdf.PdfNumber"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetIndices(iText.Kernel.Pdf.PdfArray)">
            <summary>Sets the selected items in the field.</summary>
            <param name="indices">a sorted array of indices representing selected items in the field</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetListSelected(System.String[])">
            <summary>Highlights the options.</summary>
            <remarks>
            Highlights the options. If this method is used for Combo box, the first value in input array
            will be the field value.
            </remarks>
            <param name="optionValues">Array of display values to be highlighted.</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetListSelected(System.String[],System.Boolean)">
            <summary>Highlights the options and generates field appearance if needed.</summary>
            <remarks>
            Highlights the options and generates field appearance if needed. If this method is used for Combo box,
            the first value in input array will be the field value
            </remarks>
            <param name="optionValues">Array of options to be highlighted</param>
            <param name="generateAppearance">if false, appearance won't be regenerated</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetListSelected(System.Int32[])">
            <summary>Highlights the options.</summary>
            <remarks>
            Highlights the options. If this method is used for Combo box, the first value in input array
            will be the field value
            </remarks>
            <param name="optionNumbers">The option numbers</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.GetIndices">
            <summary>Gets the currently selected items in the field</summary>
            <returns>a sorted array of indices representing the currently selected items in the field</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetCombo(System.Boolean)">
            <summary>If true, the field is a combo box; if false, the field is a list box.</summary>
            <param name="combo">whether or not the field should be a combo box</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.IsCombo">
            <summary>If true, the field is a combo box; if false, the field is a list box.</summary>
            <returns>whether or not the field is now a combo box.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetEdit(System.Boolean)">
            <summary>
            If true, the combo box shall include an editable text box as well as a
            drop-down list; if false, it shall include only a drop-down list.
            </summary>
            <remarks>
            If true, the combo box shall include an editable text box as well as a
            drop-down list; if false, it shall include only a drop-down list.
            This flag shall be used only if the Combo flag is true.
            </remarks>
            <param name="edit">whether or not to add an editable text box</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.IsEdit">
            <summary>
            If true, the combo box shall include an editable text box as well as a
            drop-down list; if false, it shall include only a drop-down list.
            </summary>
            <remarks>
            If true, the combo box shall include an editable text box as well as a
            drop-down list; if false, it shall include only a drop-down list.
            This flag shall be used only if the Combo flag is true.
            </remarks>
            <returns>whether or not there is currently an editable text box</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetSort(System.Boolean)">
            <summary>If true, the field's option items shall be sorted alphabetically.</summary>
            <remarks>
            If true, the field's option items shall be sorted alphabetically.
            This flag is intended for use by writers, not by readers.
            </remarks>
            <param name="sort">whether or not to sort the items</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.IsSort">
            <summary>If true, the field's option items shall be sorted alphabetically.</summary>
            <remarks>
            If true, the field's option items shall be sorted alphabetically.
            This flag is intended for use by writers, not by readers.
            </remarks>
            <returns>whether or not the items are currently sorted</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetMultiSelect(System.Boolean)">
            <summary>
            If true, more than one of the field's option items may be selected
            simultaneously; if false, at most one item shall be selected.
            </summary>
            <param name="multiSelect">whether or not to allow multiple selection</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.IsMultiSelect">
            <summary>
            If true, more than one of the field's option items may be selected simultaneously; if false, at most one item
            shall be selected.
            </summary>
            <returns>whether or not multiple selection is currently allowed</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetSpellCheck(System.Boolean)">
            <summary>If true, text entered in the field shall be spell-checked.</summary>
            <param name="spellCheck">whether or not to require the PDF viewer to perform a spell check</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.IsSpellCheck">
            <summary>If true, text entered in the field shall be spell-checked..</summary>
            <returns>whether or not PDF viewer must perform a spell check</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.SetCommitOnSelChange(System.Boolean)">
            <summary>If true, the new value shall be committed as soon as a selection is made (commonly with the pointing device).
                </summary>
            <param name="commitOnSelChange">whether or not to save changes immediately</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfChoiceFormField.IsCommitOnSelChange">
            <summary>If true, the new value shall be committed as soon as a selection is made (commonly with the pointing device).
                </summary>
            <returns>whether or not to save changes immediately</returns>
        </member>
        <member name="T:iText.Forms.Fields.PdfFormAnnotation">
            <summary>
            This class represents a single annotation in form fields hierarchy in an
            <see cref="T:iText.Forms.PdfAcroForm">AcroForm</see>.
            </summary>
            <remarks>
            This class represents a single annotation in form fields hierarchy in an
            <see cref="T:iText.Forms.PdfAcroForm">AcroForm</see>.
            <para />
            To be able to be wrapped with this
            <see cref="T:iText.Kernel.Pdf.PdfObjectWrapper`1"/>
            the
            <see cref="T:iText.Kernel.Pdf.PdfObject"/>
            must be indirect.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.PdfFormAnnotation.OFF_STATE_VALUE">
            <summary>Value which represents "off" state of form field.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfFormAnnotation.ON_STATE_VALUE">
            <summary>Value which represents "on" state of form field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.#ctor(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a form field annotation as a wrapper of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </param>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a form field annotation as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a form field annotation as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="pdfObject">the dictionary to be wrapped, must have an indirect reference.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.MakeFormAnnotation(iText.Kernel.Pdf.PdfObject,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            object.
            </summary>
            <param name="pdfObject">
            assumed to be either a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            , or a
            <see cref="T:iText.Kernel.Pdf.PdfIndirectReference"/>
            to a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            to create the field in.
            </param>
            <returns>
            a new
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            , or <c>null</c> if
            <c>pdfObject</c> is not a widget annotation.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.GetWidget">
            <summary>
            Gets
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            that this form field refers to.
            </summary>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.GetDefaultAppearance">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.RegenerateField">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.GetAppearanceStates">
            <summary>Gets the appearance state names.</summary>
            <returns>an array of Strings containing the names of the appearance states.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetBackgroundColor(iText.Kernel.Colors.Color)">
            <summary>Basic setter for the <c>backgroundColor</c> property.</summary>
            <remarks>
            Basic setter for the <c>backgroundColor</c> property. Regenerates
            the field appearance after setting the new value.
            </remarks>
            <param name="backgroundColor">
            The new color to be set or
            <see langword="null"/>
            if no background needed.
            </param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetCaption(System.String)">
            <summary>Basic setter for the push button caption.</summary>
            <remarks>Basic setter for the push button caption. Regenerates the field appearance after setting the new caption.
                </remarks>
            <param name="caption">button caption to be set.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetCaption(System.String,System.Boolean)">
            <summary>Basic setter for the push button caption.</summary>
            <remarks>
            Basic setter for the push button caption. Regenerates the field appearance after setting the new caption
            if corresponding parameter is specified.
            </remarks>
            <param name="caption">button caption to be set.</param>
            <param name="regenerateField">true if field should be regenerated, false otherwise.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.GetRotation">
            <summary>Get rotation property specified in this form annotation.</summary>
            <returns>
            
            <c>int</c>
            value which represents field's rotation
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetRotation(System.Int32)">
            <summary>Basic setter for the <c>degRotation</c> property.</summary>
            <remarks>
            Basic setter for the <c>degRotation</c> property. Regenerates
            the field appearance after setting the new value.
            </remarks>
            <param name="degRotation">The new degRotation to be set</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetAction(iText.Kernel.Pdf.Action.PdfAction)">
            <summary>
            Sets the action on
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation">widget</see>
            of this annotation form field.
            </summary>
            <param name="action">The action.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetVisibility(System.Int32)">
            <summary>Set the visibility flags of the form field annotation.</summary>
            <remarks>
            Set the visibility flags of the form field annotation.
            Options are: HIDDEN, HIDDEN_BUT_PRINTABLE, VISIBLE, VISIBLE_BUT_DOES_NOT_PRINT.
            </remarks>
            <param name="visibility">visibility option.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.GetBorderWidth">
            <summary>Gets the border width for the field.</summary>
            <returns>the current border width.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetBorderWidth(System.Single)">
            <summary>Sets the border width for the field.</summary>
            <param name="borderWidth">The new border width.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.GetBorder">
            <summary>Get border object specified in the widget annotation dictionary.</summary>
            <returns>
            
            <see cref="T:iText.Layout.Borders.Border"/>
            specified in the widget annotation dictionary
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetBorderStyle(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Sets the border style for the field.</summary>
            <param name="style">the new border style.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetBorderColor(iText.Kernel.Colors.Color)">
            <summary>Sets the Border Color.</summary>
            <param name="color">the new value for the Border Color.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetPage(System.Int32)">
            <summary>Specifies on which page the form field's widget must be shown.</summary>
            <param name="pageNum">the page number.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetFormFieldElement(iText.Forms.Form.Element.IFormField)">
            <summary>
            This method sets the model element associated with the current annotation and can be useful to take into account
            when drawing those properties that the annotation does not have.
            </summary>
            <remarks>
            This method sets the model element associated with the current annotation and can be useful to take into account
            when drawing those properties that the annotation does not have. Note that annotation properties will take
            precedence, so such properties cannot be overridden by using this method (e.g. background, text color, etc.).
            <para />
            Also note that the model element won't be used for annotations for choice form field.
            </remarks>
            <param name="element">model element to set</param>
            <returns>
            this
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetAppearance(iText.Kernel.Pdf.PdfName,System.String,iText.Kernel.Pdf.PdfStream)">
            <summary>Sets an appearance for (the widgets related to) the form field.</summary>
            <param name="appearanceType">
            the type of appearance stream to be added
            <list type="bullet">
            <item><description> PdfName.N: normal appearance
            </description></item>
            <item><description> PdfName.R: rollover appearance
            </description></item>
            <item><description> PdfName.D: down appearance
            </description></item>
            </list>
            </param>
            <param name="appearanceState">
            the state of the form field that needs to be true
            for the appearance to be used. Differentiates between several streams
            of the same type.
            </param>
            <param name="appearanceStream">
            the appearance instructions, as a
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>.
            </param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetCheckBoxAppearanceOnStateName(System.String)">
            <summary>Sets on state name for the checkbox annotation normal appearance and regenerates widget.</summary>
            <param name="onStateName">the new appearance name representing on state.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.SetAlternativeDescription(System.String)">
            <summary>Changes the alternative description of the annotation to the specified value.</summary>
            <param name="alternativeDescription">string to be used as alternative description.</param>
            <returns>
            The edited
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.GetAlternativeDescription">
            <summary>Gets the current alternative description.</summary>
            <returns>the current alternative description.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.GetRect(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Gets a
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            that matches the current size and position of this form field.
            </summary>
            <param name="field">current form field.</param>
            <returns>
            a
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            that matches the current size and position of this form field
            annotation.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.DrawBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Kernel.Pdf.Xobject.PdfFormXObject,System.Single,System.Single)">
            <summary>Draws a border using the borderWidth and borderColor of the form field.</summary>
            <param name="canvas">
            The
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            on which to draw
            </param>
            <param name="xObject">The PdfFormXObject</param>
            <param name="width">The width of the rectangle to draw</param>
            <param name="height">The height of the rectangle to draw</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.DrawPushButtonFieldAndSaveAppearance">
            <summary>Draws the appearance of a push button and saves it into an appearance stream.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.DrawSignatureFormFieldAndSaveAppearance">
            <summary>Draws the appearance of a signature field and saves it into an appearance stream.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.DrawRadioButtonAndSaveAppearance(System.String)">
            <summary>Draws the appearance of a radio button with a specified value and saves it into an appearance stream.
                </summary>
            <param name="value">the value of the radio button.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.DrawListFormFieldAndSaveAppearance">
            <summary>Draws the appearance of a list box form field and saves it into an appearance stream.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.DrawTextFormFieldAndSaveAppearance">
            <summary>Draws the appearance of a text form field and saves it into an appearance stream.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.DrawComboBoxAndSaveAppearance">
            <summary>Draws the appearance of a Combo box form field and saves it into an appearance stream.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.DrawCheckBoxAndSaveAppearance(System.String)">
            <summary>Draw a checkbox and save its appearance.</summary>
            <param name="onStateName">the name of the appearance state for the checked state</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.IsFieldInvisible(iText.Kernel.Geom.Rectangle)">
            <summary>Gets the visibility status of the signature.</summary>
            <returns>the visibility status of the signature</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.CreateTopLayer(System.Single,System.Single)">
            <summary>Signature appearance is assembled with the top-level and second-level XObjects and the standard layers.
                </summary>
            <remarks>
            Signature appearance is assembled with the top-level and second-level XObjects and the standard layers. The AP
            dictionary’s N attribute references a top-level XObject. This top-level XObject is necessary to properly resize
            signature appearances when these appearances are referred to by more than one signature field. The top-level
            XObject performs a Do on the second-level XObjects (n0 and n2 painted in sequence). The matrix may change if the
            signature is resized.
            </remarks>
            <param name="width">the width of the annotation rectangle.</param>
            <param name="height">the height of the annotation rectangle.</param>
            <returns>top layer xObject (/FRM).</returns>
            <seealso><a href="https://www.adobe.com/content/dam/acom/en/devnet/acrobat/pdfs/ppkappearances.pdf">Adobe Pdf
            * Digital Signature Appearances</a></seealso>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.CreateN0Layer(System.Single,System.Single)">
            <summary>The background layer that is present when creating the signature field.</summary>
            <remarks>
            The background layer that is present when creating the signature field. This layer renders the background and
            border of the annotation. The Matrix of this XObject is unity and the BBox is that of the original annotation.
            <para />
            In the default itext implementation n0 layer is either a blank xObject or normal appearance of the existed field
            (in case signature field was created but not signed) when reuseAppearance property is true, but user can modify
            n0 layer manually.
            </remarks>
            <param name="width">the width of the annotation rectangle.</param>
            <param name="height">the height of the annotation rectangle.</param>
            <returns>n0 layer xObject.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotation.CreateN2Layer(System.Single,System.Single)">
            <summary>
            The signature appearance layer that contains information about the signature, e.g. the line art for the
            handwritten signature, the text giving the signer’s name, date, reason, location and so on.
            </summary>
            <remarks>
            The signature appearance layer that contains information about the signature, e.g. the line art for the
            handwritten signature, the text giving the signer’s name, date, reason, location and so on. The content of this
            layer can be dynamically created when the signature is created, but thereafter it remains static. Specifically,
            it remains static when the validity state is changed. All appearance handlers that render text honor the font
            type and color defaults that were set for the signature annotation. So, this layer is the main layer where
            signature appearance should be drawn in the current itext implementation.
            </remarks>
            <param name="width">the width of the annotation rectangle.</param>
            <param name="height">the height of the annotation rectangle.</param>
            <returns>n2 layer xObject.</returns>
        </member>
        <member name="T:iText.Forms.Fields.PdfFormAnnotationUtil">
            <summary>
            Utility class to work with widget annotations
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            and its dictionaries.
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotationUtil.IsPureWidgetOrMergedField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Check that &lt;PdfDictionary&gt; object is widget annotation or merged field.</summary>
            <param name="fieldDict">field dictionary to check.</param>
            <returns>true if passed dictionary is a widget or merged field, false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotationUtil.IsPureWidget(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Check that &lt;PdfDictionary&gt; object is pure widget annotation.</summary>
            <param name="fieldDict">field dictionary to check.</param>
            <returns>true if passed dictionary is a widget, false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotationUtil.AddWidgetAnnotationToPage(iText.Kernel.Pdf.PdfPage,iText.Kernel.Pdf.Annot.PdfAnnotation)">
            <summary>Add widget annotation to the specified page.</summary>
            <param name="page">to which annotation should be added.</param>
            <param name="annotation">widget annotation to add.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotationUtil.AddWidgetAnnotationToPage(iText.Kernel.Pdf.PdfPage,iText.Kernel.Pdf.Annot.PdfAnnotation,System.Int32)">
            <summary>Add widget annotation to the specified page.</summary>
            <param name="page">to which annotation should be added.</param>
            <param name="annotation">widget annotation to add.</param>
            <param name="index">
            the index at which specified annotation will be added. If
            <c>-1</c>
            then annotation
            will be added to the end of an array.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotationUtil.MergeWidgetWithParentField(iText.Forms.Fields.PdfFormField)">
            <summary>Merge single widget annotation with its parent field.</summary>
            <param name="field">parent field.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormAnnotationUtil.SeparateWidgetAndField(iText.Forms.Fields.PdfFormField)">
            <summary>Separate merged field to form field and pure widget annotation.</summary>
            <remarks>
            Separate merged field to form field and pure widget annotation.
            Do nothing if the incoming field is not merged field.
            </remarks>
            <param name="field">to separate.</param>
        </member>
        <member name="T:iText.Forms.Fields.PdfFormCreator">
            <summary>Creator which shall be used in order to create all form related instances.</summary>
            <remarks>
            Creator which shall be used in order to create all form related instances. By default
            <see cref="T:iText.Forms.Fields.PdfFormFactory"/>
            is used.
            </remarks>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.SetFactory(iText.Forms.Fields.PdfFormFactory)">
            <summary>
            Set
            <see cref="T:iText.Forms.Fields.PdfFormFactory"/>
            to be used for form related instances creation.
            </summary>
            <param name="factory">
            
            <see cref="T:iText.Forms.Fields.PdfFormFactory"/>
            to set
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            using provided factory.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            </summary>
            <remarks>
            Creates a form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            using provided factory.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateTextFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            using provided factory.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateTextFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a text form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            </summary>
            <remarks>
            Creates a text form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateTextFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a text form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            using provided factory.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateButtonFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            using provided factory.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateButtonFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a button form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            </summary>
            <remarks>
            Creates a button form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateButtonFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a button form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            using provided factory.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateChoiceFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            using provided factory.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateChoiceFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a choice form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            </summary>
            <remarks>
            Creates a choice form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateChoiceFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a choice form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            using provided factory.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateSignatureFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            using provided factory.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateSignatureFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a signature form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            </summary>
            <remarks>
            Creates a signature form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateSignatureFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a signature form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            using provided factory.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateFormAnnotation(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a form field annotation as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            </summary>
            <remarks>
            Creates a form field annotation as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            using provided factory.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.CreateFormAnnotation(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a form field annotation as a wrapper of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            using provided factory.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormCreator.GetAcroForm(iText.Kernel.Pdf.PdfDocument,System.Boolean)">
            <summary>Retrieves AcroForm from the document using provided factory.</summary>
            <remarks>
            Retrieves AcroForm from the document using provided factory. If there is no AcroForm in the
            document Catalog and createIfNotExist flag is true then the AcroForm
            dictionary will be created and added to the document.
            </remarks>
            <param name="document">
            the document to retrieve the
            <see cref="T:iText.Forms.PdfAcroForm"/>
            from
            </param>
            <param name="createIfNotExist">
            when <c>true</c>, this method will create a
            <see cref="T:iText.Forms.PdfAcroForm"/>
            if none exists for this document
            </param>
            <returns>
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument">document</see>
            's AcroForm, or a new one provided that <c>createIfNotExist</c>
            parameter is <c>true</c>, otherwise <c>null</c>.
            </returns>
        </member>
        <member name="T:iText.Forms.Fields.PdfFormFactory">
            <summary>Default factory for form related instances creation.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.#ctor">
            <summary>
            Create
            <see cref="T:iText.Forms.Fields.PdfFormFactory"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateTextFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateTextFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a text form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a text form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateTextFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a text form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateButtonFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateButtonFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a button form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a button form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateButtonFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a button form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateChoiceFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateChoiceFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a choice form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a choice form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateChoiceFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a choice form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfChoiceFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateSignatureFormField(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a minimal, empty
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>.
            </summary>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateSignatureFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a signature form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a signature form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateSignatureFormField(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a signature form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>.
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateFormAnnotation(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a form field annotation as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a form field annotation as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="dictionary">the dictionary to be wrapped, must have an indirect reference.</param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.CreateFormAnnotation(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a form field annotation as a wrapper of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </param>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
            <returns>
            created
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFactory.GetAcroForm(iText.Kernel.Pdf.PdfDocument,System.Boolean)">
            <summary>Retrieves AcroForm from the document.</summary>
            <remarks>
            Retrieves AcroForm from the document. If there is no AcroForm in the
            document Catalog and createIfNotExist flag is true then the AcroForm
            dictionary will be created and added to the document.
            </remarks>
            <param name="document">
            the document to retrieve the
            <see cref="T:iText.Forms.PdfAcroForm"/>
            from
            </param>
            <param name="createIfNotExist">
            when <c>true</c>, this method will create a
            <see cref="T:iText.Forms.PdfAcroForm"/>
            if none exists for this document
            </param>
            <returns>
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument">document</see>
            's AcroForm, or a new one provided that <c>createIfNotExist</c>
            parameter is <c>true</c>, otherwise <c>null</c>.
            </returns>
        </member>
        <member name="T:iText.Forms.Fields.PdfFormField">
            <summary>
            This class represents a single field or field group in an
            <see cref="T:iText.Forms.PdfAcroForm">AcroForm</see>.
            </summary>
            <remarks>
            This class represents a single field or field group in an
            <see cref="T:iText.Forms.PdfAcroForm">AcroForm</see>.
            <para />
            To be able to be wrapped with this
            <see cref="T:iText.Kernel.Pdf.PdfObjectWrapper`1"/>
            the
            <see cref="T:iText.Kernel.Pdf.PdfObject"/>
            must be indirect.
            </remarks>
        </member>
        <member name="F:iText.Forms.Fields.PdfFormField.FF_MULTILINE">
            <summary>
            Flag that designates, if set, that the field can contain multiple lines
            of text.
            </summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfFormField.FF_PASSWORD">
            <summary>Flag that designates, if set, that the field's contents must be obfuscated.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfFormField.FF_READ_ONLY">
            <summary>The ReadOnly flag, which specifies whether or not the field can be changed.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfFormField.FF_REQUIRED">
            <summary>The Required flag, which specifies whether or not the field must be filled in.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfFormField.FF_NO_EXPORT">
            <summary>The NoExport flag, which specifies whether or not exporting is forbidden.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfFormField.FORM_FIELD_KEYS">
            <summary>List of all allowable keys in form fields.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="pdfObject">the dictionary to be wrapped, must have an indirect reference.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a minimal
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </summary>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.#ctor(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </param>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.MakeFormField(iText.Kernel.Pdf.PdfObject,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a (subtype of)
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            object.
            </summary>
            <remarks>
            Creates a (subtype of)
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            object. The type of the object
            depends on the <c>FT</c> entry in the <c>pdfObject</c> parameter.
            </remarks>
            <param name="pdfObject">
            assumed to be either a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            , or a
            <see cref="T:iText.Kernel.Pdf.PdfIndirectReference"/>
            to a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            to create the field in.
            </param>
            <returns>
            a new
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            , or <c>null</c> if
            <c>pdfObject</c> is not a form field.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.MakeFormFieldOrAnnotation(iText.Kernel.Pdf.PdfObject,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a (subtype of)
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            or
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            object depending on
            <c>pdfObject</c>.
            </summary>
            <param name="pdfObject">
            assumed to be either a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            , or a
            <see cref="T:iText.Kernel.Pdf.PdfIndirectReference"/>
            to a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            to create the field in.
            </param>
            <returns>
            a new
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>
            , or <c>null</c> if
            <c>pdfObject</c> is not a form field and is not a widget annotation.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.MakeFieldFlag(System.Int32)">
            <summary>Makes a field flag by bit position.</summary>
            <remarks>
            Makes a field flag by bit position. Bit positions are numbered 1 to 32.
            But position 0 corresponds to flag 1, position 3 corresponds to flag 4 etc.
            </remarks>
            <param name="bitPosition">bit position of a flag in range 1 to 32 from the pdf specification.</param>
            <returns>corresponding field flag.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.IsFormField(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Checks if dictionary contains any of the form field keys.</summary>
            <param name="dict">field dictionary to check.</param>
            <returns>true if it is a form field dictionary, false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetFormFieldKeys">
            <summary>
            Gets a set of all possible form field keys except
            <c>PdfName.Parent</c>.
            </summary>
            <returns>a set of form field keys.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetFormType(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Returns the type of the form field dictionary, or of the parent
            &lt;PdfDictionary&gt; object.
            </summary>
            <param name="fieldDict">field dictionary to get its type.</param>
            <returns>
            the form type, as a
            <see cref="T:iText.Kernel.Pdf.PdfName"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetStringValue(iText.Kernel.Pdf.PdfObject)">
            <summary>
            Retrieves string value from
            <see cref="T:iText.Kernel.Pdf.PdfObject"/>
            representing text string or text stream.
            </summary>
            <param name="value">
            
            <see cref="T:iText.Kernel.Pdf.PdfObject"/>
            representing text string or text stream
            </param>
            <returns>
            
            <see cref="T:System.String"/>
            value
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.ApplyAccessibilityProperties(iText.Forms.Fields.PdfFormField,iText.Layout.Tagging.IAccessibleElement,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Applies
            <see cref="T:iText.Kernel.Pdf.Tagutils.AccessibilityProperties"/>
            for provided form field and its annotation children.
            </summary>
            <param name="formField">
            
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            the form field to which the accessibility properties should be applied
            </param>
            <param name="modelElement">
            
            <see cref="T:iText.Layout.Tagging.IAccessibleElement"/>
            the form field layout element with accessibility properties
            </param>
            <param name="pdfDocument">
            
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            the document to which the form field belongs
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetFormType">
            <summary>
            Returns the type of the parent form field, or of the wrapped
            &lt;PdfDictionary&gt; object.
            </summary>
            <returns>
            the form type, as a
            <see cref="T:iText.Kernel.Pdf.PdfName"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetValue(System.String)">
            <summary>Sets a value to the field and generating field appearance if needed.</summary>
            <param name="value">of the field.</param>
            <returns>the field.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetValue(System.String,System.Boolean)">
            <summary>Sets a value to the field (and fields with the same names) and generates field appearance if needed.
                </summary>
            <param name="value">of the field.</param>
            <param name="generateAppearance">if false, appearance won't be regenerated.</param>
            <returns>the field.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetValue(System.String,iText.Kernel.Font.PdfFont,System.Single)">
            <summary>Set text field value with given font and size.</summary>
            <param name="value">text value.</param>
            <param name="font">
            a
            <see cref="T:iText.Kernel.Font.PdfFont"/>.
            </param>
            <param name="fontSize">the size of the font.</param>
            <returns>the edited field.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetValue(System.String,System.String)">
            <summary>Sets the field value and the display string.</summary>
            <remarks>
            Sets the field value and the display string. The display string
            is used to build the appearance.
            </remarks>
            <param name="value">the field value.</param>
            <param name="displayValue">
            the string that is used for the appearance. If <c>null</c>
            the <c>value</c> parameter will be used.
            </param>
            <returns>the edited field.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.RemoveChild(iText.Forms.Fields.AbstractPdfFormField)">
            <summary>Removes the childField object of this field.</summary>
            <param name="fieldName">
            a
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            , that needs to be removed from form field children.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.RemoveChildren">
            <summary>Removes all children from the current field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetKids">
            <summary>Gets the kids of this object.</summary>
            <returns>
            contents of the dictionary's <c>Kids</c> property, as a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetChildFields">
            <summary>Gets the childFields of this object.</summary>
            <returns>the children of the current field.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetChildFormFields">
            <summary>Gets all child form fields of this form field.</summary>
            <remarks>Gets all child form fields of this form field. Annotations are not returned.</remarks>
            <returns>
            a list of
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetAllChildFormFields">
            <summary>
            Gets all childFields of this object, including the children of the children
            but not annotations.
            </summary>
            <returns>the children of the current field and their children.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetAllChildFields">
            <summary>Gets all childFields of this object, including the children of the children.</summary>
            <returns>the children of the current field and their children.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetChildField(System.String)">
            <summary>Gets the child field of form field.</summary>
            <remarks>
            Gets the child field of form field. If there is no child field with such name,
            <see langword="null"/>
            is returned.
            </remarks>
            <param name="fieldName">
            a
            <see cref="T:System.String"/>
            , name of the received field.
            </param>
            <returns>
            the child of the current field as a
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.AddKid(iText.Forms.Fields.AbstractPdfFormField)">
            <summary>
            Adds a new kid to the <c>Kids</c> array property from a
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>.
            </summary>
            <remarks>
            Adds a new kid to the <c>Kids</c> array property from a
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>
            . Also sets the kid's <c>Parent</c> property to this object.
            </remarks>
            <param name="kid">
            a new
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>
            entry for the field's <c>Kids</c> array property.
            </param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.AddKid(iText.Forms.Fields.AbstractPdfFormField,System.Boolean)">
            <summary>
            Adds a new kid to the <c>Kids</c> array property from a
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>.
            </summary>
            <remarks>
            Adds a new kid to the <c>Kids</c> array property from a
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>
            . Also sets the kid's <c>Parent</c> property to this object.
            </remarks>
            <param name="kid">
            a new
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField"/>
            entry for the field's <c>Kids</c> array property.
            </param>
            <param name="throwExceptionOnError">
            define whether exception (true) or log (false) is expected in case kid with
            the same name exists and merge of two kids failed.
            </param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.AddKid(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation)">
            <summary>
            Adds a new kid to the <c>Kids</c> array property from a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <remarks>
            Adds a new kid to the <c>Kids</c> array property from a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            . Also sets the kid's <c>Parent</c> property to this object.
            </remarks>
            <param name="kid">
            a new
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            entry for the field's <c>Kids</c> array property.
            </param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetFieldName(System.String)">
            <summary>Changes the name of the field to the specified value.</summary>
            <param name="name">the new field name, as a String.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetPartialFieldName">
            <summary>Gets the current field partial name.</summary>
            <returns>
            the current field partial name, as a
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            . If the field has no partial name,
            an empty
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            is returned.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetAlternativeName(System.String)">
            <summary>Changes the alternate name of the field to the specified value.</summary>
            <remarks>
            Changes the alternate name of the field to the specified value. The
            alternate is a descriptive name to be used by status messages etc.
            </remarks>
            <param name="name">the new alternate name, as a String.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetAlternativeName">
            <summary>Gets the current alternate name.</summary>
            <remarks>
            Gets the current alternate name. The alternate is a descriptive name to
            be used by status messages etc.
            </remarks>
            <returns>
            the current alternate name, as a
            <see cref="T:iText.Kernel.Pdf.PdfString"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetMappingName(System.String)">
            <summary>Changes the mapping name of the field to the specified value.</summary>
            <remarks>
            Changes the mapping name of the field to the specified value. The
            mapping name can be used when exporting the form data in the document.
            </remarks>
            <param name="name">the new alternate name, as a String.</param>
            <returns>the edited field.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetMappingName">
            <summary>Gets the current mapping name.</summary>
            <remarks>
            Gets the current mapping name. The mapping name can be used when
            exporting the form data in the document.
            </remarks>
            <returns>
            the current mapping name, as a
            <see cref="T:iText.Kernel.Pdf.PdfString"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetFieldFlag(System.Int32)">
            <summary>
            Checks whether a certain flag, or any of a combination of flags, is set
            for this form field.
            </summary>
            <param name="flag">an <c>int</c> interpreted as a series of a binary flags.</param>
            <returns>
            true if any of the flags specified in the parameter is also set
            in the form field.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetFieldFlag(System.Int32)">
            <summary>Adds a flag, or combination of flags, for the form field.</summary>
            <remarks>
            Adds a flag, or combination of flags, for the form field. This method is
            intended to be used one flag at a time, but this is not technically
            enforced. To <em>replace</em> the current value, use
            <see cref="M:iText.Forms.Fields.PdfFormField.SetFieldFlags(System.Int32)"/>.
            </remarks>
            <param name="flag">an <c>int</c> interpreted as a series of a binary flags.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetFieldFlag(System.Int32,System.Boolean)">
            <summary>Adds or removes a flag, or combination of flags, for the form field.</summary>
            <remarks>
            Adds or removes a flag, or combination of flags, for the form field. This
            method is intended to be used one flag at a time, but this is not
            technically enforced. To <em>replace</em> the current value, use
            <see cref="M:iText.Forms.Fields.PdfFormField.SetFieldFlags(System.Int32)"/>.
            </remarks>
            <param name="flag">an <c>int</c> interpreted as a series of a binary flags.</param>
            <param name="value">
            if <c>true</c>, adds the flag(s). if <c>false</c>,
            removes the flag(s).
            </param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.IsMultiline">
            <summary>If true, the field can contain multiple lines of text; if false, the field's text is restricted to a single line.
                </summary>
            <returns>whether the field can span over multiple lines.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.IsPassword">
            <summary>If true, the field is intended for entering a secure password that should not be echoed visibly to the screen.
                </summary>
            <remarks>
            If true, the field is intended for entering a secure password that should not be echoed visibly to the screen.
            Characters typed from the keyboard should instead be echoed in some unreadable form, such as asterisks
            or bullet characters.
            </remarks>
            <returns>whether or not the contents of the field must be obfuscated.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetFieldFlags(System.Int32)">
            <summary>Sets a flag, or combination of flags, for the form field.</summary>
            <remarks>
            Sets a flag, or combination of flags, for the form field. This method
            <em>replaces</em> the previous value. Compare with
            <see cref="M:iText.Forms.Fields.PdfFormField.SetFieldFlag(System.Int32)"/>
            which <em>adds</em> a flag to the existing flags.
            </remarks>
            <param name="flags">an <c>int</c> interpreted as a series of a binary flags.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetFieldFlags">
            <summary>Gets the current list of PDF form field flags.</summary>
            <returns>the current list of flags, encoded as an <c>int</c>.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetValue">
            <summary>Gets the current value contained in the form field.</summary>
            <returns>
            the current value, as a
            <see cref="T:iText.Kernel.Pdf.PdfObject"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetValueAsString">
            <summary>Gets the current value contained in the form field.</summary>
            <returns>
            the current value, as a
            <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetDisplayValue">
            <summary>Gets the current display value of the form field.</summary>
            <returns>
            the current display value, as a
            <see cref="T:System.String"/>
            , if it exists.
            If not, returns the value as a
            <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetDefaultValue(iText.Kernel.Pdf.PdfObject)">
            <summary>Sets the default fallback value for the form field.</summary>
            <param name="value">the default value.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetDefaultValue">
            <summary>Gets the default fallback value for the form field.</summary>
            <returns>the default value.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetAdditionalAction(iText.Kernel.Pdf.PdfName,iText.Kernel.Pdf.Action.PdfAction)">
            <summary>Sets an additional action for the form field.</summary>
            <param name="key">the dictionary key to use for storing the action.</param>
            <param name="action">the action.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetAdditionalAction">
            <summary>Gets the currently additional action dictionary for the form field.</summary>
            <returns>the additional action dictionary.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetOptions(iText.Kernel.Pdf.PdfArray)">
            <summary>Sets options for the form field.</summary>
            <remarks>Sets options for the form field. Only to be used for checkboxes and radio buttons.</remarks>
            <param name="options">
            an array of
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            objects that each represent
            the 'on' state of one of the choices.
            </param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetOptions">
            <summary>Gets options for the form field.</summary>
            <remarks>
            Gets options for the form field. Should only return usable values for
            checkboxes and radio buttons.
            </remarks>
            <returns>
            the options, as an
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            objects.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetWidgets">
            <summary>
            Gets all
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>
            that its children refer to.
            </summary>
            <returns>
            a list of
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetChildFormAnnotations">
            <summary>
            Gets all child form field's annotations
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            of this form field.
            </summary>
            <returns>
            a list of
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetFirstFormAnnotation">
            <summary>
            Gets a single child form field's annotation
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>.
            </summary>
            <returns>
            
            <see cref="T:iText.Forms.Fields.PdfFormAnnotation"/>
            or null if there are no child annotations.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetDefaultAppearance">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.UpdateDefaultAppearance">
            <summary>Updates DA for Variable text, Push button and choice form fields.</summary>
            <remarks>
            Updates DA for Variable text, Push button and choice form fields.
            The resources required for DA will be put to AcroForm's DR.
            Note, for other form field types DA will be removed.
            </remarks>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetJustification">
            <summary>
            Gets a code specifying the form of quadding (justification) to be used in displaying the text:
            0 Left-justified
            1 Centered
            2 Right-justified
            </summary>
            <returns>the current justification attribute.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetJustification(System.Nullable{iText.Layout.Properties.TextAlignment})">
            <summary>
            Sets a code specifying the form of quadding (justification) to be used in displaying the text:
            0 Left-justified
            1 Centered
            2 Right-justified
            </summary>
            <param name="justification">the value to set the justification attribute to.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetDefaultStyle">
            <summary>Gets a default style string, as described in "Rich Text Strings" section of Pdf spec.</summary>
            <returns>
            the default style, as a
            <see cref="T:iText.Kernel.Pdf.PdfString"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetDefaultStyle(iText.Kernel.Pdf.PdfString)">
            <summary>Sets a default style string, as described in "Rich Text Strings" section of Pdf spec.</summary>
            <param name="defaultStyleString">a new default style for the form field.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetRichText">
            <summary>Gets a rich text string, as described in "Rich Text Strings" section of Pdf spec.</summary>
            <remarks>
            Gets a rich text string, as described in "Rich Text Strings" section of Pdf spec.
            May be either
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            or
            <see cref="T:iText.Kernel.Pdf.PdfString"/>.
            </remarks>
            <returns>the current rich text value.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetRichText(iText.Kernel.Pdf.PdfObject)">
            <summary>Sets a rich text string, as described in "Rich Text Strings" section of Pdf spec.</summary>
            <remarks>
            Sets a rich text string, as described in "Rich Text Strings" section of Pdf spec.
            It may be either
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            or
            <see cref="T:iText.Kernel.Pdf.PdfString"/>.
            </remarks>
            <param name="richText">a new rich text value</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetCheckType(iText.Forms.Fields.Properties.CheckBoxType)">
            <summary>Changes the type of graphical marker used to mark a checkbox as 'on'.</summary>
            <remarks>
            Changes the type of graphical marker used to mark a checkbox as 'on'.
            Notice that in order to complete the change one should call
            <see cref="M:iText.Forms.Fields.PdfFormField.RegenerateField">regenerateField</see>
            method.
            </remarks>
            <param name="checkType">the new checkbox marker.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.RegenerateField">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetReadOnly(System.Boolean)">
            <summary>Sets the ReadOnly flag, specifying whether or not the field can be changed.</summary>
            <param name="readOnly">if <c>true</c>, then the field cannot be changed.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.IsReadOnly">
            <summary>Gets the ReadOnly flag, specifying whether or not the field can be changed.</summary>
            <returns><c>true</c> if the field cannot be changed.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetRequired(System.Boolean)">
            <summary>Sets the Required flag, specifying whether or not the field must be filled in.</summary>
            <param name="required">if <c>true</c>, then the field must be filled in.</param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.IsRequired">
            <summary>Gets the Required flag, specifying whether or not the field must be filled in.</summary>
            <returns><c>true</c> if the field must be filled in.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetNoExport(System.Boolean)">
            <summary>Sets the NoExport flag, specifying whether or not exporting is forbidden.</summary>
            <param name="noExport">if <c>true</c>, then exporting is <em>forbidden</em></param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.IsNoExport">
            <summary>Gets the NoExport attribute.</summary>
            <returns>whether exporting the value following a form action is forbidden.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.IsInReadingMode">
            <summary>Checks if the document that contains the field is created in reading mode.</summary>
            <returns>true if reading mode is used, false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.GetAppearanceStates">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.Release">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetColor(iText.Kernel.Colors.Color)">
            <summary><inheritDoc/></summary>
            <param name="color">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.SetChildField(iText.Forms.Fields.AbstractPdfFormField)">
            <summary>Adds a field to the children of the current field.</summary>
            <param name="kid">the field, which should become a child.</param>
            <returns>the kid itself.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.ReplaceKids(System.Collections.Generic.ICollection{iText.Forms.Fields.AbstractPdfFormField})">
            <summary>Replaces /Kids value with passed kids dictionaries, and keeps old flashed fields there.</summary>
            <remarks>
            Replaces /Kids value with passed kids dictionaries, and keeps old flashed fields there.
            Also updates childFields array for
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </remarks>
            <param name="kids">collection of new kids.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.TryGenerateCheckboxAppearance(System.String)">
            <summary>
            Distinguish mutually exclusive and regular checkboxes: check all the on states of the widgets, if they are
            not all equal, then consider that this checkbox is mutually exclusive and do nothing, otherwise regenerate
            normal appearance with value as on appearance state for all the widgets.
            </summary>
            <param name="value">not empty value different from "Off".</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormField.PutAcroFormObject(iText.Kernel.Pdf.PdfName,iText.Kernel.Pdf.PdfObject)">
            <summary>Puts object directly to AcroForm dictionary.</summary>
            <remarks>
            Puts object directly to AcroForm dictionary.
            It works much faster than consequent invocation of
            <see cref="M:iText.Forms.PdfAcroForm.GetAcroForm(iText.Kernel.Pdf.PdfDocument,System.Boolean)"/>
            and
            <see cref="M:iText.Kernel.Pdf.PdfObjectWrapper`1.GetPdfObject"/>.
            <para />
            Note, this method assume that Catalog already has AcroForm object.
            <see cref="M:iText.Forms.Fields.PdfFormField.AddAcroFormToCatalog"/>
            should be called explicitly.
            </remarks>
            <param name="acroFormKey">the key of the object.</param>
            <param name="acroFormObject">the object to add.</param>
        </member>
        <member name="T:iText.Forms.Fields.PdfFormFieldMergeUtil">
            <summary>
            Utility class to merge form fields
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            with the same names.
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFieldMergeUtil.MergeKidsWithSameNames(iText.Forms.Fields.PdfFormField,System.Boolean)">
            <summary>This method merges all kids with the same names for the given parent field dictionary (recursively).
                </summary>
            <param name="parentField">a field whose kids should be checked and merged in case of same partial names.</param>
            <param name="throwExceptionOnError">true if the exception is expected after the merge failed, false if log is expected.
                </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFieldMergeUtil.MergeTwoFieldsWithTheSameNames(iText.Forms.Fields.PdfFormField,iText.Forms.Fields.PdfFormField,System.Boolean)">
            <summary>This method merges different values from two field dictionaries into the first one and combines kids.
                </summary>
            <param name="firstField">a field into which dictionary all values will be merged.</param>
            <param name="secondField">a field whose values should be merged into the first dictionary.</param>
            <param name="throwExceptionOnError">true if the exception is expected after the merge failed, false if log is expected.
                </param>
            <returns>true if fields is successfully merged, false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFieldMergeUtil.GetPartialName(iText.Forms.Fields.AbstractPdfFormField)">
            <summary>Gets partial name for the field dictionary.</summary>
            <param name="field">field to get name from.</param>
            <returns>
            field partial name. Also, null if passed dictionary is a pure widget,
            empty string in case it is a field with no /T entry.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFieldMergeUtil.ProcessDirtyAnnotations(iText.Forms.Fields.PdfFormField,System.Boolean)">
            <summary>Sometimes widgets contain field related keys, and they are the same as these field keys at parent.
                </summary>
            <remarks>
            Sometimes widgets contain field related keys, and they are the same as these field keys at parent.
            During merge process we get something like: ParentField
            <c>(/DA &lt;DA1&gt; /Ft &lt;Tx&gt; /T &lt;test&gt; /Kids &lt;Field&gt;) -&gt;</c>
            Field
            <c>(/DA &lt;DA1&gt; /Kids &lt;Annotation&gt;) -&gt;</c>
            Annotation (without any form fields)
            <para />
            This method combines ParentField with Field.
            </remarks>
            <param name="parentField">
            a field whose form field kids should be checked and merged with parent in case
            all their dictionary values (except Parent and Kids) are the same
            or parent is a radio button.
            </param>
            <param name="throwExceptionOnError">true if the exception is expected after the merge failed, false if log is expected.
                </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfFormFieldMergeUtil.MergeFormFields(iText.Forms.Fields.PdfFormField,iText.Forms.Fields.PdfFormField,System.Boolean)">
            <summary>This method combines two form fields.</summary>
            <param name="firstField">first form field to be merged</param>
            <param name="secondField">second form field to be merged</param>
            <param name="throwExceptionOnError">
            define whether exception (true) or log (false) is expected in case kid with
            the same name exists and merge of two kids failed.
            </param>
        </member>
        <member name="T:iText.Forms.Fields.PdfSignatureFormField">
            <summary>An AcroForm field containing signature data.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfSignatureFormField.reuseAppearance">
            <summary>Indicates if we need to reuse the existing appearance as a background layer.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfSignatureFormField.ignorePageRotation">
            <summary>Indicates if we need to ignore page rotation for the signature field annotation.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfSignatureFormField.n0">
            <summary>Background level of the signature appearance.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfSignatureFormField.n2">
            <summary>Signature appearance layer that contains information about the signature.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a minimal
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>.
            </summary>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.#ctor(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a signature form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>.
            </param>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a signature form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a signature form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="pdfObject">the dictionary to be wrapped, must have an indirect reference.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.GetFormType">
            <summary>Returns <c>Sig</c>, the form type for signature form fields.</summary>
            <returns>
            the form type, as a
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.SetValue(iText.Kernel.Pdf.PdfObject)">
            <summary>Adds the signature to the signature field.</summary>
            <param name="value">the signature to be contained in the signature field, or an indirect reference to it</param>
            <returns>the edited field</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.GetSigFieldLockDictionary">
            <summary>
            Gets the
            <see cref="T:iText.Forms.PdfSigFieldLock"/>
            , which contains fields that
            must be locked if the document is signed.
            </summary>
            <returns>a dictionary containing locked fields.</returns>
            <seealso cref="T:iText.Forms.PdfSigFieldLock"/>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.SetBackgroundLayer(iText.Kernel.Pdf.Xobject.PdfFormXObject)">
            <summary>Sets the background layer that is present when creating the signature field.</summary>
            <param name="n0">layer xObject.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.SetSignatureAppearanceLayer(iText.Kernel.Pdf.Xobject.PdfFormXObject)">
            <summary>
            Sets the signature appearance layer that contains information about the signature, e.g. the line art for the
            handwritten signature, the text giving the signer’s name, date, reason, location and so on.
            </summary>
            <param name="n2">layer xObject.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.SetReuseAppearance(System.Boolean)">
            <summary>Indicates that the existing appearances needs to be reused as a background.</summary>
            <param name="reuseAppearance">is an appearances reusing flag value to set.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.SetIgnorePageRotation(System.Boolean)">
            <summary>Sets the boolean value which indicates if page rotation should be ignored for the signature appearance.
                </summary>
            <remarks>
            Sets the boolean value which indicates if page rotation should be ignored for the signature appearance.
            <para />
            Default value is
            <see langword="true"/>.
            </remarks>
            <param name="ignore">boolean value to set.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.GetBackgroundLayer">
            <summary>Gets the background layer that is present when creating the signature field if it was set.</summary>
            <returns>n0 layer xObject.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.GetSignatureAppearanceLayer">
            <summary>Gets the signature appearance layer that contains information about the signature if it was set.</summary>
            <returns>n2 layer xObject.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.IsReuseAppearance">
            <summary>Indicates if the existing appearances needs to be reused as a background.</summary>
            <returns>appearances reusing flag value.</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfSignatureFormField.IsPageRotationIgnored">
            <summary>Indicates if page rotation should be ignored for the signature appearance.</summary>
            <returns>the boolean value which indicates if we need to ignore page rotation for the signature appearance.
                </returns>
        </member>
        <member name="T:iText.Forms.Fields.PdfTextFormField">
            <summary>An AcroForm field containing textual data.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfTextFormField.FF_FILE_SELECT">
            <summary>constant which determines whether field currently represents a path.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfTextFormField.FF_DO_NOT_SPELL_CHECK">
            <summary>constant which determines whether spell-checking is currently enabled</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfTextFormField.FF_DO_NOT_SCROLL">
            <summary>constant which determines whether longer texts are currently allowed.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfTextFormField.FF_COMB">
            <summary>constant which determines maximum length of the field's text.</summary>
        </member>
        <member name="F:iText.Forms.Fields.PdfTextFormField.FF_RICH_TEXT">
            <summary>constant which determines whether text is currently represented as rich text.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a minimal
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>.
            </summary>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.#ctor(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a text form field as a parent of a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfWidgetAnnotation"/>.
            </summary>
            <param name="widget">
            The widget which will be a kid of the
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>.
            </param>
            <param name="pdfDocument">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates a text form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <remarks>
            Creates a text form field as a wrapper object around a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            This
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            must be an indirect object.
            </remarks>
            <param name="pdfObject">the dictionary to be wrapped, must have an indirect reference.</param>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.GetFormType">
            <summary>Returns <c>Tx</c>, the form type for textual form fields.</summary>
            <returns>
            the form type, as a
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.SetMultiline(System.Boolean)">
            <summary>If true, the field can contain multiple lines of text; if false, the field?s text is restricted to a single line.
                </summary>
            <param name="multiline">whether or not the file can contain multiple lines of text</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.SetPassword(System.Boolean)">
            <summary>If true, the field is intended for entering a secure password that should not be echoed visibly to the screen.
                </summary>
            <remarks>
            If true, the field is intended for entering a secure password that should not be echoed visibly to the screen.
            Characters typed from the keyboard should instead be echoed in some unreadable form, such as asterisks or bullet characters.
            </remarks>
            <param name="password">whether or not to obscure the typed characters</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.IsFileSelect">
            <summary>
            If true, the text entered in the field represents the pathname of a file
            whose contents are to be submitted as the value of the field.
            </summary>
            <returns>whether or not this field currently represents a path</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.SetFileSelect(System.Boolean)">
            <summary>
            If true, the text entered in the field represents the pathname of a file
            whose contents are to be submitted as the value of the field.
            </summary>
            <param name="fileSelect">whether or not this field should represent a path</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.IsSpellCheck">
            <summary>If true, text entered in the field is spell-checked.</summary>
            <returns>whether or not spell-checking is currently enabled</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.SetSpellCheck(System.Boolean)">
            <summary>If true, text entered in the field is spell-checked.</summary>
            <param name="spellCheck">whether or not to spell-check</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.IsScroll">
            <summary>
            If true, the field scrolls (horizontally for single-line fields, vertically for multiple-line fields)
            to accommodate more text than fits within its annotation rectangle.
            </summary>
            <remarks>
            If true, the field scrolls (horizontally for single-line fields, vertically for multiple-line fields)
            to accommodate more text than fits within its annotation rectangle.
            Once the field is full, no further text is accepted.
            </remarks>
            <returns>whether or not longer texts are currently allowed</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.SetScroll(System.Boolean)">
            <summary>
            If true, the field scrolls (horizontally for single-line fields, vertically for multiple-line fields)
            to accommodate more text than fits within its annotation rectangle.
            </summary>
            <remarks>
            If true, the field scrolls (horizontally for single-line fields, vertically for multiple-line fields)
            to accommodate more text than fits within its annotation rectangle.
            Once the field is full, no further text is accepted.
            </remarks>
            <param name="scroll">whether or not to allow longer texts</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.IsComb">
            <summary>
            Meaningful only if the MaxLen entry is present in the text field dictionary
            and if the Multiline, Password, and FileSelect flags are clear.
            </summary>
            <remarks>
            Meaningful only if the MaxLen entry is present in the text field dictionary
            and if the Multiline, Password, and FileSelect flags are clear.
            If true, the field is automatically divided into as many equally spaced positions,
            or combs, as the value of MaxLen, and the text is laid out into those combs.
            </remarks>
            <returns>
            
            <see langword="true"/>
            if combing is enabled,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.SetComb(System.Boolean)">
            <summary>
            Meaningful only if the MaxLen entry is present in the text field dictionary
            and if the Multiline, Password, and FileSelect flags are clear.
            </summary>
            <remarks>
            Meaningful only if the MaxLen entry is present in the text field dictionary
            and if the Multiline, Password, and FileSelect flags are clear.
            If true, the field is automatically divided into as many equally spaced positions,
            or combs, as the value of MaxLen, and the text is laid out into those combs.
            </remarks>
            <param name="comb">whether or not to enable combing</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.IsRichText">
            <summary>If true, the value of this field should be represented as a rich text string.</summary>
            <remarks>
            If true, the value of this field should be represented as a rich text string.
            If the field has a value, the RV entry of the field dictionary specifies the rich text string.
            </remarks>
            <returns>whether or not text is currently represented as rich text</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.SetRichText(System.Boolean)">
            <summary>If true, the value of this field should be represented as a rich text string.</summary>
            <remarks>
            If true, the value of this field should be represented as a rich text string.
            If the field has a value, the RV entry of the field dictionary specifies the rich text string.
            </remarks>
            <param name="richText">whether or not to represent text as rich text</param>
            <returns>
            current
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.GetMaxLen">
            <summary>Gets the maximum length of the field's text, in characters.</summary>
            <remarks>
            Gets the maximum length of the field's text, in characters.
            This is an optional parameter, so if it is not specified, 0 value will be returned.
            </remarks>
            <returns>the current maximum text length</returns>
        </member>
        <member name="M:iText.Forms.Fields.PdfTextFormField.SetMaxLen(System.Int32)">
            <summary>Sets the maximum length of the field's text, in characters.</summary>
            <param name="maxLen">the maximum text length</param>
            <returns>current</returns>
        </member>
        <member name="T:iText.Forms.Fields.Properties.CheckBoxType">
            <summary>Enum representing possible values for check box mark drawing.</summary>
        </member>
        <member name="T:iText.Forms.Fields.Properties.SignedAppearanceText">
            <summary>Class representing the signature text identifying the signer.</summary>
        </member>
        <member name="F:iText.Forms.Fields.Properties.SignedAppearanceText.reason">
            <summary>The reason for signing.</summary>
        </member>
        <member name="F:iText.Forms.Fields.Properties.SignedAppearanceText.location">
            <summary>Holds value of property location.</summary>
        </member>
        <member name="F:iText.Forms.Fields.Properties.SignedAppearanceText.signedBy">
            <summary>The name of the signer from the certificate.</summary>
        </member>
        <member name="F:iText.Forms.Fields.Properties.SignedAppearanceText.signDate">
            <summary>Holds value of property signDate.</summary>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.#ctor">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.GetReasonLine">
            <summary>Returns the signing reason.</summary>
            <returns>reason for signing.</returns>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.SetReasonLine(System.String)">
            <summary>Sets the signing reason.</summary>
            <remarks>
            Sets the signing reason.
            <para />
            Note, that this reason won't be passed to the signature dictionary. If none is set, value set by
            <c>PdfSigner#setReason</c>
            will be used.
            </remarks>
            <param name="reason">signing reason.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.GetLocationLine">
            <summary>Returns the signing location.</summary>
            <returns>signing location.</returns>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.SetLocationLine(System.String)">
            <summary>Sets the signing location.</summary>
            <remarks>
            Sets the signing location.
            <para />
            Note, that this location won't be passed to the signature dictionary. If none is set, value set by
            <c>PdfSigner#setLocation</c>
            will be used.
            </remarks>
            <param name="location">new signing location</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.SetSignedBy(System.String)">
            <summary>Sets the name of the signer from the certificate.</summary>
            <remarks>
            Sets the name of the signer from the certificate.
            <para />
            Note, that the signer name will be replaced by the one from the signing certificate during the actual signing.
            </remarks>
            <param name="signedBy">name of the signer</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.GetSignedBy">
            <summary>Gets the name of the signer from the certificate.</summary>
            <returns>signedBy name of the signer</returns>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.GetSignDate">
            <summary>Returns the signature date.</summary>
            <returns>the signature date</returns>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.SetSignDate(System.DateTime)">
            <summary>Sets the signature date.</summary>
            <remarks>
            Sets the signature date.
            <para />
            Note, that the signing date will be replaced by the one from the
            <c>PdfSigner</c>
            during the signing.
            </remarks>
            <param name="signDate">new signature date</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.Properties.SignedAppearanceText.GenerateDescriptionText">
            <summary>Generates the signature description text based on the provided parameters.</summary>
            <returns>signature description</returns>
        </member>
        <member name="T:iText.Forms.Fields.PushButtonFormFieldBuilder">
            <summary>Builder for push button form field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.PushButtonFormFieldBuilder.#ctor(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>
            Creates builder for
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            creation.
            </summary>
            <param name="document">document to be used for form field creation</param>
            <param name="formFieldName">name of the form field</param>
        </member>
        <member name="M:iText.Forms.Fields.PushButtonFormFieldBuilder.GetCaption">
            <summary>Gets caption for button form field creation.</summary>
            <returns>caption value to be used for form field creation</returns>
        </member>
        <member name="M:iText.Forms.Fields.PushButtonFormFieldBuilder.SetCaption(System.String)">
            <summary>Sets caption for button form field creation.</summary>
            <param name="caption">caption value to be used for form field creation</param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.PushButtonFormFieldBuilder.CreatePushButton">
            <summary>Creates push button form field base on provided parameters.</summary>
            <returns>
            new
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.PushButtonFormFieldBuilder.GetThis">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Fields.RadioFormFieldBuilder">
            <summary>Builder for radio form field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.RadioFormFieldBuilder.#ctor(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>Creates builder for radio form field creation.</summary>
            <param name="document">document to be used for form field creation</param>
            <param name="radioGroupFormFieldName">name of the form field</param>
        </member>
        <member name="M:iText.Forms.Fields.RadioFormFieldBuilder.CreateRadioGroup">
            <summary>Creates radio group form field instance based on provided parameters.</summary>
            <returns>
            new
            <see cref="T:iText.Forms.Fields.PdfButtonFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.RadioFormFieldBuilder.CreateRadioButton(System.String,iText.Kernel.Geom.Rectangle)">
            <summary>Creates radio button form field instance based on provided parameters.</summary>
            <param name="appearanceName">name of the "on" appearance state.</param>
            <param name="rectangle">the place where the widget should be placed.</param>
            <returns>new radio button instance</returns>
        </member>
        <member name="M:iText.Forms.Fields.RadioFormFieldBuilder.GetThis">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Fields.SignatureFormFieldBuilder">
            <summary>Builder for signature form field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.SignatureFormFieldBuilder.#ctor(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>
            Creates builder for
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            creation.
            </summary>
            <param name="document">document to be used for form field creation</param>
            <param name="formFieldName">name of the form field</param>
        </member>
        <member name="M:iText.Forms.Fields.SignatureFormFieldBuilder.CreateSignature">
            <summary>Creates signature form field based on provided parameters.</summary>
            <returns>
            new
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.SignatureFormFieldBuilder.GetThis">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Fields.TerminalFormFieldBuilder`1">
            <summary>Builder for terminal form field.</summary>
            <typeparam name="T">specific terminal form field builder which extends this class.</typeparam>
        </member>
        <member name="F:iText.Forms.Fields.TerminalFormFieldBuilder`1.widgetRectangle">
            <summary>Rectangle which defines widget placement.</summary>
        </member>
        <member name="F:iText.Forms.Fields.TerminalFormFieldBuilder`1.page">
            <summary>Page number to place widget at.</summary>
        </member>
        <member name="M:iText.Forms.Fields.TerminalFormFieldBuilder`1.#ctor(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>Creates builder for terminal form field creation.</summary>
            <param name="document">document to be used for form field creation</param>
            <param name="formFieldName">name of the form field</param>
        </member>
        <member name="M:iText.Forms.Fields.TerminalFormFieldBuilder`1.GetWidgetRectangle">
            <summary>Gets rectangle which defines widget's placement.</summary>
            <returns>
            instance of
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            for widget placement
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.TerminalFormFieldBuilder`1.GetPage">
            <summary>Gets page to be used for widget creation.</summary>
            <returns>number of page to place widget at</returns>
        </member>
        <member name="M:iText.Forms.Fields.TerminalFormFieldBuilder`1.SetPage(iText.Kernel.Pdf.PdfPage)">
            <summary>Sets page to be used for widget creation.</summary>
            <param name="page">
            instance of
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            . Shall belong to already provided
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            </param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.TerminalFormFieldBuilder`1.SetPage(System.Int32)">
            <summary>Sets page to be used for widget creation.</summary>
            <param name="page">number of page to place widget at</param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.TerminalFormFieldBuilder`1.SetWidgetRectangle(iText.Kernel.Geom.Rectangle)">
            <summary>Sets rectangle which defines widget's placement.</summary>
            <param name="widgetRectangle">
            instance of
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            for widget placement
            </param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.TerminalFormFieldBuilder`1.SetFont(iText.Kernel.Font.PdfFont)">
            <summary>Set font to be used for form field creation.</summary>
            <param name="font">
            instance of
            <see cref="T:iText.Kernel.Font.PdfFont"/>.
            </param>
            <returns>this builder</returns>
        </member>
        <member name="M:iText.Forms.Fields.TerminalFormFieldBuilder`1.GetFont">
            <summary>Get font to be used for form field creation.</summary>
            <returns>
            instance of
            <see cref="T:iText.Kernel.Font.PdfFont"/>.
            </returns>
        </member>
        <member name="T:iText.Forms.Fields.TextFormFieldBuilder">
            <summary>Builder for text form field.</summary>
        </member>
        <member name="M:iText.Forms.Fields.TextFormFieldBuilder.#ctor(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>
            Creates builder for
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            creation.
            </summary>
            <param name="document">document to be used for form field creation</param>
            <param name="formFieldName">name of the form field</param>
        </member>
        <member name="M:iText.Forms.Fields.TextFormFieldBuilder.CreateText">
            <summary>Creates text form field based on provided parameters.</summary>
            <returns>
            new
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.TextFormFieldBuilder.CreateMultilineText">
            <summary>Creates multiline text form field based on provided parameters.</summary>
            <returns>
            new
            <see cref="T:iText.Forms.Fields.PdfTextFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Fields.TextFormFieldBuilder.GetThis">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.FormDefaultAccessibilityProperties">
            <summary>
            The
            <see cref="T:iText.Forms.FormDefaultAccessibilityProperties"/>
            class is used to create a specific forms related instance of the
            <see cref="T:iText.Kernel.Pdf.Tagutils.DefaultAccessibilityProperties"/>
            class.
            </summary>
        </member>
        <member name="F:iText.Forms.FormDefaultAccessibilityProperties.FORM_FIELD_RADIO">
            <summary>Represents the role: radio.</summary>
        </member>
        <member name="F:iText.Forms.FormDefaultAccessibilityProperties.FORM_FIELD_CHECK">
            <summary>Represents the role: Checkbox.</summary>
        </member>
        <member name="F:iText.Forms.FormDefaultAccessibilityProperties.FORM_FIELD_PUSH_BUTTON">
            <summary>Represents the role: PushButton.</summary>
        </member>
        <member name="F:iText.Forms.FormDefaultAccessibilityProperties.FORM_FIELD_LIST_BOX">
            <summary>Represents the role: ListBox.</summary>
        </member>
        <member name="F:iText.Forms.FormDefaultAccessibilityProperties.FORM_FIELD_TEXT">
            <summary>Represents the role: Text.</summary>
            <remarks>Represents the role: Text. This can be passwords, text areas, etc.</remarks>
        </member>
        <member name="M:iText.Forms.FormDefaultAccessibilityProperties.#ctor(System.String)">
            <summary>
            Instantiates a new
            <see cref="T:iText.Forms.FormDefaultAccessibilityProperties"></see>
            instance based on structure element role.
            </summary>
            <param name="formFieldType">the type of the formField</param>
        </member>
        <member name="M:iText.Forms.FormDefaultAccessibilityProperties.UpdateCheckedValue(iText.Layout.IPropertyContainer)">
            <summary>
            Updates the checked value of the form field based on the
            <see cref="F:iText.Forms.Form.FormProperty.FORM_FIELD_CHECKED"/>
            property.
            </summary>
            <remarks>
            Updates the checked value of the form field based on the
            <see cref="F:iText.Forms.Form.FormProperty.FORM_FIELD_CHECKED"/>
            property.
            If no such property is found, the checked value is set to "off".
            </remarks>
            <param name="element">
            The element which contains a
            <see cref="F:iText.Forms.Form.FormProperty.FORM_FIELD_CHECKED"/>
            property.
            </param>
        </member>
        <member name="T:iText.Forms.Form.Element.AbstractSelectField">
            <summary>An abstract class for fields that represents a control for selecting one or several of the provided options.
                </summary>
        </member>
        <member name="M:iText.Forms.Form.Element.AbstractSelectField.#ctor(System.String)">
            <summary>
            Instantiates a new
            <see cref="T:iText.Forms.Form.Element.AbstractSelectField"/>
            instance.
            </summary>
            <param name="id">the id of the field</param>
        </member>
        <member name="M:iText.Forms.Form.Element.AbstractSelectField.AddOption(iText.Forms.Form.Element.SelectFieldItem)">
            <summary>Add an option to the element.</summary>
            <param name="option">
            a
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>
            </param>
        </member>
        <member name="M:iText.Forms.Form.Element.AbstractSelectField.AddOption(iText.Forms.Form.Element.SelectFieldItem,System.Boolean)">
            <summary>Add an option to the element.</summary>
            <param name="option">
            a
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>
            </param>
            <param name="selected">
            
            <see langword="true"/>
            is the option if selected,
            <see langword="false"/>
            otherwise
            </param>
        </member>
        <member name="M:iText.Forms.Form.Element.AbstractSelectField.GetOptions">
            <summary>
            Get a list of
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>.
            </summary>
            <returns>a list of options.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.AbstractSelectField.OptionsCount">
            <summary>Gets the total amount of options available.</summary>
            <returns>the number of options in the element.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.AbstractSelectField.HasOptions">
            <summary>Checks if the element has any options.</summary>
            <returns>true if the element has options, false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.AbstractSelectField.GetOption(System.String)">
            <summary>
            Get an option
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>
            by its string value.
            </summary>
            <param name="value">string value to find an option by</param>
            <returns>
            a
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.AbstractSelectField.HasExportAndDisplayValues">
            <summary>Checks if the field has options with export and display values.</summary>
            <returns>
            
            <see langword="true"/>
            if the field has options with export and display values,
            <see langword="false"/>
            otherwise.
            </returns>
        </member>
        <member name="T:iText.Forms.Form.Element.Button">
            <summary>
            Extension of the
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            class representing a button in html.
            </summary>
        </member>
        <member name="F:iText.Forms.Form.Element.Button.singleLine">
            <summary>Indicates if it's the button with only single line caption.</summary>
        </member>
        <member name="M:iText.Forms.Form.Element.Button.#ctor(System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Element.Button"/>
            instance.
            </summary>
            <param name="id">the id</param>
        </member>
        <member name="M:iText.Forms.Form.Element.Button.Add(iText.Layout.Element.IBlockElement)">
            <summary>Adds any block element to the div's contents.</summary>
            <param name="element">
            a
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            </param>
            <returns>this Element</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.Button.Add(iText.Layout.Element.Image)">
            <summary>Adds an image to the div's contents.</summary>
            <param name="element">
            an
            <see cref="T:iText.Layout.Element.Image"/>
            </param>
            <returns>this Element</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.Button.IsSingleLine">
            <summary>Indicates if this element represents an input with type button in html.</summary>
            <returns>true if it's the button with only one line caption.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.Button.SetSingleLineValue(System.String)">
            <summary>Sets passed string value to the single line button caption.</summary>
            <remarks>
            Sets passed string value to the single line button caption.
            Value will be clipped if it is not fit into single line. For multiple line value
            use
            <see cref="M:iText.Forms.Form.Element.Button.SetValue(System.String)"/>
            . Note that when adding other elements to the button
            after this method is called, this added value can be multiline.
            </remarks>
            <param name="value">string value to be set as caption.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.Button"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.Button.SetValue(System.String)">
            <summary>Adds passed string value as paragraph to the button.</summary>
            <remarks>
            Adds passed string value as paragraph to the button.
            Value can be multiline if it is not fit into single line. For single line value
            use
            <see cref="M:iText.Forms.Form.Element.Button.SetSingleLineValue(System.String)"/>
            . Note that the new value will replace all already added elements.
            </remarks>
            <param name="value">string value to be added into button.</param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.Button.GetDefaultProperty``1(System.Int32)">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.Button.GetAccessibilityProperties">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Element.Button.MakeNewRenderer">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Forms.Form.Element.CheckBox">
            <summary>
            Extension of the
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            class representing a checkbox so that
            a
            <see cref="T:iText.Forms.Form.Renderer.CheckBoxRenderer"/>
            is used instead of the default renderer for fields.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Element.CheckBox.#ctor(System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Element.CheckBox"/>
            instance.
            </summary>
            <param name="id">the id</param>
        </member>
        <member name="M:iText.Forms.Form.Element.CheckBox.SetChecked(System.Boolean)">
            <summary>Sets the checked state of the checkbox.</summary>
            <param name="checked">the checked state to set</param>
            <returns>this checkbox instance</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.CheckBox.SetPdfConformance(iText.Kernel.Pdf.PdfConformance)">
            <summary>Sets the conformance for the checkbox.</summary>
            <param name="conformance">The PDF conformance to set.</param>
            <returns>this checkbox instance</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.CheckBox.SetCheckBoxType(iText.Forms.Fields.Properties.CheckBoxType)">
            <summary>Sets the icon of the checkbox.</summary>
            <param name="checkBoxType">the type of the checkbox to set</param>
            <returns>this checkbox instance</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.CheckBox.SetSize(System.Single)">
            <summary>Sets the size of the checkbox.</summary>
            <param name="size">the size of the checkbox to set, in points</param>
            <returns>this checkbox instance</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.CheckBox.GetAccessibilityProperties">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Element.ComboBoxField">
            <summary>A field that represents a control for selecting one of the provided options.</summary>
        </member>
        <member name="M:iText.Forms.Form.Element.ComboBoxField.#ctor(System.String)">
            <summary>Creates a new select field box.</summary>
            <param name="id">the id</param>
        </member>
        <member name="M:iText.Forms.Form.Element.ComboBoxField.GetSelectedExportValue">
            <summary>Gets the export value of the selected option.</summary>
            <returns>the export value of the selected option. This may be null if no value has been selected.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.ComboBoxField.SetSelected(System.Int32)">
            <summary>Selects an option by its index.</summary>
            <remarks>Selects an option by its index. The index is zero-based.</remarks>
            <param name="index">the index of the option to select.</param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.ComboBoxField"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.ComboBoxField.SetSelected(System.String)">
            <summary>Selects an option by its export value.</summary>
            <param name="value">the export value of the option to select.</param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.ComboBoxField"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.ComboBoxField.SetSelected(iText.Forms.Form.Element.SelectFieldItem)">
            <summary>Selects an option by its value.</summary>
            <remarks>
            Selects an option by its value. This will use the export value of the
            option to match it to existing options.
            </remarks>
            <param name="item">the option to select.</param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.ComboBoxField"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.ComboBoxField.AddOption(iText.Forms.Form.Element.SelectFieldItem)">
            <summary>Add an option to the element.</summary>
            <param name="option">
            a
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>.
            </param>
        </member>
        <member name="M:iText.Forms.Form.Element.ComboBoxField.GetSelectedOption">
            <summary>Gets the selected option.</summary>
            <returns>the selected option. This may be null if no option has been selected.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.ComboBoxField.GetAccessibilityProperties">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Element.FormField`1">
            <summary>
            Implementation of the
            <see cref="T:iText.Layout.Element.AbstractElement`1"/>
            class for form fields.
            </summary>
            <typeparam name="T">the generic type of the form field (e.g. input field, button, text area)</typeparam>
        </member>
        <member name="F:iText.Forms.Form.Element.FormField`1.id">
            <summary>The id.</summary>
        </member>
        <member name="F:iText.Forms.Form.Element.FormField`1.tagProperties">
            <summary>The tag properties.</summary>
        </member>
        <member name="M:iText.Forms.Form.Element.FormField`1.#ctor(System.String)">
            <summary>
            Instantiates a new
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            instance.
            </summary>
            <param name="id">the id</param>
        </member>
        <member name="M:iText.Forms.Form.Element.FormField`1.SetSize(System.Single)">
            <summary>Sets the form field's width and height.</summary>
            <param name="size">form field's width and height.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            element.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.FormField`1.SetWidth(System.Single)">
            <summary><inheritDoc/></summary>
            <param name="width">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.FormField`1.SetHeight(System.Single)">
            <summary><inheritDoc/></summary>
            <param name="height">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.FormField`1.SetValue(System.String)">
            <summary><inheritDoc/></summary>
            <param name="value">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.FormField`1.GetId">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.FormField`1.GetDefaultProperty``1(System.Int32)">
            <summary><inheritDoc/></summary>
            <param name="property">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.FormField`1.SetInteractive(System.Boolean)">
            <summary><inheritDoc/></summary>
            <param name="interactive">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.FormField`1.SetAlternativeDescription(System.String)">
            <summary>Sets the form field's alternative description.</summary>
            <param name="alternativeDescription">form field's alternative description.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            element.
            </returns>
        </member>
        <member name="T:iText.Forms.Form.Element.IFormField">
            <summary>Common interface for HTML form elements.</summary>
        </member>
        <member name="M:iText.Forms.Form.Element.IFormField.SetValue(System.String)">
            <summary>
            Sets the
            <see cref="F:iText.Forms.Form.FormProperty.FORM_FIELD_VALUE"/>
            property.
            </summary>
            <param name="value">string value of the property to be set</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.IFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.IFormField.SetInteractive(System.Boolean)">
            <summary>Set the form field to be interactive and added into Acroform instead of drawing it on a page.</summary>
            <param name="interactive">
            
            <see langword="true"/>
            if the form field element shall be added into Acroform,
            <see langword="false"/>
            otherwise.
            By default, the form field element is not interactive and drawn on a page
            </param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.IFormField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.IFormField.GetId">
            <summary>Gets the id.</summary>
            <returns>the id</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.IFormField.SetWidth(System.Single)">
            <summary>Set the form field's width.</summary>
            <param name="width">form field's width</param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            element
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.IFormField.SetHeight(System.Single)">
            <summary>Set the form field's height.</summary>
            <param name="height">form field's height</param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            element
            </returns>
        </member>
        <member name="T:iText.Forms.Form.Element.InputField">
            <summary>
            Extension of the
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            class representing a button so that
            a
            <see cref="T:iText.Forms.Form.Renderer.InputFieldRenderer"/>
            is used.
            </summary>
        </member>
        <member name="F:iText.Forms.Form.Element.InputField.X_OFFSET">
            <summary>Default padding X offset.</summary>
        </member>
        <member name="F:iText.Forms.Form.Element.InputField.placeholder">
            <summary>The placeholder paragraph.</summary>
        </member>
        <member name="F:iText.Forms.Form.Element.InputField.rotation">
            <summary>Field rotation, counterclockwise.</summary>
            <remarks>Field rotation, counterclockwise. Must be a multiple of 90 degrees.</remarks>
        </member>
        <member name="M:iText.Forms.Form.Element.InputField.#ctor(System.String)">
            <summary>Creates a new input field.</summary>
            <param name="id">the id</param>
        </member>
        <member name="M:iText.Forms.Form.Element.InputField.UseAsPassword(System.Boolean)">
            <summary>Determines, whether the input field will be password.</summary>
            <remarks>
            Determines, whether the input field will be password.
            <para />
            Usually means that instead of glyphs '*' will be shown in case of flatten field.
            <para />
            If the field is not flatten, value will be ignored.
            </remarks>
            <param name="isPassword">
            
            <see langword="true"/>
            is this field shall be considered as password,
            <see langword="false"/>
            otherwise
            </param>
            <returns>this input field</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.InputField.GetPlaceholder">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Element.InputField.SetPlaceholder(iText.Layout.Element.Paragraph)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Element.InputField.GetRotation">
            <summary>Get rotation.</summary>
            <returns>rotation value.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.InputField.SetRotation(System.Int32)">
            <summary>Set rotation of the input field.</summary>
            <param name="rotation">
            new rotation value, counterclockwise. Must be a multiple of 90 degrees.
            It has sense only in interactive mode, see
            <see cref="M:iText.Forms.Form.Element.FormField`1.SetInteractive(System.Boolean)"/>
            </param>
            <returns>
            the edited
            <see cref="T:iText.Forms.Form.Element.InputField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.InputField.SetComb(System.Boolean)">
            <summary>
            Sets
            <c>Comb</c>
            flag for the text field.
            </summary>
            <remarks>
            Sets
            <c>Comb</c>
            flag for the text field. Meaningful only if the MaxLen entry is present in the text field
            dictionary and if the Multiline, Password and FileSelect flags are clear.
            <para />
            If true, the field is automatically divided into as many equally spaced positions, or combs,
            as the value of MaxLen, and the text is laid out into those combs.
            </remarks>
            <param name="isComb">boolean value specifying whether to enable combing</param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.InputField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.InputField.SetMaxLen(System.Int32)">
            <summary>Sets the maximum length of the field's text, in characters.</summary>
            <param name="maxLen">the current maximum text length</param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.InputField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.InputField.GetAccessibilityProperties">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Element.IPlaceholderable">
            <summary>A marker interface that specifies that the layout object has placeholder.</summary>
        </member>
        <member name="M:iText.Forms.Form.Element.IPlaceholderable.GetPlaceholder">
            <summary>Gets the placeholder paragraph.</summary>
            <returns>the placeholder paragraph</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.IPlaceholderable.SetPlaceholder(iText.Layout.Element.Paragraph)">
            <summary>Sets the placeholder paragraph.</summary>
            <param name="placeholder">the paragraph to be used as placeholder</param>
        </member>
        <member name="T:iText.Forms.Form.Element.ListBoxField">
            <summary>A field that represents a control for selecting one or several of the provided options.</summary>
        </member>
        <member name="M:iText.Forms.Form.Element.ListBoxField.#ctor(System.String,System.Int32,System.Boolean)">
            <summary>Create a new list box field.</summary>
            <param name="size">
            the size of the list box, which will define the height of visible properties,
            shall be greater than zero
            </param>
            <param name="allowMultipleSelection">
            a boolean flag that defines whether multiple options are allowed
            to be selected at once
            </param>
            <param name="id">the id</param>
        </member>
        <member name="M:iText.Forms.Form.Element.ListBoxField.AddOption(System.String)">
            <summary>
            Add an option for
            <see cref="T:iText.Forms.Form.Element.ListBoxField"/>.
            </summary>
            <remarks>
            Add an option for
            <see cref="T:iText.Forms.Form.Element.ListBoxField"/>
            . The option is not selected.
            </remarks>
            <param name="option">string representation of the option.</param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.ListBoxField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.ListBoxField.AddOption(System.String,System.Boolean)">
            <summary>
            Add an option for
            <see cref="T:iText.Forms.Form.Element.ListBoxField"/>.
            </summary>
            <param name="option">string representation of the option.</param>
            <param name="selected">
            
            <see langword="true"/>
            is the option if selected,
            <see langword="false"/>
            otherwise.
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.ListBoxField"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.ListBoxField.GetSelectedStrings">
            <summary>Get a list of selected options.</summary>
            <returns>a list of display values of selected options.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.ListBoxField.SetTopIndex(System.Int32)">
            <summary>Sets the index of the first visible option in a scrollable list.</summary>
            <param name="topIndex">the index of the first option</param>
            <returns>
            this
            <see cref="T:iText.Forms.Form.Element.ListBoxField"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.ListBoxField.GetAccessibilityProperties">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Element.Radio">
            <summary>
            Extension of the
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            class representing a radio button so that
            a
            <see cref="T:iText.Forms.Form.Renderer.RadioRenderer"/>
            is used instead of the default renderer for fields.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Element.Radio.#ctor(System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Element.Radio"/>
            instance.
            </summary>
            <param name="id">the id.</param>
        </member>
        <member name="M:iText.Forms.Form.Element.Radio.#ctor(System.String,System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Element.Radio"/>
            instance.
            </summary>
            <param name="id">the id.</param>
            <param name="radioGroupName">
            the name of the radio group the radio button belongs to. It has sense only in case
            this Radio element will not be rendered but Acroform field will be created instead.
            </param>
        </member>
        <member name="M:iText.Forms.Form.Element.Radio.SetChecked(System.Boolean)">
            <summary>Sets the state of the radio button.</summary>
            <param name="checked">
            
            <see langword="true"/>
            if the radio button shall be checked,
            <see langword="false"/>
            otherwise.
            By default, the radio button is unchecked.
            </param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.Radio"/>
            button.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.Radio.GetProperty``1(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Element.Radio.GetAccessibilityProperties">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Element.SelectFieldItem">
            <summary>A field that represents a control for selecting one of the provided options.</summary>
            <remarks>
            A field that represents a control for selecting one of the provided options.
            It is used in the
            <see cref="T:iText.Forms.Form.Element.ComboBoxField"/>
            class.
            </remarks>
        </member>
        <member name="F:iText.Forms.Form.Element.SelectFieldItem.exportValue">
            <summary>The export value of the item.</summary>
            <remarks>
            The export value of the item.
            this is the value of the form which will be submitted. If the display value is not set, the export value will be
            used as display value.
            </remarks>
        </member>
        <member name="F:iText.Forms.Form.Element.SelectFieldItem.displayValue">
            <summary>The display value of the item.</summary>
            <remarks>
            The display value of the item.
            This is the value which will be displayed in the dropdown.
            </remarks>
        </member>
        <member name="F:iText.Forms.Form.Element.SelectFieldItem.optionElement">
            <summary>The option element of the item.</summary>
            <remarks>
            The option element of the item.
            This is the element which will be displayed in the dropdown.
            It allows for customization
            </remarks>
        </member>
        <member name="M:iText.Forms.Form.Element.SelectFieldItem.#ctor(System.String,System.String)">
            <summary>
            Create a new
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>.
            </summary>
            <param name="exportValue">the export value of the item.</param>
            <param name="displayValue">the display value of the item.</param>
        </member>
        <member name="M:iText.Forms.Form.Element.SelectFieldItem.#ctor(System.String)">
            <summary>
            Create a new
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>.
            </summary>
            <param name="value">the export value of the item.</param>
        </member>
        <member name="M:iText.Forms.Form.Element.SelectFieldItem.#ctor(System.String,iText.Layout.Element.IBlockElement)">
            <summary>
            Create a new
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>.
            </summary>
            <param name="value">the export value of the item.</param>
            <param name="optionElement">the option element of the item.</param>
        </member>
        <member name="M:iText.Forms.Form.Element.SelectFieldItem.#ctor(System.String,System.String,iText.Layout.Element.IBlockElement)">
            <summary>
            Create a new
            <see cref="T:iText.Forms.Form.Element.SelectFieldItem"/>.
            </summary>
            <param name="exportValue">the export value of the item.</param>
            <param name="displayValue">the display value of the item.</param>
            <param name="optionElement">the option element of the item.</param>
        </member>
        <member name="M:iText.Forms.Form.Element.SelectFieldItem.GetExportValue">
            <summary>Get the export value of the item.</summary>
            <returns>export value.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SelectFieldItem.GetDisplayValue">
            <summary>Get the display value of the item.</summary>
            <remarks>
            Get the display value of the item.
            If the display value is not set, the export value will be used as display value.
            </remarks>
            <returns>display value.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SelectFieldItem.GetElement">
            <summary>Get the option element of the item.</summary>
            <remarks>
            Get the option element of the item.
            <para />
            This is the element which will be displayed in the dropdown.
            It allows for customization.
            </remarks>
            <returns>option element.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SelectFieldItem.HasExportAndDisplayValues">
            <summary>Check if the item has a display value.</summary>
            <remarks>Check if the item has a display value. and export value.</remarks>
            <returns>
            
            <see langword="true"/>
            if the item has both export and display values,
            <see langword="false"/>
            otherwise.
            </returns>
        </member>
        <member name="T:iText.Forms.Form.Element.SignatureFieldAppearance">
            <summary>
            Extension of the
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            class representing a signature field in PDF.
            </summary>
        </member>
        <member name="F:iText.Forms.Form.Element.SignatureFieldAppearance.DEFAULT_PADDING">
            <summary>Default paddings for the signature field.</summary>
        </member>
        <member name="F:iText.Forms.Form.Element.SignatureFieldAppearance.contentElements">
            <summary>Collection of the layout elements which will be rendered as a signature content.</summary>
        </member>
        <member name="F:iText.Forms.Form.Element.SignatureFieldAppearance.idWithDots">
            <summary>We should support signing of existing fields with dots in name, but dots are now allowed in model element id.
                </summary>
            <remarks>
            We should support signing of existing fields with dots in name, but dots are now allowed in model element id.
            So it is a placeholder for such cases.
            </remarks>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.#ctor(System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            instance.
            </summary>
            <param name="id">
            signature field name if you use this
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            in pure layout for the new
            interactive signature field creation. ID will be ignored if this
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            is used for signing or for existing signature field
            </param>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(iText.Forms.Fields.Properties.SignedAppearanceText)">
            <summary>Sets the content for this signature.</summary>
            <param name="description">
            
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance representing the signature text identifying the signer.
            </param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(System.String)">
            <summary>Sets the content for this signature.</summary>
            <param name="description">the signature text identifying the signer.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(iText.Forms.Fields.Properties.SignedAppearanceText,iText.IO.Image.ImageData)">
            <summary>Sets the content for this signature.</summary>
            <param name="description">
            
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance representing the signature text identifying the signer.
            </param>
            <param name="image">the Image object to render.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(System.String,iText.IO.Image.ImageData)">
            <summary>Sets the content for this signature.</summary>
            <param name="description">the signature text identifying the signer.</param>
            <param name="image">the Image object to render.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(iText.IO.Image.ImageData)">
            <summary>Sets the content for this signature.</summary>
            <param name="image">the Image object to render.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(System.String,iText.Forms.Fields.Properties.SignedAppearanceText)">
            <summary>Sets the content for this signature.</summary>
            <param name="signerName">the name of the signer from the certificate.</param>
            <param name="description">
            
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance representing the signature text identifying the signer.
            </param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(System.String,System.String)">
            <summary>Sets the content for this signature.</summary>
            <param name="signerName">the name of the signer from the certificate.</param>
            <param name="description">the signature text identifying the signer.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(iText.Layout.Element.Div)">
            <summary>Sets the content for this signature.</summary>
            <param name="data">the custom signature data which will be rendered.</param>
            <returns>
            this same
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.GetContentElements">
            <summary>Gets the final content for this signature.</summary>
            <returns>collection of the layout elements which will be rendered as a signature content.</returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.GetSignedAppearanceText">
            <summary>
            Gets the
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance for this signature.
            </summary>
            <returns>
            
            <see cref="T:iText.Forms.Fields.Properties.SignedAppearanceText"/>
            instance if it was set by
            <see cref="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(iText.Forms.Fields.Properties.SignedAppearanceText)"/>
            , null otherwise.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetSignerName(System.String)">
            <summary>
            Replaces the signer name for this signature if it was set by
            <see cref="M:iText.Forms.Form.Element.SignatureFieldAppearance.SetContent(iText.Forms.Fields.Properties.SignedAppearanceText)"/>.
            </summary>
            <param name="signerName">signer name to set.</param>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.GetId">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.GetAccessibilityProperties">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Element.SignatureFieldAppearance.MakeNewRenderer">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Forms.Form.Element.TextArea">
            <summary>
            Extension of the
            <see cref="T:iText.Forms.Form.Element.FormField`1"/>
            class representing a button so that
            a
            <see cref="T:iText.Forms.Form.Renderer.TextAreaRenderer"/>
            is used instead of the default renderer for fields.
            </summary>
        </member>
        <member name="F:iText.Forms.Form.Element.TextArea.X_OFFSET">
            <summary>Default padding X offset.</summary>
        </member>
        <member name="F:iText.Forms.Form.Element.TextArea.placeholder">
            <summary>The placeholder paragraph.</summary>
        </member>
        <member name="M:iText.Forms.Form.Element.TextArea.#ctor(System.String)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Element.TextArea"/>
            instance.
            </summary>
            <param name="id">the id</param>
        </member>
        <member name="M:iText.Forms.Form.Element.TextArea.GetPlaceholder">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Element.TextArea.SetPlaceholder(iText.Layout.Element.Paragraph)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Element.TextArea.GetAccessibilityProperties">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.FormProperty">
            <summary>Set of constants that will be used as keys to get and set properties.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.PROPERTY_START">
            <summary>The Constant PROPERTY_START.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_FLATTEN">
            <summary>The Constant FORM_FIELD_FLATTEN for form related properties.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_SIZE">
            <summary>The Constant FORM_FIELD_SIZE.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_VALUE">
            <summary>The Constant FORM_FIELD_VALUE.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_PASSWORD_FLAG">
            <summary>The Constant FORM_FIELD_PASSWORD_FLAG.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_COLS">
            <summary>The Constant FORM_FIELD_COLS.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_ROWS">
            <summary>The Constant FORM_FIELD_ROWS.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_CHECKED">
            <summary>The Constant FORM_FIELD_CHECKED.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_MULTIPLE">
            <summary>The Constant FORM_FIELD_MULTIPLE.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_SELECTED">
            <summary>The Constant FORM_FIELD_SELECTED.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_LABEL">
            <summary>The Constant FORM_FIELD_LABEL.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_RADIO_GROUP_NAME">
            <summary>The Constant FORM_FIELD_RADIO_GROUP_NAME.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_FIELD_RADIO_BORDER_CIRCLE">
            <summary>The Constant FORM_FIELD_RADIO_BORDER_CIRCLE.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_CHECKBOX_TYPE">
            <summary>The Constant FORM_CHECKBOX_TYPE.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.FORM_CONFORMANCE_LEVEL">
            <summary>The Constant FORM_CONFORMANCE_LEVEL.</summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.LIST_BOX_TOP_INDEX">
            <summary>The Constant LIST_BOX_TOP_INDEX representing the index of the first visible option in a scrollable list.
                </summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.TEXT_FIELD_COMB_FLAG">
            <summary>
            The Constant TEXT_FIELD_COMB_FLAG representing
            <c>Comb</c>
            flag for the text field.
            </summary>
        </member>
        <member name="F:iText.Forms.Form.FormProperty.TEXT_FIELD_MAX_LEN">
            <summary>The Constant TEXT_FIELD_MAX_LEN representing the maximum length of the field's text, in characters.
                </summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.AbstractFormFieldRenderer">
            <summary>
            Abstract
            <see cref="T:iText.Layout.Renderer.BlockRenderer"/>
            for form fields.
            </summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.flatRenderer">
            <summary>The flat renderer.</summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.#ctor(iText.Forms.Form.Element.IFormField)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.AbstractFormFieldRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.IsFlatten">
            <summary>Checks if form fields need to be flattened.</summary>
            <returns>true, if fields need to be flattened.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.GetDefaultValue">
            <summary>Gets the default value of the form field.</summary>
            <returns>the default value of the form field.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.Layout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.Draw(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.GetMinMaxWidth">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.DrawChildren(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.ApplyAccessibilityProperties(iText.Forms.Fields.PdfFormField,iText.Kernel.Pdf.PdfDocument)">
            <summary>Applies the accessibility properties to the form field.</summary>
            <param name="formField">the form field to which the accessibility properties should be applied</param>
            <param name="pdfDocument">the document to which the form field belongs</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.AdjustFieldLayout(iText.Layout.Layout.LayoutContext)">
            <summary>Adjusts the field layout.</summary>
            <param name="layoutContext">layout context</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.CreateFlatRenderer">
            <summary>Creates the flat renderer instance.</summary>
            <returns>the renderer instance.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.ApplyAcroField(iText.Layout.Renderer.DrawContext)">
            <summary>Applies the AcroField widget.</summary>
            <param name="drawContext">the draw context</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.GetModelId">
            <summary>Gets the model id.</summary>
            <returns>the model id.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.IsRendererFit(System.Single,System.Single)">
            <summary>Checks if the renderer fits a certain width and height.</summary>
            <param name="availableWidth">the available width</param>
            <param name="availableHeight">the available height</param>
            <returns>true, if the renderer fits.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.GetLang">
            <summary>
            Gets the accessibility language using
            <see cref="M:iText.Layout.Tagging.IAccessibleElement.GetAccessibilityProperties"/>.
            </summary>
            <returns>the accessibility language.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.GetConformance(iText.Kernel.Pdf.PdfDocument)">
            <summary>Gets the conformance.</summary>
            <remarks>Gets the conformance. If the conformance is not set, the conformance of the document is used.</remarks>
            <param name="document">the document</param>
            <returns>the conformance or null if the conformance is not set.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.IsLayoutBasedOnFlatRenderer">
            <summary>Determines, whether the layout is based in the renderer itself or flat renderer.</summary>
            <returns>
            
            <see langword="true"/>
            if layout is based on flat renderer, false otherwise.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractFormFieldRenderer.WriteAcroFormFieldLangAttribute(iText.Kernel.Pdf.PdfDocument)">
            <summary>Sets the form accessibility language identifier of the form element in case the document is tagged.
                </summary>
            <param name="pdfDoc">the document which contains form field</param>
        </member>
        <member name="T:iText.Forms.Form.Renderer.AbstractOneLineTextFieldRenderer">
            <summary>
            Abstract
            <see cref="T:iText.Forms.Form.Renderer.AbstractTextFieldRenderer"/>
            for a single line of text content in a form field.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractOneLineTextFieldRenderer.#ctor(iText.Forms.Form.Element.IFormField)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.AbstractOneLineTextFieldRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractOneLineTextFieldRenderer.CropContentLines(System.Collections.Generic.IList{iText.Layout.Renderer.LineRenderer},iText.Kernel.Geom.Rectangle)">
            <summary>Crops the content lines.</summary>
            <param name="lines">a list of lines</param>
            <param name="bBox">the bounding box</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractOneLineTextFieldRenderer.UpdateParagraphHeight">
            <summary>Updates the paragraph height.</summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractOneLineTextFieldRenderer.SetContentHeight(iText.Layout.Renderer.IRenderer,System.Single)">
            <summary>Sets the content height.</summary>
            <param name="flatRenderer">the flat renderer</param>
            <param name="height">the height</param>
        </member>
        <member name="T:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer">
            <summary>
            Abstract
            <see cref="T:iText.Layout.Renderer.BlockRenderer"/>
            for select form fields.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.#ctor(iText.Forms.Form.Element.AbstractSelectField)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.Layout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.Draw(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.DrawChildren(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.GetLang">
            <summary>
            Gets the accessibility language using
            <see cref="M:iText.Layout.Tagging.IAccessibleElement.GetAccessibilityProperties"/>.
            </summary>
            <returns>the accessibility language.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.WriteAcroFormFieldLangAttribute(iText.Kernel.Pdf.PdfDocument)">
            <summary>Sets the form accessibility language identifier of the form element in case the document is tagged.
                </summary>
            <param name="pdfDoc">the document which contains form field.</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.ApplyAccessibilityProperties(iText.Forms.Fields.PdfFormField,iText.Kernel.Pdf.PdfDocument)">
            <summary>Applies the accessibility properties to the form field.</summary>
            <param name="formField">the form field to which the accessibility properties should be applied</param>
            <param name="pdfDocument">the document to which the form field belongs</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.CreateFlatRenderer">
            <summary>Creates the flat renderer instance.</summary>
            <returns>
            
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.ApplyAcroField(iText.Layout.Renderer.DrawContext)">
            <summary>Applies the AcroField widget.</summary>
            <param name="drawContext">the draw context</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.IsFlatten">
            <summary>Checks if form fields need to be flattened.</summary>
            <returns>true, if fields need to be flattened.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.GetModelId">
            <summary>Gets the model id.</summary>
            <returns>the model id.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.SetupBuilderValues(iText.Forms.Fields.ChoiceFormFieldBuilder,iText.Forms.Form.Element.AbstractSelectField)">
            <summary>
            Retrieve the options from select field (can be combo box or list box field) and set them
            to the form field builder.
            </summary>
            <param name="builder">
            
            <see cref="T:iText.Forms.Fields.ChoiceFormFieldBuilder"/>
            to set options to
            </param>
            <param name="field">
            
            <see cref="T:iText.Forms.Form.Element.AbstractSelectField"/>
            to retrieve the options from
            </param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.GetFinalSelectFieldHeight(System.Single,System.Single,System.Boolean)">
            <summary>Returns final height of the select field.</summary>
            <param name="availableHeight">available height of the layout area</param>
            <param name="actualHeight">actual occupied height of the select field</param>
            <param name="isClippedHeight">indicates whether the layout area's height is clipped or not</param>
            <returns>final height of the select field.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.GetConformance(iText.Kernel.Pdf.PdfDocument)">
            <summary>Gets the conformance.</summary>
            <remarks>Gets the conformance. If the conformance is not set, the conformance of the document is used.</remarks>
            <param name="document">the document</param>
            <returns>the conformance or null if the conformance is not set.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractSelectFieldRenderer.GetOptionsMarkedSelected(iText.Layout.Renderer.IRenderer)">
            <summary>Gets options that are marked as selected from the select field options subtree.</summary>
            <param name="optionsSubTree">options subtree to get selected options</param>
            <returns>selected options list.</returns>
        </member>
        <member name="T:iText.Forms.Form.Renderer.AbstractTextFieldRenderer">
            <summary>
            Abstract
            <see cref="T:iText.Forms.Form.Renderer.AbstractFormFieldRenderer"/>
            for form fields with text content.
            </summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.AbstractTextFieldRenderer.font">
            <summary>The font to be used for the text.</summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractTextFieldRenderer.#ctor(iText.Forms.Form.Element.IFormField)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.AbstractTextFieldRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractTextFieldRenderer.CreateParagraphRenderer(System.String)">
            <summary>Creates a paragraph renderer.</summary>
            <param name="defaultValue">the default value</param>
            <returns>the renderer</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractTextFieldRenderer.ApplyDefaultFieldProperties(iText.Forms.Fields.PdfFormField)">
            <summary>Applies the default field properties.</summary>
            <param name="inputField">the input field</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractTextFieldRenderer.UpdatePdfFont(iText.Layout.Renderer.ParagraphRenderer)">
            <summary>Updates the font.</summary>
            <param name="renderer">the renderer</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractTextFieldRenderer.ApproximateFontSize(iText.Layout.Layout.LayoutContext,System.Single,System.Single)">
            <summary>Approximates font size to fit occupied area if width anf height are specified.</summary>
            <param name="layoutContext">layout context that specifies layout area.</param>
            <param name="lFontSize">minimal font size value.</param>
            <param name="rFontSize">maximum font size value.</param>
            <returns>fitting font size or -1 in case it shouldn't be approximated.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractTextFieldRenderer.AdjustNumberOfContentLines(System.Collections.Generic.IList{iText.Layout.Renderer.LineRenderer},iText.Kernel.Geom.Rectangle,System.Int32)">
            <summary>Adjust number of content lines.</summary>
            <param name="lines">the lines that need to be rendered</param>
            <param name="bBox">the bounding box</param>
            <param name="rows">the desired number of lines</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractTextFieldRenderer.AdjustNumberOfContentLines(System.Collections.Generic.IList{iText.Layout.Renderer.LineRenderer},iText.Kernel.Geom.Rectangle,System.Single)">
            <summary>Adjust number of content lines.</summary>
            <param name="lines">the lines that need to be rendered</param>
            <param name="bBox">the bounding box</param>
            <param name="height">the desired height of content</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.AbstractTextFieldRenderer.GetLowestChildBottom(iText.Layout.Renderer.IRenderer,System.Single)">
            <summary>Gets the value of the lowest bottom coordinate for all field's children recursively.</summary>
            <returns>the lowest child bottom.</returns>
        </member>
        <member name="T:iText.Forms.Form.Renderer.ButtonRenderer">
            <summary>
            The
            <see cref="T:iText.Forms.Form.Renderer.AbstractTextFieldRenderer"/>
            implementation for buttons.
            </summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.ButtonRenderer.DEFAULT_Y_OFFSET">
            <summary>Default padding Y offset for an input button.</summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.ButtonRenderer.RELATIVE_PADDING_FOR_SMALL_SIZES">
            <summary>Relative value is quite big in order to preserve visible padding on small field sizes.</summary>
            <remarks>
            Relative value is quite big in order to preserve visible padding on small field sizes.
            This constant is taken arbitrary, based on visual similarity to Acrobat behaviour.
            </remarks>
        </member>
        <member name="F:iText.Forms.Form.Renderer.ButtonRenderer.isSplit">
            <summary>Indicates if the one line caption was split.</summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.#ctor(iText.Forms.Form.Element.Button)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.ButtonRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.AdjustFieldLayout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
            <param name="layoutContext">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.IsLayoutBasedOnFlatRenderer">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.GetLastYLineRecursively">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.CreateFlatRenderer">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.GetDefaultValue">
            <summary>Gets the default value of the form field.</summary>
            <returns>the default value of the form field.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.IsRendererFit(System.Single,System.Single)">
            <summary><inheritDoc/></summary>
            <param name="availableWidth">
            
            <inheritDoc/>
            </param>
            <param name="availableHeight">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.ApplyAcroField(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
            <param name="drawContext">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.ButtonRenderer.SetContentHeight(iText.Layout.Renderer.IRenderer,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.Checkboximpl.HtmlCheckBoxRenderingStrategy">
            <summary>This class is used to draw a checkBox icon in HTML mode.</summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.Checkboximpl.HtmlCheckBoxRenderingStrategy.#ctor">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.Checkboximpl.HtmlCheckBoxRenderingStrategy"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.Checkboximpl.HtmlCheckBoxRenderingStrategy.DrawCheckBoxContent(iText.Layout.Renderer.DrawContext,iText.Forms.Form.Renderer.CheckBoxRenderer,iText.Kernel.Geom.Rectangle)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.Checkboximpl.ICheckBoxRenderingStrategy">
            <summary>This interface is used to draw a checkBox icon.</summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.Checkboximpl.ICheckBoxRenderingStrategy.DrawCheckBoxContent(iText.Layout.Renderer.DrawContext,iText.Forms.Form.Renderer.CheckBoxRenderer,iText.Kernel.Geom.Rectangle)">
            <summary>Draws a check box icon.</summary>
            <param name="drawContext">the draw context</param>
            <param name="checkBoxRenderer">the checkBox renderer</param>
            <param name="rectangle">the rectangle where the icon should be drawn</param>
        </member>
        <member name="T:iText.Forms.Form.Renderer.Checkboximpl.PdfACheckBoxRenderingStrategy">
            <summary>This class is used to draw a checkBox icon in PDF/A mode.</summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.Checkboximpl.PdfACheckBoxRenderingStrategy.#ctor">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.Checkboximpl.PdfACheckBoxRenderingStrategy"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.Checkboximpl.PdfACheckBoxRenderingStrategy.DrawCheckBoxContent(iText.Layout.Renderer.DrawContext,iText.Forms.Form.Renderer.CheckBoxRenderer,iText.Kernel.Geom.Rectangle)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.Checkboximpl.PdfCheckBoxRenderingStrategy">
            <summary>This class is used to draw a checkBox icon in PDF mode this is the default strategy for drawing a checkBox.
                </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.Checkboximpl.PdfCheckBoxRenderingStrategy.#ctor">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.Checkboximpl.PdfCheckBoxRenderingStrategy"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.Checkboximpl.PdfCheckBoxRenderingStrategy.DrawCheckBoxContent(iText.Layout.Renderer.DrawContext,iText.Forms.Form.Renderer.CheckBoxRenderer,iText.Kernel.Geom.Rectangle)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.CheckBoxRenderer">
            <summary>
            The
            <see cref="T:iText.Forms.Form.Renderer.AbstractFormFieldRenderer"/>
            implementation for checkboxes.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.#ctor(iText.Forms.Form.Element.CheckBox)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.CheckBoxRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.GetRenderingMode">
            <summary>Gets the rendering mode of the checkbox.</summary>
            <returns>the rendering mode of the checkbox</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.GetCheckBoxType">
            <summary>Gets the checkBoxType.</summary>
            <returns>the checkBoxType</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.CreateCheckBoxRenderStrategy">
            <summary>creates a ICheckBoxRenderingStrategy based on the current settings.</summary>
            <returns>the ICheckBoxRenderingStrategy</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.DrawBackground(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.DrawBorder(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.ApplyBorderBox(iText.Kernel.Geom.Rectangle,iText.Layout.Borders.Border[],System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.IsBoxChecked">
            <summary>Defines whether the box is checked or not.</summary>
            <returns>the default value of the checkbox field</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.AdjustFieldLayout(iText.Layout.Layout.LayoutContext)">
            <summary>Adjusts the field layout.</summary>
            <param name="layoutContext">layout context</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.ApplyPaddings(iText.Kernel.Geom.Rectangle,iText.Layout.Properties.UnitValue[],System.Boolean)">
            <summary>Applies given paddings to the given rectangle.</summary>
            <remarks>
            Applies given paddings to the given rectangle.
            <para />
            Checkboxes don't support setting of paddings as they are always centered.
            So that this method returns the rectangle as is.
            </remarks>
            <param name="rect">a rectangle paddings will be applied on.</param>
            <param name="paddings">the paddings to be applied on the given rectangle</param>
            <param name="reverse">
            indicates whether paddings will be applied
            inside (in case of false) or outside (in case of true) the rectangle.
            </param>
            <returns>The rectangle NOT modified by the paddings.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.CreateFlatRenderer">
            <summary>Creates a flat renderer for the checkbox.</summary>
            <returns>an IRenderer object for the flat renderer</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.ApplyAcroField(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.IsLayoutBasedOnFlatRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.CheckBoxRenderer.FlatParagraphRenderer">
            <summary>A flat renderer for the checkbox.</summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.FlatParagraphRenderer.#ctor(iText.Forms.Form.Renderer.CheckBoxRenderer,iText.Layout.Element.Paragraph)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.CheckBoxRenderer.FlatParagraphRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.CheckBoxRenderer.FlatParagraphRenderer.DrawChildren(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.FormFieldValueNonTrimmingTextRenderer">
            <summary>Custom implementation for rendering form field values.</summary>
            <remarks>
            Custom implementation for rendering form field values. It makes sure that text value
            trimming strategy matches Acrobat's behavior
            </remarks>
        </member>
        <member name="M:iText.Forms.Form.Renderer.FormFieldValueNonTrimmingTextRenderer.CreateCopy(iText.IO.Font.Otf.GlyphLine,iText.Kernel.Font.PdfFont)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.InputFieldRenderer">
            <summary>
            The
            <see cref="T:iText.Forms.Form.Renderer.AbstractOneLineTextFieldRenderer"/>
            implementation for input fields.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.#ctor(iText.Forms.Form.Element.InputField)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.InputFieldRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.GetSize">
            <summary>Gets the size of the input field.</summary>
            <returns>the input field size</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.IsPassword">
            <summary>Checks if the input field is a password field.</summary>
            <returns>true, if the input field is a password field</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.CreateParagraphRenderer(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.AdjustFieldLayout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.CreateFlatRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.ApplyAcroField(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.GetProperty``1(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.SetMinMaxWidthBasedOnFixedWidth(iText.Layout.Minmaxwidth.MinMaxWidth)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.IsComb">
            <summary>Checks if the input field is a comb field.</summary>
            <returns>true, if the input field is a comb field</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.GetMaxLen">
            <summary>Gets the maximum length of the field's text, in characters.</summary>
            <returns>the current maximum text length</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.InputFieldRenderer.ObfuscatePassword(System.String)">
            <summary>Obfuscates the content of a password input field.</summary>
            <param name="text">the password</param>
            <returns>a string consisting of '*' characters.</returns>
        </member>
        <member name="T:iText.Forms.Form.Renderer.RadioRenderer">
            <summary>
            The
            <see cref="T:iText.Forms.Form.Renderer.AbstractFormFieldRenderer"/>
            implementation for radio buttons.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.#ctor(iText.Forms.Form.Element.Radio)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.RadioRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.DrawBorder(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
            <param name="drawContext">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.DrawBackground(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
            <param name="drawContext">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.ApplyBorderBox(iText.Kernel.Geom.Rectangle,iText.Layout.Borders.Border[],System.Boolean)">
            <summary><inheritDoc/></summary>
            <param name="rect">
            
            <inheritDoc/>
            </param>
            <param name="borders">
            
            <inheritDoc/>
            </param>
            <param name="reverse">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.CreateFlatRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.AdjustFieldLayout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.IsBoxChecked">
            <summary>Defines whether the radio is checked or not.</summary>
            <returns>the default value of the radio field</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.ApplyAcroField(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.IsLayoutBasedOnFlatRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.FlatParagraphRenderer.DrawBorder(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
            <param name="drawContext">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.RadioRenderer.FlatParagraphRenderer.DrawBackground(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
            <param name="drawContext">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="T:iText.Forms.Form.Renderer.SelectFieldComboBoxRenderer">
            <summary>
            The
            <see cref="T:iText.Forms.Form.Renderer.SelectFieldComboBoxRenderer"/>
            implementation for select field renderer.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SelectFieldComboBoxRenderer.#ctor(iText.Forms.Form.Element.AbstractSelectField)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.SelectFieldComboBoxRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="T:iText.Forms.Form.Renderer.SelectFieldListBoxRenderer">
            <summary>
            The
            <see cref="T:iText.Forms.Form.Renderer.SelectFieldListBoxRenderer"/>
            implementation for select field renderer.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SelectFieldListBoxRenderer.#ctor(iText.Forms.Form.Element.AbstractSelectField)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.SelectFieldListBoxRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SelectFieldListBoxRenderer.SetFontColorRecursively(iText.Layout.Renderer.IRenderer)">
            <summary>
            The `select` tag has default color css property,
            therefore it makes sense to explicitly override this property to all children,
            otherwise it will be not applied due to the css resolving mechanism.
            </summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.SignatureAppearanceRenderer">
            <summary>
            The
            <see cref="T:iText.Forms.Form.Renderer.AbstractTextFieldRenderer"/>
            implementation for SigFields.
            </summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.TOP_SECTION">
            <summary>Extra space at the top.</summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.#ctor(iText.Forms.Form.Element.SignatureFieldAppearance)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.SignatureAppearanceRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.IsLayoutBasedOnFlatRenderer">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.CreateFlatRenderer">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.Layout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.AdjustFieldLayout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
            <param name="layoutContext">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.GetDefaultValue">
            <summary>Gets the default value of the form field.</summary>
            <returns>the default value of the form field.</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.ApplyAcroField(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
            <param name="drawContext">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="T:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.DisplayOption">
            <summary>Signature display options.</summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.DisplayOption.DESCRIPTION">
            <summary>The display option is just the description.</summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.DisplayOption.NAME_AND_DESCRIPTION">
            <summary>The display option is the name of the signer and the description.</summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.DisplayOption.GRAPHIC_AND_DESCRIPTION">
            <summary>The display option is an image and the description.</summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.DisplayOption.GRAPHIC">
            <summary>The display option is just an image.</summary>
        </member>
        <member name="F:iText.Forms.Form.Renderer.SignatureAppearanceRenderer.DisplayOption.CUSTOM">
            <summary>The display option is div.</summary>
        </member>
        <member name="T:iText.Forms.Form.Renderer.TextAreaRenderer">
            <summary>
            The
            <see cref="T:iText.Forms.Form.Renderer.AbstractTextFieldRenderer"/>
            implementation for text area fields.
            </summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.#ctor(iText.Forms.Form.Element.TextArea)">
            <summary>
            Creates a new
            <see cref="T:iText.Forms.Form.Renderer.TextAreaRenderer"/>
            instance.
            </summary>
            <param name="modelElement">the model element</param>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.GetCols">
            <summary>Gets the number of columns.</summary>
            <returns>the cols value of the text area field</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.GetRows">
            <summary>Gets the number of rows.</summary>
            <returns>the rows value of the text area field</returns>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.Layout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.GetProperty``1(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.AdjustFieldLayout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.CreateFlatRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.ApplyAcroField(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Forms.Form.Renderer.TextAreaRenderer.SetMinMaxWidthBasedOnFixedWidth(iText.Layout.Minmaxwidth.MinMaxWidth)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Forms.Logs.FormsLogMessageConstants">
            <summary>Class containing constants to be used in logging in forms module.</summary>
        </member>
        <member name="T:iText.Forms.PdfAcroForm">
            <summary>This class represents the static form technology AcroForm on a PDF file.</summary>
        </member>
        <member name="F:iText.Forms.PdfAcroForm.SIGNATURE_EXIST">
            <summary>
            To be used with
            <see cref="M:iText.Forms.PdfAcroForm.SetSignatureFlags(System.Int32)"/>.
            </summary>
            <remarks>
            To be used with
            <see cref="M:iText.Forms.PdfAcroForm.SetSignatureFlags(System.Int32)"/>.
            <br />
            <blockquote>
            If set, the document contains at least one signature field. This flag
            allows a conforming reader to enable user interface items (such as menu
            items or pushbuttons) related to signature processing without having to
            scan the entire document for the presence of signature fields.
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
        </member>
        <member name="F:iText.Forms.PdfAcroForm.APPEND_ONLY">
            <summary>
            To be used with
            <see cref="M:iText.Forms.PdfAcroForm.SetSignatureFlags(System.Int32)"/>.
            </summary>
            <remarks>
            To be used with
            <see cref="M:iText.Forms.PdfAcroForm.SetSignatureFlags(System.Int32)"/>.
            <br />
            <blockquote>
            If set, the document contains signatures that may be invalidated if the
            file is saved (written) in a way that alters its previous contents, as
            opposed to an incremental update. Merely updating the file by appending
            new information to the end of the previous version is safe. Conforming
            readers may use this flag to inform a user requesting a full save that
            signatures will be invalidated and require explicit confirmation before
            continuing with the operation.
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
        </member>
        <member name="F:iText.Forms.PdfAcroForm.generateAppearance">
            <summary>
            Keeps track of whether or not appearances must be generated by the form
            fields themselves, or by the PDF viewer application.
            </summary>
            <remarks>
            Keeps track of whether or not appearances must be generated by the form
            fields themselves, or by the PDF viewer application. Default is
            <c>true</c>.
            </remarks>
        </member>
        <member name="F:iText.Forms.PdfAcroForm.fields">
            <summary>
            A map of field names and their associated
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            objects.
            </summary>
        </member>
        <member name="F:iText.Forms.PdfAcroForm.document">
            <summary>The PdfDocument to which the PdfAcroForm belongs.</summary>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.#ctor(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDocument)">
            <summary>Creates a PdfAcroForm as a wrapper of a dictionary.</summary>
            <remarks>
            Creates a PdfAcroForm as a wrapper of a dictionary.
            Also initializes an XFA form if an <c>/XFA</c> entry is present in
            the dictionary.
            </remarks>
            <param name="pdfObject">the PdfDictionary to be wrapped</param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.#ctor(iText.Kernel.Pdf.PdfArray)">
            <summary>
            Creates a PdfAcroForm from a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of fields.
            </summary>
            <remarks>
            Creates a PdfAcroForm from a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of fields.
            Also initializes an empty XFA form.
            </remarks>
            <param name="fields">
            a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            objects
            </param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetAcroForm(iText.Kernel.Pdf.PdfDocument,System.Boolean)">
            <summary>Retrieves AcroForm from the document.</summary>
            <remarks>
            Retrieves AcroForm from the document. If there is no AcroForm in the
            document Catalog and createIfNotExist flag is true then the AcroForm
            dictionary will be created and added to the document.
            </remarks>
            <param name="document">
            the document to retrieve the
            <see cref="T:iText.Forms.PdfAcroForm"/>
            from
            </param>
            <param name="createIfNotExist">
            when <c>true</c>, this method will create a
            <see cref="T:iText.Forms.PdfAcroForm"/>
            if none exists
            for this document
            </param>
            <returns>
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument">document</see>
            's AcroForm,
            or a new one provided that <c>createIfNotExist</c> parameter is <c>true</c>, otherwise
            <c>null</c>.
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetAcroForm(iText.Kernel.Pdf.PdfDocument,System.Boolean,iText.Forms.Fields.Merging.OnDuplicateFormFieldNameStrategy)">
            <summary>Retrieves AcroForm from the document.</summary>
            <remarks>
            Retrieves AcroForm from the document. If there is no AcroForm in the
            document Catalog and createIfNotExist flag is true then the AcroForm
            dictionary will be created and added to the document.
            </remarks>
            <param name="document">
            the document to retrieve the
            <see cref="T:iText.Forms.PdfAcroForm"/>
            from
            </param>
            <param name="createIfNotExist">
            when <c>true</c>, this method will create a
            <see cref="T:iText.Forms.PdfAcroForm"/>
            if none
            exists for
            this document
            </param>
            <param name="onDuplicateFieldNameStrategy">the strategy to be used when a field with the same name already exists
                </param>
            <returns>
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument">document</see>
            's AcroForm,
            or a new one provided that <c>createIfNotExist</c> parameter is <c>true</c>, otherwise
            <c>null</c>.
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.AddField(iText.Forms.Fields.PdfFormField)">
            <summary>This method adds the field to the last page in the document.</summary>
            <remarks>
            This method adds the field to the last page in the document.
            If there's no pages, creates a new one.
            </remarks>
            <param name="field">
            the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            to be added to the form
            </param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.AddField(iText.Forms.Fields.PdfFormField,iText.Kernel.Pdf.PdfPage)">
            <summary>This method adds the field to a specific page.</summary>
            <param name="field">
            the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            to be added to the form
            </param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            on which to add the field
            </param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.AddField(iText.Forms.Fields.PdfFormField,iText.Kernel.Pdf.PdfPage,System.Boolean)">
            <summary>This method adds the field to a specific page.</summary>
            <param name="field">
            the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            to be added to the form
            </param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            on which to add the field
            </param>
            <param name="throwExceptionOnError">true if the exception is expected to be thrown in case of error.</param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.AddFieldAppearanceToPage(iText.Forms.Fields.PdfFormField,iText.Kernel.Pdf.PdfPage)">
            <summary>
            This method merges field with its annotation and places it on the given
            page.
            </summary>
            <remarks>
            This method merges field with its annotation and places it on the given
            page. This method also work if the field has more than one widget
            annotation, but doesn't work with no annotations.
            </remarks>
            <param name="field">
            the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            to be added to the form
            </param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            on which to add the field
            </param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetRootFormFields">
            <summary>Gets root fields (i.e. direct children of Acroform dictionary).</summary>
            <returns>
            a map of field names and their associated
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            objects
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetAllFormFields">
            <summary>
            Gets all
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s as a
            <see cref="!:System.Collections.IDictionary&lt;K, V&gt;"/>
            including fields kids.
            </summary>
            <returns>
            a map of field names and their associated
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            objects
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetAllFormFieldsAndAnnotations">
            <summary>
            Gets all
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField">form field</see>
            s as a
            <see cref="!:Java.Util.Set&lt;E&gt;"/>
            including fields kids and nameless fields.
            </summary>
            <returns>
            a set of
            <see cref="T:iText.Forms.Fields.AbstractPdfFormField">form field</see>
            objects.
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetFieldsForFlattening">
            <summary>
            Gets a collection of
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s, prepared for flattening using
            <see cref="M:iText.Forms.PdfAcroForm.PartialFormFlattening(System.String)"/>
            method.
            </summary>
            <remarks>
            Gets a collection of
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s, prepared for flattening using
            <see cref="M:iText.Forms.PdfAcroForm.PartialFormFlattening(System.String)"/>
            method.
            If returned collection is empty, all form fields will be flattened on
            <see cref="M:iText.Forms.PdfAcroForm.FlattenFields">flattenFields</see>
            call.
            </remarks>
            <returns>
            a collection of
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s for flattening
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetPdfDocument">
            <summary>
            Gets the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            this
            <see cref="T:iText.Forms.PdfAcroForm"/>
            belongs to.
            </summary>
            <returns>the document of this form</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetNeedAppearances(System.Boolean)">
            <summary>Sets the <c>NeedAppearances</c> boolean property on the AcroForm.</summary>
            <remarks>
            Sets the <c>NeedAppearances</c> boolean property on the AcroForm.
            NeedAppearances has been deprecated in PDF 2.0.
            <br />
            <blockquote>
            NeedAppearances is a flag specifying whether to construct appearance
            streams and appearance dictionaries for all widget annotations in the
            document.
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
            <param name="needAppearances">a boolean. Default value is <c>false</c></param>
            <returns>current AcroForm.</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetNeedAppearances">
            <summary>Gets the <c>NeedAppearances</c> boolean property on the AcroForm.</summary>
            <remarks>
            Gets the <c>NeedAppearances</c> boolean property on the AcroForm.
            NeedAppearances has been deprecated in PDF 2.0.
            <br />
            <blockquote>
            NeedAppearances is a flag specifying whether to construct appearance
            streams and appearance dictionaries for all widget annotations in the
            document.
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
            <returns>
            the <c>NeedAppearances</c> property as a
            <see cref="T:iText.Kernel.Pdf.PdfBoolean"/>
            . Default value is <c>false</c>
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetSignatureFlags(System.Int32)">
            <summary>Sets the <c>SigFlags</c> integer property on the AcroForm.</summary>
            <remarks>
            Sets the <c>SigFlags</c> integer property on the AcroForm.
            <br />
            <blockquote>
            SigFlags is a set of flags specifying various document-level
            characteristics related to signature fields.
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
            <param name="sigFlags">
            an integer. Use
            <see cref="F:iText.Forms.PdfAcroForm.SIGNATURE_EXIST"/>
            and/or
            <see cref="F:iText.Forms.PdfAcroForm.APPEND_ONLY"/>.
            Use bitwise OR operator to combine these values. Default value is <c>0</c>
            </param>
            <returns>current AcroForm.</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetSignatureFlag(System.Int32)">
            <summary>Changes the <c>SigFlags</c> integer property on the AcroForm.</summary>
            <remarks>
            Changes the <c>SigFlags</c> integer property on the AcroForm.
            This method allows only to add flags, not to remove them.
            <br />
            <blockquote>
            SigFlags is a set of flags specifying various document-level
            characteristics related to signature fields.
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
            <param name="sigFlag">
            an integer. Use
            <see cref="F:iText.Forms.PdfAcroForm.SIGNATURE_EXIST"/>
            and/or
            <see cref="F:iText.Forms.PdfAcroForm.APPEND_ONLY"/>.
            Use bitwise OR operator to combine these values. Default is <c>0</c>
            </param>
            <returns>current AcroForm.</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetSignatureFlags">
            <summary>Gets the <c>SigFlags</c> integer property on the AcroForm.</summary>
            <remarks>
            Gets the <c>SigFlags</c> integer property on the AcroForm.
            <br />
            <blockquote>
            SigFlags is a set of flags specifying various document-level
            characteristics related to signature fields
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
            <returns>current value for <c>SigFlags</c>.</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetCalculationOrder(iText.Kernel.Pdf.PdfArray)">
            <summary>Sets the <c>CO</c> array property on the AcroForm.</summary>
            <remarks>
            Sets the <c>CO</c> array property on the AcroForm.
            <br />
            <blockquote>
            <c>CO</c>, Calculation Order, is an array of indirect references to
            field dictionaries with calculation actions, defining the calculation
            order in which their values will be recalculated when the value of any
            field changes
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
            <param name="calculationOrder">an array of indirect references</param>
            <returns>current AcroForm</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetCalculationOrder">
            <summary>Gets the <c>CO</c> array property on the AcroForm.</summary>
            <remarks>
            Gets the <c>CO</c> array property on the AcroForm.
            <br />
            <blockquote>
            <c>CO</c>, Calculation Order, is an array of indirect references to
            field dictionaries with calculation actions, defining the calculation
            order in which their values will be recalculated when the value of any
            field changes
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
            <returns>an array of indirect references</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetDefaultResources(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Sets the <c>DR</c> dictionary property on the AcroForm.</summary>
            <remarks>
            Sets the <c>DR</c> dictionary property on the AcroForm.
            <br />
            <blockquote>
            <c>DR</c> is a resource dictionary containing default resources
            (such as fonts, patterns, or colour spaces) that shall be used by form
            field appearance streams. At a minimum, this dictionary shall contain a
            Font entry specifying the resource name and font dictionary of the
            default font for displaying text.
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
            <param name="defaultResources">a resource dictionary</param>
            <returns>current AcroForm</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetDefaultResources">
            <summary>Gets the <c>DR</c> dictionary property on the AcroForm.</summary>
            <remarks>
            Gets the <c>DR</c> dictionary property on the AcroForm.
            <br />
            <blockquote>
            <c>DR</c> is a resource dictionary containing default resources
            (such as fonts, patterns, or colour spaces) that shall be used by form
            field appearance streams. At a minimum, this dictionary shall contain a
            Font entry specifying the resource name and font dictionary of the
            default font for displaying text.
            (ISO 32000-1, section 12.7.2 "Interactive Form Dictionary")
            </blockquote>
            </remarks>
            <returns>a resource dictionary</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetDefaultAppearance(System.String)">
            <summary>Sets the <c>DA</c> String property on the AcroForm.</summary>
            <remarks>
            Sets the <c>DA</c> String property on the AcroForm.
            <br />
            This method sets a default (fallback value) for the <c>DA</c>
            attribute of variable text
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s.
            </remarks>
            <param name="appearance">a String containing a sequence of valid PDF syntax</param>
            <returns>current AcroForm</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetDefaultAppearance">
            <summary>Gets the <c>DA</c> String property on the AcroForm.</summary>
            <remarks>
            Gets the <c>DA</c> String property on the AcroForm.
            <br />
            This method returns the default (fallback value) for the <c>DA</c>
            attribute of variable text
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s.
            </remarks>
            <returns>the form-wide default appearance, as a <c>String</c></returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetDefaultJustification(System.Int32)">
            <summary>Sets the <c>Q</c> integer property on the AcroForm.</summary>
            <remarks>
            Sets the <c>Q</c> integer property on the AcroForm.
            <br />
            This method sets a default (fallback value) for the <c>Q</c>
            attribute of variable text
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s.
            </remarks>
            <param name="justification">an integer representing a justification value</param>
            <returns>current AcroForm</returns>
            <seealso cref="M:iText.Forms.Fields.PdfFormField.SetJustification(System.Nullable{iText.Layout.Properties.TextAlignment})"/>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetDefaultJustification">
            <summary>Gets the <c>Q</c> integer property on the AcroForm.</summary>
            <remarks>
            Gets the <c>Q</c> integer property on the AcroForm.
            <br />
            This method gets the default (fallback value) for the <c>Q</c>
            attribute of variable text
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s.
            </remarks>
            <returns>an integer representing a justification value</returns>
            <seealso cref="M:iText.Forms.Fields.PdfFormField.GetJustification"/>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetXFAResource(iText.Kernel.Pdf.PdfStream)">
            <summary>Sets the <c>XFA</c> property on the AcroForm.</summary>
            <remarks>
            Sets the <c>XFA</c> property on the AcroForm.
            <br />
            <c>XFA</c> can either be a
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            or a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>.
            Its contents must be valid XFA.
            </remarks>
            <param name="xfaResource">a stream containing the XDP</param>
            <returns>current AcroForm</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetXFAResource(iText.Kernel.Pdf.PdfArray)">
            <summary>Sets the <c>XFA</c> property on the AcroForm.</summary>
            <remarks>
            Sets the <c>XFA</c> property on the AcroForm.
            <br />
            <c>XFA</c> can either be a
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            or a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>.
            Its contents must be valid XFA.
            </remarks>
            <param name="xfaResource">
            an array of text string and stream pairs representing
            the individual packets comprising the XML Data Package. (ISO 32000-1,
            section 12.7.2 "Interactive Form Dictionary")
            </param>
            <returns>current AcroForm</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetXFAResource">
            <summary>Gets the <c>XFA</c> property on the AcroForm.</summary>
            <returns>
            an object representing the entire XDP. It can either be a
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            or a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetField(System.String)">
            <summary>
            Gets a
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            by its name.
            </summary>
            <param name="fieldName">
            the name of the
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            to retrieve
            </param>
            <returns>
            the
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            , or <c>null</c> if it
            isn't present
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.IsGenerateAppearance">
            <summary>
            Gets the attribute generateAppearance, which tells
            <see cref="M:iText.Forms.PdfAcroForm.FlattenFields"/>
            to generate an appearance Stream for all
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s
            that don't have one.
            </summary>
            <returns>bolean value indicating if the appearances need to be generated</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.SetGenerateAppearance(System.Boolean)">
            <summary>
            Sets the attribute generateAppearance, which tells
            <see cref="M:iText.Forms.PdfAcroForm.FlattenFields"/>
            to generate an appearance Stream for all
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s
            that don't have one.
            </summary>
            <remarks>
            Sets the attribute generateAppearance, which tells
            <see cref="M:iText.Forms.PdfAcroForm.FlattenFields"/>
            to generate an appearance Stream for all
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s
            that don't have one.
            <para />
            Not generating appearances will speed up form flattening but the results
            can be unexpected in Acrobat. Don't use it unless your environment is
            well controlled. The default is <c>true</c>.
            <para />
            If generateAppearance is set to <c>true</c>, then
            <c>NeedAppearances</c> is set to <c>false</c>. This does not
            apply vice versa.
            <para />
            Note, this method does not change default behaviour of
            <see cref="M:iText.Forms.Fields.PdfFormField.SetValue(System.String)"/>
            method.
            </remarks>
            <param name="generateAppearance">a boolean</param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.FlattenFields">
            <summary>
            Flattens interactive
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s in the document.
            </summary>
            <remarks>
            Flattens interactive
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            s in the document. If
            no fields have been explicitly included via
            <see cref="M:iText.Forms.PdfAcroForm.PartialFormFlattening(System.String)"/>
            ,
            then all fields are flattened. Otherwise only the included fields are
            flattened.
            </remarks>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.RemoveField(System.String)">
            <summary>
            Tries to remove the
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            with the specified
            name from the document.
            </summary>
            <param name="fieldName">
            the name of the
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            to remove
            </param>
            <returns>a boolean representing whether or not the removal succeeded.</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.PartialFormFlattening(System.String)">
            <summary>
            Adds a
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            , identified by name, to the list of fields to be flattened.
            </summary>
            <remarks>
            Adds a
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            , identified by name, to the list of fields to be flattened.
            Does not perform a flattening operation in itself.
            </remarks>
            <param name="fieldName">
            the name of the
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            to be flattened
            </param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.RenameField(System.String,System.String)">
            <summary>
            Changes the identifier of a
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>.
            </summary>
            <param name="oldName">the current name of the field</param>
            <param name="newName">the new name of the field. Must not be used currently.</param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.CopyField(System.String)">
            <summary>
            Creates an in-memory copy of a
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </summary>
            <remarks>
            Creates an in-memory copy of a
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            . This new field is
            not added to the document.
            </remarks>
            <param name="name">
            the name of the
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            to be copied
            </param>
            <returns>
            a clone of the original
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.ReplaceField(System.String,iText.Forms.Fields.PdfFormField)">
            <summary>
            Replaces the
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            of a certain name with another
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </summary>
            <param name="name">
            the name of the
            <see cref="T:iText.Forms.Fields.PdfFormField">form field</see>
            to be replaced
            </param>
            <param name="field">
            the new
            <see cref="T:iText.Forms.Fields.PdfFormField"/>
            </param>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.DisableRegenerationForAllFields">
            <summary>
            Disables appearance stream regeneration for all the root fields in the Acroform, so all of its children
            in the hierarchy will also not be regenerated.
            </summary>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.EnableRegenerationForAllFields">
            <summary>Enables appearance stream regeneration for all the fields in the Acroform and regenerates them.</summary>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetFields">
            <summary>Gets all AcroForm fields in the document.</summary>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of field dictionaries
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.HasXfaForm">
            <summary>Determines whether the AcroForm contains XFA data.</summary>
            <returns>a boolean</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.GetXfaForm">
            <summary>
            Gets the
            <see cref="T:iText.Forms.Xfa.XfaForm"/>
            atribute.
            </summary>
            <returns>the XFA form object</returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.RemoveXfaForm">
            <summary>Removes the XFA stream from the document.</summary>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.Put(iText.Kernel.Pdf.PdfName,iText.Kernel.Pdf.PdfObject)">
            <summary>Put a key/value pair in the dictionary and overwrite previous value if it already exists.</summary>
            <param name="key">the key as pdf name</param>
            <param name="value">the value as pdf object</param>
            <returns>
            this
            <see cref="T:iText.Forms.PdfAcroForm"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Forms.PdfAcroForm.Release">
            <summary>Releases underlying pdf object and other pdf entities used by wrapper.</summary>
            <remarks>
            Releases underlying pdf object and other pdf entities used by wrapper.
            This method should be called instead of direct call to
            <see cref="M:iText.Kernel.Pdf.PdfObject.Release"/>
            if the wrapper is used.
            </remarks>
        </member>
        <member name="T:iText.Forms.PdfPageFormCopier">
            <summary>
            A sample implementation of the {#link IPdfPageExtraCopier} interface which
            copies only AcroForm fields to a new page.
            </summary>
            <remarks>
            A sample implementation of the {#link IPdfPageExtraCopier} interface which
            copies only AcroForm fields to a new page.
            <para />
            NOTE: While it's absolutely not necessary to use the same PdfPageFormCopier instance for copying operations,
            it is still worth to know that PdfPageFormCopier uses some caching logic which can potentially improve performance
            in case of the reusing of the same instance.
            </remarks>
        </member>
        <member name="T:iText.Forms.PdfSigFieldLock">
            <summary>A signature field lock dictionary.</summary>
            <remarks>
            A signature field lock dictionary. Specifies a set of form
            fields that shall be locked when this signature field is
            signed.
            </remarks>
        </member>
        <member name="M:iText.Forms.PdfSigFieldLock.#ctor">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.PdfSigFieldLock"/>.
            </summary>
        </member>
        <member name="M:iText.Forms.PdfSigFieldLock.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.PdfSigFieldLock"/>.
            </summary>
            <param name="dict">
            the dictionary whose entries should be added to
            the signature field lock dictionary
            </param>
        </member>
        <member name="M:iText.Forms.PdfSigFieldLock.SetDocumentPermissions(iText.Forms.PdfSigFieldLock.LockPermissions)">
            <summary>
            Sets the permissions granted for the document when the corresponding signature
            field is signed.
            </summary>
            <remarks>
            Sets the permissions granted for the document when the corresponding signature
            field is signed. See
            <see cref="T:iText.Forms.PdfSigFieldLock.LockPermissions"/>
            for getting more info.
            </remarks>
            <param name="permissions">the permissions granted for the document</param>
            <returns>
            this
            <see cref="T:iText.Forms.PdfSigFieldLock"/>
            object.
            </returns>
        </member>
        <member name="M:iText.Forms.PdfSigFieldLock.SetFieldLock(iText.Forms.PdfSigFieldLock.LockAction,System.String[])">
            <summary>Sets signature lock for specific fields in the document.</summary>
            <param name="action">
            indicates the set of fields that should be locked after the actual
            signing of the corresponding signature takes place
            </param>
            <param name="fields">names indicating the fields</param>
            <returns>
            this
            <see cref="T:iText.Forms.PdfSigFieldLock"/>
            object.
            </returns>
        </member>
        <member name="M:iText.Forms.PdfSigFieldLock.GetLockActionValue(iText.Forms.PdfSigFieldLock.LockAction)">
            <summary>
            Returns the specified action of a signature field lock as
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            value.
            </summary>
            <param name="action">
            the action as
            <see cref="T:iText.Forms.PdfSigFieldLock.LockAction"/>
            </param>
            <returns>
            the specified action of a signature field lock as
            <see cref="T:iText.Kernel.Pdf.PdfName"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.PdfSigFieldLock.GetLockPermission(iText.Forms.PdfSigFieldLock.LockPermissions)">
            <summary>
            Returns the specified level of access permissions granted for the document as
            <see cref="T:iText.Kernel.Pdf.PdfNumber"/>
            value.
            </summary>
            <param name="permissions">
            the level of access permissions as
            <see cref="T:iText.Forms.PdfSigFieldLock.LockPermissions"/>
            </param>
            <returns>
            the specified level of access permissions as
            <see cref="T:iText.Kernel.Pdf.PdfNumber"/>.
            </returns>
        </member>
        <member name="T:iText.Forms.PdfSigFieldLock.LockAction">
            <summary>Enumerates the different actions of a signature field lock.</summary>
            <remarks>
            Enumerates the different actions of a signature field lock.
            Indicates the set of fields that should be locked when the
            corresponding signature field is signed:
            <list type="bullet">
            <item><description>all the fields in the document,
            </description></item>
            <item><description>all the fields specified in the /Fields array,
            </description></item>
            <item><description>all the fields except those specified in the /Fields array.
            </description></item>
            </list>
            </remarks>
        </member>
        <!-- Badly formed XML comment ignored for member "T:iText.Forms.PdfSigFieldLock.LockPermissions" -->
        <member name="T:iText.Forms.Util.BorderStyleUtil">
            <summary>This file is a helper class for the internal usage only.</summary>
            <remarks>
            This file is a helper class for the internal usage only.
            Be aware that its API and functionality may be changed in the future.
            </remarks>
        </member>
        <member name="M:iText.Forms.Util.BorderStyleUtil.ApplyBorderProperty(iText.Layout.IPropertyContainer,iText.Forms.Fields.PdfFormAnnotation)">
            <summary>Applies the border property to the annotation.</summary>
            <param name="container">property container to apply border properties from.</param>
            <param name="annotation">the annotation to set border characteristics to.</param>
        </member>
        <member name="T:iText.Forms.Util.DrawingUtil">
            <summary>
            Utility class to draw form fields
            <see cref="T:iText.Forms.Fields.PdfFormField"/>.
            </summary>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfACheck(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary>Draws a PDF A compliant check mark in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
            <param name="moveX">the x coordinate of the bottom left corner of the rectangle</param>
            <param name="moveY">the y coordinate of the bottom left corner of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfACheck(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single)">
            <summary>Draws a PDF A compliant check mark in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfACircle(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary>Draws a PDF A compliant circle in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
            <param name="moveX">the x coordinate of the bottom left corner of the rectangle</param>
            <param name="moveY">the y coordinate of the bottom left corner of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfACircle(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single)">
            <summary>Draws a PDF A compliant circle in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfACross(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary>Draws a PDF A compliant cross in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
            <param name="moveX">the x coordinate of the bottom left corner of the rectangle</param>
            <param name="moveY">the y coordinate of the bottom left corner of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfACross(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single)">
            <summary>Draws a PDF A compliant cross in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfADiamond(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary>Draws a PDF A compliant diamond in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
            <param name="moveX">the x coordinate of the bottom left corner of the rectangle</param>
            <param name="moveY">the y coordinate of the bottom left corner of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfADiamond(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single)">
            <summary>Draws a PDF A compliant diamond in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfASquare(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary>Draws a PDF A compliant square in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
            <param name="moveX">the x coordinate of the bottom left corner of the rectangle</param>
            <param name="moveY">the y coordinate of the bottom left corner of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfASquare(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single)">
            <summary>Draws a PDF A compliant square in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfAStar(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary>Draws a PDF A compliant star in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
            <param name="moveX">the x coordinate of the bottom left corner of the rectangle</param>
            <param name="moveY">the y coordinate of the bottom left corner of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawPdfAStar(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single)">
            <summary>Draws a PDF A compliant star in the specified rectangle.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawCross(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single)">
            <summary>Draws a cross with the specified width and height.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="width">the width of the rectangle</param>
            <param name="height">the height of the rectangle</param>
            <param name="borderWidth">the width of the border</param>
        </member>
        <member name="M:iText.Forms.Util.DrawingUtil.DrawCircle(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single)">
            <summary>Draws a circle with the specified radius.</summary>
            <param name="canvas">the canvas to draw on</param>
            <param name="centerX">the x coordinate of the center of the circle</param>
            <param name="centerY">the y coordinate of the center of the circle</param>
            <param name="radius">the radius of the circle</param>
        </member>
        <member name="T:iText.Forms.Util.FontSizeUtil">
            <summary>Utility class for font size calculations.</summary>
        </member>
        <member name="M:iText.Forms.Util.FontSizeUtil.ApproximateFontSizeToFitSingleLine(iText.Kernel.Font.PdfFont,iText.Kernel.Geom.Rectangle,System.String,System.Single,System.Single)">
            <summary>Calculates the font size that will fit the text in the given rectangle.</summary>
            <param name="localFont">the font to be used</param>
            <param name="bBox">the bounding box of the field</param>
            <param name="value">the value of the field</param>
            <param name="minValue">the minimum font size</param>
            <param name="borderWidth">the border width of the field</param>
            <returns>the font size that will fit the text in the given rectangle</returns>
        </member>
        <member name="T:iText.Forms.Util.FormFieldRendererUtil">
            <summary>Utility class for centralized logic related to form field rendering.</summary>
        </member>
        <member name="M:iText.Forms.Util.FormFieldRendererUtil.#ctor">
            <summary>
            Creates a new instance of
            <see cref="T:iText.Forms.Util.FormFieldRendererUtil"/>.
            </summary>
        </member>
        <member name="M:iText.Forms.Util.FormFieldRendererUtil.RemoveProperties(iText.Layout.IPropertyContainer)">
            <summary>Removes properties that impact the lay outing of interactive form fields.</summary>
            <param name="modelElement">The model element to remove the properties from.</param>
            <returns>A map containing the removed properties.</returns>
        </member>
        <member name="M:iText.Forms.Util.FormFieldRendererUtil.ReapplyProperties(iText.Layout.IPropertyContainer,System.Collections.Generic.IDictionary{System.Int32,System.Object})">
            <summary>
            Reapplies the properties
            <see cref="T:iText.Layout.IPropertyContainer"/>.
            </summary>
            <param name="modelElement">The model element to reapply the properties to.</param>
            <param name="properties">The properties to reapply.</param>
        </member>
        <member name="T:iText.Forms.Util.RegisterDefaultDiContainer">
            <summary>Registers a default instance for a dependency injection container.</summary>
        </member>
        <member name="M:iText.Forms.Util.RegisterDefaultDiContainer.#ctor">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Util.RegisterDefaultDiContainer"/>.
            </summary>
        </member>
        <member name="T:iText.Forms.Xfa.AcroFieldsSearch">
            <summary>A class to process "classic" fields.</summary>
        </member>
        <member name="M:iText.Forms.Xfa.AcroFieldsSearch.#ctor(System.Collections.Generic.ICollection{System.String})">
            <summary>Creates a new instance from a Collection with the full names.</summary>
            <param name="items">the Collection</param>
        </member>
        <member name="M:iText.Forms.Xfa.AcroFieldsSearch.GetAcroShort2LongName">
            <summary>Gets the mapping from short names to long names.</summary>
            <remarks>
            Gets the mapping from short names to long names. A long
            name may contain the #subform name part.
            </remarks>
            <returns>the mapping from short names to long names</returns>
        </member>
        <member name="M:iText.Forms.Xfa.AcroFieldsSearch.SetAcroShort2LongName(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>Sets the mapping from short names to long names.</summary>
            <remarks>
            Sets the mapping from short names to long names. A long
            name may contain the #subform name part.
            </remarks>
            <param name="acroShort2LongName">the mapping from short names to long names</param>
        </member>
        <member name="T:iText.Forms.Xfa.InverseStore">
            <summary>
            A structure to store each part of a SOM name and link it to the next part
            beginning from the lower hierarchy.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfa.InverseStore.GetDefaultName">
            <summary>
            Gets the full name by traversing the hierarchy using only the
            index 0.
            </summary>
            <returns>the full name</returns>
        </member>
        <member name="M:iText.Forms.Xfa.InverseStore.IsSimilar(System.String)">
            <summary>Search the current node for a similar name.</summary>
            <remarks>
            Search the current node for a similar name. A similar name starts
            with the same name but has a different index. For example, "detail[3]"
            is similar to "detail[9]". The main use is to discard names that
            correspond to out of bounds records.
            </remarks>
            <param name="name">the name to search</param>
            <returns><c>true</c> if a similitude was found</returns>
        </member>
        <member name="T:iText.Forms.Xfa.XfaForm">
            <summary>Processes XFA forms.</summary>
        </member>
        <member name="F:iText.Forms.Xfa.XfaForm.XFA_DATA_SCHEMA">
            <summary>The URI for the XFA Data schema.</summary>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.#ctor">
            <summary>An empty constructor to build on.</summary>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.#ctor(System.IO.Stream)">
            <summary>Creates an XFA form by the stream containing all xml information</summary>
            <param name="inputStream">
            The InputStream
            </param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.#ctor(System.Xml.Linq.XDocument)">
            <summary>
            Creates an XFA form by the
            <see cref="T:iText.Layout.Document"/>
            containing all xml information
            </summary>
            <param name="domDocument">
            The document
            </param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            A constructor from a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            . It is assumed, but not
            necessary for correct initialization, that the dictionary is actually a
            <see cref="T:iText.Forms.PdfAcroForm"/>
            . An entry in the dictionary with the <code>XFA</code>
            key must contain correct XFA syntax. If the <code>XFA</code> key is
            absent, then the constructor essentially does nothing.
            </summary>
            <param name="acroFormDictionary">the dictionary object to initialize from</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>A constructor from a <CODE>PdfDocument</CODE>.</summary>
            <remarks>
            A constructor from a <CODE>PdfDocument</CODE>. It basically does everything
            from finding the XFA stream to the XML parsing.
            </remarks>
            <param name="pdfDocument">the PdfDocument instance</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.SetXfaForm(iText.Forms.Xfa.XfaForm,iText.Kernel.Pdf.PdfDocument)">
            <summary>Sets the XFA key from a byte array.</summary>
            <remarks>Sets the XFA key from a byte array. The old XFA is erased.</remarks>
            <param name="form">the data</param>
            <param name="pdfDocument">pdfDocument</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.SetXfaForm(iText.Forms.Xfa.XfaForm,iText.Forms.PdfAcroForm)">
            <summary>Sets the XFA key from a byte array.</summary>
            <remarks>Sets the XFA key from a byte array. The old XFA is erased.</remarks>
            <param name="form">the data</param>
            <param name="acroForm">an AcroForm instance</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.ExtractXFANodes(System.Xml.Linq.XDocument)">
            <summary>Extracts DOM nodes from an XFA document.</summary>
            <param name="domDocument">
            an XFA file as a
            <see cref="T:System.Xml.Linq.XDocument">
            DOM
            document
            </see>
            </param>
            <returns>
            a
            <see cref="!:System.Collections.IDictionary&lt;K, V&gt;"/>
            of XFA packet names and their associated
            <see cref="T:System.Xml.Linq.XNode">DOM nodes</see>
            </returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.Write(iText.Kernel.Pdf.PdfDocument)">
            <summary>Write the XfaForm to the provided PdfDocument.</summary>
            <param name="document">the PdfDocument to write the XFA Form to</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.Write(iText.Forms.PdfAcroForm)">
            <summary>Write the XfaForm to the provided PdfDocument.</summary>
            <param name="acroForm">the PdfAcroForm to write the XFA Form to</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.SetXfaFieldValue(System.String,System.String)">
            <summary>Changes a field value in the XFA form.</summary>
            <param name="name">the name of the field to be changed</param>
            <param name="value">the new value</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.GetXfaFieldValue(System.String)">
            <summary>Gets the xfa field value.</summary>
            <param name="name">the fully qualified field name</param>
            <returns>the field value</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.IsXfaPresent">
            <summary>Returns <CODE>true</CODE> if it is a XFA form.</summary>
            <returns><CODE>true</CODE> if it is a XFA form</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FindFieldName(System.String)">
            <summary>Finds the complete field name from a partial name.</summary>
            <param name="name">the complete or partial name</param>
            <returns>the complete name or <CODE>null</CODE> if not found</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FindDatasetsName(System.String)">
            <summary>
            Finds the complete SOM name contained in the datasets section from a
            possibly partial name.
            </summary>
            <param name="name">the complete or partial name</param>
            <returns>the complete name or <CODE>null</CODE> if not found</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FindDatasetsNode(System.String)">
            <summary>
            Finds the <CODE>Node</CODE> contained in the datasets section from a
            possibly partial name.
            </summary>
            <param name="name">the complete or partial name</param>
            <returns>the <CODE>Node</CODE> or <CODE>null</CODE> if not found</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.GetNodeText(System.Xml.Linq.XNode)">
            <summary>Gets all the text contained in the child nodes of this node.</summary>
            <param name="n">the <CODE>Node</CODE></param>
            <returns>the text found or "" if no text was found</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.GetNodeTextByPath(System.String)">
            <summary>
            Gets all the text contained in the child nodes of the node under the provided path.
            </summary>
            <param name="path">path to the node to extract text in the format "some.path.to.node"</param>
            <returns>text found under the provided path or <c>null</c> if node or text wasn't found</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.SetNodeText(System.Xml.Linq.XNode,System.String)">
            <summary>Sets the text of this node.</summary>
            <remarks>
            Sets the text of this node. All the child's node are deleted and a new
            child text node is created.
            </remarks>
            <param name="n">the <CODE>Node</CODE> to add the text to</param>
            <param name="text">the text to add</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.GetDomDocument">
            <summary>Gets the top level DOM document.</summary>
            <returns>the top level DOM document</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.SetDomDocument(System.Xml.Linq.XDocument)">
            <summary>Sets the top DOM document.</summary>
            <param name="domDocument">the top DOM document</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.GetDatasetsNode">
            <summary>Gets the <CODE>Node</CODE> that corresponds to the datasets part.</summary>
            <returns>the <CODE>Node</CODE> that corresponds to the datasets part</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FillXfaForm(System.IO.FileInfo)">
            <summary>Replaces the XFA data under datasets/data.</summary>
            <remarks>
            Replaces the XFA data under datasets/data. Accepts a
            <see cref="T:System.IO.FileInfo">
            file
            object
            </see>
            to fill this object with XFA data. The resulting DOM document may
            be modified.
            </remarks>
            <param name="file">
            the
            <see cref="T:System.IO.FileInfo"/>
            </param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FillXfaForm(System.IO.FileInfo,System.Boolean)">
            <summary>Replaces the XFA data under datasets/data.</summary>
            <remarks>
            Replaces the XFA data under datasets/data. Accepts a
            <see cref="T:System.IO.FileInfo">
            file
            object
            </see>
            to fill this object with XFA data.
            </remarks>
            <param name="file">
            the
            <see cref="T:System.IO.FileInfo"/>
            </param>
            <param name="readOnly">whether or not the resulting DOM document may be modified</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FillXfaForm(System.IO.Stream)">
            <summary>Replaces the XFA data under datasets/data.</summary>
            <remarks>
            Replaces the XFA data under datasets/data. Accepts an
            <see cref="T:System.IO.Stream"/>
            to fill this object with XFA data. The resulting DOM document may be
            modified.
            </remarks>
            <param name="is">
            the
            <see cref="T:System.IO.Stream"/>
            </param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FillXfaForm(System.IO.Stream,System.Boolean)">
            <summary>Replaces the XFA data under datasets/data.</summary>
            <remarks>
            Replaces the XFA data under datasets/data. Accepts an
            <see cref="T:System.IO.Stream"/>
            to fill this object with XFA data.
            </remarks>
            <param name="is">
            the
            <see cref="T:System.IO.Stream"/>
            </param>
            <param name="readOnly">whether or not the resulting DOM document may be modified</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FillXfaForm(System.Xml.XmlReader)">
            <summary>Replaces the XFA data under datasets/data.</summary>
            <remarks>
            Replaces the XFA data under datasets/data. Accepts an
            <see cref="T:System.Xml.XmlReader">XML reader</see>
            to fill this object with XFA data. The resulting DOM
            document may be modified.
            </remarks>
            <param name="is">
            the
            <see cref="T:System.Xml.XmlReader">XML reader</see>
            </param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FillXfaForm(System.Xml.XmlReader,System.Boolean)">
            <summary>Replaces the XFA data under datasets/data.</summary>
            <remarks>
            Replaces the XFA data under datasets/data. Accepts an
            <see cref="T:System.Xml.XmlReader">XML reader</see>
            to fill this object with XFA data.
            </remarks>
            <param name="is">
            the
            <see cref="T:System.Xml.XmlReader">XML reader</see>
            </param>
            <param name="readOnly">whether or not the resulting DOM document may be modified</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FillXfaForm(System.Xml.Linq.XNode)">
            <summary>Replaces the XFA data under datasets/data.</summary>
            <param name="node">
            the input
            <see cref="T:System.Xml.Linq.XNode"/>
            </param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.FillXfaForm(System.Xml.Linq.XNode,System.Boolean)">
            <summary>Replaces the XFA data under datasets/data.</summary>
            <param name="node">
            the input
            <see cref="T:System.Xml.Linq.XNode"/>
            </param>
            <param name="readOnly">whether or not the resulting DOM document may be modified</param>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.GetXfaObject(iText.Kernel.Pdf.PdfDocument)">
            <summary>Return the XFA Object, could be an array, could be a Stream.</summary>
            <remarks>
            Return the XFA Object, could be an array, could be a Stream.
            Returns null if no XFA Object is present.
            </remarks>
            <param name="pdfDocument">a PdfDocument instance</param>
            <returns>the XFA object</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.GetXfaObject(iText.Forms.PdfAcroForm)">
            <summary>Return the XFA Object, could be an array, could be a Stream.</summary>
            <remarks>
            Return the XFA Object, could be an array, could be a Stream.
            Returns null if no XFA Object is present.
            </remarks>
            <param name="acroForm">a PdfAcroForm instance</param>
            <returns>the XFA object</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.SerializeDocument(System.Xml.Linq.XNode)">
            <summary>Serializes a XML document to a byte array.</summary>
            <param name="n">the XML document</param>
            <returns>the serialized XML document</returns>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.ExtractNodes">
            <summary>Extracts the nodes from the domDocument.</summary>
        </member>
        <member name="M:iText.Forms.Xfa.XfaForm.CreateDatasetsNode(System.Xml.Linq.XNode)">
            <summary>Some XFA forms don't have a datasets node.</summary>
            <remarks>
            Some XFA forms don't have a datasets node.
            If this is the case, we have to add one.
            </remarks>
        </member>
        <member name="T:iText.Forms.Xfa.Xml2Som">
            <summary>A class for some basic SOM processing.</summary>
        </member>
        <member name="F:iText.Forms.Xfa.Xml2Som.order">
            <summary>The order the names appear in the XML, depth first.</summary>
        </member>
        <member name="F:iText.Forms.Xfa.Xml2Som.name2Node">
            <summary>The mapping of full names to nodes.</summary>
        </member>
        <member name="F:iText.Forms.Xfa.Xml2Som.inverseSearch">
            <summary>The data to do a search from the bottom hierarchy.</summary>
        </member>
        <member name="F:iText.Forms.Xfa.Xml2Som.stack">
            <summary>A stack to be used when parsing.</summary>
        </member>
        <member name="F:iText.Forms.Xfa.Xml2Som.anform">
            <summary>A temporary store for the repetition count.</summary>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.EscapeSom(System.String)">
            <summary>Escapes a SOM string fragment replacing "." with "\.".</summary>
            <param name="s">the unescaped string</param>
            <returns>the escaped string</returns>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.UnescapeSom(System.String)">
            <summary>Unescapes a SOM string fragment replacing "\." with ".".</summary>
            <param name="s">the escaped string</param>
            <returns>the unescaped string</returns>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.PrintStack">
            <summary>
            Outputs the stack as the sequence of elements separated
            by '.'.
            </summary>
            <returns>the stack as the sequence of elements separated by '.'</returns>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.GetShortName(System.String)">
            <summary>Gets the name with the <CODE>#subform</CODE> removed.</summary>
            <param name="s">the long name</param>
            <returns>the short name</returns>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.InverseSearchAdd(System.String)">
            <summary>Adds a SOM name to the search node chain.</summary>
            <param name="unstack">the SOM name</param>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.InverseSearchAdd(System.Collections.Generic.IDictionary{System.String,iText.Forms.Xfa.InverseStore},System.Collections.Generic.Stack{System.String},System.String)">
            <summary>Adds a SOM name to the search node chain.</summary>
            <param name="inverseSearch">the start point</param>
            <param name="stack">the stack with the separated SOM parts</param>
            <param name="unstack">the full name</param>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.InverseSearchGlobal(System.Collections.Generic.IList{System.String})">
            <summary>Searches the SOM hierarchy from the bottom.</summary>
            <param name="parts">the SOM parts</param>
            <returns>the full name or <CODE>null</CODE> if not found</returns>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.SplitParts(System.String)">
            <summary>Splits a SOM name in the individual parts.</summary>
            <param name="name">the full SOM name</param>
            <returns>the split name</returns>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.GetOrder">
            <summary>Gets the order the names appear in the XML, depth first.</summary>
            <returns>the order the names appear in the XML, depth first</returns>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.SetOrder(System.Collections.Generic.IList{System.String})">
            <summary>Sets the order the names appear in the XML, depth first</summary>
            <param name="order">the order the names appear in the XML, depth first</param>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.GetName2Node">
            <summary>Gets the mapping of full names to nodes.</summary>
            <returns>the mapping of full names to nodes</returns>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.SetName2Node(System.Collections.Generic.IDictionary{System.String,System.Xml.Linq.XNode})">
            <summary>Sets the mapping of full names to nodes.</summary>
            <param name="name2Node">the mapping of full names to nodes</param>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.GetInverseSearch">
            <summary>Gets the data to do a search from the bottom hierarchy.</summary>
            <returns>the data to do a search from the bottom hierarchy</returns>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2Som.SetInverseSearch(System.Collections.Generic.IDictionary{System.String,iText.Forms.Xfa.InverseStore})">
            <summary>Sets the data to do a search from the bottom hierarchy.</summary>
            <param name="inverseSearch">the data to do a search from the bottom hierarchy</param>
        </member>
        <member name="T:iText.Forms.Xfa.Xml2SomDatasets">
            <summary>Processes the datasets section in the XFA form.</summary>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2SomDatasets.#ctor(System.Xml.Linq.XNode)">
            <summary>Creates a new instance from the datasets node.</summary>
            <remarks>
            Creates a new instance from the datasets node. This expects
            not the datasets but the data node that comes below.
            </remarks>
            <param name="n">the datasets node</param>
        </member>
        <member name="M:iText.Forms.Xfa.Xml2SomDatasets.InsertNode(System.Xml.Linq.XNode,System.String)">
            <summary>Inserts a new <CODE>Node</CODE> that will match the short name.</summary>
            <param name="n">the datasets top <CODE>Node</CODE></param>
            <param name="shortName">the short name</param>
            <returns>the new <CODE>Node</CODE> of the inserted name</returns>
        </member>
        <member name="T:iText.Forms.Xfdf.ActionObject">
            <summary>Represent Action tag in xfdf document structure.</summary>
            <remarks>
            Represent Action tag in xfdf document structure.
            Content model: ( URI | Launch | GoTo | GoToR | Named ).
            Attributes: none.
            For more details see paragraph 6.5.1 in Xfdf specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.ActionObject.type">
            <summary>Type of inner action element.</summary>
            <remarks>
            Type of inner action element. Possible values:
            <see cref="F:iText.Kernel.Pdf.PdfName.URI"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.Launch"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.GoTo"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.GoToR"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.Named"/>.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.ActionObject.uri">
            <summary>Represents Name, required attribute of URI element.</summary>
            <remarks>Represents Name, required attribute of URI element. For more details see paragraph 6.5.30 in Xfdf specification.
                </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.ActionObject.isMap">
            <summary>Represents IsMap, optional attribute of URI element.</summary>
            <remarks>Represents IsMap, optional attribute of URI element. For more details see paragraph 6.5.30 in Xfdf specification.
                </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.ActionObject.nameAction">
            <summary>Represents Name, required attribute of Named element.</summary>
            <remarks>Represents Name, required attribute of Named element. For more details see paragraph 6.5.24 in Xfdf specification.
                </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.ActionObject.fileOriginalName">
            <summary>Represents OriginalName required attribute of File inner element of GoToR or Launch element.</summary>
            <remarks>
            Represents OriginalName required attribute of File inner element of GoToR or Launch element.
            Corresponds to F key in go-to action or launch dictionaries.
            For more details see paragraphs 6.5.11, 6.5.23 in Xfdf specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.ActionObject.isNewWindow">
            <summary>Represents NewWindow, optional attribute of Launch element.</summary>
            <remarks>Represents NewWindow, optional attribute of Launch element. For more details see paragraph 6.5.23 in Xfdf specification.
                </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.ActionObject.destination">
            <summary>Represents Dest, inner element of link, GoTo, and GoToR elements.</summary>
            <remarks>
            Represents Dest, inner element of link, GoTo, and GoToR elements.
            Corresponds to Dest key in link annotation dictionary.
            For more details see paragraph 6.5.10 in Xfdf specification.
            </remarks>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.#ctor(iText.Kernel.Pdf.PdfName)">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Xfdf.ActionObject"/>.
            </summary>
            <param name="type">
            type of inner action element. Possible values:
            <see cref="F:iText.Kernel.Pdf.PdfName.URI"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.Launch"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.GoTo"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.GoToR"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.Named"/>
            </param>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.GetType">
            <summary>Returns the type of inner action element.</summary>
            <remarks>
            Returns the type of inner action element. Possible values:
            <see cref="F:iText.Kernel.Pdf.PdfName.URI"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.Launch"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.GoTo"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.GoToR"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.Named"/>.
            </remarks>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            type of inner action element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.SetType(iText.Kernel.Pdf.PdfName)">
            <summary>Sets the type of inner action element.</summary>
            <remarks>
            Sets the type of inner action element. Possible values:
            <see cref="F:iText.Kernel.Pdf.PdfName.URI"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.Launch"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.GoTo"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.GoToR"/>
            ,
            <see cref="F:iText.Kernel.Pdf.PdfName.Named"/>.
            </remarks>
            <param name="type">
            
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            type of inner action object
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.ActionObject"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.GetUri">
            <summary>Gets the string value of URI elements.</summary>
            <remarks>
            Gets the string value of URI elements. Corresponds to Name, required attribute of URI element.
            For more details see paragraph 6.5.30 in Xfdf specification.
            </remarks>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            value of URI element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.SetUri(iText.Kernel.Pdf.PdfString)">
            <summary>Sets the string value of URI element.</summary>
            <remarks>
            Sets the string value of URI element. Corresponds to Name, required attribute of URI element.
            For more details see paragraph 6.5.30 in Xfdf specification.
            </remarks>
            <param name="uri">
            
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            value to be set to URI element
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.ActionObject"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.IsMap">
            <summary>Gets IsMap, optional attribute of URI element.</summary>
            <remarks>Gets IsMap, optional attribute of URI element. For more details see paragraph 6.5.30 in Xfdf specification.
                </remarks>
            <returns>boolean indicating if URI element is a map.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.SetMap(System.Boolean)">
            <summary>Sets IsMap, optional attribute of URI element.</summary>
            <remarks>Sets IsMap, optional attribute of URI element. For more details see paragraph 6.5.30 in Xfdf specification.
                </remarks>
            <param name="map">boolean indicating if URI element is a map</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.ActionObject"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.GetNameAction">
            <summary>Gets the value of Name, required attribute of Named element.</summary>
            <remarks>
            Gets the value of Name, required attribute of Named element.
            For more details see paragraph 6.5.24 in Xfdf specification.
            </remarks>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            value of Name attribute of a named action element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.SetNameAction(iText.Kernel.Pdf.PdfName)">
            <summary>Sets the value of Name, required attribute of Named element.</summary>
            <remarks>
            Sets the value of Name, required attribute of Named element.
            For more details see paragraph 6.5.24 in Xfdf specification.
            </remarks>
            <param name="nameAction">
            
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            value to be set to Name attribute of a named action element
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.ActionObject"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.GetFileOriginalName">
            <summary>Gets the string value of OriginalName, required attribute of File inner element of GoToR or Launch element.
                </summary>
            <remarks>
            Gets the string value of OriginalName, required attribute of File inner element of GoToR or Launch element.
            Corresponds to F key in go-to action or launch dictionaries.
            For more details see paragraphs 6.5.11, 6.5.23 in Xfdf specification.
            </remarks>
            <returns>
            
            <see cref="T:System.String"/>
            value of OriginalName attribute of current action object.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.SetFileOriginalName(System.String)">
            <summary>Sets the string value of OriginalName, required attribute of File inner element of GoToR or Launch element.
                </summary>
            <remarks>
            Sets the string value of OriginalName, required attribute of File inner element of GoToR or Launch element.
            Corresponds to F key in go-to action or launch dictionaries.
            For more details see paragraphs 6.5.11, 6.5.23 in Xfdf specification.
            </remarks>
            <param name="fileOriginalName">
            
            <see cref="T:System.String"/>
            value of OriginalName attribute of action object
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.ActionObject"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.IsNewWindow">
            <summary>Gets the boolean value of NewWindow, optional attribute of Launch element.</summary>
            <remarks>
            Gets the boolean value of NewWindow, optional attribute of Launch element.
            For more details see paragraph 6.5.23 in Xfdf specification.
            </remarks>
            <returns>boolean indicating if current Launch action element should be opened in a new window.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.SetNewWindow(System.Boolean)">
            <summary>Sets the boolean value of NewWindow, optional attribute of Launch element.</summary>
            <remarks>
            Sets the boolean value of NewWindow, optional attribute of Launch element.
            For more details see paragraph 6.5.23 in Xfdf specification.
            </remarks>
            <param name="newWindow">boolean indicating if current Launch action element should be opened in a new window
                </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.ActionObject"/>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.GetDestination">
            <summary>Gets Dest, inner element of link, GoTo, and GoToR elements.</summary>
            <remarks>
            Gets Dest, inner element of link, GoTo, and GoToR elements.
            Corresponds to Dest key in link annotation dictionary.
            For more details see paragraph 6.5.10 in Xfdf specification.
            </remarks>
            <returns>
            
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            destination attribute of current action element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.ActionObject.SetDestination(iText.Forms.Xfdf.DestObject)">
            <summary>Sets Dest, inner element of link, GoTo, and GoToR elements.</summary>
            <remarks>
            Sets Dest, inner element of link, GoTo, and GoToR elements.
            Corresponds to Dest key in link annotation dictionary.
            For more details see paragraph 6.5.10 in Xfdf specification.
            </remarks>
            <param name="destination">
            
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            destination attribute of the action element
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.ActionObject"/>.
            </returns>
        </member>
        <member name="T:iText.Forms.Xfdf.AnnotObject">
            <summary>Represents annotation, a child element of annots tag in Xfdf document structure.</summary>
            <remarks>
            Represents annotation, a child element of annots tag in Xfdf document structure.
            For more details see part 6.4 in Xfdf specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.name">
            <summary>Represents the type of annotation.</summary>
            <remarks>
            Represents the type of annotation. Possible values:
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.CARET"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.CIRCLE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.FILEATTACHMENT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.FREETEXT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.HIGHLIGHT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.INK"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.LINE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.POLYGON"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.POLYLINE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.SOUND"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.SQUARE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.SQUIGGLY"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.STAMP"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.STRIKEOUT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.TEXT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.UNDERLINE"/>.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.attributes">
            <summary>Represents a list of attributes of the annotation.</summary>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.contents">
            <summary>Represents contents tag in Xfdf document structure.</summary>
            <remarks>
            Represents contents tag in Xfdf document structure. Is a child of caret, circle, fileattachment, freetext,
            highlight, ink, line, polygon, polyline, sound, square, squiggly, stamp, strikeout, text, and
            underline elements.
            Corresponds to Contents key in annotation dictionary.
            Content model: a string or a rich text string.
            For more details see paragraph 6.5.4 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.contentsRichText">
            <summary>Represents contents-richtext tag in Xfdf document structure.</summary>
            <remarks>
            Represents contents-richtext tag in Xfdf document structure. Is a child of caret, circle, fileattachment, freetext,
            highlight, ink, line, polygon, polyline, sound, square, squiggly, stamp, strikeout, text, and
            underline elements.
            Corresponds to RC key in annotation dictionary.
            Content model: text string.
            For more details see paragraph 6.5.5 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.hasPopup">
            <summary>A boolean, indicating if annotation has inner popup element.</summary>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.popup">
            <summary>Represents a popup annotation, an inner element of the annotation element.</summary>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.action">
            <summary>Represents Action element, a child of OnActivation element of the link annotation.</summary>
            <remarks>
            Represents Action element, a child of OnActivation element of the link annotation.
            Corresponds to the A key in the link annotation dictionary.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.destination">
            <summary>Represents Dest element, a child element of link, GoTo, GoToR elements.</summary>
            <remarks>
            Represents Dest element, a child element of link, GoTo, GoToR elements.
            Corresponds to the Dest key in link annotation dictionary.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.appearance">
            <summary>Represents appearance element,  a child element of stamp element.</summary>
            <remarks>
            Represents appearance element,  a child element of stamp element.
            Corresponds to the AP key in the annotation dictionary.
            Content model: Base64 encoded string.
            For more details see paragraph 6.5.1 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.defaultAppearance">
            <summary>Represents the defaultappearance element, a child of the caret and freetext elements.</summary>
            <remarks>
            Represents the defaultappearance element, a child of the caret and freetext elements.
            Corresponds to the DA key in the free text annotation dictionary.
            Content model: text string.
            For more details see paragraph 6.5.7 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.defaultStyle">
            <summary>Represents defaultstyle element, a child of the freetext element.</summary>
            <remarks>
            Represents defaultstyle element, a child of the freetext element.
            Corresponds to the DS key in the free text annotation dictionary.
            Content model : a text string.
            For more details see paragraph 6.5.9 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.borderStyleAlt">
            <summary>Represents the BorderStyleAlt element, a child of the link element.</summary>
            <remarks>
            Represents the BorderStyleAlt element, a child of the link element.
            Corresponds to the Border key in the common annotation dictionary.
            For more details see paragraph 6.5.3 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.vertices">
            <summary>Represents the string, containing vertices element, a child of the polygon and polyline elements.
                </summary>
            <remarks>
            Represents the string, containing vertices element, a child of the polygon and polyline elements.
            Corresponds to the Vertices key in the polygon or polyline annotation dictionary.
            For more details see paragraph 6.5.31 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotObject.ref">
            <summary>
            The reference to the source
            <see cref="T:iText.Kernel.Pdf.Annot.PdfAnnotation"/>.
            </summary>
            <remarks>
            The reference to the source
            <see cref="T:iText.Kernel.Pdf.Annot.PdfAnnotation"/>
            . Used for attaching popups in case of reading data from pdf file.
            </remarks>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.#ctor">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Xfdf.AnnotObject"/>.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetName">
            <summary>Gets the string value of the type of annotation.</summary>
            <remarks>
            Gets the string value of the type of annotation. Possible values:
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.CARET"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.CIRCLE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.FILEATTACHMENT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.FREETEXT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.HIGHLIGHT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.INK"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.LINE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.POLYGON"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.POLYLINE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.SOUND"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.SQUARE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.SQUIGGLY"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.STAMP"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.STRIKEOUT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.TEXT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.UNDERLINE"/>.
            </remarks>
            <returns>
            
            <see cref="T:System.String"/>
            value of the type of annotation.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetName(System.String)">
            <summary>Sets the string value of the type of annotation.</summary>
            <remarks>
            Sets the string value of the type of annotation. Possible values:
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.CARET"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.CIRCLE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.FILEATTACHMENT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.FREETEXT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.HIGHLIGHT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.INK"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.LINE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.POLYGON"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.POLYLINE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.SOUND"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.SQUARE"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.SQUIGGLY"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.STAMP"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.STRIKEOUT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.TEXT"/>
            ,
            <see cref="F:iText.Forms.Xfdf.XfdfConstants.UNDERLINE"/>.
            </remarks>
            <param name="name">
            
            <see cref="T:System.String"/>
            value of the type of annotation
            </param>
            <returns>
            
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>
            with set name.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetAttributes">
            <summary>Gets a list of all attributes of the annotation.</summary>
            <returns>
            
            <see cref="!:System.Collections.IList&lt;E&gt;">list</see>
            containing all
            <see cref="T:iText.Forms.Xfdf.AttributeObject">attribute objects</see>
            of the annotation.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetAttribute(System.String)">
            <summary>Finds the attribute by name in attributes list.</summary>
            <param name="name">the name of the attribute to look for</param>
            <returns>
            
            <see cref="T:iText.Forms.Xfdf.AttributeObject"/>
            with the given name, or null, if no object with this name was found.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetAttributeValue(System.String)">
            <summary>Finds the attribute by name in attributes list and return its string value.</summary>
            <param name="name">the name of the attribute to look for</param>
            <returns>
            the value of the
            <see cref="T:iText.Forms.Xfdf.AttributeObject"/>
            with the given name,
            or null, if no object with this name was found.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetPopup">
            <summary>Gets the popup annotation, an inner element of the annotation element.</summary>
            <returns>
            
            <see cref="T:iText.Forms.Xfdf.AnnotObject"/>
            representing the inner popup annotation.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetPopup(iText.Forms.Xfdf.AnnotObject)">
            <summary>Sets the popup annotation, an inner element of the annotation element.</summary>
            <param name="popup">
            
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>
            representing inner popup annotation
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.IsHasPopup">
            <summary>Gets the boolean, indicating if annotation has an inner popup element.</summary>
            <returns>true if annotation has an inner popup element, false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetHasPopup(System.Boolean)">
            <summary>Sets the boolean, indicating if annotation has inner popup element.</summary>
            <param name="hasPopup">a boolean indicating if annotation has inner popup element</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetContents">
            <summary>Gets the string value of contents tag in Xfdf document structure.</summary>
            <remarks>
            Gets the string value of contents tag in Xfdf document structure. Contents is a child of caret, circle,
            fileattachment, freetext, highlight, ink, line, polygon, polyline, sound, square, squiggly, stamp, strikeout,
            text, and underline elements.
            Corresponds to Contents key in annotation dictionary.
            Content model: a string or a rich text string.
            For more details see paragraph 6.5.4 in Xfdf document specification.
            </remarks>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            value of inner contents element of current annotation object.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetContents(iText.Kernel.Pdf.PdfString)">
            <summary>Sets the string value of contents tag in Xfdf document structure.</summary>
            <param name="contents">
            
            <see cref="T:iText.Kernel.Pdf.PdfString">string</see>
            value of inner contents element
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetContentsRichText">
            <summary>Gets the string value of contents-richtext tag in Xfdf document structure.</summary>
            <remarks>
            Gets the string value of contents-richtext tag in Xfdf document structure. It is a child of caret, circle, fileattachment,
            freetext, highlight, ink, line, polygon, polyline, sound, square, squiggly, stamp, strikeout, text, and
            underline elements.
            Corresponds to RC key in annotation dictionary.
            Content model: text string.
            For more details see paragraph 6.5.5 in Xfdf document specification.
            </remarks>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            value of inner contents-richtext element of current annotation object.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetContentsRichText(iText.Kernel.Pdf.PdfString)">
            <summary>Sets the string value of contents-richtext tag in xfdf document structure.</summary>
            <param name="contentsRichRext">
            
            <see cref="T:iText.Kernel.Pdf.PdfString">rich text string</see>
            value of inner contents-richtext element
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetAction">
            <summary>Gets Action element, a child of OnActivation element of the link annotation.</summary>
            <remarks>
            Gets Action element, a child of OnActivation element of the link annotation.
            Corresponds to the A key in the link annotation dictionary.
            </remarks>
            <returns>
            inner
            <see cref="T:iText.Forms.Xfdf.ActionObject">action object</see>
            of annotation object.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetAction(iText.Forms.Xfdf.ActionObject)">
            <summary>Sets Action element, a child of OnActivation element of the link annotation.</summary>
            <remarks>
            Sets Action element, a child of OnActivation element of the link annotation.
            Corresponds to the A key in the link annotation dictionary.
            </remarks>
            <param name="action">
            
            <see cref="T:iText.Forms.Xfdf.ActionObject">action object</see>
            , an inner element of annotation object
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.AddAttribute(iText.Forms.Xfdf.AttributeObject)">
            <summary>
            Adds new
            <see cref="T:iText.Forms.Xfdf.AttributeObject"/>
            to the list of annotation attributes.
            </summary>
            <param name="attr">attribute to be added.</param>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.AddAttribute(System.String,System.Boolean)">
            <summary>Adds new attribute with given name and boolean value converted to string.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.AddAttribute(System.String,iText.Kernel.Pdf.PdfObject,System.Boolean)">
            <summary>Adds new attribute by given name and value.</summary>
            <remarks>Adds new attribute by given name and value. If required attribute is present, value of the attribute can't be null.
                </remarks>
            <param name="name">
            
            <see cref="T:System.String"/>
            attribute name
            </param>
            <param name="valueObject">
            
            <see cref="T:iText.Kernel.Pdf.PdfObject"/>
            attribute value
            </param>
            <param name="required">boolean indicating if the attribute is required</param>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.AddFdfAttributes(System.Int32)">
            <summary>Adds page, required attribute of every annotation.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetDestination">
            <summary>Gets Dest element, a child element of link, GoTo, GoToR elements.</summary>
            <remarks>
            Gets Dest element, a child element of link, GoTo, GoToR elements.
            Corresponds to the Dest key in link annotation dictionary.
            </remarks>
            <returns>
            inner
            <see cref="T:iText.Forms.Xfdf.DestObject">destination object</see>
            of annotation object
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetDestination(iText.Forms.Xfdf.DestObject)">
            <summary>Sets Dest element, a child element of link, GoTo, GoToR elements.</summary>
            <remarks>
            Sets Dest element, a child element of link, GoTo, GoToR elements.
            Corresponds to the Dest key in link annotation dictionary.
            </remarks>
            <param name="destination">
            
            <see cref="T:iText.Forms.Xfdf.DestObject">destination object</see>
            , an inner element of annotation object
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetAppearance">
            <summary>Gets the string value of the appearance element, a child element of stamp element.</summary>
            <remarks>
            Gets the string value of the appearance element, a child element of stamp element.
            Corresponds to the AP key in the annotation dictionary.
            Content model: Base64 encoded string.
            For more details see paragraph 6.5.1 in Xfdf document specification.
            </remarks>
            <returns>
            
            <see cref="T:System.String"/>
            value of inner appearance element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetAppearance(System.String)">
            <summary>Gets the string value of the appearance element,  a child element of stamp element.</summary>
            <remarks>
            Gets the string value of the appearance element,  a child element of stamp element.
            Corresponds to the AP key in the annotation dictionary.
            Content model: Base64 encoded string.
            </remarks>
            <param name="appearance">
            
            <see cref="T:System.String"/>
            value of inner appearance element of annotation object
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetDefaultAppearance">
            <summary>Gets the string value of the defaultappearance element, a child of the caret and freetext elements.
                </summary>
            <remarks>
            Gets the string value of the defaultappearance element, a child of the caret and freetext elements.
            Corresponds to the DA key in the free text annotation dictionary.
            Content model: text string.
            For more details see paragraph 6.5.7 in Xfdf document specification.
            </remarks>
            <returns>
            
            <see cref="T:System.String"/>
            value of inner default appearance element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetDefaultAppearance(System.String)">
            <summary>Sets the string value of the defaultappearance element, a child of the caret and freetext elements.
                </summary>
            <remarks>
            Sets the string value of the defaultappearance element, a child of the caret and freetext elements.
            Corresponds to the DA key in the free text annotation dictionary.
            Content model: text string.
            </remarks>
            <param name="defaultAppearance">
            
            <see cref="T:System.String"/>
            value of inner defaultappearance element of annotation object
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetDefaultStyle">
            <summary>Gets the string value of the defaultstyle element, a child of the freetext element.</summary>
            <remarks>
            Gets the string value of the defaultstyle element, a child of the freetext element.
            Corresponds to the DS key in the free text annotation dictionary.
            Content model : a text string.
            For more details see paragraph 6.5.9 in Xfdf document specification.
            </remarks>
            <returns>
            
            <see cref="T:System.String"/>
            value of inner defaultstyle element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetDefaultStyle(System.String)">
            <summary>Sets the string value of the defaultstyle element, a child of the freetext element.</summary>
            <remarks>
            Sets the string value of the defaultstyle element, a child of the freetext element.
            Corresponds to the DS key in the free text annotation dictionary.
            Content model : a text string.
            </remarks>
            <param name="defaultStyle">
            
            <see cref="T:System.String"/>
            value of inner defaultstyle element of annotation object
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetBorderStyleAlt">
            <summary>Gets the BorderStyleAlt element, a child of the link element.</summary>
            <remarks>
            Gets the BorderStyleAlt element, a child of the link element.
            Corresponds to the Border key in the common annotation dictionary.
            For more details see paragraph 6.5.3 in Xfdf document specification.
            </remarks>
            <returns>
            inner
            <see cref="T:iText.Forms.Xfdf.BorderStyleAltObject">BorderStyleAlt object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetBorderStyleAlt(iText.Forms.Xfdf.BorderStyleAltObject)">
            <summary>Sets the BorderStyleAlt element, a child of the link element.</summary>
            <remarks>
            Sets the BorderStyleAlt element, a child of the link element.
            Corresponds to the Border key in the common annotation dictionary.
            </remarks>
            <param name="borderStyleAlt">
            inner
            <see cref="T:iText.Forms.Xfdf.BorderStyleAltObject">BorderStyleAlt object</see>
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetVertices">
            <summary>Gets the string, containing vertices element, a child of the polygon and polyline elements.</summary>
            <remarks>
            Gets the string, containing vertices element, a child of the polygon and polyline elements.
            Corresponds to the Vertices key in the polygon or polyline annotation dictionary.
            For more details see paragraph 6.5.31 in Xfdf document specification.
            </remarks>
            <returns>
            
            <see cref="T:System.String"/>
            value of inner vertices element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetVertices(System.String)">
            <summary>Sets the string, containing vertices element, a child of the polygon and polyline elements.</summary>
            <remarks>
            Sets the string, containing vertices element, a child of the polygon and polyline elements.
            Corresponds to the Vertices key in the polygon or polyline annotation dictionary.
            </remarks>
            <param name="vertices">
            
            <see cref="T:System.String"/>
            value of inner vertices element
            </param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.AnnotObject">annotation object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.GetRef">
            <summary>
            Gets the reference to the source
            <see cref="T:iText.Kernel.Pdf.Annot.PdfAnnotation"/>.
            </summary>
            <remarks>
            Gets the reference to the source
            <see cref="T:iText.Kernel.Pdf.Annot.PdfAnnotation"/>
            . Used for attaching popups in case of reading data from pdf file.
            </remarks>
            <returns>
            an
            <see cref="T:iText.Kernel.Pdf.PdfIndirectReference"/>
            of the source annotation object.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotObject.SetRef(iText.Kernel.Pdf.PdfIndirectReference)">
            <summary>
            Sets the reference to the source
            <see cref="T:iText.Kernel.Pdf.Annot.PdfAnnotation"/>.
            </summary>
            <remarks>
            Sets the reference to the source
            <see cref="T:iText.Kernel.Pdf.Annot.PdfAnnotation"/>
            . Used for attaching popups in case of reading data from pdf file.
            </remarks>
            <param name="ref">
            
            <see cref="T:iText.Kernel.Pdf.PdfIndirectReference"/>
            of the source annotation object
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.AnnotObject"/>
            instance.
            </returns>
        </member>
        <member name="T:iText.Forms.Xfdf.AnnotsObject">
            <summary>Represent annots tag in xfdf document structure.</summary>
            <remarks>
            Represent annots tag in xfdf document structure.
            Content model: ( text | caret | freetext | fileattachment | highlight | ink | line | link
            | circle | square | polygon | polyline | sound | squiggly | stamp |
            strikeout | underline )*.
            Attributes: none.
            For more details see paragraph 6.4.1 in Xfdf specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.AnnotsObject.annotsList">
            <summary>Represents a list of children annotations.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotsObject.#ctor">
            <summary>Creates an instance with the empty list of children annotations.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotsObject.GetAnnotsList">
            <summary>Gets children annotations.</summary>
            <returns>
            a
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            of
            <see cref="T:iText.Forms.Xfdf.AnnotObject"/>
            each representing a child annotation of this annots tag.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AnnotsObject.AddAnnot(iText.Forms.Xfdf.AnnotObject)">
            <summary>
            Adds a new
            <see cref="T:iText.Forms.Xfdf.AnnotObject"/>
            to the list of children annotations.
            </summary>
            <param name="annot">
            
            <see cref="T:iText.Forms.Xfdf.AnnotObject"/>
            containing info about pdf document annotation.
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.AnnotsObject"/>
            instance.
            </returns>
        </member>
        <member name="T:iText.Forms.Xfdf.AttributeObject">
            <summary>Represents the attribute of any XFDF element.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.AttributeObject.#ctor(System.String,System.String)">
            <summary>Creates an instance with given attribute name and value.</summary>
            <param name="name">the name of the attribute, constrained by XML attributes specification.</param>
            <param name="value">the value of the attribute, constrained by XML attributes specification.</param>
        </member>
        <member name="M:iText.Forms.Xfdf.AttributeObject.GetName">
            <summary>Returns attribute name.</summary>
            <returns>a string representation of attribute name, case-sensitive as per XML specification.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.AttributeObject.GetValue">
            <summary>Returns attribute value.</summary>
            <returns>a string representation of attribute value.</returns>
        </member>
        <member name="T:iText.Forms.Xfdf.BorderStyleAltObject">
            <summary>Represents the BorderStyleAlt element, a child of the link element.</summary>
            <remarks>
            Represents the BorderStyleAlt element, a child of the link element.
            Corresponds to the Border key in the common annotation dictionary.
            Content model: border style encoded in the format specified in the border style attributes.
            Required attributes: HCornerRadius, VCornerRadius, Width.
            Optional attributes: DashPattern.
            For more details see paragraph 6.5.3 in Xfdf document specification.
            For more details about attributes see paragraph 6.6.19 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.BorderStyleAltObject.hCornerRadius">
            <summary>Number specifying the horizontal corner radius of the rectangular border.</summary>
            <remarks>
            Number specifying the horizontal corner radius of the rectangular border.
            Corresponds to array index 0 in the Border key in the common annotation dictionary.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.BorderStyleAltObject.vCornerRadius">
            <summary>Number specifying the vertical corner radius of the rectangular border.</summary>
            <remarks>
            Number specifying the vertical corner radius of the rectangular border.
            Corresponds to array index 1 in the Border key in the common annotation dictionary.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.BorderStyleAltObject.width">
            <summary>Number specifying the width of the rectangular border.</summary>
            <remarks>
            Number specifying the width of the rectangular border.
            Corresponds to array index 2 in the Border key in the common annotation dictionary.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.BorderStyleAltObject.dashPattern">
            <summary>An array of numbers specifying the pattern of dashes and gaps of the border.</summary>
            <remarks>
            An array of numbers specifying the pattern of dashes and gaps of the border.
            Corresponds to array index 3 in the Border key in the common annotation dictionary.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.BorderStyleAltObject.content">
            <summary>Encoded border style string.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.BorderStyleAltObject.#ctor(System.Single,System.Single,System.Single)">
            <summary>Creates an instance that encapsulates BorderStyleAlt XFDF element data.</summary>
            <param name="hCornerRadius">a float value specifying the horizontal corner radius of the rectangular border.
                </param>
            <param name="vCornerRadius">a float value specifying the vertical corner radius of the rectangular border.
                </param>
            <param name="width">a float value specifying the width of the rectangular border.</param>
        </member>
        <member name="M:iText.Forms.Xfdf.BorderStyleAltObject.GetHCornerRadius">
            <summary>Gets the horizontal corner radius of the rectangular border.</summary>
            <remarks>
            Gets the horizontal corner radius of the rectangular border.
            Corresponds to array index 0 in the Border key in the common annotation dictionary.
            </remarks>
            <returns>a float value specifying the horizontal corner radius.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.BorderStyleAltObject.GetVCornerRadius">
            <summary>Gets the vertical corner radius of the rectangular border.</summary>
            <remarks>
            Gets the vertical corner radius of the rectangular border.
            Corresponds to array index 1 in the Border key in the common annotation dictionary.
            </remarks>
            <returns>a float value specifying the vertical corner radius.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.BorderStyleAltObject.GetWidth">
            <summary>Gets the width of the rectangular border.</summary>
            <remarks>
            Gets the width of the rectangular border.
            Corresponds to array index 2 in the Border key in the common annotation dictionary.
            </remarks>
            <returns>a float value specifying the width of the border.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.BorderStyleAltObject.GetDashPattern">
            <summary>Gets the dash pattern of the border.</summary>
            <remarks>
            Gets the dash pattern of the border.
            Corresponds to array index 3 in the Border key in the common annotation dictionary.
            </remarks>
            <returns>an array of numbers specifying the pattern of dashes and gaps of the border.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.BorderStyleAltObject.SetDashPattern(System.Single[])">
            <summary>Sets the dash pattern of the border.</summary>
            <remarks>
            Sets the dash pattern of the border.
            Corresponds to array index 3 in the Border key in the common annotation dictionary.
            </remarks>
            <param name="dashPattern">an array of numbers specifying the pattern of dashes and gaps of the border.</param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.BorderStyleAltObject"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.BorderStyleAltObject.GetContent">
            <summary>Gets border style.</summary>
            <returns>an encoded border style as string.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.BorderStyleAltObject.SetContent(System.String)">
            <summary>Sets border style.</summary>
            <param name="content">an encoded border style as string.</param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.BorderStyleAltObject"/>
            instance.
            </returns>
        </member>
        <member name="T:iText.Forms.Xfdf.DestObject">
            <summary>Represents Dest element, a child of the link, GoTo, and GoToR elements.</summary>
            <remarks>
            Represents Dest element, a child of the link, GoTo, and GoToR elements.
            Corresponds to the Dest key in the link annotations dictionary.
            For more details see paragraph 6.5.10 in XFDF document specification.
            Content model: ( Named | XYZ | Fit | FitH | FitV | FitR | FitB | FitBH | FitBV )
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.DestObject.name">
            <summary>Represents Name attribute of Named element, a child of Dest element.</summary>
            <remarks>
            Represents Name attribute of Named element, a child of Dest element.
            Allows a destination to be referred to indirectly by means of a name object or a byte string.
            For more details see paragraph 6.5.25 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.DestObject.xyz">
            <summary>Represents the XYZ element, a child of the Dest element.</summary>
            <remarks>
            Represents the XYZ element, a child of the Dest element.
            Corresponds to the XYZ key in the destination syntax.
            Required attributes: Page, Left, Bottom, Right, Top.
            For more details see paragraph 6.5.32 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.DestObject.fit">
            <summary>Represents the Fit element, a child of the Dest element.</summary>
            <remarks>
            Represents the Fit element, a child of the Dest element.
            Corresponds to the Fit key in the destination syntax.
            Required attributes: Page.
            For more details see paragraph 6.5.13 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.DestObject.fitH">
            <summary>Represents the FitH element, a child of the Dest element.</summary>
            <remarks>
            Represents the FitH element, a child of the Dest element.
            Corresponds to the FitH key in the destination syntax.
            Required attributes: Page, Top.
            For more details see paragraph 6.5.17 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.DestObject.fitV">
            <summary>Represents the FitV element, a child of the Dest element.</summary>
            <remarks>
            Represents the FitV element, a child of the Dest element.
            Corresponds to the FitV key in the destination syntax.
            Required attributes: Page, Left.
            For more details see paragraph 6.5.19 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.DestObject.fitR">
            <summary>Represents the FitR element, a child of the Dest element.</summary>
            <remarks>
            Represents the FitR element, a child of the Dest element.
            Corresponds to the FitR key in the destination syntax.
            Required attributes: Page, Left, Bottom, Right, Top.
            For more details see paragraph 6.5.18 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.DestObject.fitB">
            <summary>Represents the FitB element, a child of the Dest element.</summary>
            <remarks>
            Represents the FitB element, a child of the Dest element.
            Corresponds to the FitB key in the destination syntax.
            Required attributes: Page.
            For more details see paragraph 6.5.14 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.DestObject.fitBH">
            <summary>Represents the FitBH element, a child of the Dest element.</summary>
            <remarks>
            Represents the FitBH element, a child of the Dest element.
            Corresponds to the FitBH key in the destination syntax.
            Required attributes: Page, Top.
            For more details see paragraph 6.5.15 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.DestObject.fitBV">
            <summary>Represents the FitBV element, a child of the Dest element.</summary>
            <remarks>
            Represents the FitBV element, a child of the Dest element.
            Corresponds to the FitBV key in the destination syntax.
            Required attributes: Page, Left.
            For more details see paragraph 6.5.16 in XFDF document specification.
            </remarks>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.#ctor">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Xfdf.DestObject"/>.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.GetName">
            <summary>Gets the Name attribute of Named element, a child of Dest element.</summary>
            <remarks>
            Gets the Name attribute of Named element, a child of Dest element.
            Allows a destination to be referred to indirectly by means of a name object or a byte string.
            For more details see paragraph 6.5.25 in XFDF document specification.
            </remarks>
            <returns>string value of the Name attribute.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.SetName(System.String)">
            <summary>Sets the Name attribute of Named element, a child of Dest element.</summary>
            <remarks>
            Sets the Name attribute of Named element, a child of Dest element.
            Allows a destination to be referred to indirectly by means of a name object or a byte string.
            </remarks>
            <param name="name">string value of the Name attribute</param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.GetXyz">
            <summary>Gets the XYZ element, a child of the Dest element.</summary>
            <remarks>
            Gets the XYZ element, a child of the Dest element.
            Corresponds to the XYZ key in the destination syntax.
            Required attributes: Page, Left, Bottom, Right, Top.
            For more details see paragraph 6.5.32 in XFDF document specification.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents XYZ of Dest element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.SetXyz(iText.Forms.Xfdf.FitObject)">
            <summary>Sets the XYZ element, a child of the Dest element.</summary>
            <remarks>
            Sets the XYZ element, a child of the Dest element.
            Corresponds to the XYZ key in the destination syntax.
            Required attributes: Page, Left, Bottom, Right, Top.
            </remarks>
            <param name="xyz">
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents XYZ of Dest element
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.GetFit">
            <summary>Gets the Fit element, a child of the Dest element.</summary>
            <remarks>
            Gets the Fit element, a child of the Dest element.
            Corresponds to the Fit key in the destination syntax.
            Required attributes: Page.
            For more details see paragraph 6.5.13 in XFDF document specification.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents Fit of Dest element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.SetFit(iText.Forms.Xfdf.FitObject)">
            <summary>Sets the Fit element, a child of the Dest element.</summary>
            <remarks>
            Sets the Fit element, a child of the Dest element.
            Corresponds to the Fit key in the destination syntax.
            Required attributes: Page.
            </remarks>
            <param name="fit">
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents Fit of Dest element
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.GetFitH">
            <summary>Gets the FitH element, a child of the Dest element.</summary>
            <remarks>
            Gets the FitH element, a child of the Dest element.
            Corresponds to the FitH key in the destination syntax.
            Required attributes: Page, Top.
            For more details see paragraph 6.5.17 in XFDF document specification.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitH of Dest element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.SetFitH(iText.Forms.Xfdf.FitObject)">
            <summary>Sets the FitH element, a child of the Dest element.</summary>
            <remarks>
            Sets the FitH element, a child of the Dest element.
            Corresponds to the FitH key in the destination syntax.
            Required attributes: Page, Top.
            </remarks>
            <param name="fitH">
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitH of Dest element
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.GetFitV">
            <summary>Gets the FitV element, a child of the Dest element.</summary>
            <remarks>
            Gets the FitV element, a child of the Dest element.
            Corresponds to the FitV key in the destination syntax.
            Required attributes: Page, Left.
            For more details see paragraph 6.5.19 in XFDF document specification.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitV of Dest element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.SetFitV(iText.Forms.Xfdf.FitObject)">
            <summary>Sets the FitV element, a child of the Dest element.</summary>
            <remarks>
            Sets the FitV element, a child of the Dest element.
            Corresponds to the FitV key in the destination syntax.
            Required attributes: Page, Left.
            </remarks>
            <param name="fitV">
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitV of Dest element
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.GetFitR">
            <summary>Gets the FitR element, a child of the Dest element.</summary>
            <remarks>
            Gets the FitR element, a child of the Dest element.
            Corresponds to the FitR key in the destination syntax.
            Required attributes: Page, Left, Bottom, Right, Top.
            For more details see paragraph 6.5.18 in XFDF document specification.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitR of Dest element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.SetFitR(iText.Forms.Xfdf.FitObject)">
            <summary>Sets the FitR element, a child of the Dest element.</summary>
            <remarks>
            Sets the FitR element, a child of the Dest element.
            Corresponds to the FitR key in the destination syntax.
            Required attributes: Page, Left, Bottom, Right, Top.
            </remarks>
            <param name="fitR">
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitR of Dest element
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.GetFitB">
            <summary>Sets the FitB element, a child of the Dest element.</summary>
            <remarks>
            Sets the FitB element, a child of the Dest element.
            Corresponds to the FitB key in the destination syntax.
            Required attributes: Page.
            For more details see paragraph 6.5.14 in XFDF document specification.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitB of Dest element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.SetFitB(iText.Forms.Xfdf.FitObject)">
            <summary>Gets the FitB element, a child of the Dest element.</summary>
            <remarks>
            Gets the FitB element, a child of the Dest element.
            Corresponds to the FitB key in the destination syntax.
            Required attributes: Page.
            For more details see paragraph 6.5.14 in XFDF document specification.
            </remarks>
            <param name="fitB">
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitB of Dest element
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.GetFitBH">
            <summary>Sets the FitBH element, a child of the Dest element.</summary>
            <remarks>
            Sets the FitBH element, a child of the Dest element.
            Corresponds to the FitBH key in the destination syntax.
            Required attributes: Page, Top.
            For more details see paragraph 6.5.15 in XFDF document specification.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitBH of Dest element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.SetFitBH(iText.Forms.Xfdf.FitObject)">
            <summary>Gets the FitBH element, a child of the Dest element.</summary>
            <remarks>
            Gets the FitBH element, a child of the Dest element.
            Corresponds to the FitBH key in the destination syntax.
            Required attributes: Page, Top.
            </remarks>
            <param name="fitBH">
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitBH of Dest element
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.GetFitBV">
            <summary>Sets the FitBV element, a child of the Dest element.</summary>
            <remarks>
            Sets the FitBV element, a child of the Dest element.
            Corresponds to the FitBV key in the destination syntax.
            Required attributes: Page, Left.
            For more details see paragraph 6.5.16 in XFDF document specification.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitBV of Dest element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.DestObject.SetFitBV(iText.Forms.Xfdf.FitObject)">
            <summary>Sets the FitBV element, a child of the Dest element.</summary>
            <remarks>
            Sets the FitBV element, a child of the Dest element.
            Corresponds to the FitBV key in the destination syntax.
            Required attributes: Page, Left.
            </remarks>
            <param name="fitBV">
            a
            <see cref="T:iText.Forms.Xfdf.FitObject"/>
            that represents FitBV of Dest element
            </param>
            <returns>
            this
            <see cref="T:iText.Forms.Xfdf.DestObject"/>
            instance.
            </returns>
        </member>
        <member name="T:iText.Forms.Xfdf.ElementContentEncodingFormat">
            <summary>Represents encoding attribute of data or resource elements (children of fileattachment and sound elements).
                </summary>
        </member>
        <member name="T:iText.Forms.Xfdf.FieldObject">
            <summary>Represents the field element, a child of the fields and field elements.</summary>
            <remarks>
            Represents the field element, a child of the fields and field elements.
            The field element corresponds to a form field.
            Content model: ( field* | value* | ( value? &amp; value-richtext? )).
            Required attributes: name.
            For more details see paragraph 6.3.2 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.FieldObject.name">
            <summary>Represents the name attribute of the field element.</summary>
            <remarks>
            Represents the name attribute of the field element.
            Corresponds to the T key in the field dictionary.
            In a hierarchical form field, the name is the partial field name.
            For more details see paragraph 6.3.2.2 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.FieldObject.value">
            <summary>
            Represents the value element, a child of the field element and contains the field's value, whose format may
            vary depending on the field type.
            </summary>
            <remarks>
            Represents the value element, a child of the field element and contains the field's value, whose format may
            vary depending on the field type.
            Corresponds to the V key in the FDF field dictionary.
            Content model: text string.
            For more details see paragraph 6.3.3 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.FieldObject.richTextValue">
            <summary>
            Represents the value-richtext element, a child of the field element and contains the field's value formatted as a
            rich text string.
            </summary>
            <remarks>
            Represents the value-richtext element, a child of the field element and contains the field's value formatted as a
            rich text string.
            Corresponds to the RV key in the variable text field dictionary.
            Content model: text strign or rich text string.
            Attributes: none.
            For more details see paragraph 6.3.4 in XFDF document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.FieldObject.containsRichText">
            <summary>Indicates if a value-richtext element is present inside the field.</summary>
        </member>
        <member name="F:iText.Forms.Xfdf.FieldObject.parent">
            <summary>Parent field of current field.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.#ctor">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Xfdf.FieldObject"/>.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Xfdf.FieldObject"/>.
            </summary>
            <param name="name">the name attribute of the field element</param>
            <param name="value">the field's value</param>
            <param name="containsRichText">indicates if a value-richtext element is present inside the field</param>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.GetName">
            <summary>Gets the string value of the name attribute of the field element.</summary>
            <remarks>
            Gets the string value of the name attribute of the field element.
            Corresponds to the T key in the field dictionary.
            In a hierarchical form field, the name is the partial field name.
            For more details see paragraph 6.3.2.2 in XFDF document specification.
            </remarks>
            <returns>
            
            <see cref="T:System.String"/>
            value of field name attribute.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.SetName(System.String)">
            <summary>Sets the string value of the name attribute of the field element.</summary>
            <remarks>
            Sets the string value of the name attribute of the field element.
            Corresponds to the T key in the field dictionary.
            In a hierarchical form field, the name is the partial field name.
            </remarks>
            <param name="name">
            
            <see cref="T:System.String"/>
            value of field name attribute
            </param>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.GetValue">
            <summary>
            Gets the string representation of the value element, a child of the field element and contains the field's value,
            whose format may vary depending on the field type.
            </summary>
            <remarks>
            Gets the string representation of the value element, a child of the field element and contains the field's value,
            whose format may vary depending on the field type.
            Corresponds to the V key in the FDF field dictionary.
            For more details see paragraph 6.3.3 in XFDF document specification.
            </remarks>
            <returns>
            
            <see cref="T:System.String"/>
            representation of inner value element of the field.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.SetValue(System.String)">
            <summary>
            Sets the string representation of the value element, a child of the field element and contains the field's value,
            whose format may vary depending on the field type.
            </summary>
            <remarks>
            Sets the string representation of the value element, a child of the field element and contains the field's value,
            whose format may vary depending on the field type.
            Corresponds to the V key in the FDF field dictionary.
            </remarks>
            <param name="value">
            
            <see cref="T:System.String"/>
            representation of inner value element of the field
            </param>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.GetRichTextValue">
            <summary>
            Gets the string representation of the value-richtext element, a child of the field element and contains the
            field's value formatted as a rich text string.
            </summary>
            <remarks>
            Gets the string representation of the value-richtext element, a child of the field element and contains the
            field's value formatted as a rich text string.
            Corresponds to the RV key in the variable text field dictionary.
            Content model: text strign or rich text string.
            For more details see paragraph 6.3.4 in XFDF document specification.
            </remarks>
            <returns>
            
            <see cref="T:System.String"/>
            representation of inner value-richtext element of the field.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.SetRichTextValue(System.String)">
            <summary>
            Sets the string representation of the value-richtext element, a child of the field element and contains the
            field's value formatted as a rich text string.
            </summary>
            <remarks>
            Sets the string representation of the value-richtext element, a child of the field element and contains the
            field's value formatted as a rich text string.
            Corresponds to the RV key in the variable text field dictionary.
            Content model: text string or rich text string.
            </remarks>
            <param name="richTextValue">
            
            <see cref="T:System.String"/>
            representation of inner value-richtext element of the field
            </param>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.IsContainsRichText">
            <summary>Gets a boolean indicating if a value-richtext element is present inside the field.</summary>
            <returns>true if a value-richtext element is present inside the field, false otherwise.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.SetContainsRichText(System.Boolean)">
            <summary>Sets a boolean indicating if a value-richtext element is present inside the field.</summary>
            <param name="containsRichText">a boolean indicating if a value-richtext element is present inside the field
                </param>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.GetParent">
            <summary>Gets a parent field of current field.</summary>
            <returns>
            parent
            <see cref="T:iText.Forms.Xfdf.FieldObject">field object</see>
            of the current field.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldObject.SetParent(iText.Forms.Xfdf.FieldObject)">
            <summary>Sets a parent field of current field.</summary>
            <param name="parent">
            
            <see cref="T:iText.Forms.Xfdf.FieldObject">field object</see>
            that is a parent of the current field
            </param>
        </member>
        <member name="T:iText.Forms.Xfdf.FieldsObject">
            <summary>Represents the fields element, a child of the xfdf element and is the container for form field elements.
                </summary>
            <remarks>
            Represents the fields element, a child of the xfdf element and is the container for form field elements.
            Content model: ( field* ).
            Attributes: none.
            For more details see paragraph 6.3.1 in Xfdf specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.FieldsObject.fieldList">
            <summary>Represents a list of children fields</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldsObject.#ctor">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Xfdf.FieldsObject"/>.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldsObject.GetFieldList">
            <summary>Gets a list of children fields</summary>
            <returns>
            
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            containing all children
            <see cref="T:iText.Forms.Xfdf.FieldObject">field objects</see>
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FieldsObject.AddField(iText.Forms.Xfdf.FieldObject)">
            <summary>Adds a new field to the list.</summary>
            <param name="field">FieldObject containing the info about the form field</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.FieldsObject">fields object</see>
            </returns>
        </member>
        <member name="T:iText.Forms.Xfdf.FitObject">
            <summary>Represent Fit, a child of the Dest element.</summary>
            <remarks>
            Represent Fit, a child of the Dest element.
            Content model: none.
            Attributes: depends of type of Fit (FitH, FitB, FitV etc.).
            For more details see paragraphs 6.5.13-6.5.19, 6.6.23 in Xfdf specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.FitObject.page">
            <summary>Represents the page displayed by current Fit element.</summary>
            <remarks>
            Represents the page displayed by current Fit element.
            Attribute of Fit, FitB, FitBH, FitBV, FitH, FitR, FitV, XYZ elements.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.FitObject.top">
            <summary>Vertical coordinate positioned at the top edge of the window.</summary>
        </member>
        <member name="F:iText.Forms.Xfdf.FitObject.bottom">
            <summary>Vertical coordinate positioned at the bottom edge of the window.</summary>
        </member>
        <member name="F:iText.Forms.Xfdf.FitObject.left">
            <summary>Horizontal coordinate positioned at the left edge of the window.</summary>
        </member>
        <member name="F:iText.Forms.Xfdf.FitObject.right">
            <summary>Horizontal coordinate positioned at the right edge of the window.</summary>
        </member>
        <member name="F:iText.Forms.Xfdf.FitObject.zoom">
            <summary>Corresponds to the zoom object in the destination syntax.</summary>
            <remarks>
            Corresponds to the zoom object in the destination syntax.
            Attribute of XYZ object.
            </remarks>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.#ctor(iText.Kernel.Pdf.PdfObject)">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Xfdf.FitObject"/>.
            </summary>
            <param name="page">the page displayed by current Fit element</param>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.GetPage">
            <summary>Gets the PdfObject representing the page displayed by current Fit element.</summary>
            <remarks>
            Gets the PdfObject representing the page displayed by current Fit element.
            Attribute of Fit, FitB, FitBH, FitBV, FitH, FitR, FitV, XYZ elements.
            </remarks>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.PdfObject">page</see>
            of the current Fit element.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.GetTop">
            <summary>Gets a float vertical coordinate positioned at the top edge of the window.</summary>
            <returns>top vertical coordinate.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.SetTop(System.Single)">
            <summary>Sets a float vertical coordinate positioned at the top edge of the window.</summary>
            <param name="top">vertical coordinate value</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.FitObject">fit object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.GetLeft">
            <summary>Gets a float horizontal coordinate positioned at the left edge of the window.</summary>
            <returns>left horizontal coordinate.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.SetLeft(System.Single)">
            <summary>Sets a float horizontal coordinate positioned at the left edge of the window.</summary>
            <param name="left">horizontal coordinate value</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.FitObject">fit object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.GetBottom">
            <summary>Gets a float vertical coordinate positioned at the bottom edge of the window.</summary>
            <returns>bottom vertical coordinate.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.SetBottom(System.Single)">
            <summary>Sets a float vertical coordinate positioned at the bottom edge of the window.</summary>
            <param name="bottom">vertical coordinate value</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.FitObject">fit object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.GetRight">
            <summary>Gets a float horizontal coordinate positioned at the right edge of the window.</summary>
            <returns>right horizontal coordinate.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.SetRight(System.Single)">
            <summary>Sets a float horizontal coordinate positioned at the right edge of the window.</summary>
            <param name="right">horizontal coordinate</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.FitObject">fit object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.GetZoom">
            <summary>Gets a float representing the zoom ratio.</summary>
            <remarks>
            Gets a float representing the zoom ratio.
            Attribute of XYZ object.
            </remarks>
            <returns>zoom ratio value.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FitObject.SetZoom(System.Single)">
            <summary>Sets a float representing the zoom ratio.</summary>
            <remarks>
            Sets a float representing the zoom ratio.
            Attribute of XYZ object.
            </remarks>
            <param name="zoom">ratio value</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.FitObject">fit object</see>.
            </returns>
        </member>
        <member name="T:iText.Forms.Xfdf.FObject">
            <summary>Represents f element, child of the xfdf element.</summary>
            <remarks>
            Represents f element, child of the xfdf element.
            Corresponds to the F key in the file dictionary.
            Specifies the source file or target file: the PDF document that this XFDF file was exported from or is intended to be
            imported into.
            Attributes: href.
            For more details see paragraph 6.2.2 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.FObject.href">
            <summary>The name of the source or target file.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.FObject.#ctor(System.String)">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Xfdf.FObject"/>.
            </summary>
            <param name="href">the name of the source or target file</param>
        </member>
        <member name="M:iText.Forms.Xfdf.FObject.GetHref">
            <summary>Gets the name of the source or target file.</summary>
            <returns>the name of the source or target file.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.FObject.SetHref(System.String)">
            <summary>Sets the name of the source or target file.</summary>
            <param name="href">the name of the source or target file</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.FObject">f object</see>.
            </returns>
        </member>
        <member name="T:iText.Forms.Xfdf.IdsObject">
            <summary>Represents ids element, child of the xfdf element.</summary>
            <remarks>
            Represents ids element, child of the xfdf element.
            Corresponds to the ID key in the file dictionary.
            The two attributes are file identifiers for the source or target file designated by the f element, taken
            from the ID entry in the file’s trailer dictionary.
            Attributes: original, modified.
            For more details see paragraph 6.2.3 in Xfdf document specification.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.IdsObject.original">
            <summary>
            This attribute corresponds to the permanent identifier which
            is based on the contents of the file at the time it was originally created.
            </summary>
            <remarks>
            This attribute corresponds to the permanent identifier which
            is based on the contents of the file at the time it was originally created.
            This value does not change when the file is incrementally updated.
            The value shall be a hexadecimal number.
            A common value for this is an MD5 checksum.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.IdsObject.modified">
            <summary>
            The attribute contains a unique identifier for the
            modified version of the pdf and corresponding xfdf document.
            </summary>
            <remarks>
            The attribute contains a unique identifier for the
            modified version of the pdf and corresponding xfdf document. The
            modified attribute corresponds to the changing identifier that is based
            on the file's contents at the time it was last updated.
            The value shall be a hexadecimal number.
            A common value for this is an MD5 checksum.
            </remarks>
        </member>
        <member name="M:iText.Forms.Xfdf.IdsObject.#ctor">
            <summary>
            Creates an instance of
            <see cref="T:iText.Forms.Xfdf.IdsObject"/>.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.IdsObject.GetOriginal">
            <summary>
            Gets the string value of the permanent identifier which
            is based on the contents of the file at the time it was originally created.
            </summary>
            <remarks>
            Gets the string value of the permanent identifier which
            is based on the contents of the file at the time it was originally created.
            This value does not change when the file is incrementally updated.
            The value shall be a hexadecimal number.
            </remarks>
            <returns>the permanent identifier value.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.IdsObject.SetOriginal(System.String)">
            <summary>
            Sets the string value of the permanent identifier which
            is based on the contents of the file at the time it was originally created.
            </summary>
            <remarks>
            Sets the string value of the permanent identifier which
            is based on the contents of the file at the time it was originally created.
            This value does not change when the file is incrementally updated.
            The value shall be a hexadecimal number.
            A common value for this is an MD5 checksum.
            </remarks>
            <param name="original">the permanent identifier value</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.IdsObject">ids object</see>.
            </returns>
        </member>
        <member name="M:iText.Forms.Xfdf.IdsObject.GetModified">
            <summary>
            Gets the string value of the unique identifier for the
            modified version of the pdf and corresponding xfdf document.
            </summary>
            <remarks>
            Gets the string value of the unique identifier for the
            modified version of the pdf and corresponding xfdf document. The
            modified attribute corresponds to the changing identifier that is based
            on the file's contents at the time it was last updated.
            The value shall be a hexadecimal number.
            </remarks>
            <returns>the unique identifier value.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.IdsObject.SetModified(System.String)">
            <summary>
            Sets the string value of the unique identifier for the
            modified version of the pdf and corresponding xfdf document.
            </summary>
            <remarks>
            Sets the string value of the unique identifier for the
            modified version of the pdf and corresponding xfdf document. The
            modified attribute corresponds to the changing identifier that is based
            on the file's contents at the time it was last updated.
            The value shall be a hexadecimal number.
            A common value for this is an MD5 checksum.
            </remarks>
            <param name="modified">the unique identifier value</param>
            <returns>
            current
            <see cref="T:iText.Forms.Xfdf.IdsObject">ids object</see>.
            </returns>
        </member>
        <member name="T:iText.Forms.Xfdf.Mode">
            <summary>Represents the mode attribute of data and resource elements.</summary>
            <remarks>
            Represents the mode attribute of data and resource elements.
            Does not correspond to a PDF key.
            </remarks>
        </member>
        <member name="T:iText.Forms.Xfdf.XfdfConstants">
            <summary>Class containing constants to be used in XFDF processing.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfFileUtils.CreateNewXfdfDocument">
            <summary>Creates a new xml-styled document for writing xfdf info.</summary>
            <remarks>Creates a new xml-styled document for writing xfdf info.</remarks>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfFileUtils.CreateXfdfDocumentFromStream(System.IO.Stream)">
            <summary>Creates a new xfdf document based on given input stream.</summary>
            <param name="inputStream"> the stream containing xfdf info.</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfFileUtils.SaveXfdfDocumentToFile(System.Xml.XmlDocument,System.IO.Stream)">
            <summary>Saves the info from output stream to xml-styled document.</summary>
            <param name="document"> the document to save info to.</param>
            <param name=" outputStream"> the stream containing xfdf info.</param>
        </member>
        <member name="T:iText.Forms.Xfdf.XfdfObject">
            <summary>Represents xfdf element, the top level element in an xfdf document.</summary>
            <remarks>
            Represents xfdf element, the top level element in an xfdf document.
            For more details see paragraph 6.2.1 in Xfdf document specification.
            Content model: ( f? &amp; ids? &amp; fields? &amp; annots? )
            Attributes: xml:space, xmlns.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.XfdfObject.f">
            <summary>Represents f element, child of the xfdf element.</summary>
            <remarks>
            Represents f element, child of the xfdf element.
            Corresponds to the F key in the file dictionary.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.XfdfObject.ids">
            <summary>Represents ids element, a child of the xfdf element.</summary>
            <remarks>
            Represents ids element, a child of the xfdf element.
            Corresponds to the ID key in the file dictionary.
            </remarks>
        </member>
        <member name="F:iText.Forms.Xfdf.XfdfObject.fields">
            <summary>Represents the fields element, a child of the xfdf element and is the container for form field elements.
                </summary>
        </member>
        <member name="F:iText.Forms.Xfdf.XfdfObject.annots">
            <summary>Represent annots element, a child of the xfdf element and is the container for annot elements.</summary>
        </member>
        <member name="F:iText.Forms.Xfdf.XfdfObject.attributes">
            <summary>A list of attributes of xfdf object.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.GetF">
            <summary>Gets the f element, child of the xfdf element.</summary>
            <remarks>
            Gets the f element, child of the xfdf element.
            Corresponds to the F key in the file dictionary.
            </remarks>
            <returns>the f element</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.SetF(iText.Forms.Xfdf.FObject)">
            <summary>Sets f element, child of the xfdf element.</summary>
            <remarks>
            Sets f element, child of the xfdf element.
            Corresponds to the F key in the file dictionary.
            </remarks>
            <param name="f">element</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.GetIds">
            <summary>Gets the ids element, child of the xfdf element.</summary>
            <remarks>
            Gets the ids element, child of the xfdf element.
            Corresponds to the ID key in the file dictionary.
            </remarks>
            <returns>the ids element</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.SetIds(iText.Forms.Xfdf.IdsObject)">
            <summary>Sets ids element, child of the xfdf element.</summary>
            <remarks>
            Sets ids element, child of the xfdf element.
            Corresponds to the ID key in the file dictionary.
            </remarks>
            <param name="ids">element</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.GetFields">
            <summary>Gets the fields element, a child of the xfdf element and is the container for form field elements.
                </summary>
            <returns>the fields element</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.SetFields(iText.Forms.Xfdf.FieldsObject)">
            <summary>Sets fields element, a child of the xfdf element and is the container for form field elements.</summary>
            <param name="fields">element</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.GetAnnots">
            <summary>Gets the annots element, a child of the xfdf element and is the container for annot elements.</summary>
            <returns>the annots element</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.SetAnnots(iText.Forms.Xfdf.AnnotsObject)">
            <summary>Sets the annots element, a child of the xfdf element and is the container for annot elements.</summary>
            <param name="annots">element</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.GetAttributes">
            <summary>Gets the list of attributes of xfdf object.</summary>
            <returns>the list of attributes</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.SetAttributes(System.Collections.Generic.IList{iText.Forms.Xfdf.AttributeObject})">
            <summary>Sets the list of attributes of xfdf object.</summary>
            <param name="attributes">list of attributes objects</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.MergeToPdf(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>Merges info from XfdfObject to pdf document.</summary>
            <param name="pdfDocument">the target document for merge.</param>
            <param name="pdfDocumentName">
            the name of the target document. Will be checked in the merge process to determined
            if it is the same as href attribute of f element of merged XfdfObject. If the names are
            different, a warning will be thrown.
            </param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.WriteToFile(System.String)">
            <summary>Writes info from XfdfObject to .xfdf file.</summary>
            <param name="filename">name of the target file.</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObject.WriteToFile(System.IO.Stream)">
            <summary>Writes info from XfdfObject to .xfdf file.</summary>
            <param name="os">target output stream.</param>
        </member>
        <member name="T:iText.Forms.Xfdf.XfdfObjectFactory">
            <summary>
            A factory for creating
            <see cref="T:iText.Forms.Xfdf.XfdfObject"/>
            objects.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectFactory.CreateXfdfObject(iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>Extracts data from pdf document acroform and annotations into XfdfObject.</summary>
            <param name="document">Pdf document for data extraction</param>
            <param name="filename">The name od pdf document for data extraction</param>
            <returns>XfdfObject containing data from pdf forms and annotations.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectFactory.CreateXfdfObject(System.IO.Stream)">
            <summary>Extracts data from input stream into XfdfObject.</summary>
            <remarks>Extracts data from input stream into XfdfObject. Typically input stream is based on .xfdf file.</remarks>
            <param name="xfdfInputStream">the input stream containing xml-styled xfdf data</param>
            <returns>XfdfObject containing original xfdf data.</returns>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertRectFromString(System.String)">
            <summary>
            Converts a string containing 2 or 4 float values into a
            <see cref="T:iText.Kernel.Geom.Rectangle"/>.
            </summary>
            <param name="rectString">
            the annotation rectangle, defining the location of the annotation on the page
            in default user space units. The value is four comma separated real numbers
            which may be positive or negative: (xLeft, yBottom, xRight, yTop). If only two coordinates
            are present, they should represent
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            width and height.
            </param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertFringeFromString(System.String)">
            <summary>Converts a string containing 4 float values into a PdfArray, representing rectangle fringe.</summary>
            <remarks>
            Converts a string containing 4 float values into a PdfArray, representing rectangle fringe.
            If the number of floats in the string is not equal to 4, returns and PdfArray with empty values.
            </remarks>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertDashesFromString(System.String)">
            <summary>
            Converts a string containing float values into a PdfArray, representing a pattern of dashes and gaps to be used
            in drawing a dashed border.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertDashesFromArray(iText.Kernel.Pdf.PdfArray)">
            <summary>
            Converts a PdfArray, representing a pattern of dashes and gaps to be used in drawing a dashed border,
            into a string containing float values.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertJustificationFromStringToInteger(System.String)">
            <summary>
            Converts a string containing justification value into an integer value representing a code specifying
            the form of quadding (justification).
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertJustificationFromIntegerToString(System.Int32)">
            <summary>
            Converts an integer value representing a code specifying the form of quadding (justification) into a string
            containing justification value.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.GetHighlightFullValue(iText.Kernel.Pdf.PdfName)">
            <summary>Converts H key value in the link annotation dictionary to Highlight value of xfdf link annotation attribute.
                </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.GetStyleFullValue(iText.Kernel.Pdf.PdfName)">
            <summary>Converts style (S key value) in the pdf annotation dictionary to style value of xfdf annotation attribute.
                </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertRectToString(iText.Kernel.Geom.Rectangle)">
            <summary>Converts a Rectangle to a string containing 4 float values.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertFloatToString(System.Single)">
            <summary>Converts float value to string with UTF-8 encoding.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertQuadPointsFromCoordsString(System.String)">
            <summary>Converts a string containing 8*n float values into a float array, representing quadPoints.</summary>
            <remarks>
            Converts a string containing 8*n float values into a float array, representing quadPoints.
            If the number of floats in the string is not a multiple of 8, returns an empty float array.
            </remarks>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertQuadPointsToCoordsString(System.Single[])">
            <summary>Converts a float array, representing quadPoints into a string containing 8*n float values.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertFlagsFromString(System.String)">
            <summary>
            Converts a string containing a comma separated list of names of the flags into an integer representation
            of the flags.
            </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertFlagsToString(iText.Kernel.Pdf.Annot.PdfAnnotation)">
            <summary>Converts an integer representation of the flags into a string with a comma separated list of names of the flags.
                </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertColorToString(System.Single[])">
            <summary>Converts an array of 3 floats into a hex string representing the rgb color.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertColorToString(iText.Kernel.Colors.Color)">
            <summary>Converts a Color object into a hex string representing the rgb color.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertColorFloatToHex(System.Single)">
            <summary>Converts float representation of the rgb color into a hex string representing the rgb color.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertIdToHexString(System.String)">
            <summary>Converts string containing id from decimal to hexadecimal format.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertColorFromString(System.String)">
            <summary>Converts string containing hex color code to Color object.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertColorFloatsFromString(System.String)">
            <summary>Converts string containing hex color code into an array of 3 integer values representing rgb color.
                </summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertVerticesToString(System.Single[])">
            <summary>Converts an array of float vertices to string.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertFringeToString(System.Single[])">
            <summary>Converts to string an array of floats representing the fringe.</summary>
            <remarks>
            Converts to string an array of floats representing the fringe.
            If the number of floats doesn't equal 4, an empty string is returned.
            </remarks>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertVerticesFromString(System.String)">
            <summary>Converts a string into an array of floats representing vertices.</summary>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertLineStartToString(System.Single[])">
            <summary>Returns a string representation of the start point of the line (x_1, y_1) based on given line array.
                </summary>
            <remarks>
            Returns a string representation of the start point of the line (x_1, y_1) based on given line array.
            If the line array doesn't contain 4 floats, returns an empty string.
            </remarks>
            <param name="line">an array of 4 floats representing the line (x_1, y_1, x_2, y_2)</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfObjectUtils.ConvertLineEndToString(System.Single[])">
            <summary>Returns a string representation of the end point of the line (x_2, y_2) based on given line array.
                </summary>
            <remarks>
            Returns a string representation of the end point of the line (x_2, y_2) based on given line array.
            If the line array doesn't contain 4 floats, returns an empty string.
            </remarks>
            <param name="line">an array of 4 floats representing the line (x_1, y_1, x_2, y_2)</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfReader.MergeXfdfIntoPdf(iText.Forms.Xfdf.XfdfObject,iText.Kernel.Pdf.PdfDocument,System.String)">
            <summary>Merges existing XfdfObject into pdf document associated with it.</summary>
            <param name="xfdfObject">The object to be merged.</param>
            <param name="pdfDocument">The associated pdf document.</param>
            <param name="pdfDocumentName">The name of the associated pdf document.</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfReader.MergeFields(iText.Forms.Xfdf.FieldsObject,iText.Forms.PdfAcroForm)">
            <summary>
            Merges existing FieldsObject and children FieldObject entities into the form of the pdf document
            associated with it.
            </summary>
            <param name="fieldsObject">object containing acroform fields data to be merged.</param>
            <param name="form">acroform to be filled with xfdf data.</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfReader.MergeAnnotations(iText.Forms.Xfdf.AnnotsObject,iText.Kernel.Pdf.PdfDocument)">
            <summary>Merges existing XfdfObject into pdf document associated with it.</summary>
            <param name="annotsObject">The AnnotsObject with children AnnotObject entities to be mapped into PdfAnnotations.
                </param>
            <param name="pdfDocument">The associated pdf document.</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfWriter.#ctor(System.IO.Stream)">
            <summary>Creates a XfdfWriter for output stream specified.</summary>
            <param name="outputStream">A stream to write xfdf file into.</param>
        </member>
        <member name="M:iText.Forms.Xfdf.XfdfWriter.Write(iText.Forms.Xfdf.XfdfObject)">
            <summary>
            Writes data from
            <see cref="T:iText.Forms.Xfdf.XfdfObject"/>
            into a xfdf data file.
            </summary>
            <param name="xfdfObject">
            
            <see cref="T:iText.Forms.Xfdf.XfdfObject"/>
            containing the data.
            </param>
        </member>
    </members>
</doc>
