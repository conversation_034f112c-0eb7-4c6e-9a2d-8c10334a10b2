using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class test_query : System.Web.UI.Page
{
	private dbconnection db = new dbconnection();

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			lblConnectionResult.Text = "點擊按鈕測試資料庫連線";
			lblQueryResult.Text = "選擇一個視圖進行測試";
		}
	}

	protected void btnTestConnection_Click(object sender, EventArgs e)
	{
		try
		{
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			lblConnectionResult.Text = $"✅ 連線成功！<br/>資料庫: {db.conn.Database}<br/>伺服器: {db.conn.DataSource}<br/>版本: {db.conn.ServerVersion}";
			lblConnectionResult.CssClass = "success";

			db.conn.Close();
		}
		catch (Exception ex)
		{
			lblConnectionResult.Text = $"❌ 連線失敗: {ex.Message}";
			lblConnectionResult.CssClass = "error";
		}
	}

	protected void btnTestV1_Click(object sender, EventArgs e)
	{
		TestView("V_recowish1", "日間部");
	}

	protected void btnTestV3_Click(object sender, EventArgs e)
	{
		TestView("V_recowish3", "碩士班");
	}

	protected void btnTestV5_Click(object sender, EventArgs e)
	{
		TestView("V_recowish5", "進修部");
	}

	private void TestView(string viewName, string description)
	{
		try
		{
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			string sql = $@"SELECT [學制], [科系], [實名制人數], [報到人數], [繳交畢業證書人數], [繳費人數] 
                           FROM [school].[dbo].[{viewName}]";

			using (SqlCommand cmd = new SqlCommand(sql, db.conn))
			{
				using (SqlDataAdapter da = new SqlDataAdapter(cmd))
				{
					DataTable dt = new DataTable();
					da.Fill(dt);

					if (dt.Rows.Count > 0)
					{
						lblQueryResult.Text = $"✅ {description} ({viewName}) 查詢成功，獲得 {dt.Rows.Count} 筆記錄";
						lblQueryResult.CssClass = "success";

						gvTestData.DataSource = dt;
						gvTestData.DataBind();
					}
					else
					{
						lblQueryResult.Text = $"⚠️ {description} ({viewName}) 查詢成功，但沒有資料";
						lblQueryResult.CssClass = "error";
						gvTestData.DataSource = null;
						gvTestData.DataBind();
					}
				}
			}
		}
		catch (Exception ex)
		{
			lblQueryResult.Text = $"❌ {description} ({viewName}) 查詢失敗: {ex.Message}";
			lblQueryResult.CssClass = "error";
			gvTestData.DataSource = null;
			gvTestData.DataBind();
		}
		finally
		{
			if (db.conn.State == ConnectionState.Open)
			{
				db.conn.Close();
			}
		}
	}
}
