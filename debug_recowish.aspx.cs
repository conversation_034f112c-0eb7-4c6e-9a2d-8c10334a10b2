using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class debug_recowish : System.Web.UI.Page
{
    dbconnection db = new dbconnection();
    StringBuilder results = new StringBuilder();

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            lblResults.Text = "🔍 準備開始診斷 Recowish 系統...";
        }
    }

    protected void TestConnection_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h4>🔗 資料庫連線測試</h4>");

        try
        {
            results.AppendLine($"<p>📍 連線字串資訊: {GetSafeConnectionString()}</p>");

            if (db.conn.State != ConnectionState.Open)
                db.conn.Open();

            results.AppendLine("<p><span class='status-indicator status-ok'></span>✅ 資料庫連線成功！</p>");
            results.AppendLine($"<p>🏢 資料庫名稱: {db.conn.Database}</p>");
            results.AppendLine($"🖥️ 伺服器: {db.conn.DataSource}</p>");
            results.AppendLine($"⏱️ 連線逾時: {db.conn.ConnectionTimeout} 秒</p>");
            results.AppendLine($"📊 連線狀態: {db.conn.State}</p>");

            db.conn.Close();
        }
        catch (Exception ex)
        {
            results.AppendLine($"<p><span class='status-indicator status-error'></span>❌ 連線失敗: {ex.Message}</p>");
            results.AppendLine($"<p>📝 詳細錯誤: {ex.ToString()}</p>");
        }

        lblResults.Text = results.ToString();
    }

    protected void TestViews_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h4>👁️ 視圖存在性檢查</h4>");

        try
        {
            if (db.conn.State != ConnectionState.Open)
                db.conn.Open();

            string[] viewNames = { "V_recowish1", "V_recowish3", "V_recowish5" };
            string[] descriptions = { "日間部", "碩士班", "進修部" };

            for (int i = 0; i < viewNames.Length; i++)
            {
                results.AppendLine($"<h5>📊 檢查 {descriptions[i]} 視圖: {viewNames[i]}</h5>");

                // 檢查視圖是否存在
                SqlCommand cmd = new SqlCommand($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = '{viewNames[i]}'", db.conn);
                int viewExists = (int)cmd.ExecuteScalar();

                if (viewExists > 0)
                {
                    results.AppendLine($"<p><span class='status-indicator status-ok'></span>✅ 視圖存在</p>");

                    // 檢查資料筆數
                    cmd.CommandText = $"SELECT COUNT(*) FROM [school].[dbo].[{viewNames[i]}]";
                    var rowCount = cmd.ExecuteScalar();
                    results.AppendLine($"<p>📊 資料筆數: {rowCount}</p>");

                    // 檢查欄位結構
                    cmd.CommandText = $"SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{viewNames[i]}' ORDER BY ORDINAL_POSITION";
                    using (var reader = cmd.ExecuteReader())
                    {
                        results.AppendLine("<p>📋 欄位結構:</p><ul>");
                        while (reader.Read())
                        {
                            results.AppendLine($"<li>{reader["COLUMN_NAME"]} ({reader["DATA_TYPE"]})</li>");
                        }
                        results.AppendLine("</ul>");
                        reader.Close();
                    }

                    // 顯示前3筆資料
                    if (Convert.ToInt32(rowCount) > 0)
                    {
                        cmd.CommandText = $"SELECT TOP 3 * FROM [school].[dbo].[{viewNames[i]}]";
                        using (var reader = cmd.ExecuteReader())
                        {
                            results.AppendLine("<p>📝 前3筆資料:</p>");
                            int recordNum = 1;
                            while (reader.Read())
                            {
                                results.AppendLine($"<p><strong>第{recordNum}筆:</strong> ");
                                for (int j = 0; j < reader.FieldCount; j++)
                                {
                                    results.AppendLine($"{reader.GetName(j)}={reader[j]} | ");
                                }
                                results.AppendLine("</p>");
                                recordNum++;
                            }
                            reader.Close();
                        }
                    }
                    else
                    {
                        results.AppendLine("<p><span class='status-indicator status-warning'></span>⚠️ 視圖存在但沒有資料</p>");
                    }
                }
                else
                {
                    results.AppendLine($"<p><span class='status-indicator status-error'></span>❌ 視圖不存在</p>");
                }
                results.AppendLine("<hr/>");
            }

            db.conn.Close();
        }
        catch (Exception ex)
        {
            results.AppendLine($"<p><span class='status-indicator status-error'></span>❌ 檢查失敗: {ex.Message}</p>");
        }

        lblResults.Text = results.ToString();
    }

    protected void TestData_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h4>📊 資料查詢測試</h4>");

        try
        {
            if (db.conn.State != ConnectionState.Open)
                db.conn.Open();

            // 測試實際的查詢語句
            string[] sqlQueries = {
                "SELECT [學制],[科系],[實名制人數],[報到人數],[繳交畢業證書人數],[繳費人數] FROM [school].[dbo].[V_recowish1]",
                "SELECT [學制],[科系],[實名制人數],[報到人數],[繳交畢業證書人數],[繳費人數] FROM [school].[dbo].[V_recowish3]",
                "SELECT [學制],[科系],[實名制人數],[報到人數],[繳交畢業證書人數],[繳費人數] FROM [school].[dbo].[V_recowish5]"
            };
            string[] queryNames = { "日間部查詢", "碩士班查詢", "進修部查詢" };

            for (int i = 0; i < sqlQueries.Length; i++)
            {
                results.AppendLine($"<h5>🔍 {queryNames[i]}</h5>");
                results.AppendLine($"<p>SQL: <code>{sqlQueries[i]}</code></p>");

                try
                {
                    SqlCommand cmd = new SqlCommand(sqlQueries[i], db.conn);
                    SqlDataAdapter da = new SqlDataAdapter(cmd);
                    DataTable dt = new DataTable();
                    da.Fill(dt);

                    results.AppendLine($"<p><span class='status-indicator status-ok'></span>✅ 查詢成功，獲得 {dt.Rows.Count} 筆資料</p>");

                    if (dt.Rows.Count > 0 && i == 0) // 只顯示第一個查詢的結果
                    {
                        gvPreview.DataSource = dt;
                        gvPreview.DataBind();
                    }
                }
                catch (Exception queryEx)
                {
                    results.AppendLine($"<p><span class='status-indicator status-error'></span>❌ 查詢失敗: {queryEx.Message}</p>");
                }
                results.AppendLine("<hr/>");
            }

            db.conn.Close();
        }
        catch (Exception ex)
        {
            results.AppendLine($"<p><span class='status-indicator status-error'></span>❌ 測試失敗: {ex.Message}</p>");
        }

        lblResults.Text = results.ToString();
    }

    protected void TestPermissions_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h4>🔐 權限檢查</h4>");

        // 檢查 Session 狀態
        results.AppendLine("<h5>📋 Session 狀態檢查</h5>");
        
        if (Session["tech_no"] != null)
        {
            results.AppendLine($"<p><span class='status-indicator status-ok'></span>✅ tech_no: {Session["tech_no"]}</p>");
        }
        else
        {
            results.AppendLine("<p><span class='status-indicator status-warning'></span>⚠️ tech_no: 未設定</p>");
        }

        if (Session["master_single"] != null)
        {
            results.AppendLine($"<p><span class='status-indicator status-ok'></span>✅ master_single: {Session["master_single"]}</p>");
            
            if (Session["master_single"].ToString() == "3")
            {
                results.AppendLine("<p><span class='status-indicator status-ok'></span>✅ 權限足夠 (master_single = 3)</p>");
            }
            else
            {
                results.AppendLine("<p><span class='status-indicator status-error'></span>❌ 權限不足 (需要 master_single = 3)</p>");
            }
        }
        else
        {
            results.AppendLine("<p><span class='status-indicator status-warning'></span>⚠️ master_single: 未設定</p>");
        }

        lblResults.Text = results.ToString();
    }

    protected void RunAll_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h3>🚀 完整診斷報告</h3>");
        results.AppendLine($"<p>📅 執行時間: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
        results.AppendLine("<hr/>");

        // 執行所有測試
        TestConnection_Click(sender, e);
        string connectionResult = results.ToString();
        
        TestViews_Click(sender, e);
        string viewResult = results.ToString();
        
        TestData_Click(sender, e);
        string dataResult = results.ToString();
        
        TestPermissions_Click(sender, e);
        string permissionResult = results.ToString();

        // 合併所有結果
        results.Clear();
        results.AppendLine("<h3>🚀 完整診斷報告</h3>");
        results.AppendLine($"<p>📅 執行時間: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
        results.AppendLine("<hr/>");
        results.AppendLine(connectionResult);
        results.AppendLine(viewResult);
        results.AppendLine(dataResult);
        results.AppendLine(permissionResult);

        // 生成修復建議
        GenerateFixSuggestions();

        lblResults.Text = results.ToString();
    }

    private void GenerateFixSuggestions()
    {
        StringBuilder suggestions = new StringBuilder();
        suggestions.AppendLine("<h4>🔧 修復建議</h4>");
        
        suggestions.AppendLine("<div class='info'>");
        suggestions.AppendLine("<h5>📝 常見問題解決方案：</h5>");
        suggestions.AppendLine("<ol>");
        suggestions.AppendLine("<li><strong>視圖不存在：</strong>請聯繫資料庫管理員建立 V_recowish1、V_recowish3、V_recowish5 視圖</li>");
        suggestions.AppendLine("<li><strong>沒有資料：</strong>檢查基礎資料表是否有資料，確認資料匯入是否正確</li>");
        suggestions.AppendLine("<li><strong>權限問題：</strong>確保 Session['master_single'] = '3'</li>");
        suggestions.AppendLine("<li><strong>連線問題：</strong>檢查網路連線和資料庫伺服器狀態</li>");
        suggestions.AppendLine("</ol>");
        suggestions.AppendLine("</div>");

        fixSuggestions.InnerHtml = suggestions.ToString();
    }

    private string GetSafeConnectionString()
    {
        try
        {
            string connStr = db.conn.ConnectionString;
            // 隱藏密碼
            if (connStr.Contains("pwd="))
            {
                int pwdStart = connStr.IndexOf("pwd=") + 4;
                int pwdEnd = connStr.IndexOf(";", pwdStart);
                if (pwdEnd == -1) pwdEnd = connStr.Length;

                string hiddenPassword = new string('*', Math.Min(8, pwdEnd - pwdStart));
                connStr = connStr.Substring(0, pwdStart) + hiddenPassword + connStr.Substring(pwdEnd);
            }
            return connStr;
        }
        catch
        {
            return "無法獲取連線字串資訊";
        }
    }
}
