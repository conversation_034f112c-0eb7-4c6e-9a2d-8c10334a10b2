﻿<%@ Master Language="C#" AutoEventWireup="true" CodeFile="Site.Master.cs" Inherits="Site" %>

<!DOCTYPE html>

<html>
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>招生決策管理系統</title>
    <!-- Tailwind CSS CDN (moved to top) -->
    <script src="https://cdn.tailwindcss.com"></script>
    <%--<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet" />--%>
    <!-- 清除HTML預設格式 -->
    <%-- <link rel="stylesheet" href="css/bootstrap-reboot.min.css" /> --%>
    <!-- bootstrap網格系統 -->
    <%-- <link rel="stylesheet" href="css/bootstrap-grid.min.css" /> --%>
    <!-- bootstrap -->
    <!-- <link rel="stylesheet" href="css/bootstrap.min.css" /> -->
    <!-- <link rel="stylesheet" href="css/site.css" /> -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style type="text/css">
        .sidebar {
            margin-left: 0px;
            padding-left: 0px;
            width: 240px; /* 固定左側導航欄的寬度 */
            height: 100vh; /* 讓左側導航欄佔滿整個垂直高度 基於視窗高度，適用於需要元素高度固定為視窗高度的情況。 */
            overflow-y: auto; /* 當內容超出時顯示垂直滾動條 */
            background-color: lightgoldenrodyellow; /* 根據需要調整背景色 */
            padding: 20px;
            transition: all 0.3s ease;
        }

            /* NEW Sidebar Styles */
            .new-sidebar {
                width: 260px;
                height: calc(100vh - 60px); /* 減去header的高度 */
                display: flex;
                flex-direction: column;
                transition: width 0.3s ease;
                position: fixed; /* 固定位置 */
                z-index: 5; /* 確保在適當層級，但低於header */
                margin-top: 60px; /* 為頂部header留出空間 */
                bottom: 0; /* 確保側邊欄延伸到底部 */
                overflow-y: auto; /* 允許內容滾動 */
            }
            .new-sidebar-header {
                padding: 1rem;
                margin-bottom: 1rem;
                border-bottom: 1px solid #374151; /* Tailwind gray-700 */
                text-align: center;
                flex-shrink: 0; /* Ensure header doesn't shrink */
            }
            .new-sidebar-header .user-role {
                display: block;
                font-size: 1.125rem; /* Tailwind text-lg */
                font-weight: 600; /* Tailwind semibold */
            }
            .new-sidebar-nav {
                flex-grow: 1;
                overflow-y: auto;
                min-height: 0; /* Added to help with flex overflow */
                list-style: none; /* Ensure no bullets */
                margin: 0; /* Ensure no default margin */
            }
            .new-sidebar-nav .nav-item {
                margin-bottom: 0.25rem; /* Adjusted space between items */
                padding: 0 1rem; /* Added padding to li instead of ul */
            }
            .new-sidebar-nav .nav-link,
            .new-sidebar-nav .dropdown-btn-new {
                display: flex;
                align-items: center;
                padding: 0.75rem 0.5rem;
                border-radius: 0.375rem; /* rounded-md */
                text-decoration: none;
                transition: background-color 0.2s ease, color 0.2s ease;
            }
            .new-sidebar-nav .nav-link:hover,
            .new-sidebar-nav .dropdown-btn-new:hover {
                background-color: #374151; /* Tailwind gray-700 */
            }
            .new-sidebar-nav .nav-link i,
            .new-sidebar-nav .dropdown-btn-new i.menu-icon {
                width: 1.5rem; /* w-6 */
                height: 1.5rem; /* h-6 */
                margin-right: 0.75rem; /* mr-3 */
                text-align: center;
                line-height: 1.5rem;
            }
            .new-sidebar-nav .dropdown-btn-new .arrow-icon {
                margin-left: auto;
                transition: transform 0.3s ease;
            }
            .new-sidebar-nav .dropdown-btn-new.open .arrow-icon {
                transform: rotate(180deg);
            }
            .dropdown-container-new {
                margin-top: 0.25rem; /* mt-1 */
                display: none; /* Initially hidden */
                overflow: hidden;
                background-color: rgba(0,0,0,0.1);
                border-radius: 0.375rem;
            }
            .dropdown-container-new .nav-link {
                padding-left: 2.5rem;
                font-size: 0.875rem;
            }
            .new-sidebar-footer {
                padding: 1rem;
                border-top: 1px solid #374151; /* Tailwind gray-700 */
                flex-shrink: 0;
                margin-top: auto; /* 將 footer 推到 flex 容器底部 */
                position: sticky; /* 確保在滾動時保持可見 */
                bottom: 0; /* 固定在底部 */
                background-color: #1f2937; /* 與側邊欄背景相同 */
                z-index: 6; /* 確保在側邊欄內容之上 */
            }
            .new-sidebar-footer .btn-logout-new {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                padding: 0.85rem 0.75rem; /* Increased top/bottom padding slightly from 0.75rem (12px) to 0.85rem (~13.6px) */
                /* background-color: #00ff00 !important; /* Bright green for testing visibility - REMOVED */
                /* color: black !important; /* Black text for testing visibility - REMOVED */
                /* Tailwind classes will be applied directly in HTML for bg-red-600 and text-white */
                border-radius: 0.375rem;
                text-decoration: none;
                transition: background-color 0.2s ease, transform 0.2s ease; /* Added transform transition */
                line-height: 1; /* Ensure line-height doesn't add extra space */
            }
            .new-sidebar-footer .btn-logout-new:hover { /* Added hover effect for consistency */
                transform: scale(1.02);
            }
            .new-sidebar-footer .btn-logout-new:active { /* Added active effect for consistency */
                transform: scale(0.98);
            }
            .new-sidebar-footer .btn-logout-new i {
                width: 1.25rem; /* 20px */
                height: 1.25rem; /* 20px */
                margin-right: 0.5rem; /* 8px */
                font-size: 1rem; /* Explicitly set icon font-size if needed, 1rem = 16px */
                line-height: 1; /* Match button's line-height */
            }
            /* Original sidebar hide, new content area adjust */
            .sidebar { display: none; }
            .content-area {
                margin-left: 260px;
                padding: 1.5rem;
                transition: margin-left 0.3s ease;
                margin-top: 60px; /* 為頂部header留出空間 */
                min-height: calc(100vh - 120px); /* 確保內容區域至少有足夠高度，減去header和footer的高度 */
                padding-bottom: 2rem; /* 增加底部內邊距，避免內容與footer重疊 */
                overflow-x: hidden; /* Prevent content from breaking layout horizontally */
            }

    </style>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body class="bg-gray-100 flex flex-col min-h-screen">
    <div class="wrapper flex flex-col flex-grow">
        <header class="header bg-gray-800 text-white shadow-md" style="position: fixed; top: 0; left: 0; right: 0; z-index: 10; height: 60px;">
            <div class="container mx-auto flex justify-between items-center h-full p-4">
                <div class="flex items-center">
                    <a href="Home.aspx" class="mr-4 text-2xl font-bold">
                        中信科大
                    </a>
                    <asp:Label ID="labsys" runat="server" Text="招生決策管理系統" CssClass="text-xl font-semibold"></asp:Label>
                </div>
                <div>
                </div>
            </div>
        </header>

        <form id="form1" runat="server" enctype="multipart/form-data" class="flex flex-grow"> 
            <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
            
            <nav class="new-sidebar bg-gray-800 text-gray-100 flex-shrink-0"> 
                <div class="new-sidebar-header">
                    <span class="user-role">
                        <asp:LoginName ID="LoginName1" runat="server" FormatString="使用者：{0}" />
                    </span>
                </div>
                <ul class="new-sidebar-nav">
                    <li class="nav-item" id="adminMenuControl" runat="server" visible="false">
                        <a href="#" class="dropdown-btn-new">
                            <i class="fas fa-user-shield menu-icon"></i>
                            <span>查詢與分析</span>
                            <i class="fas fa-chevron-down arrow-icon"></i>
                        </a>
                        <div class="dropdown-container-new">
                            <a href="Analyze_recowish01.aspx" class="nav-link">人數分析項目資料</a>
                            <a href="Analyze_recowish02.aspx" class="nav-link">分析項目</a>
                            <a href="Analyze_recowish03.aspx" class="nav-link">分析項目</a>
                            <a href="Analyze_recowish04.aspx" class="nav-link">分析項目</a>
                            <a href="Analyze_recowish05.aspx" class="nav-link">分析項目</a>
                            <a href="Analyze_recowish06.aspx" class="nav-link">分析項目</a>
                            <a href="simple_database_check.aspx" class="nav-link">最終功能驗證測試</a>
                        </div>
                    </li>
                    <%--<li class="nav-item" id="departmentAssistantMenu" runat="server" visible="false">
                        <a href="#" class="dropdown-btn-new">
                            <i class="fas fa-users-cog menu-icon"></i>
                            <span>系助理維護</span>
                            <i class="fas fa-chevron-down arrow-icon"></i>
                        </a>
                        <div class="dropdown-container-new">
                            <a href="add_edit.aspx" class="nav-link">查詢與更新</a>
                            <a href="StatisticsReport.aspx" class="nav-link">統計報表</a>
                            <a href="FillRate2.aspx" class="nav-link">班級更新率</a>
                            <a href="departmentAuthority.aspx" class="nav-link">老師權限設定</a>
                        </div>
                    </li>
                    <li class="nav-item" id="teacherMenu" runat="server" visible="false">
                        <a href="#" class="dropdown-btn-new">
                            <i class="fas fa-chalkboard-teacher menu-icon"></i>
                            <span>教師維護</span>
                            <i class="fas fa-chevron-down arrow-icon"></i>
                        </a>
                        <div class="dropdown-container-new">
                            <a href="add_edit.aspx" class="nav-link">查詢與更新</a>
                        </div>
                    </li>--%>
                     <li class="nav-item">
                        <a href="Home.aspx" class="nav-link">
                            <i class="fas fa-home menu-icon"></i>
                            <span>首頁</span>
                        </a>
                    </li>
                </ul>
                <div class="new-sidebar-footer">
                    <a href="Logout.aspx" class="btn-logout-new bg-red-600 hover:bg-red-700 text-white">
                        <i class="fas fa-sign-out-alt"></i>
                        登出
                    </a>
                </div>
            </nav>

            <div class="content-area flex-grow">
                    <asp:ContentPlaceHolder ID="MainContent" runat="server">
                    </asp:ContentPlaceHolder>
                </div>
            </div>
        </form>

        <div fdata="" class="footer" style="position: relative; z-index: 5; margin-left: 260px; padding: 15px; text-align: center; background-color: #f8f9fa; border-top: 1px solid #e9ecef;">
            <a class="txt-center">Copyright &copy;<script>document.write(new Date().getFullYear());</script>
                All rights reserved <i class="fa fa-heart-o" aria-hidden="true"></i>by <a href="https://www.ctbctech.edu.tw" target="_blank">中信科技大學</a> & Made with <a href="https://computercenter.ctbctech.edu.tw/" target="_blank">資訊系統整合中心</a>
            </a>
        </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.js"></script>
    <script src="js/jquery-ui.min.js"></script>

    <script>
        $(document).ready(function () {
            $('.dropdown-btn-new').click(function (e) {
                e.preventDefault();
                var $this = $(this);
                var $dropdownContainer = $this.next('.dropdown-container-new');
                var $caret = $this.find('.arrow-icon');

                if (!$this.hasClass('open')) {
                    $('.dropdown-container-new').slideUp(300);
                    $('.dropdown-btn-new').removeClass('open');
                    $('.dropdown-btn-new .arrow-icon').removeClass('rotate');
                }

                $this.toggleClass('open');
                $caret.toggleClass('rotate');
                $dropdownContainer.slideToggle(300);
            });
        });
    </script>
</body>
</html>
