<%@ Page Language="C#" AutoEventWireup="true" CodeFile="database_check.aspx.cs" Inherits="database_check" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">

<head runat="server">
	<title>資料庫檢查</title>
	<style>
		body {
			font-family: Arial, sans-serif;
			margin: 20px;
		}

		.result {
			background: #f5f5f5;
			padding: 15px;
			margin: 10px 0;
			border-radius: 5px;
			border-left: 4px solid #007cba;
		}

		.error {
			border-left-color: #dc3545;
		}

		.success {
			border-left-color: #28a745;
		}

		table {
			border-collapse: collapse;
			width: 100%;
			margin: 10px 0;
		}

		th,
		td {
			border: 1px solid #ddd;
			padding: 8px;
			text-align: left;
		}

		th {
			background-color: #f2f2f2;
		}

		.btn {
			background: #007cba;
			color: white;
			padding: 10px 20px;
			border: none;
			border-radius: 4px;
			cursor: pointer;
			margin: 5px;
		}

		.btn:hover {
			background: #005a8b;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<h1>資料庫連線和視圖檢查</h1>

		<div>
			<asp:Button ID="btnCheckConnection" runat="server" Text="檢查連線" CssClass="btn" OnClick="CheckConnection_Click" />
			<asp:Button ID="btnCheckViews" runat="server" Text="檢查視圖" CssClass="btn" OnClick="CheckViews_Click" />
			<asp:Button ID="btnCheckTables" runat="server" Text="檢查原始表格" CssClass="btn" OnClick="CheckTables_Click" />
			<asp:Button ID="btnTestData" runat="server" Text="測試數據查詢" CssClass="btn" OnClick="TestData_Click" />
		</div>

		<br />

		<asp:Literal ID="litResults" runat="server"></asp:Literal>

	</form>
</body>

</html>