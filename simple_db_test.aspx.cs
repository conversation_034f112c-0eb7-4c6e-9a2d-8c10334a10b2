using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class simple_db_test : System.Web.UI.Page
{
	dbconnection db = new dbconnection();

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			TestDatabaseConnection();
		}
	}

	private void TestDatabaseConnection()
	{
		string result = "🔍 開始資料庫連接測試...<br/>";

		try
		{
			// 測試連接
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			result += "✅ 資料庫連接成功<br/>";
			result += "📋 資料庫名稱: " + db.conn.Database + "<br/>";
			result += "📋 伺服器: " + db.conn.DataSource + "<br/><br/>";

			// 測試 V_recowish1 表格
			result += "🔍 測試 V_recowish1 表格...<br/>";
			string sql1 = "SELECT COUNT(*) FROM [school].[dbo].[V_recowish1]";
			using (SqlCommand cmd1 = new SqlCommand(sql1, db.conn))
			{
				try
				{
					int count1 = (int)cmd1.ExecuteScalar();
					result += "✅ V_recowish1 記錄數: " + count1 + "<br/>";
				}
				catch (Exception ex1)
				{
					result += "❌ V_recowish1 錯誤: " + ex1.Message + "<br/>";
				}
			}

			// 測試 V_recowish3 表格
			result += "🔍 測試 V_recowish3 表格...<br/>";
			string sql3 = "SELECT COUNT(*) FROM [school].[dbo].[V_recowish3]";
			using (SqlCommand cmd3 = new SqlCommand(sql3, db.conn))
			{
				try
				{
					int count3 = (int)cmd3.ExecuteScalar();
					result += "✅ V_recowish3 記錄數: " + count3 + "<br/>";
				}
				catch (Exception ex3)
				{
					result += "❌ V_recowish3 錯誤: " + ex3.Message + "<br/>";
				}
			}

			// 測試 V_recowish5 表格
			result += "🔍 測試 V_recowish5 表格...<br/>";
			string sql5 = "SELECT COUNT(*) FROM [school].[dbo].[V_recowish5]";
			using (SqlCommand cmd5 = new SqlCommand(sql5, db.conn))
			{
				try
				{
					int count5 = (int)cmd5.ExecuteScalar();
					result += "✅ V_recowish5 記錄數: " + count5 + "<br/>";
				}
				catch (Exception ex5)
				{
					result += "❌ V_recowish5 錯誤: " + ex5.Message + "<br/>";
				}
			}

			// 測試完整查詢 V_recowish1
			result += "<br/>🔍 測試完整查詢 V_recowish1...<br/>";
			string fullSql = @"SELECT [學制]
                                     ,[科系]
                                     ,[實名制人數]
                                     ,[報到人數]
                                     ,[繳交畢業證書人數]
                                     ,[繳費人數]
                              FROM [school].[dbo].[V_recowish1]";

			DataSet ds = new DataSet();
			SqlDataAdapter da = new SqlDataAdapter(fullSql, db.conn);

			try
			{
				int fillResult = da.Fill(ds, "test");
				result += "✅ DataAdapter.Fill 成功，返回 " + fillResult + " 筆記錄<br/>";

				if (ds.Tables.Contains("test"))
				{
					DataTable dt = ds.Tables["test"];
					result += "✅ DataTable 創建成功<br/>";
					result += "📊 DataTable.Rows.Count: " + dt.Rows.Count + "<br/>";
					result += "📊 DataTable.Columns.Count: " + dt.Columns.Count + "<br/>";

					// 顯示欄位名稱
					result += "📊 欄位名稱: ";
					foreach (DataColumn col in dt.Columns)
					{
						result += col.ColumnName + " | ";
					}
					result += "<br/>";

					// 顯示前 3 筆資料
					if (dt.Rows.Count > 0)
					{
						result += "<br/>📋 前 3 筆資料內容:<br/>";
						int displayCount = Math.Min(3, dt.Rows.Count);
						for (int i = 0; i < displayCount; i++)
						{
							DataRow row = dt.Rows[i];
							result += "第 " + (i + 1) + " 筆: ";
							foreach (var item in row.ItemArray)
							{
								result += item.ToString() + " | ";
							}
							result += "<br/>";
						}

						// 綁定到 GridView 測試
						result += "<br/>🔍 測試 GridView 綁定...<br/>";
						try
						{
							GridView1.DataSource = dt;
							GridView1.DataBind();
							result += "✅ GridView 綁定成功，顯示 " + GridView1.Rows.Count + " 行<br/>";
						}
						catch (Exception gvEx)
						{
							result += "❌ GridView 綁定錯誤: " + gvEx.Message + "<br/>";
						}
					}
					else
					{
						result += "⚠️ 警告: DataTable 沒有資料<br/>";
					}
				}
				else
				{
					result += "❌ 錯誤: DataSet 中沒有找到 'test' 表格<br/>";
				}
			}
			catch (Exception fillEx)
			{
				result += "❌ 查詢執行錯誤: " + fillEx.Message + "<br/>";
				result += "❌ 錯誤詳細: " + fillEx.StackTrace + "<br/>";
			}
			finally
			{
				da.Dispose();
				ds.Dispose();
			}
		}
		catch (Exception ex)
		{
			result += "❌ 連接錯誤: " + ex.Message + "<br/>";
			result += "❌ 錯誤詳細: " + ex.StackTrace + "<br/>";
		}
		finally
		{
			if (db.conn.State == ConnectionState.Open)
			{
				db.conn.Close();
				result += "<br/>✅ 資料庫連接已關閉<br/>";
			}
		}

		lblResult.Text = result;
	}

	protected void btnRetest_Click(object sender, EventArgs e)
	{
		TestDatabaseConnection();
	}
}
