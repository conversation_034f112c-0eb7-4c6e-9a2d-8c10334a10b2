using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class simple_database_check : System.Web.UI.Page
{
	dbconnection db = new dbconnection();

	protected void Page_Load(object sender, EventArgs e)
	{
		// 頁面載入時不做任何事
	}

	protected void CheckViews_Click(object sender, EventArgs e)
	{
		StringBuilder sb = new StringBuilder();
		sb.Append("<div class='result'>");
		sb.Append("<h3>👁️ 視圖檢查結果</h3>");

		try
		{
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			// 檢查視圖是否存在
			string[] views = { "V_recowish1", "V_recowish3", "V_recowish5" };

			foreach (string viewName in views)
			{
				sb.Append($"<h4>📊 {viewName}</h4>");

				// 檢查視圖是否存在
				SqlCommand cmd = new SqlCommand($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = '{viewName}'", db.conn);
				int viewExists = (int)cmd.ExecuteScalar();

				if (viewExists > 0)
				{
					sb.Append("<p style='color: green;'>✅ 視圖存在</p>");

					// 檢查記錄數
					cmd.CommandText = $"SELECT COUNT(*) FROM [school].[dbo].[{viewName}]";
					try
					{
						int recordCount = (int)cmd.ExecuteScalar();
						sb.Append($"<p>📈 記錄數量: <strong>{recordCount}</strong></p>");

						if (recordCount > 0)
						{
							// 顯示欄位結構
							cmd.CommandText = $"SELECT TOP 1 * FROM [school].[dbo].[{viewName}]";
							SqlDataAdapter da = new SqlDataAdapter(cmd);
							DataTable dt = new DataTable();
							da.Fill(dt);

							sb.Append("<p>📋 欄位結構:</p>");
							sb.Append("<table>");
							sb.Append("<tr><th>欄位名稱</th><th>資料類型</th><th>範例數據</th></tr>");

							foreach (DataColumn col in dt.Columns)
							{
								string sampleData = dt.Rows.Count > 0 ? dt.Rows[0][col.ColumnName].ToString() : "無資料";
								sb.Append($"<tr><td>{col.ColumnName}</td><td>{col.DataType.Name}</td><td>{sampleData}</td></tr>");
							}
							sb.Append("</table>");

							// 顯示前3筆完整資料
							sb.Append("<p>📄 前3筆完整資料:</p>");
							cmd.CommandText = $"SELECT TOP 3 * FROM [school].[dbo].[{viewName}]";
							da = new SqlDataAdapter(cmd);
							dt = new DataTable();
							da.Fill(dt);

							if (dt.Rows.Count > 0)
							{
								sb.Append("<table>");
								sb.Append("<tr>");
								foreach (DataColumn col in dt.Columns)
								{
									sb.Append($"<th>{col.ColumnName}</th>");
								}
								sb.Append("</tr>");

								for (int i = 0; i < dt.Rows.Count; i++)
								{
									sb.Append("<tr>");
									foreach (DataColumn col in dt.Columns)
									{
										sb.Append($"<td>{dt.Rows[i][col.ColumnName]}</td>");
									}
									sb.Append("</tr>");
								}
								sb.Append("</table>");
							}
						}
						else
						{
							sb.Append("<p style='color: orange;'>⚠️ 視圖存在但無資料</p>");
						}
					}
					catch (Exception ex)
					{
						sb.Append($"<p style='color: red;'>❌ 查詢錯誤: {ex.Message}</p>");
					}
				}
				else
				{
					sb.Append("<p style='color: red;'>❌ 視圖不存在</p>");
				}

				sb.Append("<hr/>");
			}

			// 額外測試：檢查是否有相似名稱的視圖
			sb.Append("<h4>🔍 尋找相似視圖</h4>");
			SqlCommand searchCmd = new SqlCommand("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME LIKE '%recowish%' ORDER BY TABLE_NAME", db.conn);
			SqlDataAdapter da2 = new SqlDataAdapter(searchCmd);
			DataTable dt2 = new DataTable();
			da2.Fill(dt2);

			if (dt2.Rows.Count > 0)
			{
				sb.Append("<p>找到相似的視圖:</p>");
				sb.Append("<ul>");
				foreach (DataRow row in dt2.Rows)
				{
					sb.Append($"<li>{row["TABLE_NAME"]}</li>");
				}
				sb.Append("</ul>");
			}
			else
			{
				sb.Append("<p>未找到任何包含 'recowish' 的視圖</p>");
			}

			db.conn.Close();
		}
		catch (Exception ex)
		{
			sb.Append($"<p style='color: red;'>❌ 檢查失敗: {ex.Message}</p>");
			sb.Append($"<p>詳細錯誤: {ex.StackTrace}</p>");
		}

		sb.Append("</div>");

		// 直接設定 Label 的 Text 屬性
		lblResults.Text = sb.ToString();
	}
}
