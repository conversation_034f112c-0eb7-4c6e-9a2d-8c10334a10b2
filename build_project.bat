@echo off
echo Building project...

REM Try different MSBuild paths
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" recowishstat.csproj /p:Configuration=Debug /v:detailed > build_output.txt 2>&1
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" recowishstat.csproj /p:Configuration=Debug /v:detailed > build_output.txt 2>&1
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    "%ProgramFiles%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" recowishstat.csproj /p:Configuration=Debug /v:detailed > build_output.txt 2>&1
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" recowishstat.csproj /p:Configuration=Debug /v:detailed > build_output.txt 2>&1
) else if exist "%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe" (
    "%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe" recowishstat.csproj /p:Configuration=Debug /v:detailed > build_output.txt 2>&1
) else (
    echo MSBuild not found! > build_output.txt 2>&1
)

echo Build completed. Check build_output.txt for details.
pause
