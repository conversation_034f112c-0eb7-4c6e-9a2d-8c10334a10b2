@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap");

* {
  font-family: "Poppins", sans-serif;
}

html,
body {
  height: 100%;
  margin: 0;
}

.wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.header {
  width: 100%;
  background-color: #007c7c;
  color: white;
  padding: 10px 0; /* 增加 padding 讓 header 有更好的視覺間距 */
  position: fixed; /* 讓 header 固定在頂部 */
  top: 0;
  left: 0;
  z-index: 1000; /* 確保 header 在最頂層 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.container {
  display: flex;
  justify-content: center; /* 水平置中 */
  align-items: center; /* 垂直居中 */
}

.logo {
  max-width: 150px; /* 限制 logo 圖片的最大寬度 */
  height: auto; /* 確保圖片按比例縮放 */
}

.mr-3 {
  margin-right: 15px; /* 讓 logo 與標題之間有一點間距 */
}

.content-area {
  flex: 1; /* 讓右側內容區域佔據剩餘的水平空間 */
  max-width: 100%; /* 根據需要調整 */
  overflow-x: auto; /* 當內容超出時顯示水平滾動條 */
  padding: 1% 2px 2px; /* 根據需要調整 */
  box-sizing: border-box; /* 包含內邊距和邊框在內的總寬度 */
}

.main-content {
  display: flex; /* 使用 flexbox 來構建左右佈局 */
  flex: 1;
  padding-top: 80px; /* 確保內容不會與固定的 header 重疊 */
  /*margin-left: 25%;*/ /* 與.sidebar的寬度一致 */
}

.custom-line {
  border: none; /* 移除默認邊框 */
  border-top: 3px solid #007bff; /* 設置上邊框樣式 */
  margin: 10px 0; /* 上下空間 */
  width: 100%; /* 設定寬度 */
}

.dashed-line {
  /* 設置為虛線*/
  border: none;
  border-top: 2px dashed #999;
  margin: 20px 0;
  width: 100%; /* 設定寬度 */
}

.footer[fdata] {
  /*background-color: #f8f9fa;*/
  background: linear-gradient(-45deg, #9adcff, #fff89a, #ffb2a6, #ff8aae);
  text-align: center;
  /*padding: 10px 0;*/
  padding-top: 10px;
  background-size: 400% 400%;
  /*box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);*/
  -webkit-animation: gradient-fdata 15s ease infinite;
  animation: gradient-fdata 15s ease infinite;
}

@-webkit-keyframes gradient-fdata {
  0% {
    background-position: 0 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  to {
    background-position: 0 50%;
  }
}

@keyframes gradient-fdata {
  0% {
    background-position: 0 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  to {
    background-position: 0 50%;
  }
}
