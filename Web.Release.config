<?xml version="1.0" encoding="utf-8"?>

<!-- 如需使用 web.config 轉換的詳細資訊，請前往 https://go.microsoft.com/fwlink/?LinkId=125889 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    在下面的範例中，"SetAttributes" 轉換只會在 "Match" 定位程式找到
    值為 "MyDB" 的屬性 "name" 時，才將 "connectionString" 的值變
    更為使用 "ReleaseSQLServer"。
    
    <connectionStrings>
      <add name="MyDB" 
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True" 
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
    <!--
      
      在下面的範例中，"Replace" 轉換將會取代 web.config 檔案
      的整個 <customErrors> 區段。
      請注意，因為在 <system.web> 節點之下 
      只有一個 customErrors 區段，所以不需要使用 "xdt:Locator" 屬性。
      
      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    -->
  </system.web>
</configuration>