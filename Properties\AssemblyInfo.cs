﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// 組件的一般資訊是由下列的屬性集 
// 控制。變更這些屬性值可修改與組件關聯的
// 資訊。
[assembly: AssemblyTitle("recowishstat")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("recowishstat")]
[assembly: AssemblyCopyright("Copyright ©  2025")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// 將 ComVisible 設定為 false 會使得這個組件中的型別 
// 對 COM 元件而言為不可見。如果您需要從 COM 存取此組件中 
// 的型別，請在該型別上將 ComVisible 屬性設定為 true。
[assembly: ComVisible(false)]

// 下列 GUID 為專案公開 (Expose) 至 COM 時所要使用的 typelib ID
[assembly: Guid("2a64fa35-312d-44df-8898-8fcdc92c525e")]

// 組件的版本資訊是由下列四項值構成:
//
//      主要版本
//      次要版本 
//      組建編號
//      修訂編號
//
// 您可以指定所有的值，也可以依照以下的方式，使用 '*' 將修訂和組建編號 
// 指定為預設值:
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
