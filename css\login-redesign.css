@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Noto Sans TC", sans-serif;
}

body {
  min-height: 100vh;
  background-image: url("../img/sgrs_bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-container {
  width: 100%;
  max-width: 450px;
  padding: 20px;
}

.login-box {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
}

.login-box::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  transform: rotate(30deg);
  pointer-events: none;
  z-index: 0;
}

.login-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

h1 {
  color: #2c3e50;
  text-align: center;
  font-size: 2.2rem;
  margin-bottom: 30px;
  font-weight: 700;
  position: relative;
}

h1::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -10px;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #3498db;
  border-radius: 2px;
}

.input-container {
  position: relative;
  margin-bottom: 30px;
}

.input-container input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  outline: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: transparent;
  z-index: 1;
  position: relative;
}

.input-container label {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  color: #666;
  pointer-events: none;
  transition: all 0.3s ease;
  z-index: 2;
}

.input-container input:focus,
.input-container input:valid {
  border-color: #3498db;
}

.input-container input:focus ~ label,
.input-container input:valid ~ label,
.input-container label.active {
  top: -10px;
  left: 10px;
  font-size: 0.8rem;
  padding: 0 5px;
  background: white;
  color: #3498db;
}

.btn-login {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 10px;
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-login::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: linear-gradient(45deg, #2980b9, #3498db);
  transition: all 0.5s ease;
  z-index: -1;
}

.btn-login:hover::before {
  width: 100%;
}

.btn-login:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.btn-confirm {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 10px;
  background: linear-gradient(45deg, #2ecc71, #27ae60);
  color: white;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-confirm::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: linear-gradient(45deg, #27ae60, #2ecc71);
  transition: all 0.5s ease;
  z-index: -1;
}

.btn-confirm:hover::before {
  width: 100%;
}

.btn-confirm:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
}

.btn-confirm:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  margin-bottom: 15px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

/* 增加下拉選單的高度和可見性 */
select.form-control {
  height: auto;
  min-height: 45px;
  font-size: 1.1rem;
  padding-right: 30px;
  text-overflow: ellipsis;
  white-space: normal;
}

/* 特別為大型下拉選單設計的樣式 */
.dropdown-large {
  min-height: 50px;
  font-size: 1.2rem !important;
  padding: 12px 15px !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* 確保下拉選單選項可見 */
select.form-control option {
  padding: 10px;
  font-size: 1.1rem;
  background-color: white;
  color: #333;
}

/* 為大型下拉選單的選項設計特別樣式 */
.dropdown-large option {
  padding: 12px !important;
  font-size: 1.2rem !important;
  line-height: 1.5 !important;
  min-height: 30px !important;
}

.form-control:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.error-message {
  color: #e74c3c;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-box {
  animation: fadeIn 0.8s ease forwards;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .login-container {
    padding: 10px;
  }

  .login-box {
    padding: 25px;
  }

  h1 {
    font-size: 1.8rem;
  }
}
