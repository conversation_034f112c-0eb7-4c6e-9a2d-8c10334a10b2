This software uses the following third-party libraries under the following licenses:
| woff2              | Apache License Version 2.0 | Library code and tests are embedded under iText.IO.Font.Woff2 package.
| jai_imageio:1.0_01 | BSD                        | Library code and tests are embedded under iText.IO.Codec package.
| libtiff            | BSD                        | Library code and tests are embedded under iText.IO.Codec package.
| brotli             | MIT                        | Library code and tests are embedded under iText.IO.Codec.Brotli.Dec package.
| zlib-1.1.3         | BSD                        | Library code and tests are embedded under System.util.zlib package.
| jzlib v1.0.7       | BSD                        | Library code and tests are embedded under System.util.zlib package.




------------------------------------------------------------------------------------------------------------------------


woff2 full license agreement text is below:

    Copyright 2013 Google Inc. All Rights Reserved.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

    The parts of ots.h & opentype-sanitiser.h that we need, taken from the
    https://code.google.com/p/ots/ project.

    This is part of java port of project hosted at https://github.com/google/woff2


------------------------------------------------------------------------------------------------------------------------


jai_imageio full license agreement text is below:

    Copyright 2003-2012 by Paulo Soares.

    This code was originally released in 2001 by SUN (see class
    com.sun.media.imageioimpl.plugins.tiff.TIFFLZWDecompressor.java)
    using the BSD license in a specific wording. In a mail dating from
    January 23, 2008, Brian Burkhalter (@sun.com) gave us permission
    to use the code under the following version of the BSD license:

    Copyright (c) 2005 Sun Microsystems, Inc. All  Rights Reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    - Redistribution of source code must retain the above copyright
      notice, this  list of conditions and the following disclaimer.

    - Redistribution in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in
      the documentation and/or other materials provided with the
      distribution.

    Neither the name of Sun Microsystems, Inc. or the names of
    contributors may be used to endorse or promote products derived
    from this software without specific prior written permission.

    This software is provided "AS IS," without a warranty of any
    kind. ALL EXPRESS OR IMPLIED CONDITIONS, REPRESENTATIONS AND
    WARRANTIES, INCLUDING ANY IMPLIED WARRANTY OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE OR NON-INFRINGEMENT, ARE HEREBY
    EXCLUDED. SUN MIDROSYSTEMS, INC. ("SUN") AND ITS LICENSORS SHALL
    NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY LICENSEE AS A RESULT OF
    USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS
    DERIVATIVES. IN NO EVENT WILL SUN OR ITS LICENSORS BE LIABLE FOR
    ANY LOST REVENUE, PROFIT OR DATA, OR FOR DIRECT, INDIRECT, SPECIAL,
    CONSEQUENTIAL, INCIDENTAL OR PUNITIVE DAMAGES, HOWEVER CAUSED AND
    REGARDLESS OF THE THEORY OF LIABILITY, ARISING OUT OF THE USE OF OR
    INABILITY TO USE THIS SOFTWARE, EVEN IF SUN HAS BEEN ADVISED OF THE
    POSSIBILITY OF SUCH DAMAGES.

    You acknowledge that this software is not designed or intended for
    use in the design, construction, operation or maintenance of any
    nuclear facility.


------------------------------------------------------------------------------------------------------------------------


libtiff full license agreement text is below:

    Copyright 2003-2012 by Paulo Soares.

    This list of constants was originally released with libtiff
    under the following license:

    Copyright (c) 1988-1997 Sam Leffler
    Copyright (c) 1991-1997 Silicon Graphics, Inc.

    Permission to use, copy, modify, distribute, and sell this software and
    its documentation for any purpose is hereby granted without fee, provided
    that (i) the above copyright notices and this permission notice appear in
    all copies of the software and related documentation, and (ii) the names of
    Sam Leffler and Silicon Graphics may not be used in any advertising or
    publicity relating to the software without the specific, prior written
    permission of Sam Leffler and Silicon Graphics.

    THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND,
    EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY
    WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.

    IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
    ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
    OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
    WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF
    LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE
    OF THIS SOFTWARE.


------------------------------------------------------------------------------------------------------------------------


brotli full license agreement text is below:

   Copyright 2015 Google Inc. All Rights Reserved.

   Distributed under MIT license.
   See file LICENSE for detail or copy at https://opensource.org/licenses/MIT
   

------------------------------------------------------------------------------------------------------------------------


zlib-1.1.3 full license agreement text is below:

    Copyright (c) 2000,2001,2002,2003 ymnk, JCraft,Inc. All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

    2. Redistributions in binary form must reproduce the above copyright 
     notice, this list of conditions and the following disclaimer in 
     the documentation and/or other materials provided with the distribution.

    3. The names of the authors may not be used to endorse or promote products
     derived from this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED WARRANTIES,
    INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
    FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL JCRAFT,
    INC. OR ANY CONTRIBUTORS TO THIS SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,
    INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
    LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
    OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
    NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
    EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.


    This program is based on zlib-1.1.3, so all credit should go authors
    Jean-loup Gailly(<EMAIL>) and Mark Adler(<EMAIL>)
    and contributors of zlib.


------------------------------------------------------------------------------------------------------------------------


jzlib v1.0.7 full license agreement text is below:

    Copyright (c) 2001 Lapo Luchini.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

    2. Redistributions in binary form must reproduce the above copyright 
     notice, this list of conditions and the following disclaimer in 
     the documentation and/or other materials provided with the distribution.

    3. The names of the authors may not be used to endorse or promote products
     derived from this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED WARRANTIES,
    INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
    FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE AUTHORS
    OR ANY CONTRIBUTORS TO THIS SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,
    INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
    LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
    OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
    NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
    EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 
    This program is based on zlib-1.1.3, so all credit should go authors
    Jean-loup Gailly(<EMAIL>) and Mark Adler(<EMAIL>)
    and contributors of zlib.
 
