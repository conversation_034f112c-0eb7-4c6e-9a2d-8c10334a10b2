<%@ Page Language="C#" AutoEventWireup="true" CodeFile="test_login.aspx.cs" Inherits="test_login" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head runat="server">
	<title>測試登入 - 設定必要的 Session</title>
	<style>
		.container {
			max-width: 600px;
			margin: 50px auto;
			padding: 20px;
			border: 1px solid #ddd;
			border-radius: 10px;
			background: #f9f9f9;
		}

		.success {
			color: green;
			font-weight: bold;
		}

		.error {
			color: red;
			font-weight: bold;
		}

		.info {
			color: blue;
			background: #e7f3ff;
			padding: 10px;
			margin: 10px 0;
			border-radius: 5px;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<div class="container">
			<h1>🔐 測試登入系統</h1>
			<p>此頁面用於設定必要的 Session 變數，以便測試主頁面功能。</p>

			<div class="info">
				<strong>📋 所需的 Session 變數：</strong><br />
				• Session["tech_no"] - 使用者代號<br />
				• Session["user"] - 使用者姓名<br />
				• Session["master_single"] - 權限等級 ("3" = 管理者)<br />
				• Session["office"] - 單位代碼
			</div>

			<h3>快速設定 Session</h3>
			<asp:Button ID="btnSetupAdmin" runat="server" Text="設定管理者權限 Session" OnClick="btnSetupAdmin_Click" CssClass="btn btn-primary" />
			<br /><br />

			<h3>當前 Session 狀態</h3>
			<asp:Label ID="lblSessionStatus" runat="server" Text="點擊按鈕檢查 Session"></asp:Label>
			<br /><br />

			<asp:Button ID="btnCheckSession" runat="server" Text="檢查當前 Session" OnClick="btnCheckSession_Click" />
			<asp:Button ID="btnClearSession" runat="server" Text="清除 Session" OnClick="btnClearSession_Click" />
			<br /><br />

			<h3>測試頁面連結</h3>
			<a href="Analyze_recowish01.aspx" target="_blank">📊 主要分析頁面 (需要 Session)</a><br />
			<a href="test_main_functionality.aspx" target="_blank">🧪 功能測試頁面 (無需 Session)</a><br />
			<a href="debug_analyze.aspx" target="_blank">🔧 調試分析頁面</a>

			<div style="margin-top: 30px;">
				<asp:Label ID="lblResult" runat="server"></asp:Label>
			</div>
		</div>
	</form>
</body>

</html>