﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices</name>
  </assembly>
  <members>
    <member name="T:System.DataMisalignedException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn eine Dateneinheit aus einer Adresse gelesen bzw. in eine solche geschrieben wird, die kein Mehrfaches der Datengröße ist.Diese Klasse kann nicht vererbt werden.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DataMisalignedException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.DataMisalignedException" />-Klasse. </summary>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.DataMisalignedException" />-Klasse mit der angegebenen Fehlermeldung.</summary>
      <param name="message">Ein <see cref="T:System.String" />-<PERSON><PERSON><PERSON><PERSON>, das den Fehler beschreibt.Der Inhalt der <paramref name="message" /> soll in verständlicher Sprache gehalten sein.Der Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.DataMisalignedException" />-Klasse mit der angegebenen Fehlermeldung und der angegebenen zugrunde liegenden Ausnahme.</summary>
      <param name="message">Ein <see cref="T:System.String" />-Objekt, das den Fehler beschreibt.Der Inhalt der <paramref name="message" /> soll in verständlicher Sprache gehalten sein.Der Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
      <param name="innerException">Die Ausnahme, die die aktuelle <see cref="T:System.DataMisalignedException" />-Ausnahme verursacht.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.DllNotFoundException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn eine in einem DLL-Import angegebene DLL nicht gefunden werden kann.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DllNotFoundException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.DllNotFoundException" />-Klasse mit Standardeigenschaften.</summary>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.DllNotFoundException" />-Klasse mit einer angegebenen Fehlermeldung.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.DllNotFoundException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Reflection.Missing">
      <summary>Stellt ein fehlendes <see cref="T:System.Object" /> dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="F:System.Reflection.Missing.Value">
      <summary>Stellt die einzige Instanz der <see cref="T:System.Reflection.Missing" />-Klasse dar.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ArrayWithOffset">
      <summary>Kapselt ein Array und einen Offset im angegebenen Array.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.#ctor(System.Object,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Struktur.</summary>
      <param name="array">Ein verwaltetes Array. </param>
      <param name="offset">Der Offset des Elements, das durch Aufrufen der Plattform übergeben werden soll (in Byte). </param>
      <exception cref="T:System.ArgumentException">Das Array ist größer als 2 Gigabyte (GB).</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Object)">
      <summary>Gibt an, ob das angegebene Objekt dem aktuellen <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt entspricht.</summary>
      <returns>true, wenn das Objekt diesem <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> entspricht, andernfalls false.</returns>
      <param name="obj">Objekt, das mit dieser Instanz verglichen werden soll. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Gibt an, ob das angegebene <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt der aktuellen Instanz entspricht.</summary>
      <returns>true, wenn das angegebene <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt der aktuellen Instanz entspricht, andernfalls false.</returns>
      <param name="obj">Ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt, das mit dieser Instanz verglichen werden soll.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetArray">
      <summary>Gibt das verwaltete Array zurück, auf das von diesem <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> verwiesen wird.</summary>
      <returns>Das verwaltete Array, auf das diese Instanz verweist.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetHashCode">
      <summary>Gibt einen Hashcode für diesen Werttyp zurück.</summary>
      <returns>Der Hashcode für diese Instanz.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetOffset">
      <summary>Gibt den Offset zurück, der beim Erstellen dieses <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> bereitgestellt wurde.</summary>
      <returns>Der Offset für diese Instanz.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Equality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Bestimmt, ob zwei angegebene <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekte denselben Wert haben.</summary>
      <returns>true, wenn <paramref name="a" /> und <paramref name="b" /> denselben Wert haben, andernfalls false.</returns>
      <param name="a">Ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt, das mit dem <paramref name="b" />-Parameter verglichen werden soll. </param>
      <param name="b">Ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt, das mit dem <paramref name="a" />-Parameter verglichen werden soll.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Inequality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Bestimmt, ob zwei angegebene <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekte nicht den gleichen Wert haben.</summary>
      <returns>true, wenn der Wert von <paramref name="a" /> ungleich dem Wert von <paramref name="b" /> ist, andernfalls false.</returns>
      <param name="a">Ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt, das mit dem <paramref name="b" />-Parameter verglichen werden soll. </param>
      <param name="b">Ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt, das mit dem <paramref name="a" />-Parameter verglichen werden soll.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.BestFitMappingAttribute">
      <summary>Steuert, ob Unicode-Zeichen in die ANSI-Zeichen konvertiert werden, die ihnen am ähnlichsten sind.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BestFitMappingAttribute.#ctor(System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.BestFitMappingAttribute" />-Klasse, die auf den Wert der <see cref="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping" />-Eigenschaft festgelegt ist.</summary>
      <param name="BestFitMapping">true, um anzugeben, dass die optimale Zuordnung aktiviert ist, andernfalls false.Die Standardeinstellung ist true.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping">
      <summary>Ruft das Verhalten der optimalen Zuordnung bei der Konvertierung von Unicode-Zeichen in ANSI-Zeichen ab.</summary>
      <returns>true, wenn die optimale Zuordnung aktiviert ist, andernfalls false.Die Standardeinstellung ist true.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.BestFitMappingAttribute.ThrowOnUnmappableChar">
      <summary>Aktiviert bzw. deaktiviert das Auslösen einer Ausnahme bei einem nicht zuzuordnenden Unicode-Zeichen, das in das ANSI-Zeichen "?" konvertiert wird.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.BStrWrapper">
      <summary>Marshallt Daten vom Typ VT_BSTR aus verwaltetem zu nicht verwaltetem Code.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.BStrWrapper" />-Klasse mit dem angegebenen <see cref="T:System.Object" />-Objekt.</summary>
      <param name="value">Das zu umschließende Objekt, das als VT_BSTR gemarshallt werden soll.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.BStrWrapper" />-Klasse mit dem angegebenen <see cref="T:System.String" />-Objekt.</summary>
      <param name="value">Das zu umschließende Objekt, das als VT_BSTR gemarshallt werden soll.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BStrWrapper.WrappedObject">
      <summary>Ruft das umschlossene <see cref="T:System.String" />-Objekt ab, das als VT_BSTR-Typ gemarshallt werden soll.</summary>
      <returns>Das Objekt, das von <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> umschlossen wird.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CallingConvention">
      <summary>Gibt die Aufrufkonvention für das Aufrufen von Methoden an, die in nicht verwaltetem Code implementiert sind.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Cdecl">
      <summary>Der Aufrufer entleert den Stapel.Dies aktiviert Aufruffunktionen mit varargs, wodurch die Verwendung für Methoden ermöglicht wird, die eine variable Anzahl von Parametern akzeptieren, beispielsweise Printf.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.StdCall">
      <summary>Der Aufgerufene entleert den Stapel.Dies ist die Standardkonvention für das Aufrufen nicht verwalteter Funktionen mit Plattformaufruf.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.ThisCall">
      <summary>Der erste Parameter ist der this-Zeiger, der im Register ECX gespeichert wird.Weitere Parameter werden in den Stapel verschoben.Diese Aufrufkonvention wird zum Aufrufen von Methoden für Klassen verwendet, die aus einer nicht verwalteten DLL exportiert wurden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Winapi">
      <summary>Dieser Member stellt keine tatsächliche Aufrufkonvention dar, sondern verwendet die Standardkonvention für Plattformaufrufe.Unter Windows ist der Standard beispielsweise <see cref="F:System.Runtime.InteropServices.CallingConvention.StdCall" />, und unter Windows CE.NET ist der Standard <see cref="F:System.Runtime.InteropServices.CallingConvention.Cdecl" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceAttribute">
      <summary>Gibt den Typ der Klassenschnittstelle an, die für eine für COM verfügbar gemachte Klasse generiert werden soll (sofern eine Schnittstelle erstellt wird).</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Int16)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />-Klasse mit dem angegebenen <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />-Enumerationswert.</summary>
      <param name="classInterfaceType">Beschreibt den Schnittstellentyp, der für eine Klasse generiert wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Runtime.InteropServices.ClassInterfaceType)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />-Klasse mit dem angegebenen <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />-Enumerationsmember.</summary>
      <param name="classInterfaceType">Einer der <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />-Werte, der den Typ der für eine Klasse generierten Schnittstelle beschreibt. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ClassInterfaceAttribute.Value">
      <summary>Ruft den <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />-Wert ab, der beschreibt, welcher Schnittstellentyp für die Klasse generiert werden soll.</summary>
      <returns>Der <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" />-Wert, der beschreibt, welcher Schnittstellentyp für die Klasse generiert werden soll.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceType">
      <summary>Bezeichnet den Typ der Klassenschnittstelle, der für eine Klasse generiert wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDispatch">
      <summary>Gibt an, dass die Klasse nur spätes Binden für COM-Clients unterstützt.Eine dispinterface für die Klasse wird auf Anforderung automatisch für COM-Clients verfügbar gemacht.Die vom Tlbexp.exe (Type Library Exporter-Tool) erstellte Typbibliothek enthält keine Typinformationen für die dispinterface, um zu verhindern, dass Clients die DISPIDs der Schnittstelle zwischenspeichern.Bei der dispinterface treten keine der in <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> beschriebenen Versionsprobleme auf, da für Clients nur spätes Binden an die Schnittstelle möglich ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDual">
      <summary>Gibt an, dass für die Klasse automatisch eine duale Klassenschnittstelle generiert und für COM verfügbar gemacht wird.Für die Klassenschnittstelle werden Typinformationen erstellt und in der Typbibliothek veröffentlicht.Aufgrund der unter <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> beschriebenen Versionseinschränkungen wird von der Verwendung von AutoDual dringend abgeraten.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.None">
      <summary>Gibt an, dass für die Klasse keine Klassenschnittstelle generiert wird.Wenn keine Schnittstellen explizit implementiert werden, kann die Klasse nur Zugriff mit spätem Binden über die IDispatch-Schnittstelle bereitstellen.Dies ist die empfohlene Einstellung für <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />.Das Verwenden von ClassInterfaceType.None ist die einzige Möglichkeit, Funktionen über Schnittstellen verfügbar zu machen, die von der Klasse explizit implementiert werden.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CoClassAttribute">
      <summary>Gibt den Klassenbezeichner einer Co-Klasse an, die aus einer Typbibliothek importiert wurde.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CoClassAttribute.#ctor(System.Type)">
      <summary>Initialisiert eine neue Instanz von <see cref="T:System.Runtime.InteropServices.CoClassAttribute" /> mit dem Klassenbezeichner der ursprünglichen Co-Klasse.</summary>
      <param name="coClass">Ein <see cref="T:System.Type" />, der den Klassenbezeichner der ursprünglichen Co-Klasse enthält. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.CoClassAttribute.CoClass">
      <summary>Ruft den Klassenbezeichner der ursprünglichen Co-Klasse ab.</summary>
      <returns>Ein <see cref="T:System.Type" />, der den Klassenbezeichner der ursprünglichen Co-Klasse enthält.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComAwareEventInfo">
      <summary>Lässt die spät gebundene Registrierung eines Ereignishandlers zu.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.#ctor(System.Type,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ComAwareEventInfo" />-Klasse unter Verwendung des angegebenen Typs und Namens des Ereignisses für den Typ.</summary>
      <param name="type">Der Objekttyp. </param>
      <param name="eventName">Der Name eines Ereignisses für <paramref name="type" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>Fügt einen Ereignishandler an ein COM-Objekt an.</summary>
      <param name="target">Das Zielobjekt, an das der Ereignisdelegat gebunden werden soll.</param>
      <param name="handler">Der Ereignisdelegat.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Attributes">
      <summary>Ruft die Attribute für dieses Ereignis ab.</summary>
      <returns>Die Schreibschutzattribute für dieses Ereignis.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.DeclaringType">
      <summary>Ruft die Klasse ab, die diesen Member deklariert.</summary>
      <returns>Das <see cref="T:System.Type" />-Objekt für die Klasse, in der dieser Member deklariert ist.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Name">
      <summary>Ruft den Namen des aktuellen Members ab.</summary>
      <returns>Der Name dieses Members.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>Trennt einen Ereignishandler von einem COM-Objekt.</summary>
      <param name="target">Das Zielobjekt, an das der Ereignisdelegat gebunden ist.</param>
      <param name="handler">Der Ereignisdelegat.</param>
      <exception cref="T:System.InvalidOperationException">Das Ereignis besitzt keinen öffentlichen remove-Accessor.</exception>
      <exception cref="T:System.ArgumentException">Der übergebene Handler kann nicht verwendet werden.</exception>
      <exception cref="T:System.Reflection.TargetException">Unter .NET for Windows Store apps oder in der Portable Klassenbibliothek verwenden Sie stattdessen <see cref="T:System.Exception" />.Der <paramref name="target" />-Parameter ist null, und das Ereignis ist nicht statisch.- oder - <see cref="T:System.Reflection.EventInfo" /> ist für das Ziel nicht deklariert.</exception>
      <exception cref="T:System.MethodAccessException">Unter .NET for Windows Store apps oder in der Portable Klassenbibliothek verwenden Sie stattdessen die Basisklassenausnahme <see cref="T:System.MemberAccessException" />.Der Aufrufer besitzt keine Zugriffsberechtigung für den Member.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute">
      <summary>Gibt eine Standardschnittstelle an, die für COM verfügbar gemacht werden soll.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute" />-Klasse mit dem angegebenen <see cref="T:System.Type" />-Objekt als für COM verfügbar gemachte Standardschnittstelle.</summary>
      <param name="defaultInterface">Ein <see cref="T:System.Type" />-Wert, der die Standardschnittstelle angibt, die für COM verfügbar gemacht werden soll. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.Value">
      <summary>Ruft das <see cref="T:System.Type" />-Objekt ab, das die Standardschnittstelle angibt, die für COM verfügbar gemacht werden soll.</summary>
      <returns>Das <see cref="T:System.Type" />-Objekt, das die Standardschnittstelle angibt, die für COM verfügbar gemacht werden soll.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventInterfaceAttribute">
      <summary>Bezeichnet die Quellschnittstelle und die Klasse zum Implementieren der Methoden der Ereignisschnittstelle, die beim Importieren einer Co-Klasse aus einer COM-Typbibliothek generiert wird.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventInterfaceAttribute.#ctor(System.Type,System.Type)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ComEventInterfaceAttribute" />-Klasse mit der Quellschnittstellen- und der Ereignisanbieterklasse.</summary>
      <param name="SourceInterface">Ein <see cref="T:System.Type" />, der die ursprüngliche Quellschnittstelle aus der Typbibliothek enthält.COM verwendet diese Schnittstelle für Rückrufe an die verwaltete Klasse.</param>
      <param name="EventProvider">Ein <see cref="T:System.Type" />, der die Klasse zum Implementieren der Methoden der Ereignisschnittstelle enthält. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.EventProvider">
      <summary>Ruft die Klasse ab, die die Methoden der Ereignisschnittstelle implementiert.</summary>
      <returns>Ein <see cref="T:System.Type" />, der die Klasse zum Implementieren der Methoden der Ereignisschnittstelle enthält.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.SourceInterface">
      <summary>Ruft die ursprüngliche Quellschnittstelle aus der Typbibliothek ab.</summary>
      <returns>Ein <see cref="T:System.Type" />, der die Quellschnittstelle enthält.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventsHelper">
      <summary>Stellt Methoden bereit, mit denen .NET Framework-Delegaten, die Ereignisse behandeln, in COM-Objekten hinzugefügt und entfernt werden können.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Combine(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Fügt der Aufrufliste von Ereignissen, die aus einem COM-Objekt stammen, einen Delegaten hinzu.</summary>
      <param name="rcw">Das COM-Objekt, das die Ereignisse auslöst, auf die der Aufrufer reagieren möchte.</param>
      <param name="iid">Der Bezeichner der Quellschnittstelle, mit der das COM-Objekt Ereignisse auslöst. </param>
      <param name="dispid">Der Dispatchbezeichner der Methode für die Quellschnittstelle.</param>
      <param name="d">Der beim Auslösen des COM-Ereignisses aufzurufende Delegat.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Remove(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Entfernt einen Delegaten aus der Aufrufliste der Ereignisse, die von einem COM-Objekt stammen.</summary>
      <returns>Der Delegat, der aus der Aufrufliste entfernt wurde.</returns>
      <param name="rcw">Das COM-Objekt, an das der Delegat angefügt ist.</param>
      <param name="iid">Der Bezeichner der Quellschnittstelle, mit der das COM-Objekt Ereignisse auslöst. </param>
      <param name="dispid">Der Dispatchbezeichner der Methode für die Quellschnittstelle.</param>
      <param name="d">Der Delegat, der aus der Aufrufliste entfernt werden soll.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.COMException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn ein COM-Methodenaufruf ein nicht erkanntes HRESULT zurückgibt.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.COMException" />-Klasse mit Standardwerten.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.COMException" />-Klasse mit einer angegebenen Meldung.</summary>
      <param name="message">Die Meldung, in der die Ursache für die Ausnahme angegeben wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.COMException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.COMException" />-Klasse mit einer angegebenen Meldung und einem angegebenen Fehlercode.</summary>
      <param name="message">Die Meldung, in der die Ursache für die eingetretene Ausnahme angegeben wird. </param>
      <param name="errorCode">Der Wert des Fehlercodes (HRESULT), der dieser Ausnahme zugeordnet ist. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComImportAttribute">
      <summary>Gibt an, dass der Attributtyp zuvor in COM definiert war.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComImportAttribute.#ctor">
      <summary>Initialisiert eine neue Instanz des <see cref="T:System.Runtime.InteropServices.ComImportAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComInterfaceType">
      <summary>Bestimmt, wie eine Schnittstelle für COM verfügbar gemacht wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual">
      <summary>Gibt an, dass die Schnittstelle für COM als duale Schnittstelle verfügbar gemacht wird. Dadurch werden sowohl frühes als auch spätes Binden ermöglicht.Der Standardwert lautet <see cref="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIDispatch">
      <summary>Gibt an, dass eine Schnittstelle für COM als eine -Dispatchschnittstelle verfügbar gemacht wird, wodurch nur spätes Binden ermöglicht wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIInspectable">
      <summary>Gibt an, dass eine Schnittstelle für COM als Windows-Runtime-Schnittstelle verfügbar gemacht wird. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIUnknown">
      <summary>Gibt an, dass eine Schnittstelle für COM als eine von IUnknown abgeleitete Schnittstelle verfügbar gemacht wird, wodurch nur frühes Binden ermöglicht wird.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComMemberType">
      <summary>Beschreibt den Typ eines COM-Members.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.Method">
      <summary>Der Member ist eine normale Methode.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropGet">
      <summary>Der Member ruft Eigenschaften ab.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropSet">
      <summary>Der Member legt Eigenschaften fest.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute">
      <summary>Bezeichnet eine Liste von Schnittstellen, die als COM-Ereignisquellen für die attributierte Klasse verfügbar gemacht werden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" />-Klasse mit dem Namen der Quellschnittstelle des Ereignisses.</summary>
      <param name="sourceInterfaces">Eine durch NULL getrennte Liste von voll gekennzeichneten Namen der Ereignisquellen-Schnittstellen. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" />-Klasse mit dem Typ, der als Quellschnittstelle verwendet werden soll.</summary>
      <param name="sourceInterface">Der <see cref="T:System.Type" /> der Quellschnittstelle. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" />-Klasse mit den Typen, die als Quellschnittstellen verwendet werden sollen.</summary>
      <param name="sourceInterface1">Der <see cref="T:System.Type" /> der Standard-Quellschnittstelle. </param>
      <param name="sourceInterface2">Der <see cref="T:System.Type" /> einer Quellschnittstelle. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type)">
      <summary>Initialisiert eine neue Instanz der ComSourceInterfacesAttribute-Klasse mit den Typen, die als Quellschnittstellen verwendet werden sollen.</summary>
      <param name="sourceInterface1">Der <see cref="T:System.Type" /> der Standard-Quellschnittstelle. </param>
      <param name="sourceInterface2">Der <see cref="T:System.Type" /> einer Quellschnittstelle. </param>
      <param name="sourceInterface3">Der <see cref="T:System.Type" /> einer Quellschnittstelle. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type,System.Type)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" />-Klasse mit den Typen, die als Quellschnittstellen verwendet werden sollen.</summary>
      <param name="sourceInterface1">Der <see cref="T:System.Type" /> der Standard-Quellschnittstelle. </param>
      <param name="sourceInterface2">Der <see cref="T:System.Type" /> einer Quellschnittstelle. </param>
      <param name="sourceInterface3">Der <see cref="T:System.Type" /> einer Quellschnittstelle. </param>
      <param name="sourceInterface4">Der <see cref="T:System.Type" /> einer Quellschnittstelle. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComSourceInterfacesAttribute.Value">
      <summary>Ruft den voll gekennzeichneten Namen der Quellschnittstelle des Ereignisses ab.</summary>
      <returns>Der voll gekennzeichnete Name der Quellschnittstelle des Ereignisses.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CurrencyWrapper">
      <summary>Erstellt einen Wrapper für Objekte, die der Marshaller als VT_CY marshallen soll.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Decimal)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" />-Klasse mit dem Decimal, das umfasst und als Typ VT_CY gemarshallt werden soll.</summary>
      <param name="obj">Das Decimal, das umfasst und als VT_CY gemarshallt werden soll. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" />-Klasse mit dem Objekt, das das Decimal enthält, das umfasst und als Typ VT_CY gemarshallt werden soll.</summary>
      <param name="obj">Das Objekt, das das Decimal enthält, das umfasst und als VT_CY gemarshallt werden soll. </param>
      <exception cref="T:System.ArgumentException">Der <paramref name="obj" />-Parameter ist kein <see cref="T:System.Decimal" />-Typ.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.CurrencyWrapper.WrappedObject">
      <summary>Ruft das umfasste Objekt ab, das als Typ VT_CY gemarshallt werden soll.</summary>
      <returns>Das umfasste Objekt, das als Typ VT_CY gemarshallt werden soll.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceMode">
      <summary>Gibt an, ob IUnknown::QueryInterface-Aufrufe der <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" />-Methode die <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />-Schnittstelle verwenden können.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Allow">
      <summary>IUnknown::QueryInterface-Methodenaufrufe können die Schnittstelle <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> verwenden.Wenn Sie diesen Wert verwenden, funktioniert die <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" />-Methodenüberladung wie die <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)" />-Überladung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Ignore">
      <summary>IUnknown::QueryInterface-Methodenaufrufe sollten die Schnittstelle <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> ignorieren.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceResult">
      <summary>Stellt Rückgabewerte für die <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" />-Methode bereit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Failed">
      <summary>Die Schnittstelle für eine bestimmte Schnittstellen-ID ist nicht verfügbar.In diesem Fall ist die zurückgegebene Schnittstelle null.E_NOINTERFACE wird an den Aufrufer von IUnknown::QueryInterface zurückgegeben.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Handled">
      <summary>Der Schnittstellenzeiger, der von der <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" />-Methode zurückgegeben wird, kann als Ergebnis von IUnknown::QueryInterface verwendet werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.NotHandled">
      <summary>Die benutzerdefinierte QueryInterface wurde nicht verwendet.Stattdessen sollte die Standardimplementierung von IUnknown::QueryInterface verwendet werden.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultCharSetAttribute">
      <summary>Gibt den Wert der <see cref="T:System.Runtime.InteropServices.CharSet" />-Enumeration an.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultCharSetAttribute.#ctor(System.Runtime.InteropServices.CharSet)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.DefaultCharSetAttribute" />-Klasse mit dem angegebenen <see cref="T:System.Runtime.InteropServices.CharSet" />-Wert.</summary>
      <param name="charSet">Einer der <see cref="T:System.Runtime.InteropServices.CharSet" />-Werte.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultCharSetAttribute.CharSet">
      <summary>Ruft den Standardwert von <see cref="T:System.Runtime.InteropServices.CharSet" /> für jeden Aufruf von <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> ab.</summary>
      <returns>Der Standardwert von <see cref="T:System.Runtime.InteropServices.CharSet" /> für jeden Aufruf von <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute">
      <summary>Gibt die Pfade an, die verwendet werden, um nach DLLs zu finden, die Funktionen für Plattformaufrufe bereitstellen. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.#ctor(System.Runtime.InteropServices.DllImportSearchPath)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" />-Klasse und gibt die Pfade an, die bei der Suche nach den Zielen von Plattformaufrufen zu verwenden sind. </summary>
      <param name="paths">Eine bitweise Kombination von Enumerationswerten, die die Pfade angeben, welche die LoadLibraryEx-Funktion während der Plattformaufrufe durchsuchen soll. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.Paths">
      <summary>Ruft eine bitweise Kombination von Enumerationswerten ab, die die Pfade angeben, welche die LoadLibraryEx-Funktion während der Plattformaufrufe durchsuchen soll. </summary>
      <returns>Eine bitweise Kombination von Enumerationswerten, die Suchpfade für Plattformaufrufe angeben. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultParameterValueAttribute">
      <summary>Legt den Standardwert eines Parameters beim Aufruf aus einer Sprache fest, die Standardparameter unterstützt.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultParameterValueAttribute.#ctor(System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.DefaultParameterValueAttribute" />-Klasse mit dem Standardwert eines Parameters.</summary>
      <param name="value">Ein Objekt, das den Standardwert eines Parameters darstellt.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultParameterValueAttribute.Value">
      <summary>Ruft den Standardwert eines Parameters ab.</summary>
      <returns>Ein Objekt, das den Standardwert eines Parameters darstellt.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispatchWrapper">
      <summary>Schließt Objekte in einen Wrapper ein, die der Marshaller als VT_DISPATCH marshallen soll.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispatchWrapper.#ctor(System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />-Klasse mit dem Objekt, das umschlossen wird.</summary>
      <param name="obj">Das Objekt, das in <see cref="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH" /> umgewandelt werden soll und das umschlossen wird. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> ist keine Klasse und kein Array.- oder - <paramref name="obj" /> bietet keine Unterstützung von IDispatch. </exception>
      <exception cref="T:System.InvalidOperationException">Der <paramref name="obj" />-Parameter wurde mit einem <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" />-Attribut gekennzeichnet, an das der Wert false übergeben wurde.- oder -Der <paramref name="obj" />-Parameter erbt von einem Typ, der mit einem <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" />-Attribut gekennzeichnet ist, an das der Wert false übergeben wurde.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.DispatchWrapper.WrappedObject">
      <summary>Ruft das Objekt ab, das von <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> umschlossen wird.</summary>
      <returns>Das Objekt, das von <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> umschlossen wird.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispIdAttribute">
      <summary>Gibt die COM DISPID (Dispatch Identifier) einer Methode, eines Felds oder einer Eigenschaft an.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispIdAttribute.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der DispIdAttribute-Klasse mit der angegebenen DISPID.</summary>
      <param name="dispId">Die DISPID des Members. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DispIdAttribute.Value">
      <summary>Ruft die DISPID des Members ab.</summary>
      <returns>Die DISPID des Members.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportAttribute">
      <summary>Gibt an, dass die attributierte Methode durch eine nicht verwaltete DLL (Dynamic Link Library) als statischer Einstiegspunkt verfügbar gemacht wird.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DllImportAttribute.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />-Klasse mit dem Namen der DLL, die die zu importierende Methode enthält.</summary>
      <param name="dllName">Der Name der DLL, die die nicht verwaltete Methode enthält.Dieser kann einen Assemblyanzeigenamen einschließen, wenn die DLL in einer Assembly enthalten ist.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.BestFitMapping">
      <summary>Aktiviert bzw. deaktiviert das Verhalten der optimalen Zuordnung bei der Konvertierung von Unicode-Zeichen in ANSI-Zeichen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CallingConvention">
      <summary>Gibt die Aufrufkonvention eines Einstiegspunktes an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CharSet">
      <summary>Gibt an, wie Zeichenfolgenparameter an die Methode gemarshallt werden, und steuert die Namenszerlegung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.EntryPoint">
      <summary>Gibt den Namen oder die Ordnungszahl des aufzurufenden DLL-Einstiegspunktes an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ExactSpelling">
      <summary>Steuert, ob das <see cref="F:System.Runtime.InteropServices.DllImportAttribute.CharSet" />-Feld die Common Language Runtime anweist, eine nicht verwaltete DLL nach anderen Einstiegspunktnamen als dem angegebenen zu durchsuchen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.PreserveSig">
      <summary>Gibt an, ob nicht verwaltete Methoden, die über HRESULT-Rückgabewerte oder retval-Rückgabewerte verfügen, direkt übersetzt werden oder ob HRESULT-Rückgabewerte oder retval-Rückgabewerte automatisch in Ausnahmen konvertiert werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError">
      <summary>Gibt an, ob der Aufgerufene vor dem Zurückgeben aus der attributierten Methode die Win32-API SetLastError aufruft.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ThrowOnUnmappableChar">
      <summary>Aktiviert bzw. deaktiviert das Auslösen einer Ausnahme bei einem nicht zuzuordnenden Unicode-Zeichen, das in das ANSI-Zeichen "?" konvertiert wird.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.DllImportAttribute.Value">
      <summary>Ruft den Namen der DLL-Datei ab, die den Einstiegspunkt enthält.</summary>
      <returns>Der Name der DLL-Datei, die den Einstiegspunkt enthält.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportSearchPath">
      <summary>Gibt die Pfade an, die verwendet werden, um nach DLLs zu finden, die Funktionen für Plattformaufrufe bereitstellen. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.ApplicationDirectory">
      <summary>Nehmen Sie das Anwendungsverzeichnis in den DLL-Suchpfad auf. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.AssemblyDirectory">
      <summary>Bei der Suche nach Assembly-Abhängigkeiten schließen Sie das Verzeichnis ein, das die Assembly selbst enthält, und durchsuchen dieses Verzeichnis zuerst.Dieser Wert wird von .NET Framework verwendet wird, bevor die Pfade der Win32-Funktion LoadLibraryEx übergeben werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.LegacyBehavior">
      <summary>Suchen Sie das Anwendungsverzeichnis, und rufen Sie anschließend die Win32-Funktion LoadLibraryEx mit dem LOAD_WITH_ALTERED_SEARCH_PATH-Flag auf.Dieser Wert wird ignoriert, wenn ein anderer Wert angegeben wird.Betriebssysteme, die das <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" />-Attribut nicht unterstützen, verwenden diesen Wert und ignorieren andere Werte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.SafeDirectories">
      <summary>Nehmen Sie das Anwendungsverzeichnis, das %WinDir%\System32-Verzeichnis und die Benutzerverzeichnisse in den DLL-Suchpfad auf. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.System32">
      <summary>Nehmen Sie das %WinDir%\System32-Verzeichnis in den DLL-Suchpfad auf. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UseDllDirectoryForDependencies">
      <summary>Suche nach den Abhängigkeiten einer DLL in dem Ordner, in dem sich die DLL befindet, bevor nach anderen Ordnern gesucht wird. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UserDirectories">
      <summary>Schließen Sie jeden Pfad ein, der explizit im prozessweiten Suchpfad hinzugefügt wurde, indem die Win32-Funktion AddDllDirectory übergeben wurde. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ErrorWrapper">
      <summary>Schließt Objekte in einen Wrapper ein, die der Marshaller als VT_ERROR marshallen soll.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ErrorWrapper" />-Klasse mit dem entsprechenden HRESULT für die angegebene Ausnahme.</summary>
      <param name="e">Die Ausnahme, die in einen Fehlercode konvertiert werden soll. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ErrorWrapper" />-Klasse mit dem HRESULT des Fehlers.</summary>
      <param name="errorCode">HRESULT des Fehlers. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.ErrorWrapper" />-Klasse mit einem Objekt, das das HRESULT des Fehlers enthält.</summary>
      <param name="errorCode">Das Objekt, das das HRESULT des Fehlers enthält. </param>
      <exception cref="T:System.ArgumentException">Der <paramref name="errorCode" />-Parameter ist kein <see cref="T:System.Int32" />-Typ.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.ErrorWrapper.ErrorCode">
      <summary>Ruft den Fehlercode des Wrappers ab.</summary>
      <returns>HRESULT des Fehlers.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandle">
      <summary>Bietet die Möglichkeit, aus nicht verwaltetem Speicher auf ein verwaltetes Objekt zuzugreifen.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.AddrOfPinnedObject">
      <summary>Ruft die Adresse eines Objekts in einem <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />-Handle ab.</summary>
      <returns>Die Adresse des fixierten Objekts als <see cref="T:System.IntPtr" />. </returns>
      <exception cref="T:System.InvalidOperationException">The handle is any type other than <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object)">
      <summary>Reserviert ein <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" />-Handle für das angegebene Objekt.</summary>
      <returns>Ein neues <see cref="T:System.Runtime.InteropServices.GCHandle" />, das das Objekt vor der Garbage Collection schützt.Dieses <see cref="T:System.Runtime.InteropServices.GCHandle" /> muss mit <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> freigegeben werden, wenn es nicht mehr benötigt wird.</returns>
      <param name="value">Das Objekt, das das <see cref="T:System.Runtime.InteropServices.GCHandle" /> verwendet. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object,System.Runtime.InteropServices.GCHandleType)">
      <summary>Reserviert ein Handle vom angegebenen Typ für das angegebene Objekt.</summary>
      <returns>Ein neues <see cref="T:System.Runtime.InteropServices.GCHandle" /> vom angegebenen Typ.Dieses <see cref="T:System.Runtime.InteropServices.GCHandle" /> muss mit <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> freigegeben werden, wenn es nicht mehr benötigt wird.</returns>
      <param name="value">Das Objekt, das das <see cref="T:System.Runtime.InteropServices.GCHandle" /> verwendet. </param>
      <param name="type">Einer der <see cref="T:System.Runtime.InteropServices.GCHandleType" />-Werte, der den Typ des zu erstellenden <see cref="T:System.Runtime.InteropServices.GCHandle" /> angibt. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt mit dem aktuellen <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt identisch ist.</summary>
      <returns>true, wenn das angegebene <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt und das aktuelle <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt gleich sind, andernfalls false.</returns>
      <param name="o">Das <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt, das mit dem aktuellen <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Free">
      <summary>Gibt ein <see cref="T:System.Runtime.InteropServices.GCHandle" /> frei.</summary>
      <exception cref="T:System.InvalidOperationException">The handle was freed or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.FromIntPtr(System.IntPtr)">
      <summary>Gibt ein neues <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt zurück, das aus einem Handle für ein verwaltetes Objekt erstellt wurde.</summary>
      <returns>Ein neues <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt, das dem Wertparameter entspricht.  </returns>
      <param name="value">Ein <see cref="T:System.IntPtr" />-Handle für ein verwaltetes Objekt, aus dem ein <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt erstellt werden soll.</param>
      <exception cref="T:System.InvalidOperationException">The value of the <paramref name="value" /> parameter is <see cref="F:System.IntPtr.Zero" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.GetHashCode">
      <summary>Gibt einen Bezeichner für das aktuelle <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt zurück.</summary>
      <returns>Ein Bezeichner für das aktuelle <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.IsAllocated">
      <summary>Ruft einen Wert ab, der angibt, ob das Handle reserviert ist.</summary>
      <returns>true, wenn das Handle reserviert ist, andernfalls false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Equality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Gibt einen Wert zurück, der angibt, ob zwei <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekte gleich sind.</summary>
      <returns>true, wenn der <paramref name="a" />-Parameter und der <paramref name="b" />-Parameter gleich sind, andernfalls false.</returns>
      <param name="a">Ein <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt, das mit dem <paramref name="b" />-Parameter verglichen werden soll. </param>
      <param name="b">Ein <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt, das mit dem <paramref name="a" />-Parameter verglichen werden soll.  </param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.IntPtr)~System.Runtime.InteropServices.GCHandle">
      <summary>Ein <see cref="T:System.Runtime.InteropServices.GCHandle" /> wird mithilfe einer internen Ganzzahldarstellung gespeichert.</summary>
      <returns>Das gespeicherte <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt mit einer internen Ganzzahldarstellung.</returns>
      <param name="value">Ein <see cref="T:System.IntPtr" />, der das Handle angibt, für das die Konvertierung erforderlich ist. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.Runtime.InteropServices.GCHandle)~System.IntPtr">
      <summary>Ein <see cref="T:System.Runtime.InteropServices.GCHandle" /> wird mithilfe einer internen Ganzzahldarstellung gespeichert.</summary>
      <returns>Der Ganzzahlwert.</returns>
      <param name="value">Das <see cref="T:System.Runtime.InteropServices.GCHandle" />, für das die ganze Zahl erforderlich ist. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Inequality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Gibt einen Wert zurück, der angibt, ob zwei <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekte ungleich sind.</summary>
      <returns>true, wenn der <paramref name="a" />-Parameter und der <paramref name="b" />-Parameter ungleich sind, andernfalls false.</returns>
      <param name="a">Ein <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt, das mit dem <paramref name="b" />-Parameter verglichen werden soll. </param>
      <param name="b">Ein <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt, das mit dem <paramref name="a" />-Parameter verglichen werden soll.  </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.Target">
      <summary>Ruft das Objekt ab, das von diesem Handle dargestellt wird, oder legt dieses fest.</summary>
      <returns>Das Objekt, das von diesem Handle dargestellt wird.</returns>
      <exception cref="T:System.InvalidOperationException">The handle was freed, or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.ToIntPtr(System.Runtime.InteropServices.GCHandle)">
      <summary>Gibt die interne Ganzzahldarstellung eines <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekts zurück.</summary>
      <returns>Ein <see cref="T:System.IntPtr" />-Objekt, das ein <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt darstellt. </returns>
      <param name="value">Ein <see cref="T:System.Runtime.InteropServices.GCHandle" />-Objekt, aus dem eine interne Ganzzahldarstellung abgerufen werden soll.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandleType">
      <summary>Stellt die Handletypen dar, die von der <see cref="T:System.Runtime.InteropServices.GCHandle" />-Klasse reserviert werden können.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Normal">
      <summary>Dieser Handletyp stellt ein nicht transparentes Handle dar, d. h., dass die Adresse des fixierten Objekts nicht über das Handle aufgelöst werden kann.Sie können mit diesem Typ ein Objekt überwachen und verhindern, dass es vom Garbage Collector erfasst wird.Dieser Enumerationsmember ist nützlich, wenn ein nicht verwalteter Client den einzigen Verweis auf ein verwaltetes Objekt enthält, der vom Garbage Collector nicht erkannt werden kann.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Pinned">
      <summary>Dieser Handletyp ähnelt <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" />, lässt jedoch das Erfassen der Adresse des fixierten Objekts zu.Andernfalls wird das Verschieben des Objekts durch den Garbage Collector verhindert und somit dessen Effizienz verringert.Verwenden Sie die <see cref="M:System.Runtime.InteropServices.GCHandle.Free" />-Methode, um das reservierte Handle so bald wie möglich freizugeben.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Weak">
      <summary>Dieser Handletyp wird zum Überwachen eines Objekts verwendet, lässt jedoch das Sammeln zu.Beim Sammeln eines Objekts wird der Inhalt von <see cref="T:System.Runtime.InteropServices.GCHandle" /> auf 0 festgelegt.Weak-Verweise werden vor dem Ausführen des Finalizers auf 0 festgelegt. Selbst wenn der Finalizer das Objekt wieder zugänglich macht, ist der Weak-Verweis immer noch auf 0 festgelegt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.WeakTrackResurrection">
      <summary>Dieser Handletyp ähnelt <see cref="F:System.Runtime.InteropServices.GCHandleType.Weak" />, das Handle wird jedoch nicht auf 0 festgelegt, wenn das Objekt während der Finalisierung wieder zugänglich gemacht wird.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.GuidAttribute">
      <summary>Stellt eine explizite <see cref="T:System.Guid" /> bereit, wenn eine automatische GUID nicht erwünscht ist.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GuidAttribute.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.GuidAttribute" />-Klasse mit dem angegebenen GUID.</summary>
      <param name="guid">Die zuzuweisende <see cref="T:System.Guid" />. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GuidAttribute.Value">
      <summary>Ruft die <see cref="T:System.Guid" /> der Klasse ab.</summary>
      <returns>Der <see cref="T:System.Guid" /> der Klasse.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.HandleCollector">
      <summary>Verfolgt ausstehende Handles und erzwingt eine Garbage Collection, wenn der angegebenen Schwellenwert erreicht wird.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.HandleCollector" />-Klasse mit einem Namen und einem Schwellenwert, an dem die Handle-Erfassung beginnen soll. </summary>
      <param name="name">Ein Name für den Collector.Dieser Parameter ermöglicht Ihnen das Benennen von Collectors, die Typen von Handles separat verfolgen.</param>
      <param name="initialThreshold">Ein Wert, der den Punkt angibt, bei dem die Erfassung beginnen soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="initialThreshold" />-Parameter ist kleiner als 0 (null).</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.HandleCollector" />-Klasse mit einem Namen, einem Schwellenwert für den Beginn der Handle-Erfassung sowie einem Schwellenwert, ab dem die Handle-Erfassung auftreten muss. </summary>
      <param name="name">Ein Name für den Collector.  Dieser Parameter ermöglicht Ihnen das Benennen von Collectors, die Typen von Handles separat verfolgen.</param>
      <param name="initialThreshold">Ein Wert, der den Punkt angibt, bei dem die Erfassung beginnen soll.</param>
      <param name="maximumThreshold">Ein Wert, der den Punkt angibt, bei dem Erfassungen auftreten müssen.Dieser muss auf die maximale Anzahl verfügbarer Handles festgelegt werden.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="initialThreshold" />-Parameter ist kleiner als 0 (null).- oder -Der <paramref name="maximumThreshold" />-Parameter ist kleiner als 0 (null).</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="maximumThreshold" />-Parameter ist kleiner als der <paramref name="initialThreshold" />-Parameter.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Add">
      <summary>Erhöht die aktuelle Anzahl der Handles.</summary>
      <exception cref="T:System.InvalidOperationException">Die <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" />-Eigenschaft ist kleiner als 0 (null).</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Count">
      <summary>Ruft die Anzahl der erfassten Handles ab.</summary>
      <returns>Die Anzahl der erfassten Handles.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.InitialThreshold">
      <summary>Ruft einen Wert ab, der den Punkt angibt, bei dem die Erfassung beginnen soll.</summary>
      <returns>Ein Wert, der den Punkt angibt, bei dem die Erfassung beginnen soll.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.MaximumThreshold">
      <summary>Ruft einen Wert ab, der den Punkt angibt, bei dem Erfassungen auftreten müssen.</summary>
      <returns>Ein Wert, der den Punkt angibt, bei dem Erfassungen auftreten müssen.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Name">
      <summary>Ruft den Namen eines <see cref="T:System.Runtime.InteropServices.HandleCollector" />-Objekts ab.</summary>
      <returns>Diese <see cref="P:System.Runtime.InteropServices.HandleCollector.Name" />-Eigenschaft ermöglicht Ihnen das Benennen von Collectors, die Typen von Handles separat verfolgen.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Remove">
      <summary>Verringert die aktuelle Anzahl der Handles.</summary>
      <exception cref="T:System.InvalidOperationException">Die <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" />-Eigenschaft ist kleiner als 0 (null).</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomAdapter">
      <summary>Ermöglicht es Clients, auf das tatsächliche Objekt anstatt auf das von einem benutzerdefinierten Marshaller ausgegebene Adapterobjekt zuzugreifen.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomAdapter.GetUnderlyingObject">
      <summary>Stellt den Zugriff auf das zugrunde liegende Objekt bereit, das von einem benutzerdefinierten Marshaller umfasst wird.</summary>
      <returns>Das Objekt, das im Adapterobjekt enthalten ist.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomQueryInterface">
      <summary>Ermöglicht Entwicklern das Bereitstellen einer benutzerdefinierten, verwalteten Implementierung der IUnknown::QueryInterface(REFIID riid, void **ppvObject)-Methode.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)">
      <summary>Gibt entsprechend einer angegebenen Schnittstellen-ID eine Schnittstelle zurück.</summary>
      <returns>Einer der Enumerationswerte, der angibt, ob eine benutzerdefinierte Implementierung von IUnknown::QueryInterface verwendet wurde.</returns>
      <param name="iid">Die GUID der angeforderten Schnittstelle.</param>
      <param name="ppv">Nach dem Beenden der Methode ein Verweis auf die angeforderte Schnittstelle.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InAttribute">
      <summary>Gibt an, dass Daten vom Aufrufer an den Aufgerufenen gemarshallt werden, jedoch nicht zurück an den Aufrufer.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InAttribute.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.InAttribute" />-Klasse.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.InterfaceTypeAttribute">
      <summary>Gibt an, ob es sich bei einer verwalteten Schnittstelle um eine duale, auf Dispatch beschränkte oder auf IUnknown beschränkte Schnittstelle handelt, wenn diese für COM verfügbar gemacht wird.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Int16)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" />-Klasse mit dem angegebenen <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />-Enumerationsmember.</summary>
      <param name="interfaceType">Beschreibt, wie die Schnittstelle für COM-Clients verfügbar gemacht werden soll. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Runtime.InteropServices.ComInterfaceType)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" />-Klasse mit dem angegebenen <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />-Enumerationsmember.</summary>
      <param name="interfaceType">Einer der <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />-Werte, der beschreibt, wie die Schnittstelle für COM-Clients verfügbar gemacht werden soll. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.InterfaceTypeAttribute.Value">
      <summary>Ruft den <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />-Wert ab, der beschreibt, wie die Schnittstelle für COM verfügbar gemacht werden soll.</summary>
      <returns>Der <see cref="T:System.Runtime.InteropServices.ComInterfaceType" />-Wert, der beschreibt, wie die Schnittstelle für COM verfügbar gemacht werden soll.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidComObjectException">
      <summary>Die Ausnahme, die bei Verwendung eines ungültigen COM-Objekts ausgelöst wird.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor">
      <summary>Initialisiert eine Instanz von InvalidComObjectException mit Standardeigenschaften.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String)">
      <summary>Initialisiert eine Instanz von InvalidComObjectException mit einer Meldung.</summary>
      <param name="message">Die Meldung, in der die Ursache für die Ausnahme angegeben wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.InvalidComObjectException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
      <summary>Die Ausnahme, die vom Marshaller ausgelöst wird, wenn ein Argument eines Varianten-Typs auftritt, das nicht an verwalteten Code gemarshallt werden kann.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor">
      <summary>Initialisiert eine neue Instanz der InvalidOleVariantTypeException-Klasse mit Standardwerten.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der InvalidOleVariantTypeException-Klasse mit einer angegebenen Meldung.</summary>
      <param name="message">Die Meldung, in der die Ursache für die Ausnahme angegeben wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.Marshal">
      <summary>Bietet eine Auflistung von Methoden zum Belegen von nicht verwaltetem Speicher, zum Kopieren von nicht verwalteten Speicherblöcken und zum Konvertieren von verwalteten in nicht verwaltete Typen sowie sonstige Methoden, die bei der Interaktion mit nicht verwaltetem Code verwendet werden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AddRef(System.IntPtr)">
      <summary>Inkrementiert den Verweiszähler für die angegebene Schnittstelle.</summary>
      <returns>Der neue Wert des Verweiszählers für den <paramref name="pUnk" />-Parameter.</returns>
      <param name="pUnk">Der zu inkrementierende Verweiszähler für Schnittstellen.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)">
      <summary>Belegt einen Speicherblock der angegebenen Größe aus der Speicherbelegungsfunktion für COM-Aufgaben.</summary>
      <returns>Eine ganze Zahl, die die Adresse des belegten Speicherblocks darstellt.Dieser Speicher muss mit <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" /> freigegeben werden.</returns>
      <param name="cb">Die Größe des zu belegenden Speicherblocks.</param>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um die Anforderung zu erfüllen.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.Int32)">
      <summary>Reserviert Speicher aus dem nicht verwalteten Arbeitsspeicher des Prozesses mit der angegebenen Anzahl von Bytes.</summary>
      <returns>Ein Zeiger auf den neu reservierten Speicher.Dieser Speicher muss unter Verwendung der <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />-Methode freigegeben werden.</returns>
      <param name="cb">Die erforderliche Anzahl der Bytes im Speicher.</param>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um die Anforderung zu erfüllen.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)">
      <summary>Reserviert Speicher aus dem nicht verwalteten Arbeitsspeicher des Prozesses mithilfe des Zeigers auf die angegebene Anzahl von Bytes.</summary>
      <returns>Ein Zeiger auf den neu reservierten Speicher.Dieser Speicher muss unter Verwendung der <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />-Methode freigegeben werden.</returns>
      <param name="cb">Die erforderliche Anzahl der Bytes im Speicher.</param>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um die Anforderung zu erfüllen.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AreComObjectsAvailableForCleanup">
      <summary>Gibt an, ob Runtime Callable Wrappers (RCWs) aus einem beliebigen Kontext zur Bereinigung verfügbar sind.</summary>
      <returns>true, wenn RCWs zur Bereinigung verfügbar sind, andernfalls false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Byte[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Kopiert Daten aus einem eindimensionalen, verwalteten Array von 8-Bit-Ganzzahlen ohne Vorzeichen in einen nicht verwalteten Speicherzeiger.</summary>
      <param name="source">Das eindimensionale Array, aus dem kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Quellarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="destination">Der Speicherzeiger, in den kopiert werden soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> und <paramref name="length" /> sind ungültig.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Char[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Kopiert Daten aus einem eindimensionalen, verwalteten Zeichenarray in einen nicht verwalteten Speicherzeiger.</summary>
      <param name="source">Das eindimensionale Array, aus dem kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Quellarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="destination">Der Speicherzeiger, in den kopiert werden soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> und <paramref name="length" /> sind ungültig.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" />, <paramref name="destination" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Double[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Kopiert Daten aus einem eindimensionalen, verwalteten Array von Gleitkommazahlen mit doppelter Genauigkeit in einen nicht verwalteten Speicherzeiger.</summary>
      <param name="source">Das eindimensionale Array, aus dem kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Quellarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="destination">Der Speicherzeiger, in den kopiert werden soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> und <paramref name="length" /> sind ungültig.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int16[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Kopiert Daten aus einem eindimensionalen, verwalteten Array von 16-Bit-Ganzzahlen mit Vorzeichen in einen nicht verwalteten Speicherzeiger.</summary>
      <param name="source">Das eindimensionale Array, aus dem kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Quellarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="destination">Der Speicherzeiger, in den kopiert werden soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> und <paramref name="length" /> sind ungültig.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int32[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Kopiert Daten aus einem eindimensionalen, verwalteten Array von 32-Bit-Ganzzahlen mit Vorzeichen in einen nicht verwalteten Speicherzeiger.</summary>
      <param name="source">Das eindimensionale Array, aus dem kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Quellarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="destination">Der Speicherzeiger, in den kopiert werden soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> und <paramref name="length" /> sind ungültig.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int64[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Kopiert Daten aus einem eindimensionalen, verwalteten Array von 64-Bit-Ganzzahlen mit Vorzeichen in einen nicht verwalteten Speicherzeiger.</summary>
      <param name="source">Das eindimensionale Array, aus dem kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Quellarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="destination">Der Speicherzeiger, in den kopiert werden soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> und <paramref name="length" /> sind ungültig.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Byte[],System.Int32,System.Int32)">
      <summary>Kopiert Daten aus einem nicht verwalteten Speicherzeiger in ein verwaltetes Array von 8-Bit-Ganzzahlen ohne Vorzeichen.</summary>
      <param name="source">Der Speicherzeiger, aus dem kopiert werden soll.</param>
      <param name="destination">Das Array, in das kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Zielarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Char[],System.Int32,System.Int32)">
      <summary>Kopiert Daten aus einem nicht verwalteten Speicherzeiger in ein verwaltetes Zeichenarray.</summary>
      <param name="source">Der Speicherzeiger, aus dem kopiert werden soll.</param>
      <param name="destination">Das Array, in das kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Zielarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Double[],System.Int32,System.Int32)">
      <summary>Kopiert Daten aus einem nicht verwalteten Speicherzeiger in ein verwaltetes Array von Gleitkommazahlen mit doppelter Genauigkeit.</summary>
      <param name="source">Der Speicherzeiger, aus dem kopiert werden soll.</param>
      <param name="destination">Das Array, in das kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Zielarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int16[],System.Int32,System.Int32)">
      <summary>Kopiert Daten aus einem nicht verwalteten Speicherzeiger in ein verwaltetes Array von 16-Bit-Ganzzahlen mit Vorzeichen.</summary>
      <param name="source">Der Speicherzeiger, aus dem kopiert werden soll.</param>
      <param name="destination">Das Array, in das kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Zielarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int32[],System.Int32,System.Int32)">
      <summary>Kopiert Daten aus einem nicht verwalteten Speicherzeiger in ein verwaltetes Array von 32-Bit-Ganzzahlen mit Vorzeichen.</summary>
      <param name="source">Der Speicherzeiger, aus dem kopiert werden soll.</param>
      <param name="destination">Das Array, in das kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Zielarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int64[],System.Int32,System.Int32)">
      <summary>Kopiert Daten aus einem nicht verwalteten Speicherzeiger in ein verwaltetes Array von 64-Bit-Ganzzahlen mit Vorzeichen.</summary>
      <param name="source">Der Speicherzeiger, aus dem kopiert werden soll.</param>
      <param name="destination">Das Array, in das kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Zielarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> oder <paramref name="length" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
      <summary>Kopiert Daten aus einem nicht verwalteten Speicherzeiger in ein verwaltetes <see cref="T:System.IntPtr" />-Array.</summary>
      <param name="source">Der Speicherzeiger, aus dem kopiert werden soll. </param>
      <param name="destination">Das Array, in das kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Zielarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> oder <paramref name="length" /> ist null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Single[],System.Int32,System.Int32)">
      <summary>Kopiert Daten aus einem nicht verwalteten Speicherzeiger in ein verwaltetes Array von Gleitkommazahlen mit einfacher Genauigkeit.</summary>
      <param name="source">Der Speicherzeiger, aus dem kopiert werden soll. </param>
      <param name="destination">Das Array, in das kopiert werden soll. </param>
      <param name="startIndex">Der nullbasierte Index im Zielarray, an dem der Kopiervorgang beginnen soll. </param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> oder <paramref name="length" /> ist null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Kopiert Daten aus einem eindimensionalen, verwalteten <see cref="T:System.IntPtr" />-Array in einen nicht verwalteten Speicherzeiger.</summary>
      <param name="source">Das eindimensionale Array, aus dem kopiert werden soll.</param>
      <param name="startIndex">Der nullbasierte Index im Quellarray, an dem der Kopiervorgang beginnen soll.</param>
      <param name="destination">Der Speicherzeiger, in den kopiert werden soll.</param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> oder <paramref name="length" /> ist null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Single[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Kopiert Daten aus einem eindimensionalen, verwalteten Array von Gleitkommazahlen mit einfacher Genauigkeit in einen nicht verwalteten Speicherzeiger.</summary>
      <param name="source">Das eindimensionale Array, aus dem kopiert werden soll. </param>
      <param name="startIndex">Der nullbasierte Index im Quellarray, an dem der Kopiervorgang beginnen soll. </param>
      <param name="destination">Der Speicherzeiger, in den kopiert werden soll. </param>
      <param name="length">Die Anzahl der zu kopierenden Arrayelemente. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> und <paramref name="length" /> sind ungültig. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> oder <paramref name="length" /> ist null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject(System.IntPtr,System.Object)">
      <summary>Verbindet ein verwaltetes Objekt mit dem angegebenen COM-Objekt.</summary>
      <returns>Der innere IUnknown-Zeiger des verwalteten Objekts.</returns>
      <param name="pOuter">Der äußere IUnknown-Zeiger.</param>
      <param name="o">Ein zu aggregierendes Objekt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> ist ein Windows-Runtime-Objekt.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject``1(System.IntPtr,``0)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Aggregiert ein verwaltetes Objekt des angegebenen Typs mit dem angegebenen COM-Objekt. </summary>
      <returns>Der innere IUnknown-Zeiger des verwalteten Objekts. </returns>
      <param name="pOuter">Der äußere IUnknown-Zeiger. </param>
      <param name="o">Das zu aggregierende -Objekt. </param>
      <typeparam name="T">Der Typ des zu aggregierenden verwalteten Objekts. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> ist ein Windows-Runtime-Objekt. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType(System.Object,System.Type)">
      <summary>Umschließt das angegebene COM-Objekt mit einem Objekt des angegebenen Typs.</summary>
      <returns>Das neu umschlossene Objekt, das eine Instanz des gewünschten Typs darstellt.</returns>
      <param name="o">Das zu umschließende Objekt. </param>
      <param name="t">Der Typ des zu erstellenden Wrappers. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> muss von __ComObject abgeleitet sein. - oder - <paramref name="t" /> ist ein Windows-Runtime-Typ.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="t" />-Parameter ist null.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> kann nicht in den Zieltyp konvertiert werden, da nicht alle erforderlichen Schnittstellen unterstützt werden. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType``2(``0)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Umschließt das angegebene COM-Objekt mit einem Objekt des angegebenen Typs.</summary>
      <returns>Das neu umschlossene Objekt. </returns>
      <param name="o">Das zu umschließende Objekt. </param>
      <typeparam name="T">Der Typ des zurückzugebenden Umbruchs. </typeparam>
      <typeparam name="TWrapper">Der Typ des zurückzugebenden Objekts. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> muss von __ComObject abgeleitet sein. - oder - <paramref name="T" /> ist ein Windows-Runtime-Typ.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> kann nicht in <paramref name="TWrapper" /> konvertiert werden, da nicht alle erforderlichen Schnittstellen unterstützt werden. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Gibt alle Unterstrukturen eines angegebenen Typs frei, auf die der angegebene nicht verwaltete Speicherblock zeigt. </summary>
      <param name="ptr">Ein Zeiger auf einen nicht verwalteten Speicherblock. </param>
      <typeparam name="T">Der Typ der formatierten Struktur.Damit werden die Layoutinformationen bereitgestellt, die zum Löschen des Puffers im <paramref name="ptr" />-Parameter benötigt werden.</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> weist ein automatisch angelegtes Layout auf.Verwenden Sie stattdessen sequenzielles oder explizites Layout.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)">
      <summary>Gibt alle Unterstrukturen frei, auf die der angegebene nicht verwaltete Speicherblock zeigt.</summary>
      <param name="ptr">Ein Zeiger auf einen nicht verwalteten Speicherblock. </param>
      <param name="structuretype">Typ einer formatierten Klasse.Damit werden die Layoutinformationen bereitgestellt, die zum Löschen des Puffers im <paramref name="ptr" />-Parameter benötigt werden.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> weist ein automatisch angelegtes Layout auf.Verwenden Sie stattdessen sequenzielles oder explizites Layout.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FinalReleaseComObject(System.Object)">
      <summary>Gibt alle Verweise auf einen Runtime Callable Wrapper (RCW) (RCW) durch Festlegen des Verweiszählers auf 0 frei.</summary>
      <returns>Der neue Wert des Verweiszählers des RCWs, der dem <paramref name="o" />-Parameter zugeordnet ist. Dieser ist bei erfolgreicher Freigabe 0 (null).</returns>
      <param name="o">Der RCW, der freigegeben werden soll.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> ist kein gültiges COM-Objekt.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)">
      <summary>Gibt einen BSTR mithilfe der COM-Funktion SysFreeString frei.</summary>
      <param name="ptr">Die Adresse des freizugebenden BSTR. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)">
      <summary>Gibt einen Speicherblock frei, der von der nicht verwalteten Speicherbelegungsfunktion für COM-Aufgaben belegt wurde.</summary>
      <param name="ptr">Die Adresse des freizugebenden Speichers. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)">
      <summary>Gibt Speicherplatz frei, der zuvor aus dem nicht verwalteten Speicher des Prozesses belegt wurde.</summary>
      <param name="hglobal">Das Handle, das beim ursprünglichen übereinstimmenden Aufruf von <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> zurückgegeben wurde. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)">
      <summary>Gibt einen Zeiger auf eine IUnknown-Schnittstelle zurück, die die angegebene Schnittstelle für das angegebene Objekt darstellt.Der benutzerdefinierte Zugriff auf Abfrageschnittstellen ist standardmäßig aktiviert.</summary>
      <returns>Der Schnittstellenzeiger, der die angegebene Schnittstelle für das Objekt darstellt.</returns>
      <param name="o">Das Objekt, das die Schnittstelle bereitstellt. </param>
      <param name="T">Der Typ der angeforderten Schnittstelle. </param>
      <exception cref="T:System.ArgumentException">Der <paramref name="T" />-Parameter ist keine Schnittstelle.- oder -  Der Typ ist für COM nicht sichtbar. - oder - Der <paramref name="T" />-Parameter ist ein generischer Typ.</exception>
      <exception cref="T:System.InvalidCastException">Der <paramref name="o" />-Parameter unterstützt die angeforderte Schnittstelle nicht. </exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="o" />-Parameter ist null.- oder -  Der <paramref name="T" />-Parameter ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)">
      <summary>Gibt einen Zeiger auf eine IUnknown-Schnittstelle zurück, die die angegebene Schnittstelle für das angegebene Objekt darstellt.Der benutzerdefinierte Zugriff auf die Abfrageschnittstelle wird vom angegebenen Anpassungsmodus gesteuert.</summary>
      <returns>Der Schnittstellenzeiger, der die Schnittstelle für das Objekt darstellt.</returns>
      <param name="o">Das Objekt, das die Schnittstelle bereitstellt.</param>
      <param name="T">Der Typ der angeforderten Schnittstelle.</param>
      <param name="mode">Einer der Enumerationswerte, die angeben, ob eine IUnknown::QueryInterface-Anpassung übernommen werden soll, die von einer <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> angegeben wird.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="T" />-Parameter ist keine Schnittstelle.- oder -  Der Typ ist für COM nicht sichtbar.- oder - Der <paramref name="T" />-Parameter ist ein generischer Typ.</exception>
      <exception cref="T:System.InvalidCastException">Das Objekt <paramref name="o" /> unterstützt die angeforderte Schnittstelle nicht.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="o" />-Parameter ist null.- oder -  Der <paramref name="T" />-Parameter ist null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject``2(``0)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Gibt einen Zeiger auf eine IUnknown-Schnittstelle zurück, die die angegebene Schnittstelle für das Objekt des angegebenen Typs darstellt.Der benutzerdefinierte Zugriff auf Abfrageschnittstellen ist standardmäßig aktiviert.</summary>
      <returns>Der Schnittstellenzeiger, der die <paramref name="TInterface" />-Schnittstelle darstellt.</returns>
      <param name="o">Das Objekt, das die Schnittstelle bereitstellt. </param>
      <typeparam name="T">Der Typ von <paramref name="o" />. </typeparam>
      <typeparam name="TInterface">Der Typ der zurückzugebenden Schnittstelle. </typeparam>
      <exception cref="T:System.ArgumentException">Der <paramref name="TInterface" />-Parameter ist keine Schnittstelle.- oder -  Der Typ ist für COM nicht sichtbar. - oder - Der <paramref name="T" />-Parameter ist ein offener generischer Typ.</exception>
      <exception cref="T:System.InvalidCastException">Der <paramref name="o" />-Parameter unterstützt die angeforderte <paramref name="TInterface" /> Schnittstelle nicht. </exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="o" />-Parameter ist null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer``1(System.IntPtr)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Konvertiert einen nicht verwalteten Funktionszeiger in einen Delegaten eines angegebenen Typs. </summary>
      <returns>Eine Instanz vom angegebenen Delegaten.</returns>
      <param name="ptr">Der nicht verwaltete Funktionszeiger, der konvertiert werden soll. </param>
      <typeparam name="TDelegate">Der Typ des zurückzugebenden Delegaten. </typeparam>
      <exception cref="T:System.ArgumentException">Der <paramref name="TDelegate" /> generische Parameter ist kein Delegat, oder es ist ein offener generischer Typ.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="ptr" />-Parameter ist null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(System.IntPtr,System.Type)">
      <summary>Konvertiert einen nicht verwalteten Funktionszeiger in einen Delegaten.</summary>
      <returns>Eine Delegatinstanz, die in den entsprechenden Delegattyp umgewandelt werden kann.</returns>
      <param name="ptr">Der nicht verwaltete Funktionszeiger, der konvertiert werden soll.</param>
      <param name="t">Der Typ des zurückzugebenden Delegaten.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="t" />-Parameter ist weder ein Delegat, noch ist er generisch.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="ptr" />-Parameter ist null.- oder - Der <paramref name="t" />-Parameter ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionCode">
      <summary>Ruft einen Code ab, der den Typ der aufgetretenen Ausnahme bezeichnet.</summary>
      <returns>Der Typ der Ausnahme.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32)">
      <summary>Konvertiert den angegebenen HRESULT-Fehlercode in ein entsprechendes <see cref="T:System.Exception" />-Objekt.</summary>
      <returns>Ein Objekt, das das konvertierte HRESULT darstellt.</returns>
      <param name="errorCode">Das zu konvertierende HRESULT.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Konvertiert den angegebenen HRESULT-Fehlercode in ein entsprechendes <see cref="T:System.Exception" />-Objekt, wobei zusätzliche Fehlerinformationen in eine IErrorInfo-Schnittstelle für das Ausnahmeobjekt übergeben werden.</summary>
      <returns>Ein Objekt, das das konvertierte HRESULT und die aus <paramref name="errorInfo" /> abgerufenen Informationen darstellt.</returns>
      <param name="errorCode">Das zu konvertierende HRESULT.</param>
      <param name="errorInfo">Ein Zeiger auf die IErrorInfo-Schnittstelle, die weitere Informationen über den Fehler bereitstellt.Sie können IntPtr(0) angeben, um die aktuelle IErrorInfo-Schnittstelle zu verwenden, oder Sie können IntPtr(-1) angeben, um die aktuelle IErrorInfo-Schnittstelle zu ignorieren und die Ausnahme direkt aus dem Fehlercode zu erstellen.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(System.Delegate)">
      <summary>Konvertiert einen Delegaten in einen Funktionszeiger, der aus nicht verwaltetem Code aufgerufen werden kann.</summary>
      <returns>Ein Wert, der an nicht verwalteten Code übergeben werden kann, der mit diesem Wert wiederum den zugrunde liegenden verwalteten Delegaten aufrufen kann. </returns>
      <param name="d">Der Delegat, der an nicht verwalteten Code übergeben werden soll.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="d" />-Parameter ist ein generischer Typ.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="d" />-Parameter ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate``1(``0)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Konvertiert einen Delegaten eines angegebenen Typs in einen Funktionszeiger, der aus nicht verwaltetem Code aufgerufen werden kann. </summary>
      <returns>Ein Wert, der an nicht verwalteten Code übergeben werden kann, der mit diesem Wert wiederum den zugrunde liegenden verwalteten Delegaten aufrufen kann. </returns>
      <param name="d">Der Delegat, der an nicht verwalteten Code übergeben werden soll. </param>
      <typeparam name="TDelegate">Der Typ des zu konvertierenden Delegaten. </typeparam>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="d" />-Parameter ist null. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForException(System.Exception)">
      <summary>Konvertiert die angegebene Ausnahme in ein HRESULT.</summary>
      <returns>Das HRESULT, das der bereitgestellten Ausnahme zugeordnet ist.</returns>
      <param name="e">Die in ein HRESULT zu konvertierende Ausnahme.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForLastWin32Error">
      <summary>Gibt das HRESULT für den letzten Fehler zurück, der durch einen mit <see cref="T:System.Runtime.InteropServices.Marshal" /> ausgeführten Win32-Code ausgelöst wurde.</summary>
      <returns>Das HRESULT für den letzten Win32-Fehlercode.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetIUnknownForObject(System.Object)">
      <summary>Gibt eine IUnknown-Schnittstelle von einem verwalteten Objekt zurück.</summary>
      <returns>Der IUnknown-Zeiger für den <paramref name="o" />-Parameter.</returns>
      <param name="o">Das Objekt, dessen IUnknown-Schnittstelle angefordert wird.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error">
      <summary>Gibt den Fehlercode zurück, der durch die letzte nicht verwaltete Funktion zurückgegeben wurde, die mit einem Plattformaufruf aufgerufen wurde und bei der das <see cref="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError" />-Flag festgelegt wurde.</summary>
      <returns>Der letzte durch einen Aufruf der Win32-SetLastError-Funktion festgelegte Fehlercode.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject(System.Object,System.IntPtr)">
      <summary>Konvertiert ein Objekt in einen COM-VARIANT.</summary>
      <param name="obj">Das Objekt, für das ein COM-VARIANT abgerufen werden soll.</param>
      <param name="pDstNativeVariant">Ein Zeiger, der den VARIANT empfangen soll, der dem <paramref name="obj" />-Parameter entspricht.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="obj" />-Parameter ist ein generischer Typ.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject``1(``0,System.IntPtr)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Konvertiert ein Objekt eines angegebenen Typs in eine COM VARIANTE. </summary>
      <param name="obj">Das Objekt, für das ein COM-VARIANT abgerufen werden soll. </param>
      <param name="pDstNativeVariant">Ein Zeiger, der den VARIANT empfangen soll, der dem <paramref name="obj" />-Parameter entspricht. </param>
      <typeparam name="T">Der Typ, des Objekts, das konvertiert werden soll. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForIUnknown(System.IntPtr)">
      <summary>Gibt eine Instanz eines Typs zurück, der ein COM-Objekt mit einem Zeiger auf die IUnknown-Schnittstelle darstellt.</summary>
      <returns>Ein Objekt, das das angegebene nicht verwaltete COM-Objekt darstellt.</returns>
      <param name="pUnk">Ein Zeiger auf die IUnknown-Schnittstelle. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant(System.IntPtr)">
      <summary>Konvertiert einen COM-VARIANT in ein Objekt.</summary>
      <returns>Ein Objekt, das dem <paramref name="pSrcNativeVariant" />-Parameter entspricht.</returns>
      <param name="pSrcNativeVariant">Ein Zeiger auf ein COM-VARIANT.</param>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> ist kein gültiger VARIANT-Typ.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> weist einen nicht unterstützten Typ auf.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant``1(System.IntPtr)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Konvertiert ein COM VARIANTE in ein Objekt eines angegebenen Typs . </summary>
      <returns>Ein Objekt des angegebenen Typs, der dem <paramref name="pSrcNativeVariant" />-Parameter entspricht. </returns>
      <param name="pSrcNativeVariant">Ein Zeiger auf ein COM-VARIANT. </param>
      <typeparam name="T">Der Typ, in den die COM VARIANTE konvertiert werden soll. </typeparam>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> ist kein gültiger VARIANT-Typ. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> weist einen nicht unterstützten Typ auf. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants(System.IntPtr,System.Int32)">
      <summary>Konvertiert einem Array von COM-VARIANTs in ein Objektarray. </summary>
      <returns>Ein Objektarray, das diesem <paramref name="aSrcNativeVariant" /> entspricht.</returns>
      <param name="aSrcNativeVariant">Ein Zeiger auf das erste Element eines Arrays von COM-VARIANTs.</param>
      <param name="cVars">Die Zählung der COM-VARIANT in <paramref name="aSrcNativeVariant" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> ist eine negative Zahl.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants``1(System.IntPtr,System.Int32)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Konvertiert ein Array mit COM-VARIANT in ein Array eines angegebenen Typs. </summary>
      <returns>Ein Array von <paramref name="T" />-Objekten, das <paramref name="aSrcNativeVariant" /> entspricht. </returns>
      <param name="aSrcNativeVariant">Ein Zeiger auf das erste Element eines Arrays von COM-VARIANTs. </param>
      <param name="cVars">Die Zählung der COM-VARIANT in <paramref name="aSrcNativeVariant" />. </param>
      <typeparam name="T">Der Typ des zurückzugebenden Arrays. </typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> ist eine negative Zahl. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetStartComSlot(System.Type)">
      <summary>Ruft den ersten Slot in der VTBL (Vtable oder virtuelle Funktionstabelle) ab, der benutzerdefinierte Methoden enthält.</summary>
      <returns>Der erste VTBL-Slot, der benutzerdefinierte Methoden enthält.Der erste Slot lautet 3, wenn die Schnittstelle auf IUnknown basiert. Er lautet 7, wenn die Schnittstelle auf IDispatch basiert.</returns>
      <param name="t">Ein Typ, der eine Schnittstelle darstellt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> ist für COM nicht sichtbar.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeFromCLSID(System.Guid)">
      <summary>Gibt den Typ zurück, dem die angegebene Klassen-ID (CLSID) zugeordnet ist. </summary>
      <returns>System.__ComObject unabhängig von der Gültigkeit der CLSID. </returns>
      <param name="clsid">Die CLSID des zurückzugebenden Typs. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeInfoName(System.Runtime.InteropServices.ComTypes.ITypeInfo)">
      <summary>Ruft den Namen des Typs ab, der durch ein ITypeInfo-Objekt dargestellt wird.</summary>
      <returns>Der Name des Typs, auf den der <paramref name="typeInfo" />-Parameter zeigt.</returns>
      <param name="typeInfo">Ein Objekt, das einen ITypeInfo-Zeiger darstellt.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="typeInfo" />-Parameter ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetUniqueObjectForIUnknown(System.IntPtr)">
      <summary>Erstellt ein eindeutiges Runtime Callable Wrapper (RCW)-Objekt (RCW) für eine angegebene IUnknown-Schnittstelle.</summary>
      <returns>Ein eindeutiger RCW für die angegebene IUnknown-Schnittstelle.</returns>
      <param name="unknown">Ein verwalteter Zeiger auf eine IUnknown-Schnittstelle.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.IsComObject(System.Object)">
      <summary>Gibt an, ob ein angegebenes Objekt ein COM-Objekt darstellt.</summary>
      <returns>true, wenn der <paramref name="o" />-Parameter ein COM-Typ ist, andernfalls false.</returns>
      <param name="o">Das zu überprüfende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf``1(System.String)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Gibt den Feldoffset der nicht verwalteten Form einer festgelegten verwalteten Klasse zurück.</summary>
      <returns>Der Offset in Bytes für den <paramref name="fieldName" />-Parameter in der durch Plattformaufrufe deklarierten angegebenen Klasse. </returns>
      <param name="fieldName">Der Name des Felds im <paramref name="T" />-Typ. </param>
      <typeparam name="T">Ein verwalteter Wert- oder formatierter Verweistyp.Sie müssen das <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" />-Attribut auf die Klasse anwenden.</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf(System.Type,System.String)">
      <summary>Gibt den Feldoffset der nicht verwalteten Form der verwalteten Klasse zurück.</summary>
      <returns>Der Offset in Bytes für den <paramref name="fieldName" />-Parameter in der durch Plattformaufrufe deklarierten angegebenen Klasse.</returns>
      <param name="t">Ein Werttyp oder formatierter Referenztyp, der die verwaltete Klasse angibt.Sie müssen das <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> auf die Klasse anwenden.</param>
      <param name="fieldName">Das Feld im <paramref name="t" />-Parameter.</param>
      <exception cref="T:System.ArgumentException">Die Klasse kann nicht als Struktur exportiert werden, oder das Feld ist nicht öffentlich.Ab .NET Framework, Version 2.0, ist das Feld möglicherweise privat.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="t" />-Parameter ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr)">
      <summary>Kopiert alle Zeichen bis zum ersten NULL-Zeichen aus einer nicht verwalteten ANSI-Zeichenfolge in einen verwalteten <see cref="T:System.String" /> und erweitert die einzelnen ANSI-Zeichen zu UNICODE-Zeichen.</summary>
      <returns>Eine verwaltete Zeichenfolge, die eine Kopie der nicht verwalteten ANSI-Zeichenfolge enthält.Wenn <paramref name="ptr" /> den Wert null aufweist, gibt die Methode eine NULL-Zeichenfolge zurück.</returns>
      <param name="ptr">Die Adresse des ersten Zeichens der nicht verwalteten Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr,System.Int32)">
      <summary>Reserviert einen verwalteten <see cref="T:System.String" />, kopiert eine angegebene Anzahl von Zeichen aus einer nicht verwalteten ANSI-Zeichenfolge hinein und erweitert jedes ANSI-Zeichen zu einem Unicode-Zeichen.</summary>
      <returns>Eine verwaltete Zeichenfolge, die eine Kopie der systemeigenen ANSI-Zeichenfolge enthält, wenn der Wert des <paramref name="ptr" />-Parameters nicht null ist. Andernfalls gibt diese Methode null zurück.</returns>
      <param name="ptr">Die Adresse des ersten Zeichens der nicht verwalteten Zeichenfolge.</param>
      <param name="len">Die zu kopierende Anzahl von Bytes aus der Eingabezeichenfolge.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="len" /> ist kleiner als 0.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringBSTR(System.IntPtr)">
      <summary>Reserviert einen verwalteten <see cref="T:System.String" /> und kopiert eine BSTR-Zeichenfolge aus dem nicht verwalteten Speicher hinein.</summary>
      <returns>Eine verwaltete Zeichenfolge, die eine Kopie der nicht verwalteten Zeichenfolge enthält, wenn der Wert des <paramref name="ptr" />-Parameters nicht null ist. Andernfalls gibt diese Methode null zurück.</returns>
      <param name="ptr">Die Adresse des ersten Zeichens der nicht verwalteten Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr)">
      <summary>Reserviert einen verwalteten <see cref="T:System.String" /> und kopiert alle Zeichen bis zum ersten NULL-Zeichen aus einer nicht verwalteten Unicode-Zeichenfolge hinein.</summary>
      <returns>Eine verwaltete Zeichenfolge, die eine Kopie der nicht verwalteten Zeichenfolge enthält, wenn der Wert des <paramref name="ptr" />-Parameters nicht null ist. Andernfalls gibt diese Methode null zurück.</returns>
      <param name="ptr">Die Adresse des ersten Zeichens der nicht verwalteten Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr,System.Int32)">
      <summary>Reserviert einen verwalteten <see cref="T:System.String" /> und kopiert eine angegebene Anzahl von Zeichen aus einer nicht verwalteten Unicode-Zeichenfolge hinein.</summary>
      <returns>Eine verwaltete Zeichenfolge, die eine Kopie der nicht verwalteten Zeichenfolge enthält, wenn der Wert des <paramref name="ptr" />-Parameters nicht null ist. Andernfalls gibt diese Methode null zurück.</returns>
      <param name="ptr">Die Adresse des ersten Zeichens der nicht verwalteten Zeichenfolge.</param>
      <param name="len">Die Anzahl der zu kopierenden Unicode-Zeichen.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Marshallt Daten aus einem nicht verwalteten Speicherblock zu einem neu belegten, verwalteten Objekt des von einem generischen Typparameter angegebenen Typs. </summary>
      <returns>Ein verwaltetes Objekt mit Daten, auf die der <paramref name="ptr" />-Parameter zeigt. </returns>
      <param name="ptr">Ein Zeiger auf einen nicht verwalteten Speicherblock. </param>
      <typeparam name="T">Der Typ des Objekts, in das die Daten kopiert werden sollen.Dies muss eine formatierte Klasse oder eine Struktur sein .</typeparam>
      <exception cref="T:System.ArgumentException">Das Layout von <paramref name="T" />ist weder sequenziell noch explizit.</exception>
      <exception cref="T:System.MissingMethodException">Die - Klasse, die von <paramref name="T" /> angegeben wird, hat keinen verwendbaren Standardkonstruktor. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Object)">
      <summary>Marshallt Daten aus einem nicht verwalteten Speicherblock zu einem verwalteten Objekt.</summary>
      <param name="ptr">Ein Zeiger auf einen nicht verwalteten Speicherblock.</param>
      <param name="structure">Das Objekt, in das die Daten kopiert werden sollen.Dabei muss es sich um eine Instanz einer formatierten Klasse handeln.</param>
      <exception cref="T:System.ArgumentException">Das Strukturlayout ist weder sequenziell noch explizit.- oder -  Die Struktur ist ein geschachtelter Werttyp.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Type)">
      <summary>Marshallt Daten aus einem nicht verwalteten Speicherblock zu einem neu belegten, verwalteten Objekt des angegebenen Typs.</summary>
      <returns>Ein verwaltetes Objekt mit Daten, auf die der <paramref name="ptr" />-Parameter zeigt.</returns>
      <param name="ptr">Ein Zeiger auf einen nicht verwalteten Speicherblock.</param>
      <param name="structureType">Der Typ des zu erstellenden Objekts.Dieses Objekt muss eine formatierte Klasse oder eine Struktur darstellen.</param>
      <exception cref="T:System.ArgumentException">Das <paramref name="structureType" />-Parameterlayout ist weder sequenziell noch explizit.- oder - Der <paramref name="structureType" />-Parameter ist ein generischer Typ.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structureType" /> ist null.</exception>
      <exception cref="T:System.MissingMethodException">Die - Klasse, die von <paramref name="structureType" /> angegeben wird, hat keinen verwendbaren Standardkonstruktor. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr,``0)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Marshallt Daten aus einem nicht verwalteten Speicherblock zu einem verwalteten Objekt eines festgelegten Typs. </summary>
      <param name="ptr">Ein Zeiger auf einen nicht verwalteten Speicherblock. </param>
      <param name="structure">Das Objekt, in das die Daten kopiert werden sollen. </param>
      <typeparam name="T">Der Typ von <paramref name="structure" />.Muss eine formatierte Klasse sein.</typeparam>
      <exception cref="T:System.ArgumentException">Das Strukturlayout ist weder sequenziell noch explizit. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.QueryInterface(System.IntPtr,System.Guid@,System.IntPtr@)">
      <summary>Fordert einen Zeiger auf eine angegebene Schnittstelle von einem COM-Objekt an.</summary>
      <returns>Ein HRESULT, das den Erfolg oder Fehler des Aufrufs angibt.</returns>
      <param name="pUnk">Die abzufragende Schnittstelle.</param>
      <param name="iid">Der Schnittstellenbezeichner (Interface Identifier, IID) der angeforderten Schnittstelle.</param>
      <param name="ppv">Enthält nach dem Beenden der Methode einen Verweis auf die zurückgegebene Schnittstelle.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr)">
      <summary>Liest ein einzelnes Byte aus dem nicht verwalteten Speicher.</summary>
      <returns>Das aus dem nicht verwalteten Arbeitsspeicher gelesene Byte.</returns>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null. - oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr,System.Int32)">
      <summary>Liest ein einzelnes Byte an einem angegebenen Offset (oder Index) aus dem nicht verwalteten Speicher.</summary>
      <returns>Das aus dem nicht verwalteten Arbeitsspeicher am angegebenen Offset gelesene Byte.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.Object,System.Int32)">
      <summary>Liest ein einzelnes Byte an einem angegebenen Offset (oder Index) aus dem nicht verwalteten Speicher. </summary>
      <returns>Das aus dem nicht verwalteten Arbeitsspeicher am angegebenen Offset gelesene Byte.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Quellobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr)">
      <summary>Liest eine 16-Bit-Ganzzahl mit Vorzeichen aus dem nicht verwalteten Speicher.</summary>
      <returns>Die 16-Bit-Ganzzahl mit Vorzeichen, die aus dem nicht verwalteten Speicher gelesen wurde.</returns>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null.- oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr,System.Int32)">
      <summary>Liest eine 16-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher.</summary>
      <returns>Die 16-Bit-Ganzzahl mit Vorzeichen, die am angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher gelesen wurde.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.Object,System.Int32)">
      <summary>Liest eine 16-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher.</summary>
      <returns>Die 16-Bit-Ganzzahl mit Vorzeichen, die am angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher gelesen wurde.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Quellobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr)">
      <summary>Liest eine 32-Bit-Ganzzahl mit Vorzeichen aus dem nicht verwalteten Speicher.</summary>
      <returns>Die 32-Bit-Ganzzahl mit Vorzeichen, die aus dem nicht verwalteten Speicher gelesen wurde.</returns>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null.- oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr,System.Int32)">
      <summary>Liest eine 32-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher.</summary>
      <returns>Die 32-Bit-Ganzzahl mit Vorzeichen, die aus dem nicht verwalteten Speicher gelesen wurde.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.Object,System.Int32)">
      <summary>Liest eine 32-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher.</summary>
      <returns>Die 32-Bit-Ganzzahl mit Vorzeichen, die am angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher gelesen wurde.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Quellobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr)">
      <summary>Liest eine 64-Bit-Ganzzahl mit Vorzeichen aus dem nicht verwalteten Speicher.</summary>
      <returns>Die 64-Bit-Ganzzahl mit Vorzeichen, die aus dem nicht verwalteten Speicher gelesen wurde.</returns>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null.- oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr,System.Int32)">
      <summary>Liest eine 64-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher.</summary>
      <returns>Die 64-Bit-Ganzzahl mit Vorzeichen, die am angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher gelesen wurde.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.Object,System.Int32)">
      <summary>Liest eine 64-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher.</summary>
      <returns>Die 64-Bit-Ganzzahl mit Vorzeichen, die am angegebenen Offset aus dem nicht verwalteten Arbeitsspeicher gelesen wurde.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Quellobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr)">
      <summary>Liest eine ganze Zahl von prozessoreigener Größe aus dem nicht verwalteten Speicher.</summary>
      <returns>Die aus dem nicht verwaltetem Arbeitsspeicher gelesene Ganzzahl.Auf 32-Bit-Computern wird eine 32-Bit-Ganzzahl zurückgegeben und auf 64-Bit-Computern eine 64-Bit-Ganzzahl.</returns>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null. - oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr,System.Int32)">
      <summary>Liest eine ganze Zahl von prozessoreigener Größe an einem angegebenen Offset aus dem nicht verwalteten Speicher.</summary>
      <returns>Die aus dem nicht verwalteten Arbeitsspeicher am angegebenen Offset gelesene ganze Zahl.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher, an der gelesen werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.Object,System.Int32)">
      <summary>Liest eine ganze Zahl von prozessoreigener Größe aus dem nicht verwalteten Speicher.</summary>
      <returns>Die aus dem nicht verwalteten Arbeitsspeicher am angegebenen Offset gelesene ganze Zahl.</returns>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Quellobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Lesen hinzugefügt wird.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocCoTaskMem(System.IntPtr,System.Int32)">
      <summary>Ändert die Größe eines bereits mit <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" /> belegten Speicherblocks.</summary>
      <returns>Eine ganze Zahl, die die Adresse des neu belegten Speicherblocks darstellt.Dieser Speicher muss mit <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" /> freigegeben werden.</returns>
      <param name="pv">Ein Zeiger auf den mit <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" /> belegten Speicher.</param>
      <param name="cb">Die neue Größe des belegten Blocks.</param>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um die Anforderung zu erfüllen.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocHGlobal(System.IntPtr,System.IntPtr)">
      <summary>Ändert die Größe eines bereits mit <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> belegten Speicherblocks.</summary>
      <returns>Ein Zeiger auf den neu reservierten Arbeitsspeicher.Dieser Speicher muss mit <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> freigegeben werden.</returns>
      <param name="pv">Ein Zeiger auf den mit <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> belegten Speicher.</param>
      <param name="cb">Die neue Größe des belegten Blocks.Dies ist kein Zeiger; es ist die Byteanzahl, die Sie anfordern, umgewandelt zum Typ <see cref="T:System.IntPtr" />.Wenn Sie einen Zeiger übergeben, wird er als Größe behandelt.</param>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um die Anforderung zu erfüllen.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Release(System.IntPtr)">
      <summary>Dekrementiert den Verweiszähler für die angegebene Schnittstelle.</summary>
      <returns>Der neue Wert des Verweiszählers für die Schnittstelle, die durch den <paramref name="pUnk" />-Parameter angegeben ist.</returns>
      <param name="pUnk">Die freizugebende Schnittstelle.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)">
      <summary>Dekrementiert den Verweiszähler des dem angegebenen COM-Objekt zugeordneten angegebenen Runtime Callable Wrapper (RCW) (RCW).</summary>
      <returns>Der neue Wert für den Verweiszähler des RCW, der <paramref name="o" /> zugeordnet ist.Dieser Wert ist in der Regel 0, da der RCW unabhängig von der Anzahl der aufrufenden verwalteten Clients genau einen Verweis auf das umschlossene COM-Objekt beibehält.</returns>
      <param name="o">Das freizugebende COM-Objekt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> ist kein gültiges COM-Objekt.</exception>
      <exception cref="T:System.NullReferenceException">
        <paramref name="o" /> ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Gibt die Größe eines nicht verwalteten Typs in Bytes zurück. </summary>
      <returns>Die Größe, in Bytes, des Typs, der durch den <paramref name="T" /> generischen Typparameter angegeben wird. </returns>
      <typeparam name="T">Der Typ, dessen Größe zurückgegeben werden soll. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Object)">
      <summary>Gibt die nicht verwaltete Größe eines Objekts in Bytes zurück.</summary>
      <returns>Die Größe des angegebenen Objekts in nicht verwaltetem Code.</returns>
      <param name="structure">Das Objekt, dessen Größe zurückgegeben werden soll.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="structure" />-Parameter ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Type)">
      <summary>Gibt die Größe eines nicht verwalteten Typs in Bytes zurück.</summary>
      <returns>Die Größe des angegebenen Typs in nicht verwaltetem Code.</returns>
      <param name="t">Der Typ, dessen Größe zurückgegeben werden soll.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="t" />-Parameter ist ein generischer Typ.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="t" />-Parameter ist null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1(``0)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Gibt die nicht verwaltete Größe eines Objekts eines angegebenen Typs in Bytes zurück. </summary>
      <returns>Die Größe des angegebenen Objekts in nicht verwaltetem Code in Bytes. </returns>
      <param name="structure">Das Objekt, dessen Größe zurückgegeben werden soll. </param>
      <typeparam name="T">Der Typ des <paramref name="structure" />-Parameters. </typeparam>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="structure" />-Parameter ist null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToBSTR(System.String)">
      <summary>Reserviert einen BSTR und kopiert den Inhalt eines verwalteten <see cref="T:System.String" /> hinein.</summary>
      <returns>Ein nicht verwalteter Zeiger auf das BSTR oder 0, wenn <paramref name="s" /> NULL ist.</returns>
      <param name="s">Die zu kopierende verwaltete Zeichenfolge.</param>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Länge für <paramref name="s" /> liegt außerhalb des gültigen Bereichs.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemAnsi(System.String)">
      <summary>Kopiert den Inhalt eines verwalteten <see cref="T:System.String" /> in einen Speicherblock, der von der nicht verwalteten Speicherbelegung für COM-Aufgaben reserviert wurde.</summary>
      <returns>Eine ganze Zahl, die einen Zeiger auf den für die Zeichenfolge belegten Speicherblock darstellt, oder 0, wenn <paramref name="s" /> gleich null ist.</returns>
      <param name="s">Eine zu kopierende verwaltete Zeichenfolge.</param>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="s" />-Parameter übersteigt die vom Betriebssystem zugelassene maximale Länge.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni(System.String)">
      <summary>Kopiert den Inhalt eines verwalteten <see cref="T:System.String" /> in einen Speicherblock, der von der nicht verwalteten Speicherbelegung für COM-Aufgaben reserviert wurde.</summary>
      <returns>Eine ganze Zahl, die einen Zeiger auf den für die Zeichenfolge belegten Speicherblock darstellt, oder 0, wenn s gleich null ist.</returns>
      <param name="s">Eine zu kopierende verwaltete Zeichenfolge.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="s" />-Parameter übersteigt die vom Betriebssystem zugelassene maximale Länge.</exception>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalAnsi(System.String)">
      <summary>Kopiert den Inhalt eines verwalteten <see cref="T:System.String" /> in den nicht verwalteten Speicher und konvertiert ihn beim Kopieren in das ANSI-Format.</summary>
      <returns>Die Adresse im nicht verwalteten Arbeitsspeicher, an die <paramref name="s" /> kopiert wurde, oder 0, wenn <paramref name="s" /> gleich null ist.</returns>
      <param name="s">Eine zu kopierende verwaltete Zeichenfolge.</param>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="s" />-Parameter übersteigt die vom Betriebssystem zugelassene maximale Länge.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalUni(System.String)">
      <summary>Kopiert den Inhalt eines verwalteten <see cref="T:System.String" /> in den nicht verwalteten Speicher.</summary>
      <returns>Die Adresse im nicht verwalteten Arbeitsspeicher, an die <paramref name="s" /> kopiert wurde, oder 0, wenn <paramref name="s" /> gleich null ist.</returns>
      <param name="s">Eine zu kopierende verwaltete Zeichenfolge.</param>
      <exception cref="T:System.OutOfMemoryException">Die Methode konnte nicht genügend Speicher für den systemeigenen Heap belegen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="s" />-Parameter übersteigt die vom Betriebssystem zugelassene maximale Länge.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr(System.Object,System.IntPtr,System.Boolean)">
      <summary>Marshallt Daten aus einem verwalteten Objekt zu einem nicht verwalteten Speicherblock.</summary>
      <param name="structure">Ein verwaltetes Objekt mit den zu marshallenden Daten.Dieses Objekt muss eine Struktur oder Instanz einer formatierten Klasse sein.</param>
      <param name="ptr">Ein Zeiger auf einen nicht verwalteten Speicherblock, der vor dem Aufruf der Methode belegt werden muss.</param>
      <param name="fDeleteOld">true, wenn die <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)" />-Methode für den <paramref name="ptr" />-Parameter aufgerufen werden soll, bevor diese Methode die Daten kopiert.Der Block muss gültige Daten enthalten.Beachten Sie, dass das Übergeben von false, wenn der Speicherblock bereits Daten enthält, zu einem Speicherverlust führen kann.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> ist ein Referenztyp, der keine formatierte Klasse ist. - oder - <paramref name="structure" /> ist ein generischer Typ. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr``1(``0,System.IntPtr,System.Boolean)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Marshallt Daten aus einem verwalteten Objekt eines festgelegten Typs zu einem nicht verwalteten Speicherblock. </summary>
      <param name="structure">Ein verwaltetes Objekt mit den zu marshallenden Daten.Dieses Objekt muss eine Struktur oder Instanz einer formatierten Klasse sein.</param>
      <param name="ptr">Ein Zeiger auf einen nicht verwalteten Speicherblock, der vor dem Aufruf der Methode belegt werden muss. </param>
      <param name="fDeleteOld">true, wenn die <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)" />-Methode für den <paramref name="ptr" />-Parameter aufgerufen werden soll, bevor diese Methode die Daten kopiert.Der Block muss gültige Daten enthalten.Beachten Sie, dass das Übergeben von false, wenn der Speicherblock bereits Daten enthält, zu einem Speicherverlust führen kann.</param>
      <typeparam name="T">Der Typ des verwalteten Objekts. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> ist ein Referenztyp, der keine formatierte Klasse ist. </exception>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemDefaultCharSize">
      <summary>Stellt die Standardzeichengröße für das System dar. Der Standardwert ist 2 für Unicode-Systeme und 1 für ANSI-Systeme.Dieses Feld ist schreibgeschützt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemMaxDBCSCharSize">
      <summary>Stellt die maximale Größe eines DBCS (Double-Byte Character Set) für das aktuelle Betriebssystem in Bytes dar.Dieses Feld ist schreibgeschützt.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32)">
      <summary>Löst eine Ausnahme mit einem bestimmten HRESULT-Fehlerwert aus.</summary>
      <param name="errorCode">Das der gewünschten Ausnahme entsprechende HRESULT.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Löst eine Ausnahme mit einem bestimmten Fehler-HRESULT auf Grundlage der angegebenen IErrorInfo-Schnittstelle aus.</summary>
      <param name="errorCode">Das der gewünschten Ausnahme entsprechende HRESULT.</param>
      <param name="errorInfo">Ein Zeiger auf die IErrorInfo-Schnittstelle, die weitere Informationen über den Fehler bereitstellt.Sie können IntPtr(0) angeben, um die aktuelle IErrorInfo-Schnittstelle zu verwenden, oder Sie können IntPtr(-1) angeben, um die aktuelle IErrorInfo-Schnittstelle zu ignorieren und die Ausnahme direkt aus dem Fehlercode zu erstellen.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement(System.Array,System.Int32)">
      <summary>Ruft die Adresse des Elements am angegebenen Index des angegebenen Arrays ab.</summary>
      <returns>Die Adresse des <paramref name="index" /> im <paramref name="arr" />.</returns>
      <param name="arr">Das Array, das das gewünschte Element enthält.</param>
      <param name="index">Der Index im <paramref name="arr" />-Parameter des gewünschten Elements.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement``1(``0[],System.Int32)">
      <summary>[Wird nur in .NET Framework 4.5.1 und neueren Versionen unterstützt] Ruft die Adresse des Elements am angegebenen Index in einem Arrays des angegebenen Typs ab. </summary>
      <returns>Die Adresse des <paramref name="index" /> im <paramref name="arr" />. </returns>
      <param name="arr">Das Array, das das gewünschte Element enthält. </param>
      <param name="index">Der Index des gewünschten Elements im <paramref name="arr" />-Array. </param>
      <typeparam name="T">Der Typ des Arrays. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Byte)">
      <summary>Schreibt einen einzelnen Bytewert in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null.- oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Int32,System.Byte)">
      <summary>Schreibt einen einzelnen Bytewert an einem angegebenen Offset in den nicht verwalteten Arbeitsspeicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.Object,System.Int32,System.Byte)">
      <summary>Schreibt einen einzelnen Bytewert an einem angegebenen Offset in den nicht verwalteten Arbeitsspeicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Zielobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Char)">
      <summary>Schreibt ein Zeichen als 16-Bit-Ganzzahl in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null.- oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int16)">
      <summary>Schreibt eine 16-Bit-Ganzzahl in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null.- oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Char)">
      <summary>Schreibt den Wert einer 16-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Basisadresse im systemeigenen Heap, in die geschrieben werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Int16)">
      <summary>Schreibt den Wert einer 16-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Char)">
      <summary>Schreibt den Wert einer 16-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Zielobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Int16)">
      <summary>Schreibt den Wert einer 16-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Zielobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird. </param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32)">
      <summary>Schreibt eine 32-Bit-Ganzzahl mit Vorzeichen in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null. - oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32,System.Int32)">
      <summary>Schreibt den Wert einer 32-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.Object,System.Int32,System.Int32)">
      <summary>Schreibt den Wert einer 32-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Zielobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int32,System.Int64)">
      <summary>Schreibt den Wert einer 64-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die zu schreibende Basisadresse im nicht verwalteten Speicher.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int64)">
      <summary>Schreibt eine 64-Bit-Ganzzahl mit Vorzeichen in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null.- oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.Object,System.Int32,System.Int64)">
      <summary>Schreibt den Wert einer 64-Bit-Ganzzahl mit Vorzeichen an einem angegebenen Offset in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Zielobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.Int32,System.IntPtr)">
      <summary>Schreibt einen Ganzzahlwert von prozessoreigener Größe am angegebenen Offset in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.IntPtr)">
      <summary>Schreibt einen Ganzzahlwert von prozessoreigener Größe in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Adresse im nicht verwalteten Speicher, in die geschrieben werden soll.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> ist kein erkanntes Format.- oder - <paramref name="ptr" /> ist null.- oder - "<paramref name="ptr" />" ist ungültig.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.Object,System.Int32,System.IntPtr)">
      <summary>Schreibt einen Ganzzahlwert von prozessoreigener Größe in den nicht verwalteten Speicher.</summary>
      <param name="ptr">Die Basisadresse im nicht verwalteten Speicher des Zielobjekts.</param>
      <param name="ofs">Ein zusätzlicher Byteoffset, der dem <paramref name="ptr" />-Parameter vor dem Schreiben hinzugefügt wird.</param>
      <param name="val">Der zu schreibende Wert.</param>
      <exception cref="T:System.AccessViolationException">Die Basisadresse (<paramref name="ptr" />) und das Offsetbyte (<paramref name="ofs" />) erzeugen NULL oder eine ungültige Adresse.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> ist ein <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Objekt.Diese Methode akzeptiert keine <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />-Parameter.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(System.IntPtr)">
      <summary>Gibt einen BSTR-Zeiger frei, der mit der <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToBSTR(System.Security.SecureString)" />-Methode reserviert wurde.</summary>
      <param name="s">Die Adresse des freizugebenden BSTR.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemAnsi(System.IntPtr)">
      <summary>Gibt einen nicht verwalteten Zeichenfolgenzeiger frei, der mit der <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemAnsi(System.Security.SecureString)" />-Methode reserviert wurde.</summary>
      <param name="s">Die Adresse der nicht verwalteten Zeichenfolge, die freigegeben werden soll.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemUnicode(System.IntPtr)">
      <summary>Gibt einen nicht verwalteten Zeichenfolgenzeiger frei, der mit der <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemUnicode(System.Security.SecureString)" />-Methode reserviert wurde.</summary>
      <param name="s">Die Adresse der nicht verwalteten Zeichenfolge, die freigegeben werden soll.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocAnsi(System.IntPtr)">
      <summary>Gibt einen nicht verwalteten Zeichenfolgenzeiger frei, der mit der <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocAnsi(System.Security.SecureString)" />-Methode reserviert wurde.</summary>
      <param name="s">Die Adresse der nicht verwalteten Zeichenfolge, die freigegeben werden soll.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocUnicode(System.IntPtr)">
      <summary>Gibt einen nicht verwalteten Zeichenfolgenzeiger frei, der mit der <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocUnicode(System.Security.SecureString)" />-Methode reserviert wurde.</summary>
      <param name="s">Die Adresse der nicht verwalteten Zeichenfolge, die freigegeben werden soll.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalAsAttribute">
      <summary>Gibt an, wie die Daten zwischen verwaltetem und nicht verwaltetem Code gemarshallt werden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Int16)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" />-Klasse mit dem angegebenen <see cref="T:System.Runtime.InteropServices.UnmanagedType" />-Wert.</summary>
      <param name="unmanagedType">Der Wert, als der die Daten gemarshallt werden sollen. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Runtime.InteropServices.UnmanagedType)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" />-Klasse mit dem angegebenen <see cref="T:System.Runtime.InteropServices.UnmanagedType" />-Enumerationsmember.</summary>
      <param name="unmanagedType">Der Wert, als der die Daten gemarshallt werden sollen. </param>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType">
      <summary>Gibt den Elementtyp des nicht verwalteten <see cref="F:System.Runtime.InteropServices.UnmanagedType.LPArray" /> oder <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValArray" /> an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.IidParameterIndex">
      <summary>Gibt den Parameterindex des nicht verwalteten iid_is-Attributs an, das von COM verwendet wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalCookie">
      <summary>Stellt zusätzliche Informationen für einen benutzerdefinierten Marshaller zur Verfügung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType">
      <summary>Gibt den voll gekennzeichneten Namen eines benutzerdefinierten Marshallers an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalTypeRef">
      <summary>Implementiert <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType" /> als Typ.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType">
      <summary>Gibt den Elementtyp von <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArrayUserDefinedSubType">
      <summary>Gibt den benutzerdefinierten Elementtyp von <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst">
      <summary>Gibt die Anzahl der Elemente im Array fester Länger oder die Anzahl der Zeichen (nicht der Bytes) in einer zu importierenden Zeichenfolge an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex">
      <summary>Gibt den nullbasierten Parameter an, der die Anzahl von Arrayelementen enthält, vergleichbar mit size_is in COM.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.MarshalAsAttribute.Value">
      <summary>Ruft den <see cref="T:System.Runtime.InteropServices.UnmanagedType" />-Wert ab, als der die Daten gemarshallt werden sollen.</summary>
      <returns>Der <see cref="T:System.Runtime.InteropServices.UnmanagedType" />-Wert, als der die Daten gemarshallt werden sollen.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalDirectiveException">
      <summary>Die vom Marshaller ausgelöste Ausnahme, wenn dieser ein nicht unterstütztes <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> entdeckt.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor">
      <summary>Initialisiert eine neue Instanz der MarshalDirectiveException-Klasse mit Standardeigenschaften.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der MarshalDirectiveException-Klasse mit einer angegebenen Fehlermeldung.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache für die Ausnahme angegeben wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.MarshalDirectiveException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.OptionalAttribute">
      <summary>Gibt an, dass ein Parameter optional ist.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.OptionalAttribute.#ctor">
      <summary>Initialisiert eine neue Instanz der OptionalAttribute-Klasse mit Standardwerten.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.PreserveSigAttribute">
      <summary>Gibt an, dass die während COM-Interop-Aufrufen stattfindende HRESULT- oder retval-Signaturtransformation unterdrückt werden soll.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.PreserveSigAttribute.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.PreserveSigAttribute" />-Klasse.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayRankMismatchException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn der Rang eines eingehenden SAFEARRAY nicht mit dem in der verwalteten Signatur angegebenen Rang übereinstimmt.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor">
      <summary>Initialisiert eine neue Instanz der SafeArrayTypeMismatchException-Klasse mit Standardwerten.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der SafeArrayRankMismatchException-Klasse mit der angegebenen Meldung.</summary>
      <param name="message">Die Meldung, in der die Ursache für die Ausnahme angegeben wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.SafeArrayRankMismatchException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn der Typ des eingehenden SAFEARRAY nicht mit dem in der verwalteten Signatur angegebenen Typ übereinstimmt.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor">
      <summary>Initialisiert eine neue Instanz der SafeArrayTypeMismatchException-Klasse mit Standardwerten.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der SafeArrayTypeMismatchException-Klasse mit der angegebenen Meldung.</summary>
      <param name="message">Die Meldung, in der die Ursache für die Ausnahme angegeben wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeBuffer">
      <summary>Stellt einen gesteuerten Arbeitsspeicherpuffer bereit, der zum Lesen und Schreiben verwendet werden kann.Durch den Versuch, außerhalb des gesteuerten Puffers auf Speicher zuzugreifen (Unterläufe und Überläufe), werden Ausnahmen ausgelöst.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.#ctor(System.Boolean)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Runtime.InteropServices.SafeBuffer" />-Klasse und gibt an, ob das Pufferhandle zuverlässig freigegeben werden soll. </summary>
      <param name="ownsHandle">true, um das Handle während der Finalisierungsphase zuverlässig freizugeben, und false, um eine zuverlässige Freigabe zu verhindern (nicht empfohlen).</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)">
      <summary>Ruft einen Zeiger aus einem <see cref="T:System.Runtime.InteropServices.SafeBuffer" />-Objekt für einen Speicherblock ab.</summary>
      <param name="pointer">Ein als Verweis übergebener Bytezeiger zum Abrufen des Zeigers aus dem <see cref="T:System.Runtime.InteropServices.SafeBuffer" />-Objekt.Sie müssen diesen Zeiger auf null festlegen, bevor diese Methode aufgerufen werden kann.</param>
      <exception cref="T:System.InvalidOperationException">Die <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />-Methode wurde nicht aufgerufen. </exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.ByteLength">
      <summary>Ruft die Größe des Puffers in Bytes ab.</summary>
      <returns>Die Anzahl der Bytes im Speicherpuffer.</returns>
      <exception cref="T:System.InvalidOperationException">Die <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />-Methode wurde nicht aufgerufen.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize``1(System.UInt32)">
      <summary>Definiert die Zuordnungsgröße des Arbeitsspeicherbereichs, indem die Anzahl der Werttypen angegeben wird.Diese Methode muss aufgerufen werden, bevor die <see cref="T:System.Runtime.InteropServices.SafeBuffer" />-Instanz verwendet wird.</summary>
      <param name="numElements">Die Anzahl von Elementen des Werttyps, für die Speicher belegt werden soll.</param>
      <typeparam name="T">Der Werttyp, für den Speicher belegt werden soll.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> ist kleiner als null.- oder -<paramref name="numElements" /> multipliziert mit der Größe der einzelnen Elemente ist größer als der verfügbare Adressbereich.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt32,System.UInt32)">
      <summary>Gibt die Zuordnungsgröße des Arbeitsspeicherpuffers mit der angegebenen Anzahl von Elementen und der Elementgröße an.Diese Methode muss aufgerufen werden, bevor die <see cref="T:System.Runtime.InteropServices.SafeBuffer" />-Instanz verwendet wird.</summary>
      <param name="numElements">Die Anzahl der Elemente im Puffer.</param>
      <param name="sizeOfEachElement">Die Größe der einzelnen Elemente im Puffer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> ist kleiner als null. - oder -<paramref name="sizeOfEachElement" /> ist kleiner als null.- oder -<paramref name="numElements" /> multipliziert mit <paramref name="sizeOfEachElement" /> ist größer als der verfügbare Adressbereich.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt64)">
      <summary>Definiert die Zuordnungsgröße des Arbeitsspeicherbereichs in Bytes.Diese Methode muss aufgerufen werden, bevor die <see cref="T:System.Runtime.InteropServices.SafeBuffer" />-Instanz verwendet wird.</summary>
      <param name="numBytes">Die Anzahl der Bytes im Puffer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numBytes" /> ist kleiner als null.- oder -<paramref name="numBytes" /> ist größer als der verfügbare Adressbereich.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.IsInvalid"></member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Read``1(System.UInt64)">
      <summary>Liest einen Werttyp am angegebenen Offset aus dem Arbeitsspeicher.</summary>
      <returns>Der Werttyp, der aus dem Arbeitsspeicher gelesen wurde.</returns>
      <param name="byteOffset">Die Position, an der der Werttyp gelesen werden soll.Möglicherweise müssen Ausrichtungsprobleme berücksichtigt werden.</param>
      <typeparam name="T">Der zu lesende Werttyp.</typeparam>
      <exception cref="T:System.InvalidOperationException">Die <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />-Methode wurde nicht aufgerufen.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReadArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Liest beginnend am Offset die angegebene Anzahl von Werttypen aus dem Arbeitsspeicher und schreibt sie beginnend am Index in ein Array. </summary>
      <param name="byteOffset">Die Position, an der der Lesevorgang begonnen werden soll.</param>
      <param name="array">Das Ausgabearray, in das geschrieben werden soll.</param>
      <param name="index">Die Position im Ausgabearray, an der der Schreibvorgang begonnen werden soll.</param>
      <param name="count">Die Anzahl der Werttypen, die aus dem Eingabearray gelesen und in das Ausgabearray geschrieben werden sollen.</param>
      <typeparam name="T">Der zu lesende Werttyp.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder -<paramref name="count" /> ist kleiner als 0 (null).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Die Länge des Array minus Index ist kleiner als <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">Die <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />-Methode wurde nicht aufgerufen.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReleasePointer">
      <summary>Gibt einen Zeiger frei, der von der <see cref="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)" />-Methode abgerufen wurde.</summary>
      <exception cref="T:System.InvalidOperationException">Die <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />-Methode wurde nicht aufgerufen.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Write``1(System.UInt64,``0)">
      <summary>Schreibt einen Werttyp an der angegebenen Position in den Arbeitsspeicher.</summary>
      <param name="byteOffset">Die Position, an der der Schreibvorgang begonnen werden soll.Möglicherweise müssen Ausrichtungsprobleme berücksichtigt werden.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <typeparam name="T">Der zu schreibende Werttyp.</typeparam>
      <exception cref="T:System.InvalidOperationException">Die <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />-Methode wurde nicht aufgerufen.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.WriteArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Schreibt die angegebene Anzahl von Werttypen an eine Speicheradresse, indem beginnend an der angegebenen Position im Eingabearray Bytes gelesen werden.</summary>
      <param name="byteOffset">Die Position im Arbeitsspeicher, in die geschrieben werden soll.</param>
      <param name="array">Das Eingabearray.</param>
      <param name="index">Der Offset im Array, bei dem der Lesevorgang begonnen werden soll.</param>
      <param name="count">Die Anzahl der Werttypen, die geschrieben werden sollen.</param>
      <typeparam name="T">Der zu schreibende Werttyp.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist kleiner als 0 (null).</exception>
      <exception cref="T:System.ArgumentException">Die Länge des Eingabearrays minus <paramref name="index" /> ist kleiner als <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">Die <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" />-Methode wurde nicht aufgerufen.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SEHException">
      <summary>Stellt SEH-Fehler (Structured Exception Handler, Strukturierter Ausnahmehandler) dar. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.SEHException" />-Klasse. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.SEHException" />-Klasse mit einer angegebenen Meldung.</summary>
      <param name="message">Die Meldung, in der die Ursache für die Ausnahme angegeben wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.SEHException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.CanResume">
      <summary>Gibt an, ob der Zustand vor der Ausnahme wiederhergestellt werden kann und ob der Code ab der Stelle, an der die Ausnahme ausgelöst wurde, weiter ausgeführt werden kann.</summary>
      <returns>Immer false, da Ausnahmen mit der Möglichkeit zur Wiederaufnahme nicht implementiert sind.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.TypeIdentifierAttribute">
      <summary>Unterstützt die Typäquivalenz.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" />-Klasse. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" />-Klasse mit dem angegebenen Bereich und Bezeichner. </summary>
      <param name="scope">Die erste Typäquivalenzzeichenfolge.</param>
      <param name="identifier">Die zweite Typäquivalenzzeichenfolge.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Identifier">
      <summary>Ruft den Wert des <paramref name="identifier" />-Parameters ab, der an den <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />-Konstruktor übergeben wurde.</summary>
      <returns>Der Wert des <paramref name="identifier" />-Parameters für den Konstruktor.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Scope">
      <summary>Ruft den Wert des <paramref name="scope" />-Parameters ab, der an den <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />-Konstruktor übergeben wurde.</summary>
      <returns>Der Wert des <paramref name="scope" />-Parameters für den Konstruktor.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnknownWrapper">
      <summary>Erstellt einen Wrapper für Objekte, die der Marshaller als VT_UNKNOWN marshallen soll.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnknownWrapper.#ctor(System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.UnknownWrapper" />-Klasse mit dem Objekt, für das ein Wrapper erstellt werden soll.</summary>
      <param name="obj">Das Objekt, für das ein Wrapper erstellt werden soll. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.UnknownWrapper.WrappedObject">
      <summary>Ruft das in diesem Wrapper enthaltene Objekt ab.</summary>
      <returns>Das Objekt, das umschlossen wird.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute">
      <summary>Steuert das Marshallingverhalten der Signatur eines Delegaten, die als nicht verwalteter Funktionszeiger an nicht verwalteten Code bzw. aus nicht verwaltetem Code übergeben wird.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute" />-Klasse mit der angegebenen Aufrufkonvention. </summary>
      <param name="callingConvention">Die angegebene Aufrufkonvention.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.BestFitMapping">
      <summary>Aktiviert bzw. deaktiviert das Verhalten der optimalen Zuordnung bei der Konvertierung von Unicode-Zeichen in ANSI-Zeichen.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CallingConvention">
      <summary>Ruft den Wert der Aufrufkonvention ab.</summary>
      <returns>Der Wert der vom <see cref="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)" />-Konstruktor angegebenen Aufrufkonvention.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CharSet">
      <summary>Gibt an, wie Zeichenfolgenparameter an die Methode gemarshallt werden, und steuert die Namenszerlegung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.SetLastError">
      <summary>Gibt an, ob der Aufgerufene vor dem Zurückgeben aus der attributierten Methode die Win32-API SetLastError aufruft.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.ThrowOnUnmappableChar">
      <summary>Aktiviert bzw. deaktiviert das Auslösen einer Ausnahme bei einem nicht zuzuordnenden Unicode-Zeichen, das in das ANSI-Zeichen "?" konvertiert wird.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedType">
      <summary>Gibt an, wie Parameter oder Felder in nicht verwaltetem Code gemarshallt werden. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AnsiBStr">
      <summary>Eine Zeichenfolge aus ANSI-Zeichen, die ein Einzelbyte mit Längenpräfix ist.Sie können diesen Member für den <see cref="T:System.String" />-Datentyp verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AsAny">
      <summary>Ein dynamischer Typ, der zur Laufzeit den Typ eines Objekts bestimmt und das Objekt als diesen Typ marshallt.Dieser Member ist nur für Plattformaufrufmethoden gültig.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Bool">
      <summary>Ein boolescher Wert mit 4 Bytes (true != 0, false = 0).Dies ist der Win32-BOOL-Typ.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.BStr">
      <summary>Eine Zeichenfolge aus Unicode-Zeichen, die ein Doppelbyte mit Längenpräfix ist.Sie können diesen Member, der die Standardzeichenfolge in COM darstellt, für den <see cref="T:System.String" />-Datentyp verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValArray">
      <summary>Wenn die <see cref="P:System.Runtime.InteropServices.MarshalAsAttribute.Value" />-Eigenschaft auf ByValArray festgelegt wird, muss das <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" />-Feld so festgelegt werden, dass die Anzahl der Elemente im Array angegeben wird.Das <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType" />-Feld kann optional den <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> der Arrayelemente enthalten, wenn zwischen verschiedenen Zeichenfolgetypen unterschieden werden muss.Sie können diesen <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> nur für ein Array verwenden, dessen Elemente in einer Struktur in Form von Feldern angezeigt werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr">
      <summary>Wird für Inlinearrays mit Zeichen fester Länge verwendet, die in einer Struktur dargestellt werden.Der in <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr" /> verwendete Zeichentyp wird durch das <see cref="T:System.Runtime.InteropServices.CharSet" />-Argument des <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" />-Attributs bestimmt, das auf die enthaltende Struktur angewendet wird.Verwenden Sie immer das <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" />-Feld, um die Größe des Arrays anzugeben.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Currency">
      <summary>Ein Währungstyp.Wird für <see cref="T:System.Decimal" /> verwendet, um den Dezimalwert als COM-Währungstyp und nicht als Decimal zu marshallen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Error">
      <summary>Ein systemeigener Typ, der mit einem <see cref="F:System.Runtime.InteropServices.UnmanagedType.I4" /> oder einem <see cref="F:System.Runtime.InteropServices.UnmanagedType.U4" /> verknüpft ist und bewirkt, dass der Parameter als ein HRESULT in die Bibliothek mit den Exporttypen exportiert wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.FunctionPtr">
      <summary>Eine Ganzzahl, die als C-Funktionszeiger verwendet werden kann.Sie können diesen Member für einen <see cref="T:System.Delegate" />-Datentyp oder einen Typ verwenden, der von einem <see cref="T:System.Delegate" /> erbt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.HString">
      <summary>Eine Windows-Runtime-Zeichenfolge.Sie können diesen Member für den <see cref="T:System.String" />-Datentyp verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I1">
      <summary>Eine 1-Byte-Ganzzahl mit Vorzeichen.Sie können mit diesem Member einen booleschen Wert in einen 1-Byte-bool im C-Format (true = 1, false = 0) transformieren.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I2">
      <summary>Eine 2-Byte-Ganzzahl mit Vorzeichen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I4">
      <summary>Eine 4-Byte-Ganzzahl mit Vorzeichen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I8">
      <summary>Eine 8-Byte-Ganzzahl mit Vorzeichen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IDispatch">
      <summary>Ein IDispatch-Zeiger in COM (Object in Microsoft Visual Basic 6.0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IInspectable">
      <summary>Ein Windows-Runtime-Schnittstellenzeiger.Sie können diesen Member für den <see cref="T:System.Object" />-Datentyp verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Interface">
      <summary>Ein COM-Schnittstellenzeiger.Die <see cref="T:System.Guid" /> der Schnittstelle wird aus den Metadaten der Klasse ermittelt.Mit diesem Member geben Sie den genauen Schnittstellentyp oder den Standardschnittstellentyp an, wenn Sie ihn auf eine Klasse anwenden.Dieser Member bewirkt das gleiche Verhalten wie <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" />, wenn Sie ihn auf den <see cref="T:System.Object" />-Datentyp anwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IUnknown">
      <summary>Ein COM-IUnknown-Zeiger.Sie können diesen Member für den <see cref="T:System.Object" />-Datentyp verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPArray">
      <summary>Ein Zeiger auf das erste Element eines C-Arrays.Beim Marshallen von verwaltetem zu nicht verwalteten Code wird die Länge des Arrays anhand der Länge des verwalteten Arrays bestimmt.Beim Marshallen von nicht verwaltetem in verwalteten Code wird die Länge des Arrays vom <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" />-Feld und vom <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex" />-Feld bestimmt, auf die optional der nicht verwaltete Typ der Elemente im Array folgt, wenn eine Unterscheidung zwischen Zeichenfolgentypen erforderlich ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStr">
      <summary>Eine aus ANSI-Zeichen bestehende, mit NULL beendete Einzelbyte-Zeichenfolge.Sie können diesen Member für den <see cref="T:System.String" />-Datentyp und den <see cref="T:System.Text.StringBuilder" />-Datentyp verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStruct">
      <summary>Ein Zeiger auf eine C-Struktur, mit dem verwaltete formatierte Klassen gemarshallt werden.Dieser Member ist nur für Plattformaufrufmethoden gültig.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPTStr">
      <summary>Eine plattformabhängige Zeichenfolge, d.&amp;nbsp;h. ANSI unter Windows&amp;nbsp;98 und Unicode unter Windows&amp;nbsp;NT und Windows&amp;nbsp;XP.Dieser Wert wird nur für Plattformaufrufe und nicht für COM-Interop unterstützt, da das Exportieren einer Zeichenfolge vom Typ LPTStr nicht unterstützt wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPWStr">
      <summary>Eine aus Unicode-Zeichen bestehende, mit NULL beendete 2-Byte-Zeichenfolge.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R4">
      <summary>Eine 4-Byte-Gleitkommazahl.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R8">
      <summary>Eine 8-Byte-Gleitkommazahl.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SafeArray">
      <summary>Ein SafeArray ist ein selbstbeschreibendes Array mit dem Typ, dem Rang und den Grenzen der verknüpften Arraydaten.Sie können diesen Member mit dem <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType" />-Feld verwenden, um den Standardelementtyp zu überschreiben.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Struct">
      <summary>Ein VARIANT zum Marshallen verwalteter formatierter Klassen und Werttypen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysInt">
      <summary>Eine plattformabhängige Ganzzahl mit Vorzeichen.: 4 Byte für 32-Bit-Windows, 8 Bytes auf 64-Bit-Windows.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysUInt">
      <summary>Eine plattformabhängige Ganzzahl ohne Vorzeichen.: 4 Byte für 32-Bit-Windows, 8 Bytes auf 64-Bit-Windows.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.TBStr">
      <summary>Eine plattformabhängige char-Zeichenfolge mit Längenpräfix: ANSI unter Windows 98, Unicode in Windows NT.Dieser BSTR-ähnliche Member wird nur selten verwendet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U1">
      <summary>Eine 1-Byte-Ganzzahl ohne Vorzeichen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U2">
      <summary>Eine 2-Byte-Ganzzahl ohne Vorzeichen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U4">
      <summary>Eine 4-Byte-Ganzzahl ohne Vorzeichen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U8">
      <summary>Eine 8-Byte-Ganzzahl ohne Vorzeichen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VariantBool">
      <summary>Ein durch OLE definierter VARIANT_BOOL-Typ mit 2 Bytes (true = -1, false = 0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VBByRefStr">
      <summary>Ein Wert, der es Visual Basic ermöglicht, eine Zeichenfolge in nicht verwaltetem Code zu ändern und die Ergebnisse in verwaltetem Code wiederzugeben.Dieser Wert wird nur für Plattformaufrufe unterstützt.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VarEnum">
      <summary>Gibt an, wie die Arrayelemente gemarshallt werden sollen, wenn ein Array als <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> aus verwaltetem in nicht verwalteten Code gemarshallt wird. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ARRAY">
      <summary>Gibt einen SAFEARRAY-Zeiger an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB">
      <summary>Gibt Bytes mit Längenpräfix an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB_OBJECT">
      <summary>Gibt an, dass ein BLOB ein Objekt enthält.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BOOL">
      <summary>Gibt einen booleschen Wert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BSTR">
      <summary>Gibt eine BSTR-Zeichenfolge an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BYREF">
      <summary>Gibt an, dass ein Wert ein Verweis ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CARRAY">
      <summary>Gibt ein C-Array an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CF">
      <summary>Gibt das Format der Zwischenablage an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CLSID">
      <summary>Gibt eine Klassen-ID an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CY">
      <summary>Gibt einen Währungswert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DATE">
      <summary>Gibt einen DATE-Wert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DECIMAL">
      <summary>Gibt einen decimal-Wert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH">
      <summary>Gibt einen IDispatch-Zeiger an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_EMPTY">
      <summary>Gibt an, dass ein Wert nicht angegeben wurde.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ERROR">
      <summary>Gibt einen SCODE an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_FILETIME">
      <summary>Gibt einen FILETIME-Wert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_HRESULT">
      <summary>Gibt ein HRESULT an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I1">
      <summary>Gibt einen char-Wert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I2">
      <summary>Gibt eine short-Ganzzahl an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I4">
      <summary>Gibt eine long-Ganzzahl an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I8">
      <summary>Gibt eine 64-Bit-Ganzzahl mit Vorzeichen an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_INT">
      <summary>Gibt einen Ganzzahlwert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPSTR">
      <summary>Gibt eine auf NULL endende Zeichenfolge an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPWSTR">
      <summary>Gibt eine auf null endende breite Zeichenfolge an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_NULL">
      <summary>Gibt einen NULL-Wert an, der mit einem NULL-Wert in SQL vergleichbar ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_PTR">
      <summary>Gibt einen Zeigertyp an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R4">
      <summary>Gibt einen float-Wert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R8">
      <summary>Gibt einen double-Wert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_RECORD">
      <summary>Gibt einen benutzerdefinierten Typ an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_SAFEARRAY">
      <summary>Gibt ein SAFEARRAY an.In einem VARIANT nicht gültig.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORAGE">
      <summary>Gibt an, dass der Name eines Speichers folgt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORED_OBJECT">
      <summary>Gibt an, dass ein Speicher ein Objekt enthält.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAM">
      <summary>Gibt an, dass der Name eines Streams folgt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAMED_OBJECT">
      <summary>Gibt an, dass ein Stream ein Objekt enthält.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI1">
      <summary>Gibt ein byte an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI2">
      <summary>Gibt ein unsignedshort an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI4">
      <summary>Gibt ein unsignedlong an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI8">
      <summary>Gibt eine 64-Bit-Ganzzahl ohne Vorzeichen an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UINT">
      <summary>Gibt einen unsigned-Ganzzahlwert an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UNKNOWN">
      <summary>Gibt einen IUnknown-Zeiger an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_USERDEFINED">
      <summary>Gibt einen benutzerdefinierten Typ an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VARIANT">
      <summary>Gibt einen VARIANT-far-Zeiger an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VECTOR">
      <summary>Gibt ein einfaches, gezähltes Array an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VOID">
      <summary>Gibt void im Stil von C an.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VariantWrapper">
      <summary>Marshallt Daten vom Typ VT_VARIANT | VT_BYREF von verwaltetem an nicht verwalteten Code.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.VariantWrapper.#ctor(System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Runtime.InteropServices.VariantWrapper" />-Klasse für den angegebenen <see cref="T:System.Object" />-Parameter.</summary>
      <param name="obj">Das zu marshallende Objekt. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.VariantWrapper.WrappedObject">
      <summary>Ruft das von dem <see cref="T:System.Runtime.InteropServices.VariantWrapper" />-Objekt umschlossene Objekt ab.</summary>
      <returns>Das von dem <see cref="T:System.Runtime.InteropServices.VariantWrapper" />-Objekt umschlossene Objekt.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ADVF">
      <summary>Gibt das angeforderte Verhalten beim Einrichten einer Advise-Senke oder einer Cachingverbindung mit einem Objekt an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_DATAONSTOP">
      <summary>Stellt bei Daten-Advise-Verbindungen den Zugriff auf die Daten sicher. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_NODATA">
      <summary>Bei Daten-Advise-Verbindungen (<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.DAdvise(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.ADVF,System.Runtime.InteropServices.ComTypes.IAdviseSink,System.Int32@)" /> oder <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />) fordert dieses Flag das Datenobjekt auf, keine Daten zu senden, wenn <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> aufgerufen wird. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_ONLYONCE">
      <summary>Fordert an, dass das Objekt nur eine Änderungsbenachrichtigung oder Zwischenspeicheraktualisierung durchführt, bevor die Verbindung beendet wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_PRIMEFIRST">
      <summary>Fordert an, dass das Objekt nicht auf die Daten- oder Ansichtsänderung wartet, bevor es <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> zum ersten Mal aufruft (bei Daten- oder Ansichts-Advise-Verbindungen) oder den Zwischenspeicher aktualisiert (bei Cachingverbindungen).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN">
      <summary>Dieser Wert wird von DLL-Objektanwendungen und -Objekthandlern verwendet, die ihre Objekte selbst zeichnen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_NOHANDLER">
      <summary>Synonym für <see cref="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN" />, das öfter verwendet wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_ONSAVE">
      <summary>Bei Cachingverbindungen aktualisiert dieses Flag die zwischengespeicherte Darstellung nur dann, wenn das Objekt mit dem Cache gespeichert wird.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BIND_OPTS">
      <summary>Speichert die Parameter, die bei einem Monikerbindungsvorgang verwendet werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.cbStruct">
      <summary>Gibt die Größe der BIND_OPTS-Struktur in Bytes an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.dwTickCountDeadline">
      <summary>Gibt die Zeitspanne an (Systemzeit in Millisekunden, wie von der GetTickCount-Funktion zurückgegeben), die der Aufrufer für die Durchführung des Bindungsvorgangs angegeben hat.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfFlags">
      <summary>Steuert Aspekte von Vorgängen der Monikerbindung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfMode">
      <summary>Stellt die Flags dar, die beim Öffnen der Datei verwendet werden sollen, die das vom Moniker angegebene Objekt enthält.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BINDPTR">
      <summary>Enthält einen Zeiger auf eine gebundene <see cref="T:System.Runtime.InteropServices.FUNCDESC" />-Struktur, eine gebundene <see cref="T:System.Runtime.InteropServices.VARDESC" />-Struktur oder eine gebundene ITypeComp-Schnittstelle.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpfuncdesc">
      <summary>Stellt einen Zeiger auf eine <see cref="T:System.Runtime.InteropServices.FUNCDESC" />-Struktur dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lptcomp">
      <summary>Stellt einen Zeiger auf eine <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeComp" />-Schnittstelle dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpvardesc">
      <summary>Stellt einen Zeiger auf eine <see cref="T:System.Runtime.InteropServices.VARDESC" />-Struktur dar.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CALLCONV">
      <summary>Gibt die in einer METHODDATA-Struktur beschriebene Aufrufkonvention einer Methode an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_CDECL">
      <summary>Gibt an, dass die Aufrufkonvention C-Deklaration (DCECL) für eine Methode verwendet werden soll. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MACPASCAL">
      <summary>Gibt an, dass die Aufrufkonvention Macintosh Pascal (MACPASCAL) für eine Methode verwendet werden soll.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MAX">
      <summary>Gibt das Ende der <see cref="T:System.Runtime.InteropServices.ComTypes.CALLCONV" />-Enumeration an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWCDECL">
      <summary>Gibt an, dass die Aufrufkonvention Macintosh Programmers' Workbench (MPW) CDECL für eine Methode verwendet wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWPASCAL">
      <summary>Gibt an, dass die Aufrufkonvention Macintosh Programmers' Workbench (MPW) PASCAL für eine Methode verwendet wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MSCPASCAL">
      <summary>Gibt an, dass die Aufrufkonvention MSC Pascal (MSCPASCAL) für eine Methode verwendet werden soll.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_PASCAL">
      <summary>Gibt an, dass die Pascal-Aufrufkonvention für eine Methode verwendet werden soll.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_RESERVED">
      <summary>Dieser Wert ist für eine spätere Verwendung vorgesehen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_STDCALL">
      <summary>Gibt an, dass die Standardaufrufkonvention (STDCALL) für eine Methode verwendet werden soll.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_SYSCALL">
      <summary>Gibt an, dass die Standardaufrufkonvention SYSCALL für eine Methode verwendet werden soll.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CONNECTDATA">
      <summary>Beschreibt eine Verbindung, die mit einem bestimmten Verbindungspunkt besteht.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.dwCookie">
      <summary>Stellt ein Verbindungstoken dar, das bei einem Aufruf von <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> zurückgegeben wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.pUnk">
      <summary>Stellt einen Zeiger auf die IUnknown-Schnittstelle für eine verbundene Advisesenke dar.Wenn die CONNECTDATA-Struktur nicht mehr benötigt wird, muss der Aufrufer für diesen Zeiger IUnknown::Release aufrufen.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DATADIR">
      <summary>Gibt die Richtung des Datenflusses im <paramref name="dwDirection" />-Parameter der <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" />-Methode an.Dieser bestimmt die Formate, die der resultierende Enumerator auflisten kann.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_GET">
      <summary>Fordert von <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> einen Enumerator für die Formate an, die in <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.GetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> angegeben werden können.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_SET">
      <summary>Fordert von <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> einen Enumerator für die Formate an, die in <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.SetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@,System.Boolean)" /> angegeben werden können.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DESCKIND">
      <summary>Gibt die Typbeschreibung an, an die gebunden wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_FUNCDESC">
      <summary>Gibt an, dass eine <see cref="T:System.Runtime.InteropServices.FUNCDESC" />-Struktur zurückgegeben wurde.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_IMPLICITAPPOBJ">
      <summary>Gibt an, dass ein IMPLICITAPPOBJ zurückgegeben wurde.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_MAX">
      <summary>Gibt eine Markierung für das Ende der Enumeration an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_NONE">
      <summary>Gibt an, dass keine Übereinstimmung gefunden wurde.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_TYPECOMP">
      <summary>Gibt an, dass ein TYPECOMP zurückgegeben wurde.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_VARDESC">
      <summary>Gibt an, dass ein VARDESC zurückgegeben wurde.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DISPPARAMS">
      <summary>Enthält die Argumente, die einer Methode oder Eigenschaft von IDispatch::Invoke übergeben wurden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cArgs">
      <summary>Stellt die Anzahl der Argumente dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cNamedArgs">
      <summary>Stellt die Anzahl der benannten Argumente dar. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgdispidNamedArgs">
      <summary>Stellt die Dispatch-IDs der benannten Argumente dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgvarg">
      <summary>Stellt einen Verweis auf das Argumentarray dar.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DVASPECT">
      <summary>Gibt beim Zeichnen oder Abrufen von Daten den gewünschten Aspekt für die Daten oder die Ansicht des Objekts an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_CONTENT">
      <summary>Eine Darstellung eines Objekts, die das Anzeigen des Objekts als eingebettetes Objekt in einem Container ermöglicht.Dieser Wert wird i. d. R. für Verbunddokumentobjekte angegeben.Die Darstellung kann für den Bildschirm oder den Drucker bereitgestellt werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_DOCPRINT">
      <summary>Eine Darstellung eines Objekts auf dem Bildschirm, die dem Ausdruck mit dem Drucken-Befehl im Menü Datei gleicht.Die beschriebenen Daten können auf mehreren Seiten dargestellt werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_ICON">
      <summary>Eine Symboldarstellung eines Objekts.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_THUMBNAIL">
      <summary>Eine Miniaturansicht eines Objekts, die das Anzeigen des Objekts in einem Browser ermöglicht.Die Miniaturansicht ist eine Bitmap mit ungefähr 120 x 120 Pixel, 16 Farben (empfohlen) und geräteunabhängig, die wahrscheinlich von einer Metadatei umschlossen ist.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC">
      <summary>Enthält die Typbeschreibung und Prozessübertragungsinformationen für eine Variable, eine Funktion oder einen Funktionsparameter.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.desc">
      <summary>Enthält Informationen über ein Element.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.tdesc">
      <summary>Gibt den Elementtyp an.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION">
      <summary>Enthält Informationen über ein Element. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.idldesc">
      <summary>Enthält Informationen über Remoting des Elements.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.paramdesc">
      <summary>Enthält Informationen über den Parameter.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.EXCEPINFO">
      <summary>Beschreibt die Ausnahmen, die während IDispatch::Invoke auftreten.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrDescription">
      <summary>Eine für den Kunden bestimmte Beschreibung des Fehlers.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrHelpFile">
      <summary>Enthält das Laufwerk, den vollqualifizierten Pfad und den Dateinamen einer Hilfedatei, in der Sie weitere Informationen über den Fehler finden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrSource">
      <summary>Gibt den Namen für die Quelle der Ausnahme an.In der Regel ist dies ein Anwendungsname.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.dwHelpContext">
      <summary>Gibt die Hilfekontext-ID des Themas in der Hilfedatei an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pfnDeferredFillIn">
      <summary>Stellt einen Zeiger auf die Funktion dar, die als Argument eine <see cref="T:System.Runtime.InteropServices.EXCEPINFO" />-Struktur annimmt und einen HRESULT-Wert zurückgibt.Wenn ein verzögertes Ausfüllen nicht gewünscht wird, wird dieses Feld auf null festgelegt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pvReserved">
      <summary>Dieses Feld ist reserviert und muss auf null festgelegt werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.scode">
      <summary>Ein den Fehler beschreibender Rückgabewert.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wCode">
      <summary>Stellt einen Fehlercode dar, der den Fehler bezeichnet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wReserved">
      <summary>Dieses Feld ist reserviert und muss auf 0 (null) festgelegt werden.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FILETIME">
      <summary>Stellt die Anzahl der seit dem 1. Januar 1601 vergangenen 100-Nanosekundenintervalle dar.Bei dieser Struktur handelt es sich um einen 64-Bit-Wert.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwHighDateTime">
      <summary>Gibt die oberen 32 Bits von FILETIME an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwLowDateTime">
      <summary>Gibt die unteren 32 Bits von FILETIME an.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FORMATETC">
      <summary>Stellt ein allgemeines Zwischenablageformat dar. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.cfFormat">
      <summary>Gibt das spezielle Zwischenablageformat an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.dwAspect">
      <summary>Gibt eine der <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" />-Enumerationskonstanten an, die angibt, wie detailreich das Rendering erfolgen soll.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.lindex">
      <summary>Gibt einen Teil des Aspektes an, wenn die Daten über Seitenbegrenzungen hinaus verteilt werden müssen. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.ptd">
      <summary>Gibt einen Zeiger auf eine DVTARGETDEVICE-Struktur an, die Informationen über das Zielgerät enthält, für das die Daten erstellt werden. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.tymed">
      <summary>Gibt eine der <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" />-Enumerationskonstanten an, die den Typ des Speichermediums angibt, mit dem die Objektdaten übertragen werden. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCDESC">
      <summary>Definiert eine Funktionsbeschreibung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.callconv">
      <summary>Gibt die Aufrufkonvention einer Funktion an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParams">
      <summary>Zählt die Gesamtanzahl der Parameter.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParamsOpt">
      <summary>Zählt die optionalen Parameter.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cScodes">
      <summary>Zählt die zulässigen Rückgabewerte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.elemdescFunc">
      <summary>Enthält den Rückgabetyp der Funktion.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.funckind">
      <summary>Gibt an, ob die Funktion virtuell, statisch oder auf Dispatch beschränkt ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.invkind">
      <summary>Gibt den Typ einer Eigenschaftsfunktion an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgelemdescParam">
      <summary>Gibt die Größe von <see cref="F:System.Runtime.InteropServices.FUNCDESC.cParams" /> an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgscode">
      <summary>Speichert die Anzahl der Fehler, die eine Funktion auf einem 16-Bit-System zurückgeben kann.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.memid">
      <summary>Bezeichnet die Funktionsmember-ID.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.oVft">
      <summary>Gibt den Offset in der VTBL für <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_VIRTUAL" /> an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.wFuncFlags">
      <summary>Gibt den <see cref="T:System.Runtime.InteropServices.FUNCFLAGS" /> einer Funktion an.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCFLAGS">
      <summary>Gibt die Konstanten an, die die Eigenschaften einer Funktion definieren.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FBINDABLE">
      <summary>Die Funktion, die Datenbindung unterstützt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTBIND">
      <summary>Die Funktion, die das Objekt am besten darstellt.Nur eine Funktion in einem Typ kann über dieses Attribut verfügen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTCOLLELEM">
      <summary>Lässt eine Optimierung zu, bei der der Compiler im Typ "abc" nach dem Member "xyz" sucht.Wenn ein solcher Member gefunden und als Accessorfunktion für einen Member der Standardauflistung markiert wird, wird ein Aufruf dieser Memberfunktion generiert.Zulässig für Member an Dispatchschnittstellen und Schnittstellen, nicht zulässig für Module.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDISPLAYBIND">
      <summary>Die dem Benutzer als bindungsfähig angezeigte Funktion.<see cref="F:System.Runtime.InteropServices.FUNCFLAGS.FUNCFLAG_FBINDABLE" /> muss auch festgelegt werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FHIDDEN">
      <summary>Die Funktion darf dem Benutzer nicht angezeigt werden, obwohl sie vorhanden und bindbar ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FIMMEDIATEBIND">
      <summary>Als einzelne bindbare Eigenschaften zugeordnet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FNONBROWSABLE">
      <summary>Die Eigenschaft wird in einem Objektkatalog, nicht jedoch in einem Eigenschaftenbrowser angezeigt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREPLACEABLE">
      <summary>Markiert die Schnittstelle als Schnittstelle mit Standardverhalten.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREQUESTEDIT">
      <summary>Bei Festlegung hat jeder Aufruf einer Methode, die die Eigenschaft festlegt, zunächst einen Aufruf von IPropertyNotifySink::OnRequestEdit zur Folge.Die Implementierung von OnRequestEdit bestimmt, ob der Aufruf zum Festlegen der Eigenschaft zulässig ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FRESTRICTED">
      <summary>Der Zugriff auf die Funktion durch Makrosprachen darf nicht möglich sein.Dieses Flag ist für Funktionen auf Systemebene oder für Funktionen bestimmt, die von Typenbrowsern nicht angezeigt werden sollen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FSOURCE">
      <summary>Die Funktion gibt ein Objekt zurück, das eine Ereignisquelle ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUIDEFAULT">
      <summary>Der Typinformationsmember ist der Standardmember für die Anzeige auf der Benutzeroberfläche.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUSESGETLASTERROR">
      <summary>Die Funktion unterstützt GetLastError.Wenn beim Ausführen der Funktion ein Fehler auftritt, kann der Aufrufer GetLastError aufrufen, um den Fehlercode abzurufen.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCKIND">
      <summary>Definiert, wie auf eine Funktion zugriffen werden kann.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_DISPATCH">
      <summary>Auf die Funktion kann nur anhand von IDispatch zugegriffen werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_NONVIRTUAL">
      <summary>Der Zugriff auf die Funktion erfolgt über eine static-Adresse, und die Funktion akzeptiert einen impliziten this-Zeiger.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_PUREVIRTUAL">
      <summary>Auf die Funktion wird über VTBL (Virtual Function Table) zugegriffen, und sie enthält einen impliziten this-Zeiger.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_STATIC">
      <summary>Der Zugriff auf die Funktion erfolgt über eine static-Adresse, und die Funktion akzeptiert keinen impliziten this-Zeiger.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_VIRTUAL">
      <summary>Auf die Funktion wird ebenso wie auf <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_PUREVIRTUAL" /> zugegriffen, sofern die Funktion keine Implementierung besitzt.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IAdviseSink">
      <summary>Stellt eine verwaltete Definition der IAdviseSink-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnClose">
      <summary>Benachrichtigt alle registrierten Advise-Senken, dass das Objekt aus dem laufenden Zustand in den geladenen Zustand gewechselt ist.  Diese Methode wird von einem Server aufgerufen.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)">
      <summary>Benachrichtigt alle aktuell registrierten Advise-Senken eines Datenobjekts, dass die Daten im Objekt geändert wurden.</summary>
      <param name="format">Ein <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />, das als Verweis übergeben wird und das Format, das Zielgerät, das Rendering und Speicherinformationen des aufrufenden Datenobjekts beschreibt.</param>
      <param name="stgmedium">Ein als Verweis übergebenes <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />, das das Speichermedium (globaler Speicher, Datenträgerdatei, Speicherobjekt, Streamobjekt, GDI (Graphics Device Interface)-Objekt oder nicht definiert) und den Besitz dieses Mediums für das aufrufende Datenobjekt definiert.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnRename(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Benachrichtigt alle registrierten Advise-Senken, dass das Objekt umbenannt wurde.Diese Methode wird von einem Server aufgerufen.</summary>
      <param name="moniker">Ein Zeiger auf die IMoniker-Schnittstelle im neuen vollständigen Moniker des Objekts.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnSave">
      <summary>Benachrichtigt alle registrierten Advise-Senken, dass das Objekt gespeichert wurde.Diese Methode wird von einem Server aufgerufen.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnViewChange(System.Int32,System.Int32)">
      <summary>Benachrichtigt die registrierten Advise-Senken eines Objekts, die seine Ansicht geändert wurde.Diese Methode wird von einem Server aufgerufen.</summary>
      <param name="aspect">Der Aspekt oder die Ansicht des Objekts.Enthält einen aus der <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" />-Enumeration entnommenen Wert.</param>
      <param name="index">Der Teil der Ansicht, die sich geändert hat.Zurzeit ist nur -1 gültig.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IBindCtx">
      <summary>Stellt die verwaltete Definition der IBindCtx-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.EnumObjectParam(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Zählt die Zeichenfolgen auf, die die Schlüssel der intern verwalteten Tabelle von Kontextparametern für Objekte sind.</summary>
      <param name="ppenum">Die Methode gibt einen Verweis auf den Enumerator von Objektparametern zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Gibt die aktuellen Bindungsoptionen zurück, die im aktuellen Bindungskontext gespeichert sind.</summary>
      <param name="pbindopts">Ein Zeiger, der auf die Struktur zum Empfangen der Bindungsoptionen zeigt. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetObjectParam(System.String,System.Object@)">
      <summary>Sucht den angegebenen Schlüssel in der intern verwalteten Tabelle der Kontextobjektparameter und gibt ggf. das entsprechende Objekt zurück.</summary>
      <param name="pszKey">Der Name des zu suchenden Objekts. </param>
      <param name="ppunk">Die Methode gibt den Objektschnittstellenzeiger zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetRunningObjectTable(System.Runtime.InteropServices.ComTypes.IRunningObjectTable@)">
      <summary>Gibt den Zugriff auf die ROT (Running Object Table) zurück, die für diesen Bindungsvorgang relevant ist.</summary>
      <param name="pprot">Die Methode gibt einen Verweis auf die Running Object Table (ROT) zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)">
      <summary>Registriert das übergebene Objekt als eines der während einer Moniker-Operation gebundenen Objekte, das nach Abschluss der Operation freigegeben werden soll.</summary>
      <param name="punk">Das zur Freigabe zu registrierende Objekt. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectParam(System.String,System.Object)">
      <summary>Registriert den angegebenen Objektzeiger unter dem angegebenen Namen in der intern verwalteten Tabelle von Objektzeigern.</summary>
      <param name="pszKey">Der Name, unter dem <paramref name="punk" /> zu registrieren ist. </param>
      <param name="punk">Das zu registrierende Objekt. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.ReleaseBoundObjects">
      <summary>Gibt alle momentan im Bindungskontext mithilfe der <see cref="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)" />-Methode registrierten Objekte frei.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectBound(System.Object)">
      <summary>Entfernt das Objekt aus dem Satz freizugebender registrierter Objekte.</summary>
      <param name="punk">Das Objekt, dessen Registrierung für die Freigabe aufzuheben ist. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectParam(System.String)">
      <summary>Widerruft die Registrierung des Objekts, das momentan unter dem angegebenen Schlüssel in der intern verwalteten Tabelle von Kontextobjektparametern zu finden ist, wenn dieser Schlüssel derzeit registriert ist.</summary>
      <returns>Ein S_OKHRESULT-Wert, wenn der angegebene Schlüssel erfolgreich aus der Tabelle entfernt wurde; andernfalls ein S_FALSEHRESULT-Wert.</returns>
      <param name="pszKey">Der Schlüssel, dessen Registrierung aufgehoben werden soll. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.SetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Speichert im Bindungskontext einen Block von Parametern.Diese Parameter gelten für spätere UCOMIMoniker-Operationen, die diesen Bindungskontext verwenden.</summary>
      <param name="pbindopts">Die Struktur mit den festzulegenden Bindungsoptionen. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPoint">
      <summary>Stellt die verwaltete Definition der IConnectionPoint-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)">
      <summary>Richtet eine Advise-Verbindung zwischen dem Verbindungspunkt und dem Senkenobjekt des Aufrufers ein.</summary>
      <param name="pUnkSink">Ein Verweis auf die Senke, die Aufrufe für die von diesem Verbindungspunkt verwaltete Ausgangsschnittstelle empfangen soll. </param>
      <param name="pdwCookie">Diese Methode gibt das Verbindungscookie zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.EnumConnections(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Erstellt ein Enumerationsobjekt zum Durchlaufen der bis zu diesem Verbindungspunkt vorhandenen Verbindungen.</summary>
      <param name="ppEnum">Diese Methode gibt den neu erstellten Enumerator zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionInterface(System.Guid@)">
      <summary>Gibt die IID der von diesem Verbindungspunkt verwalteten Ausgangsschnittstelle zurück.</summary>
      <param name="pIID">Dieser Parameter gibt die IID der von diesem Verbindungspunkt verwalteten Ausgangsschnittstelle zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionPointContainer(System.Runtime.InteropServices.ComTypes.IConnectionPointContainer@)">
      <summary>Ruft den IConnectionPointContainer-Schnittstellenzeiger auf das verbindungsfähige Objekt ab, das den Verbindungspunkt konzeptionell besitzt.</summary>
      <param name="ppCPC">Dieser Parameters gibt die IConnectionPointContainer-Schnittstelle des verbindungsfähigen Objekts zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Unadvise(System.Int32)">
      <summary>Beendet eine Advise-Verbindung, die zuvor mit der <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />-Methode eingerichtet wurde.</summary>
      <param name="dwCookie">Das zuvor von der <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />-Methode zurückgegebene Verbindungscookie. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer">
      <summary>Stellt die verwaltete Definition der IConnectionPointContainer-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.EnumConnectionPoints(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Erstellt einen Enumerator aller Verbindungspunkte, die in dem verbindungsfähigen Objekt unterstützt werden, mit einem Verbindungspunkt pro IID.</summary>
      <param name="ppEnum">Diese Methode gibt den Schnittstellenzeiger des Enumerators zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.FindConnectionPoint(System.Guid@,System.Runtime.InteropServices.ComTypes.IConnectionPoint@)">
      <summary>Fragt das verbindungsfähige Objekt nach einem Verbindungspunkt für eine bestimmte IID. Wenn dieser vorhanden ist, wird der IConnectionPoint-Schnittstellenzeiger zurückgegeben, der auf diesen Verbindungspunkt verweist.</summary>
      <param name="riid">Ein Verweis auf die IID der Ausgangsschnittstelle, deren Verbindungspunkt angefragt wird. </param>
      <param name="ppCP">Diese Methode gibt den Verbindungspunkt zurück, der die <paramref name="riid" /> der Ausgangschnittstelle verwaltet.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLDESC">
      <summary>Enthält Informationen, die benötigt werden, um ein Strukturelement, einen Parameter oder den Rückgabewert einer Funktion zwischen Prozessen zu übertragen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.dwReserved">
      <summary>Reserviert, auf null festgelegt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.wIDLFlags">
      <summary>Gibt einen <see cref="T:System.Runtime.InteropServices.IDLFLAG" />-Wert an, der den Typ beschreibt.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLFLAG">
      <summary>Beschreibt die Übertragung eines Strukturelements, Parameters oder Rückgabewerts einer Funktion zwischen Prozessen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FIN">
      <summary>Der Parameter übergibt Informationen vom Aufrufer zum Aufgerufenen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FLCID">
      <summary>Der Parameter ist der lokale Bezeichner einer Clientanwendung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FOUT">
      <summary>Der Parameter gibt Informationen vom Aufgerufenen zum Aufrufer zurück.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FRETVAL">
      <summary>Der Parameter ist der Rückgabewert des Members.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_NONE">
      <summary>Es wird nicht angegeben, ob der Parameter Informationen weitergibt oder empfängt.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints">
      <summary>Verwaltet die Definition der IEnumConnectionPoints-Schnittstelle.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Erstellt einen neuen Enumerator, der den gleichen Enumerationszustand wie der aktuelle Enumerator enthält.</summary>
      <param name="ppenum">Enthält nach dem Beenden dieser Methode einen Verweis auf den neu erstellten Enumerator.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IConnectionPoint[],System.IntPtr)">
      <summary>Ruft eine angegebene Anzahl von Elementen in der Enumerationsfolge ab.</summary>
      <returns>S_OK, wenn der <paramref name="pceltFetched" />-Parameter gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der IConnectionPoint-Verweise, die in <paramref name="rgelt" /> zurückgegeben werden sollen. </param>
      <param name="rgelt">Enthält nach dem Beenden der Methode einen Verweis auf die aufgelisteten Verbindungen.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pceltFetched">Enthält nach dem Beenden dieser Methode einen Verweis auf die tatsächliche Anzahl der in <paramref name="rgelt" /> aufgelisteten Verbindungen. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Reset">
      <summary>Setzt die Enumerationsfolge auf den Anfang zurück.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Skip(System.Int32)">
      <summary>Überspringt eine angegebene Anzahl von Elementen in der Enumerationsfolge.</summary>
      <returns>S_OK, wenn die Anzahl der übersprungenen Elemente gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der Elemente, die in der Enumeration übersprungen werden sollen. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnections">
      <summary>Verwaltet die Definition der IEnumConnections-Schnittstelle.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Erstellt einen neuen Enumerator, der den gleichen Enumerationszustand wie der aktuelle Enumerator enthält.</summary>
      <param name="ppenum">Enthält nach dem Beenden dieser Methode einen Verweis auf den neu erstellten Enumerator.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Next(System.Int32,System.Runtime.InteropServices.ComTypes.CONNECTDATA[],System.IntPtr)">
      <summary>Ruft eine angegebene Anzahl von Elementen in der Enumerationsfolge ab.</summary>
      <returns>S_OK, wenn der <paramref name="pceltFetched" />-Parameter gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der <see cref="T:System.Runtime.InteropServices.CONNECTDATA" />-Strukturen, die in <paramref name="rgelt" /> zurückgegeben werden sollen. </param>
      <param name="rgelt">Enthält nach dem Beenden der Methode einen Verweis auf die aufgelisteten Verbindungen.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pceltFetched">Enthält nach dem Beenden dieser Methode einen Verweis auf die tatsächliche Anzahl der in <paramref name="rgelt" /> aufgelisteten Verbindungen. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Reset">
      <summary>Setzt die Enumerationsfolge auf den Anfang zurück.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Skip(System.Int32)">
      <summary>Überspringt eine angegebene Anzahl von Elementen in der Enumerationsfolge.</summary>
      <returns>S_OK, wenn die Anzahl der übersprungenen Elemente gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der Elemente, die in der Enumeration übersprungen werden sollen. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC">
      <summary>Stellt die verwaltete Definition der IEnumFORMATETC-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Clone(System.Runtime.InteropServices.ComTypes.IEnumFORMATETC@)">
      <summary>Erstellt einen neuen Enumerator, der den gleichen Enumerationszustand wie der aktuelle Enumerator enthält.</summary>
      <param name="newEnum">Enthält nach dem Beenden dieser Methode einen Verweis auf den neu erstellten Enumerator.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Next(System.Int32,System.Runtime.InteropServices.ComTypes.FORMATETC[],System.Int32[])">
      <summary>Ruft eine angegebene Anzahl von Elementen in der Enumerationsfolge ab.</summary>
      <returns>S_OK, wenn der <paramref name="pceltFetched" />-Parameter gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />-Verweise, die in <paramref name="rgelt" /> zurückgegeben werden sollen.</param>
      <param name="rgelt">Enthält nach dem Beenden dieser Methode einen Verweis auf die aufgelisteten <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />-Verweise.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pceltFetched">Enthält nach dem Beenden dieser Methode einen Verweis auf die tatsächliche Anzahl der in <paramref name="rgelt" /> aufgelisteten Verweise.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Reset">
      <summary>Setzt die Enumerationsfolge auf den Anfang zurück.</summary>
      <returns>Ein HRESULT mit dem Wert S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Skip(System.Int32)">
      <summary>Überspringt eine angegebene Anzahl von Elementen in der Enumerationsfolge.</summary>
      <returns>S_OK, wenn die Anzahl der übersprungenen Elemente gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der Elemente, die in der Enumeration übersprungen werden sollen.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumMoniker">
      <summary>Verwaltet die Definition der IEnumMoniker-Schnittstelle.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Clone(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Erstellt einen neuen Enumerator, der den gleichen Enumerationszustand wie der aktuelle Enumerator enthält.</summary>
      <param name="ppenum">Enthält nach dem Beenden dieser Methode einen Verweis auf den neu erstellten Enumerator.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker[],System.IntPtr)">
      <summary>Ruft eine angegebene Anzahl von Elementen in der Enumerationsfolge ab.</summary>
      <returns>S_OK, wenn der <paramref name="pceltFetched" />-Parameter gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der Moniker, die in <paramref name="rgelt" /> zurückgegeben werden sollen. </param>
      <param name="rgelt">Die Methode gibt einen Verweis auf die aufgelisteten Moniker zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pceltFetched">Die Methode gibt einen Verweis auf die tatsächliche Anzahl der in <paramref name="rgelt" /> aufgelisteten Moniker zurück. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Reset">
      <summary>Setzt die Enumerationsfolge auf den Anfang zurück.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Skip(System.Int32)">
      <summary>Überspringt eine angegebene Anzahl von Elementen in der Enumerationsfolge.</summary>
      <returns>S_OK, wenn die Anzahl der übersprungenen Elemente gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der Elemente, die in der Enumeration übersprungen werden sollen. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumString">
      <summary>Verwaltet die Definition der IEnumString-Schnittstelle.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Clone(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Erstellt einen neuen Enumerator, der den gleichen Enumerationszustand wie der aktuelle Enumerator enthält.</summary>
      <param name="ppenum">Enthält nach dem Beenden dieser Methode einen Verweis auf den neu erstellten Enumerator.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Next(System.Int32,System.String[],System.IntPtr)">
      <summary>Ruft eine angegebene Anzahl von Elementen in der Enumerationsfolge ab.</summary>
      <returns>S_OK, wenn der <paramref name="pceltFetched" />-Parameter gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der Zeichenfolgen, die in <paramref name="rgelt" /> zurückgegeben werden sollen. </param>
      <param name="rgelt">Die Methode gibt einen Verweis auf die aufgelisteten Zeichenfolgen zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pceltFetched">Diese Methode gibt einen Verweis auf die tatsächliche Anzahl der in <paramref name="rgelt" /> aufgelisteten Zeichenfolgen zurück. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Reset">
      <summary>Setzt die Enumerationsfolge auf den Anfang zurück.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Skip(System.Int32)">
      <summary>Überspringt eine angegebene Anzahl von Elementen in der Enumerationsfolge.</summary>
      <returns>S_OK, wenn die Anzahl der übersprungenen Elemente gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der Elemente, die in der Enumeration übersprungen werden sollen. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT">
      <summary>Verwaltet die Definition der IEnumVARIANT-Schnittstelle.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Clone">
      <summary>Erstellt einen neuen Enumerator, der den gleichen Enumerationszustand wie der aktuelle Enumerator enthält.</summary>
      <returns>Ein <see cref="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT" />-Verweis auf den neu erstellten Enumerator.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Next(System.Int32,System.Object[],System.IntPtr)">
      <summary>Ruft eine angegebene Anzahl von Elementen in der Enumerationsfolge ab.</summary>
      <returns>S_OK, wenn der <paramref name="pceltFetched" />-Parameter gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der Elemente, die in <paramref name="rgelt" /> zurückgegeben werden sollen. </param>
      <param name="rgVar">Diese Methode gibt einen Verweis auf die aufgelisteten Elemente zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pceltFetched">Diese Methode gibt einen Verweis auf die tatsächliche Anzahl der in <paramref name="rgelt" /> aufgelisteten Elemente zurück. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Reset">
      <summary>Setzt die Enumerationsfolge auf den Anfang zurück.</summary>
      <returns>Ein HRESULT mit dem Wert S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Skip(System.Int32)">
      <summary>Überspringt eine angegebene Anzahl von Elementen in der Enumerationsfolge.</summary>
      <returns>S_OK, wenn die Anzahl der übersprungenen Elemente gleich dem <paramref name="celt" />-Parameter ist, andernfalls S_FALSE.</returns>
      <param name="celt">Die Anzahl der Elemente, die in der Enumeration übersprungen werden sollen. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMoniker">
      <summary>Stellt die verwaltete Definition der IMoniker-Schnittstelle mit COM-Funktionen von IPersist und IPersistStream bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToObject(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Verwendet den Moniker zum Binden an das von ihm bestimmte Objekt.</summary>
      <param name="pbc">Ein Verweis auf die IBindCtx-Schnittstelle für das Bindungskontextobjekt, das in diesem Bindungsvorgang verwendet wird. </param>
      <param name="pmkToLeft">Ein Verweis auf den Moniker, der sich links vom aktuellen Moniker befindet, wenn der Moniker ein Teil eines zusammengesetzten Monikers ist. </param>
      <param name="riidResult">Der Schnittstellenbezeichner (Interface Identifier, IID) der Schnittstelle, die der Client für die Kommunikation mit dem Objekt verwenden soll, das vom Moniker angegeben wird. </param>
      <param name="ppvResult">Die Methode gibt einen Verweis auf die von <paramref name="riidResult" /> angeforderte Schnittstelle zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToStorage(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Ruft einen Schnittstellenzeiger auf den Speicher ab, der das vom Moniker bezeichnete Objekt enthält.</summary>
      <param name="pbc">Ein Verweis auf die IBindCtx-Schnittstelle für das Bindungskontextobjekt, das in diesem Bindungsvorgang verwendet wird. </param>
      <param name="pmkToLeft">Ein Verweis auf den Moniker, der sich links vom aktuellen Moniker befindet, wenn der Moniker ein Teil eines zusammengesetzten Monikers ist. </param>
      <param name="riid">Der Schnittstellenbezeichner (Interface Identifier, IID) der angeforderten Speicherschnittstelle. </param>
      <param name="ppvObj">Die Methode gibt einen Verweis auf die von <paramref name="riid" /> angeforderte Schnittstelle zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.CommonPrefixWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Erstellt einen neuen Moniker auf der Grundlage des Präfixes, das dieser Moniker mit einem anderen gemeinsam hat.</summary>
      <param name="pmkOther">Ein Verweis auf die IMoniker-Schnittstelle für einen anderen Moniker, der mit dem aktuellen Moniker auf ein gemeinsames Präfix hin verglichen wird. </param>
      <param name="ppmkPrefix">Die Methode gibt den Moniker zurück, der das gemeinsame Präfix des aktuellen Monikers und von <paramref name="pmkOther" /> darstellt.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ComposeWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Boolean,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Kombiniert den aktuellen Moniker mit einem anderen Moniker und erstellt auf diese Weise einen neuen zusammengesetzten Moniker.</summary>
      <param name="pmkRight">Ein Verweis auf die IMoniker-Schnittstelle für einen Moniker, der am Ende des aktuellen Monikers angefügt werden soll. </param>
      <param name="fOnlyIfNotGeneric">true, wenn der Aufrufer eine nicht generische Zusammensetzung erfordert.Die Operation wird nur dann fortgesetzt, wenn <paramref name="pmkRight" /> eine Monikerklasse darstellt, mit der der aktuelle Moniker kombiniert werden kann, ohne dabei einen generisch zusammengesetzten Moniker zu bilden.false, wenn mithilfe der Methode bei Bedarf ein generisch zusammengesetzter Moniker erstellt werden kann.</param>
      <param name="ppmkComposite">Die Methode gibt einen Verweis auf den sich ergebenden zusammengesetzten Moniker zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Enum(System.Boolean,System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Stellt einen Zeiger auf einen Enumerator bereit, der die Komponenten eines zusammengesetzten Monikers auflisten kann.</summary>
      <param name="fForward">true, um die Moniker von links nach rechts aufzulisten.false für die Auflistung von rechts nach links.</param>
      <param name="ppenumMoniker">Die Methode gibt einen Verweis auf das Enumeratorobjekt für den Moniker zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetClassID(System.Guid@)">
      <summary>Ruft die CLSID (Klassen-ID) für ein Objekt ab.</summary>
      <param name="pClassID">Diese Methode gibt die CLSID zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String@)">
      <summary>Ruft den Anzeigenamen ab, der eine für den Benutzer lesbare Darstellung des aktuellen Monikers ist.</summary>
      <param name="pbc">Ein Verweis auf den in diesem Vorgang zu verwendenden Bindungskontext. </param>
      <param name="pmkToLeft">Ein Verweis auf den Moniker, der sich links vom aktuellen Moniker befindet, wenn der Moniker ein Teil eines zusammengesetzten Monikers ist. </param>
      <param name="ppszDisplayName">Die Methode gibt die Zeichenfolge des Anzeigenamens zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetSizeMax(System.Int64@)">
      <summary>Gibt die Größe des Streams in Bytes zurück, der für das Speichern des Objekts benötigt wird.</summary>
      <param name="pcbSize">Die Methode gibt einen long-Wert zurück, der die Größe des Streams in Bytes zurückgibt, der für das Speichern dieses Objekts benötigt wird.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Stellt eine Zahl bereit, die den Zeitpunkt angibt, an dem das vom aktuellen Moniker angegebene Objekt zuletzt geändert wurde.</summary>
      <param name="pbc">Ein Verweis auf den in diesem Bindungsvorgang zu verwendenden Bindungskontext. </param>
      <param name="pmkToLeft">Ein Verweis auf den Moniker, der sich links vom aktuellen Moniker befindet, wenn der Moniker ein Teil eines zusammengesetzten Monikers ist. </param>
      <param name="pFileTime">Die Methode gibt den Zeitpunkt der letzten Änderung zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Hash(System.Int32@)">
      <summary>Berechnet eine 32-Bit-Ganzzahl anhand des inneren Zustands des Monikers.</summary>
      <param name="pdwHash">Die Methode gibt den Hashwert für diesen Moniker zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Inverse(System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Stellt einen Moniker bereit, der nicht zu einer Zusammensetzung führt, wenn er rechts an den aktuellen Moniker bzw. an einen Moniker mit ähnlicher Struktur angefügt wird.</summary>
      <param name="ppmk">Die Methode gibt einen Moniker zurück, der das Gegenteil vom aktuellen Moniker ist.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsDirty">
      <summary>Überprüft das Objekt auf Änderungen, die nach dem letzen Speichern vorgenommen wurden.</summary>
      <returns>Ein S_OKHRESULT-Wert, wenn das Objekt geändert wurde; andernfalls ein S_FALSEHRESULT-Wert.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsEqual(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Vergleicht den aktuellen Moniker mit einem angegebenen Moniker und gibt an, ob diese identisch sind.</summary>
      <returns>Ein S_OKHRESULT-Wert, wenn die Moniker identisch sind; andernfalls ein S_FALSEHRESULT-Wert.  </returns>
      <param name="pmkOtherMoniker">Ein für Vergleichszwecke vorgesehener Verweis auf den Moniker. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsRunning(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Bestimmt, ob das vom aktuellen Moniker angegebene Objekt gegenwärtig geladen ist und ausgeführt wird.</summary>
      <returns>Ein S_OKHRESULT-Wert, wenn der Moniker ausgeführt wird; ein S_FALSEHRESULT-Wert, wenn der Moniker nicht ausgeführt wird, oder ein E_UNEXPECTEDHRESULT-Wert.</returns>
      <param name="pbc">Ein Verweis auf den in diesem Bindungsvorgang zu verwendenden Bindungskontext. </param>
      <param name="pmkToLeft">Ein Verweis auf den Moniker, der sich links vom aktuellen Moniker befindet, wenn der aktuelle Moniker ein Teil eines zusammengesetzten Monikers ist. </param>
      <param name="pmkNewlyRunning">Ein Verweis auf den Moniker, der der ROT (Running Object Table) zuletzt hinzugefügt wurde. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsSystemMoniker(System.Int32@)">
      <summary>Gibt an, ob dieser Moniker zu den vom System gelieferten Monikerklassen gehört.</summary>
      <returns>Ein S_OKHRESULT-Wert, wenn der Moniker ein Systemmoniker ist; andernfalls ein S_FALSEHRESULT-Wert.</returns>
      <param name="pdwMksys">Die Methode gibt einen Zeiger auf eine Ganzzahl zurück, die einen Wert aus der MKSYS-Enumeration darstellt und sich auf eine der COM-Monikerklassen bezieht.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Load(System.Runtime.InteropServices.ComTypes.IStream)">
      <summary>Initialisiert ein Objekt aus dem Stream, in dem es zuvor gespeichert wurde.</summary>
      <param name="pStm">Der Stream, aus dem das Objekt geladen wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Liest so viele Zeichen des angegebenen Anzeigenamens, wie von <see cref="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)" /> verstanden werden, und erstellt entsprechend der gelesenen Teilzeichenfolge einen Moniker.</summary>
      <param name="pbc">Ein Verweis auf den in diesem Bindungsvorgang zu verwendenden Bindungskontext. </param>
      <param name="pmkToLeft">Ein Verweis auf den Moniker, der bis zu diesem Punkt aus dem Anzeigenamen erstellt wurde. </param>
      <param name="pszDisplayName">Ein Verweis auf die Zeichenfolge mit dem verbleibenden Anzeigenamen, der analysiert werden soll. </param>
      <param name="pchEaten">Die Methode gibt die Anzahl von Zeichen zurück, die bei der Analyse von <paramref name="pszDisplayName" /> verwendet wurde.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="ppmkOut">Die Methode gibt einen Verweis auf den Moniker zurück, der aus <paramref name="pszDisplayName" /> erstellt wurde.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Reduce(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Gibt einen reduzierten Moniker zurück. Dies ist ein anderer Moniker, der auf dasselbe Objekt wie der aktuelle Moniker verweist, jedoch u. U. effizienter gebunden werden kann.</summary>
      <param name="pbc">Ein Verweis auf die IBindCtx-Schnittstelle für den in diesem Bindungsvorgang verwendeten Bindungskontext. </param>
      <param name="dwReduceHowFar">Ein Wert, der angibt, inwieweit der aktuelle Moniker reduziert werden soll. </param>
      <param name="ppmkToLeft">Ein Verweis auf den Moniker, der sich links vom aktuellen Moniker befindet. </param>
      <param name="ppmkReduced">Die Methode gibt einen Verweis auf die reduzierte Form des aktuellen Monikers zurück. Bei Auftreten eines Fehlers oder vollständiger Reduzierung des Monikers auf 0 (null) kann sie null sein.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.RelativePathTo(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Stellt einen Moniker bereit, der beim Anfügen an den aktuellen Moniker (oder einen mit ähnlicher Struktur) den angegebenen Moniker ergibt.</summary>
      <param name="pmkOther">Ein Verweis auf den Moniker, für den ein relativer Pfad verwendet werden soll. </param>
      <param name="ppmkRelPath">Die Methode gibt einen Verweis auf den relativen Moniker zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Save(System.Runtime.InteropServices.ComTypes.IStream,System.Boolean)">
      <summary>Speichert ein Objekt im angegebenen Stream.</summary>
      <param name="pStm">Der Stream, in dem das Objekt gespeichert wird. </param>
      <param name="fClearDirty">true, um das Änderungsflag zu löschen, nachdem die Speicherung abgeschlossen ist, andernfalls false</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS">
      <summary>Definiert die Attribute einer implementierten oder geerbten Schnittstelle eines Typs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULT">
      <summary>Die Schnittstelle bzw. Dispatchschnittstelle stellt den Standard für die Quelle oder die Senke dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULTVTABLE">
      <summary>Senken empfangen Ereignisse über VTBL (Virtual Function Table).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FRESTRICTED">
      <summary>Der Member darf Benutzern nicht angezeigt werden und darf für sie auch nicht programmierbar sein.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FSOURCE">
      <summary>Dieser Member einer Co-Klasse wird aufgerufen und nicht implementiert.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND">
      <summary>Gibt an, wie eine Funktion von IDispatch::Invoke aufgerufen wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_FUNC">
      <summary>Der Member wird mit der üblichen Aufrufsyntax für Funktionen aufgerufen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYGET">
      <summary>Die Funktion wird mit der üblichen Syntax für den Zugriff auf Eigenschaften aufgerufen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUT">
      <summary>Die Funktion wird mit Syntax für das Zuweisen von Eigenschaftswerten aufgerufen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUTREF">
      <summary>Die Funktion wird mit Syntax für das Zuweisen von Verweisen auf Eigenschaften aufgerufen.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IPersistFile">
      <summary>Stellt die verwaltete Definition der IPersistFile-Schnittstelle mit IPersist-Funktionalität bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetClassID(System.Guid@)">
      <summary>Ruft die CLSID (Klassen-ID) für ein Objekt ab.</summary>
      <param name="pClassID">Diese Methode gibt einen Verweis auf die CLSID zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetCurFile(System.String@)">
      <summary>Ruft entweder den absoluten Pfad zur aktuellen Arbeitsdatei des Objekts ab, oder, wenn keine Arbeitsdatei vorhanden ist, die Eingabeaufforderung für den Standarddateinamen des Objekts.</summary>
      <param name="ppszFileName">Diese Methode gibt die Adresse eines Zeigers auf eine auf 0 (null) endende Zeichenfolge zurück, die den Pfad für die aktuelle Datei oder die Eingabeaufforderung für Standarddateinamen (z. B. *.txt) enthält.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.IsDirty">
      <summary>Überprüft ein Objekt auf Änderungen, die nach dem letzten Speichern in der aktuellen Datei vorgenommen wurden.</summary>
      <returns>S_OK, wenn die Datei nach dem letzten Speichern geändert wurde, andernfalls S_FALSE.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Load(System.String,System.Int32)">
      <summary>Öffnet die angegebene Datei, und initialisiert ein Objekt aus dem Dateiinhalt.</summary>
      <param name="pszFileName">Eine auf 0 endende Zeichenfolge mit dem absoluten Pfad der zu öffnenden Datei. </param>
      <param name="dwMode">Eine Kombination von Werten aus der STGM-Enumeration, die den Zugriffsmodus für das Öffnen von <paramref name="pszFileName" /> angibt. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Save(System.String,System.Boolean)">
      <summary>Speichert eine Kopie des Objekts in der angegebenen Datei.</summary>
      <param name="pszFileName">Eine auf 0 endende Zeichenfolge mit dem absoluten Pfad der Datei, in der das Objekt gespeichert ist. </param>
      <param name="fRemember">true für die Verwendung des <paramref name="pszFileName" />-Parameters als aktuelle Arbeitsdatei, andernfalls false. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.SaveCompleted(System.String)">
      <summary>Benachrichtigt das Objekt, dass dieses in die Datei schreiben kann.</summary>
      <param name="pszFileName">Der absolute Pfad der Datei, in der das Objekt zuvor gespeichert wurde. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IRunningObjectTable">
      <summary>Stellt die verwaltete Definition der IRunningObjectTable-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.EnumRunning(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Zählt die derzeit als ausgeführt registrierten Objekte auf.</summary>
      <param name="ppenumMoniker">Enthält nach dem Beenden der Methode den neuen Enumerator für die Running Object Table (ROT).Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetObject(System.Runtime.InteropServices.ComTypes.IMoniker,System.Object@)">
      <summary>Gibt das registriertes Objekt zurück, wenn das Objekt des angegebenen Namens als ausgeführt registriert ist.</summary>
      <returns>Ein HRESULT-Wert, der den Erfolg oder Fehler der Operation angibt. </returns>
      <param name="pmkObjectName">Ein Verweis auf den in der Running Object Table (ROT) zu suchenden Moniker. </param>
      <param name="ppunkObject">Enthält nach dem Beenden dieser Methode das angeforderte ausgeführte Objekt.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Sucht in der Running Object Table (ROT) nach diesem Moniker und teilt ggf. die aufgezeichnete Änderungszeit mit.</summary>
      <returns>Ein HRESULT-Wert, der den Erfolg oder Fehler der Operation angibt.</returns>
      <param name="pmkObjectName">Ein Verweis auf den in der Running Object Table (ROT) zu suchenden Moniker. </param>
      <param name="pfiletime">Enthält nach dem Beenden dieses Objekts die Zeit der letzten Änderung für dieses Objekt.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.IsRunning(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Bestimmt, ob der angegebene Moniker derzeit in der Running Object Table (ROT) registriert ist.</summary>
      <returns>Ein HRESULT-Wert, der den Erfolg oder Fehler der Operation angibt.</returns>
      <param name="pmkObjectName">Ein Verweis auf den in der Running Object Table (ROT) zu suchenden Moniker. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Stellt die Zeit fest, zu der ein bestimmtes Objekt geändert wurde, sodass von IMoniker::GetTimeOfLastChange eine entsprechende Änderungszeit mitgeteilt werden kann.</summary>
      <param name="dwRegister">Der ROT-Eintrag (Running Object Table) des geänderten Objekts. </param>
      <param name="pfiletime">Ein Verweis auf die Zeit der letzten Änderung des Objekts. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Register(System.Int32,System.Object,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Registriert, dass das bereitgestellte Objekt inzwischen ausgeführt wird.</summary>
      <returns>Ein Wert, mit dem dieser ROT-Eintrag bei nachfolgenden Aufrufen von <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)" /> oder <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)" /> bezeichnet werden kann.</returns>
      <param name="grfFlags">Gibt an, ob der ROT-Verweis (Running Object Table) auf <paramref name="punkObject" /> schwach oder stark ist und steuert den Zugriff auf das Objekt über dessen Eintrag in der ROT. </param>
      <param name="punkObject">Ein Verweis auf das als ausgeführt registrierte Objekt. </param>
      <param name="pmkObjectName">Ein Verweis auf den Moniker, der das <paramref name="punkObject" /> bezeichnet. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)">
      <summary>Hebt für das angegebene Objekt die Registrierung in der Running Object Table (ROT) auf.</summary>
      <param name="dwRegister">Der rückgängig zu machende ROT-Eintrag (Running Object Table). </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IStream">
      <summary>Stellt die verwaltete Definition der IStream-Schnittstelle mit ISequentialStream-Funktionalität bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
      <summary>Erstellt ein neues Streamobjekt mit eigenem Suchzeiger, der auf dieselben Bytes wie der ursprüngliche Stream verweist.</summary>
      <param name="ppstm">Die Methode gibt das neue Streamobjekt zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)">
      <summary>Stellt sicher, dass alle Änderungen, die am im transaktiven Modus geöffneten Streamobjekt vorgenommen werden, im übergeordneten Speicher reflektiert werden.</summary>
      <param name="grfCommitFlags">Ein Wert, der steuert, auf welche Weise ein Commit für die Änderungen am Streamobjekt ausgeführt wird. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
      <summary>Kopiert eine angegebene Anzahl von Bytes aus dem aktuellen Suchzeiger im Stream in den aktuellen Suchzeiger eines anderen Streams.</summary>
      <param name="pstm">Ein Verweis auf den Zielstream. </param>
      <param name="cb">Die Anzahl der aus dem Quellstream zu kopierenden Bytes. </param>
      <param name="pcbRead">Bei erfolgreicher Rückgabe ist die tatsächliche Anzahl der aus der Quelle gelesenen Bytes enthalten. </param>
      <param name="pcbWritten">Bei erfolgreicher Rückgabe ist die tatsächliche Anzahl der in das Ziel geschriebenen Bytes enthalten. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Schränkt den Zugriff auf einen bestimmten Bereich von Bytes im Stream ein.</summary>
      <param name="libOffset">Der Byteoffset für den Anfang des Bereichs. </param>
      <param name="cb">Die Länge des einzuschränkenden Bereichs in Bytes. </param>
      <param name="dwLockType">Die angeforderten Einschränkungen beim Zugriff auf den Bereich. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Read(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Liest beginnend beim aktuellen Suchzeiger eine angegebene Anzahl von Bytes aus dem Streamobjekt in den Speicher.</summary>
      <param name="pv">Die Methode gibt die vom Stream gelesenen Daten zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="cb">Die Anzahl der aus dem Streamobjekt zu lesenden Bytes. </param>
      <param name="pcbRead">Ein Zeiger auf eine ULONG-Variable, die die tatsächliche Anzahl der aus dem Streamobjekt gelesenen Bytes empfängt. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Revert">
      <summary>Verwirft alle Änderungen, die an einem transaktiven Stream seit dem letzten <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)" />-Aufruf vorgenommen wurden.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Seek(System.Int64,System.Int32,System.IntPtr)">
      <summary>Verschiebt den Suchzeiger an eine neue Position relativ zum Anfang oder zum Ende des Streams bzw. zum aktuellen Suchzeiger.</summary>
      <param name="dlibMove">Die Verschiebung, die <paramref name="dwOrigin" /> hinzugefügt wird. </param>
      <param name="dwOrigin">Der Ursprung der Suche.Der Ursprung kann der Dateianfang, der aktuelle Suchzeiger oder das Dateiende sein.</param>
      <param name="plibNewPosition">Bei erfolgreicher Rückgabe ist der Offset des Suchzeigers vom Anfang des Streams enthalten. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.SetSize(System.Int64)">
      <summary>Ändert die Größe des Streamobjekts.</summary>
      <param name="libNewSize">Die neue Streamgröße als Anzahl von Bytes. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
      <summary>Ruft die <see cref="T:System.Runtime.InteropServices.STATSTG" />-Struktur für diesen Stream ab.</summary>
      <param name="pstatstg">Die Methode gibt eine STATSTG-Struktur zurück, die dieses Streamobjekt beschreibt.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="grfStatFlag">Die Member in der STATSTG-Struktur, die von dieser Methode nicht zurückgegeben werden. Somit werden einige Operationen zur Speicherbelegung erspart. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Entfernt die Zugriffsbeschränkung für einen Bereich von Bytes, der zuvor mit der <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)" />-Methode eingeschränkt wurde.</summary>
      <param name="libOffset">Der Byteoffset für den Anfang des Bereichs. </param>
      <param name="cb">Die Länge des einzuschränkenden Bereichs in Bytes. </param>
      <param name="dwLockType">Die zuvor für den Bereich festgelegten Zugriffsbeschränkungen. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Write(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Schreibt beginnend am aktuellen Suchzeiger eine angegebene Anzahl von Bytes in das Streamobjekt.</summary>
      <param name="pv">Der Puffer, in den dieser Stream geschrieben werden soll. </param>
      <param name="cb">Die Anzahl von Bytes, die in den Stream geschrieben werden soll. </param>
      <param name="pcbWritten">Bei erfolgreicher Rückgabe ist die tatsächliche Anzahl der in das Streamobjekt geschriebenen Bytes enthalten.Wenn der Aufrufer diesen Zeiger auf <see cref="F:System.IntPtr.Zero" /> festlegt, gibt diese Methode nicht die tatsächliche Anzahl von geschriebenen Bytes zurück.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeComp">
      <summary>Stellt die verwaltete Definition der ITypeComp-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.Bind(System.String,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.DESCKIND@,System.Runtime.InteropServices.ComTypes.BINDPTR@)">
      <summary>Ordnet dem Member eines Typs einen Namen zu oder bindet globale Variablen und Funktionen in einer Typbibliothek.</summary>
      <param name="szName">Der zu bindende Name. </param>
      <param name="lHashVal">Ein von LHashValOfNameSys berechneter Hashwert für <paramref name="szName" />. </param>
      <param name="wFlags">Ein Flagwort mit mindestens einem Aufrufflag, das in der INVOKEKIND-Enumeration definiert ist. </param>
      <param name="ppTInfo">Die Methode gibt einen Verweis auf die Typbeschreibung mit dem Element zurück, an das diese gebunden ist, wenn FUNCDESC oder VARDESC zurückgegeben wurde.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pDescKind">Die Methode gibt einen Verweis auf einen DESCKIND-Enumerator zurück, der angibt, ob es sich bei dem gebundenen Namen um VARDESC, FUNCDESC oder TYPECOMP handelt.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pBindPtr">Die Methode gibt einen Verweis auf die gebundene VARDESC, FUNCDESC oder ITypeComp-Schnittstelle zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.BindType(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Bindet an die Typbeschreibungen in einer Typbibliothek.</summary>
      <param name="szName">Der zu bindende Name. </param>
      <param name="lHashVal">Ein von LHashValOfNameSys bestimmter Hashwert für <paramref name="szName" />. </param>
      <param name="ppTInfo">Die Methode gibt einen Verweis auf die ITypeInfo des Typs zurück, an den <paramref name="szName" /> gebunden ist.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="ppTComp">Die Methode gibt einen Verweis auf eine ITypeComp-Variable zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo">
      <summary>Stellt die verwaltete Definition der ITypeInfo-Automatisierungsschnittstelle für Komponenten bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Ruft die Adressen statischer Funktionen oder Variablen ab, wie sie z. B. in einer DLL definiert sind.</summary>
      <param name="memid">Die Member-ID der abzurufenden Adresse des static-Members. </param>
      <param name="invKind">Ein <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />-Wert, der angibt, ob es sich bei dem Member um eine Eigenschaft handelt, und ggf. um welche Art von Eigenschaft es sich handelt. </param>
      <param name="ppv">Diese Methode gibt einen Verweis auf den static-Member zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Erstellt eine neue Instanz eines Typs, der eine Komponentenklasse (Co-Klasse) beschreibt.</summary>
      <param name="pUnkOuter">Das Objekt, das als steuernde IUnknown fungiert. </param>
      <param name="riid">Die IID der Schnittstelle, die der Aufrufer zur Kommunikation mit dem resultierenden Objekt verwendet. </param>
      <param name="ppvObj">Diese Methode gibt einen Verweis auf das erstellte Objekt zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Ruft die Typbibliothek ab, in der die Typenbeschreibung und der Index in der Typbibliothek enthalten ist.</summary>
      <param name="ppTLB">Diese Methode gibt einen Verweis auf die enthaltende Typbibliothek zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pIndex">Diese Methode gibt einen Verweis auf den Index der Typbeschreibung in der enthaltenden Typbibliothek zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Ruft eine Beschreibung oder Angabe eines Einstiegspunkts für eine Funktion in einer DLL ab.</summary>
      <param name="memid">Die ID der Memberfunktion, deren Beschreibung des DLL-Eintrags zurückgegeben werden soll. </param>
      <param name="invKind">Einer der <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />-Werte, der die durch <paramref name="memid" /> festgelegte Art von Member angibt. </param>
      <param name="pBstrDllName">Wenn dies nicht null ist, legt die Funktion <paramref name="pBstrDllName" /> auf ein BSTR fest, das den Namen der DLL enthält. </param>
      <param name="pBstrName">Wenn dies nicht null ist, legt die Funktion <paramref name="lpbstrName" /> auf ein BSTR fest, das den Namen des Einstiegspunktes enthält. </param>
      <param name="pwOrdinal">Wenn dies nicht null ist und die Funktion durch eine Ordnungszahl definiert ist, ist <paramref name="lpwOrdinal" /> zum Zeigen auf die Ordnungszahl festgelegt. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Ruft die Dokumentationszeichenfolge, den vollständigen Namen und Pfad der Hilfedatei sowie die Kontext-ID des Hilfethemas für eine angegebene Typenbeschreibung ab.</summary>
      <param name="index">Die ID des Members, dessen Dokumentation zurückgegeben werden soll. </param>
      <param name="strName">Diese Methode gibt den Namen der Elementmethode zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="strDocString">Enthält nach dem Beenden dieser Methode die Dokumentationszeichenfolge für das angegebene Element.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="dwHelpContext">Diese Methode gibt einen Verweis auf den Hilfekontext zurück, der dem angegebenen Element zugeordnet ist.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="strHelpFile">Diese Methode gibt den voll qualifizierten Namen der Hilfedatei zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Ruft die <see cref="T:System.Runtime.InteropServices.FUNCDESC" />-Struktur ab, in der Informationen über eine angegebene Funktion enthalten sind.</summary>
      <param name="index">Der Index der zurückzugebenden Funktionsbeschreibung. </param>
      <param name="ppFuncDesc">Diese Methode gibt einen Verweis auf eine FUNCDESC-Struktur zurück, die die angegebene Funktion beschreibt.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Erstellt Zuordnungen zwischen Membernamen und Member-IDs sowie zwischen Parameternamen und Parameter-IDs.</summary>
      <param name="rgszNames">Ein Array von zuzuordnenden Namen. </param>
      <param name="cNames">Die Anzahl der zuzuordnenden Namen. </param>
      <param name="pMemId">Diese Methode gibt einen Verweis auf ein Array mit Namenszuordnungen zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Ruft den <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" />-Wert für eine einzelne implementierte Schnittstelle oder Basisschnittstelle in einer Typenbeschreibung ab.</summary>
      <param name="index">Der Index der implementierten Schnittstelle bzw. Basisschnittstelle. </param>
      <param name="pImplTypeFlags">Diese Methode gibt einen Verweis auf die IMPLTYPEFLAGS-Enumeration zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)">
      <summary>Ruft Marshallinformationen ab.</summary>
      <param name="memid">Die Member-ID, die angibt, welche Marshallinformationen erforderlich sind. </param>
      <param name="pBstrMops">Diese Methode gibt einen Verweis auf die opcode-Zeichenfolge für das Marshallen der Felder in der Struktur zurück, die von der Typbeschreibung beschrieben wird, auf die verwiesen wird. Wenn jedoch keine zurückzugebenden Informationen vorhanden sind, wird null zurückgegeben.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Ruft die Variable mit der angegebenen Member-ID (oder den Namen der Eigenschaft bzw. Methode und deren Parameter) ab, die der angegebenen Funktions-ID entsprechen.</summary>
      <param name="memid">ID des Members, dessen Name bzw. Namen zurückgegeben werden sollen. </param>
      <param name="rgBstrNames">Diese Methode gibt den bzw. die dem Member zugeordneten Namen zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="cMaxNames">Die Länge des <paramref name="rgBstrNames" />-Arrays. </param>
      <param name="pcNames">Diese Methode gibt die Anzahl der Namen im <paramref name="rgBstrNames" />-Array zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Ruft die Typbeschreibungen ab, auf die verwiesen wird, wenn eine Typbeschreibung auf andere Typbeschreibungen verweist.</summary>
      <param name="hRef">Ein Handle für die zurückzugebende Typbeschreibung, auf die verwiesen wird. </param>
      <param name="ppTI">Diese Methode gibt die Typbeschreibung zurück, auf die verwiesen wird.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Ruft die Typbeschreibung der implementierten Schnittstellentypen ab, wenn eine Typbeschreibung eine COM-Klasse beschreibt.</summary>
      <param name="index">Der Index des implementierten Typs, dessen Handle zurückgegeben wird. </param>
      <param name="href">Diese Methode gibt einen Verweis auf ein Handle für die implementierte Schnittstelle zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>Ruft eine <see cref="T:System.Runtime.InteropServices.TYPEATTR" />-Struktur mit den Attributen der Typenbeschreibung ab.</summary>
      <param name="ppTypeAttr">Diese Methode gibt einen Verweis auf die Struktur zurück, die die Attribute dieser Typbeschreibung enthält.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Ruft die ITypeComp-Schnittstelle für die Typenbeschreibung ab, sodass ein Clientcompiler eine Bindung an die Member der Typenbeschreibung vornehmen kann.</summary>
      <param name="ppTComp">Diese Methode gibt einen Verweis auf die ITypeComp-Schnittstelle der enthaltenden Typbibliothek zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Ruft eine VARDESC-Struktur ab, die die angegebene Variable beschreibt.</summary>
      <param name="index">Der Index der zurückzugebenden Variablenbeschreibung. </param>
      <param name="ppVarDesc">Diese Methode gibt einen Verweis auf die VARDESC-Struktur zurück, die die angegebene Variable beschreibt.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Ruft eine Methode auf bzw. greift auf die Eigenschaft eines Objekts zu, die bzw. das die von der Typenbeschreibung beschriebene Schnittstelle implementiert.</summary>
      <param name="pvInstance">Ein Verweis auf die von dieser Typbeschreibung beschriebene Schnittstelle. </param>
      <param name="memid">Ein Wert, der den Schnittstellenmember angibt. </param>
      <param name="wFlags">Flags, die den Kontext des Invoke-Aufrufs beschreiben. </param>
      <param name="pDispParams">Ein Verweis auf eine Struktur, die ein Array von Argumenten und ein Array von DISPIDs für benannte Argumente enthält und die Anzahl der Elemente in jedem Array zählt. </param>
      <param name="pVarResult">Ein Verweis auf den gewünschten Speicherort für das Ergebnis.Wenn <paramref name="wFlags" />DISPATCH_PROPERTYPUT oder DISPATCH_PROPERTYPUTREF angibt, wird <paramref name="pVarResult" /> ignoriert.Wenn kein Ergebnis erforderlich ist, wird dies auf null festgelegt.</param>
      <param name="pExcepInfo">Ein Zeiger auf eine Struktur für Ausnahmeinformationen, die nur bei Rückgabe von DISP_E_EXCEPTION aufgefüllt wird. </param>
      <param name="puArgErr">Wenn InvokeDISP_E_TYPEMISMATCH zurückgibt, gibt <paramref name="puArgErr" /> den Index in <paramref name="rgvarg" /> des Arguments vom falschen Typ an.Wenn mehrere Argumente einen Fehler zurückgeben, gibt <paramref name="puArgErr" /> nur das erste Argument mit einem Fehler an.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>Gibt eine <see cref="T:System.Runtime.InteropServices.FUNCDESC" />-Struktur frei, die zuvor von der <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />-Methode zurückgegeben wurde.</summary>
      <param name="pFuncDesc">Ein Verweis auf die freizugebende FUNCDESC-Struktur. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>Gibt eine <see cref="T:System.Runtime.InteropServices.TYPEATTR" />-Struktur frei, die zuvor von der <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />-Methode zurückgegeben wurde.</summary>
      <param name="pTypeAttr">Ein Verweis auf die freizugebende TYPEATTR-Struktur. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>Gibt eine VARDESC-Struktur frei, die zuvor von der <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />-Methode zurückgegeben wurde.</summary>
      <param name="pVarDesc">Ein Verweis auf die freizugebende VARDESC-Struktur. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo2">
      <summary>Stellt die verwaltete Definition der ITypeInfo2-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Ruft die Adressen statischer Funktionen oder Variablen ab, wie sie z. B. in einer DLL definiert sind.</summary>
      <param name="memid">Die Member-ID der abzurufenden Adresse des static-Members. </param>
      <param name="invKind">Ein <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />-Wert, der angibt, ob es sich bei dem Member um eine Eigenschaft handelt, und ggf. um welche Art von Eigenschaft es sich handelt. </param>
      <param name="ppv">Diese Methode gibt einen Verweis auf den static-Member zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Erstellt eine neue Instanz eines Typs, der eine Komponentenklasse (Co-Klasse) beschreibt.</summary>
      <param name="pUnkOuter">Ein Objekt, das als steuernde IUnknown fungiert. </param>
      <param name="riid">Die IID der Schnittstelle, die der Aufrufer zur Kommunikation mit dem resultierenden Objekt verwendet. </param>
      <param name="ppvObj">Diese Methode gibt einen Verweis auf das erstellte Objekt zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllCustData(System.IntPtr)">
      <summary>Ruft alle benutzerdefinierten Datenelemente für die Bibliothek ab.</summary>
      <param name="pCustData">Ein Zeiger auf CUSTDATA, worin alle benutzerdefinierten Datenelemente enthalten sind. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllFuncCustData(System.Int32,System.IntPtr)">
      <summary>Ruft alle benutzerdefinierten Daten aus der angegebenen Funktion ab.</summary>
      <param name="index">Der Index der Funktion, für die die benutzerdefinierten Daten abgerufen werden. </param>
      <param name="pCustData">Ein Zeiger auf CUSTDATA, worin alle benutzerdefinierten Datenelemente enthalten sind. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllImplTypeCustData(System.Int32,System.IntPtr)">
      <summary>Ruft alle benutzerdefinierten Daten für den angegebenen Implementierungstyp ab.</summary>
      <param name="index">Der Index des Implementierungstyps für die benutzerdefinierten Daten. </param>
      <param name="pCustData">Ein Zeiger auf CUSTDATA, worin alle benutzerdefinierten Datenelemente enthalten sind. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllParamCustData(System.Int32,System.Int32,System.IntPtr)">
      <summary>Ruft alle benutzerdefinierten Daten für den angegebenen Funktionsparameter ab.</summary>
      <param name="indexFunc">Der Index der Funktion, für die die benutzerdefinierten Daten abgerufen werden. </param>
      <param name="indexParam">Der Index des Parameters dieser Funktion, wofür die benutzerdefinierten Daten abgerufen werden. </param>
      <param name="pCustData">Ein Zeiger auf CUSTDATA, worin alle benutzerdefinierten Datenelemente enthalten sind. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllVarCustData(System.Int32,System.IntPtr)">
      <summary>Ruft die Variable für die benutzerdefinierten Daten ab.</summary>
      <param name="index">Der Index der Variablen, für die die benutzerdefinierten Daten abgerufen werden. </param>
      <param name="pCustData">Ein Zeiger auf CUSTDATA, worin alle benutzerdefinierten Datenelemente enthalten sind. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Ruft die Typbibliothek ab, in der die Typenbeschreibung und der Index in der Typbibliothek enthalten ist.</summary>
      <param name="ppTLB">Diese Methode gibt einen Verweis auf die enthaltende Typbibliothek zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pIndex">Diese Methode gibt einen Verweis auf den Index der Typbeschreibung in der enthaltenden Typbibliothek zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetCustData(System.Guid@,System.Object@)">
      <summary>Ruft die benutzerdefinierten Daten ab.</summary>
      <param name="guid">Die für die Bezeichnung der Daten verwendete GUID. </param>
      <param name="pVarVal">Diese Methode gibt ein Object zurück, das angibt, wo die abgerufenen Daten abzulegen sind.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Ruft eine Beschreibung oder Angabe eines Einstiegspunkts für eine Funktion in einer DLL ab.</summary>
      <param name="memid">Die ID der Memberfunktion, deren Beschreibung des DLL-Eintrags zurückgegeben werden soll. </param>
      <param name="invKind">Einer der <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />-Werte, der die durch <paramref name="memid" /> festgelegte Art von Member angibt. </param>
      <param name="pBstrDllName">Wenn dies nicht null ist, legt die Funktion <paramref name="pBstrDllName" /> auf ein BSTR fest, das den Namen der DLL enthält. </param>
      <param name="pBstrName">Wenn dies nicht null ist, legt die Funktion <paramref name="lpbstrName" /> auf ein BSTR fest, das den Namen des Einstiegspunktes enthält. </param>
      <param name="pwOrdinal">Wenn dies nicht null ist und die Funktion durch eine Ordnungszahl definiert ist, ist <paramref name="lpwOrdinal" /> zum Zeigen auf die Ordnungszahl festgelegt. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Ruft die Dokumentationszeichenfolge, den vollständigen Namen und Pfad der Hilfedatei sowie die Kontext-ID des Hilfethemas für eine angegebene Typenbeschreibung ab.</summary>
      <param name="index">Die ID des Members, dessen Dokumentation zurückgegeben werden soll. </param>
      <param name="strName">Diese Methode gibt den Namen der Elementmethode zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="strDocString">Enthält nach dem Beenden dieser Methode die Dokumentationszeichenfolge für das angegebene Element.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="dwHelpContext">Diese Methode gibt einen Verweis auf den Hilfekontext zurück, der dem angegebenen Element zugeordnet ist.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="strHelpFile">Diese Methode gibt den voll qualifizierten Namen der Hilfedatei zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Ruft die Dokumentationszeichenfolge der Bibliothek, den vollständigen Namen und Pfad der Hilfedatei sowie den zu verwendenden Lokalisierungskontext und die Kontext-ID des Hilfethemas für die Bibliothek in der Hilfedatei ab.</summary>
      <param name="memid">Der Memberbezeichner für die Typenbeschreibung. </param>
      <param name="pbstrHelpString">Diese Methode gibt ein BSTR zurück, das den Namen des angegebenen Elements enthält.Wenn der Aufrufer den Elementnamen nicht benötigt, kann <paramref name="pbstrHelpString" />null sein.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pdwHelpStringContext">Enthält nach dem Beenden dieser Methode den Lokalisierungskontext der Hilfe.Wenn der Aufrufer den Hilfekontext nicht benötigt, kann <paramref name="pdwHelpStringContext" />null sein.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pbstrHelpStringDll">Diese Methode gibt ein BSTR zurück, das den vollqualifizierten Namen der Datei enthält, die die für die Hilfedatei verwendete DLL enthält.Wenn der Aufrufer den Dateinamen nicht benötigt, kann <paramref name="pbstrHelpStringDll" />null sein.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Ruft die benutzerdefinierten Daten aus der angegebenen Funktion ab.</summary>
      <param name="index">Der Index der Funktion, für die die benutzerdefinierten Daten abgerufen werden. </param>
      <param name="guid">Die für die Bezeichnung der Daten verwendete GUID. </param>
      <param name="pVarVal">Diese Methode gibt ein Object zurück, das angibt, wo die Daten abzulegen sind.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Ruft die <see cref="T:System.Runtime.InteropServices.FUNCDESC" />-Struktur ab, in der Informationen über eine angegebene Funktion enthalten sind.</summary>
      <param name="index">Der Index der zurückzugebenden Funktionsbeschreibung. </param>
      <param name="ppFuncDesc">Diese Methode gibt einen Verweis auf eine FUNCDESC-Struktur zurück, die die angegebene Funktion beschreibt.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncIndexOfMemId(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.Int32@)">
      <summary>Bindet an einen bestimmten Member aufgrund einer bekannten DISPID, wobei der Membername nicht bekannt ist (z. B. beim Binden an einen Standardmember).</summary>
      <param name="memid">Der Memberbezeichner. </param>
      <param name="invKind">Einer der <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" />-Werte, der die durch memid festgelegte Art von Member angibt.</param>
      <param name="pFuncIndex">Diese Funktion gibt einen Index in die Funktion zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Erstellt Zuordnungen zwischen Membernamen und Member-IDs sowie zwischen Parameternamen und Parameter-IDs.</summary>
      <param name="rgszNames">Ein Array von zuzuordnenden Namen. </param>
      <param name="cNames">Die Anzahl der zuzuordnenden Namen. </param>
      <param name="pMemId">Diese Methode gibt einen Verweis auf ein Array mit Namenszuordnungen zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Ruft den Implementierungstyp der benutzerdefinierten Daten ab.</summary>
      <param name="index">Der Index des Implementierungstyps für die benutzerdefinierten Daten. </param>
      <param name="guid">Die für die Bezeichnung der Daten verwendete GUID. </param>
      <param name="pVarVal">Diese Methode gibt ein Object zurück, das angibt, wo die abgerufenen Daten abzulegen sind.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Ruft den <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" />-Wert für eine einzelne implementierte Schnittstelle oder Basisschnittstelle in einer Typenbeschreibung ab.</summary>
      <param name="index">Der Index der implementierten Schnittstelle bzw. Basisschnittstelle. </param>
      <param name="pImplTypeFlags">Diese Methode gibt einen Verweis auf die IMPLTYPEFLAGS-Enumeration zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetMops(System.Int32,System.String@)">
      <summary>Ruft Marshallinformationen ab.</summary>
      <param name="memid">Die Member-ID, die angibt, welche Marshallinformationen erforderlich sind. </param>
      <param name="pBstrMops">Diese Methode gibt einen Verweis auf die opcode-Zeichenfolge für das Marshallen der Felder in der Struktur zurück, die von der Typbeschreibung beschrieben wird, auf die verwiesen wird. Wenn jedoch keine zurückzugebenden Informationen vorhanden sind, wird null zurückgegeben.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Ruft die Variable mit der angegebenen Member-ID (oder den Namen der Eigenschaft bzw. Methode und deren Parameter) ab, die der angegebenen Funktions-ID entsprechen.</summary>
      <param name="memid">ID des Members, dessen Name bzw. Namen zurückgegeben werden sollen. </param>
      <param name="rgBstrNames">Diese Methode gibt den bzw. die dem Member zugeordneten Namen zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="cMaxNames">Die Länge des <paramref name="rgBstrNames" />-Arrays. </param>
      <param name="pcNames">Diese Methode gibt die Anzahl der Namen im <paramref name="rgBstrNames" />-Array zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetParamCustData(System.Int32,System.Int32,System.Guid@,System.Object@)">
      <summary>Ruft den angegebenen benutzerdefinierten Datenparameter ab.</summary>
      <param name="indexFunc">Der Index der Funktion, für die die benutzerdefinierten Daten abgerufen werden. </param>
      <param name="indexParam">Der Index des Parameters dieser Funktion, wofür die benutzerdefinierten Daten abgerufen werden. </param>
      <param name="guid">Die für die Bezeichnung der Daten verwendete GUID. </param>
      <param name="pVarVal">Diese Methode gibt ein Object zurück, das angibt, wo die abgerufenen Daten abzulegen sind.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Ruft die Typbeschreibungen ab, auf die verwiesen wird, wenn eine Typbeschreibung auf andere Typbeschreibungen verweist.</summary>
      <param name="hRef">Ein Handle für die zurückzugebende Typbeschreibung, auf die verwiesen wird. </param>
      <param name="ppTI">Diese Methode gibt die Typbeschreibung zurück, auf die verwiesen wird.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Ruft die Typbeschreibung der implementierten Schnittstellentypen ab, wenn eine Typbeschreibung eine COM-Klasse beschreibt.</summary>
      <param name="index">Der Index des implementierten Typs, dessen Handle zurückgegeben wird. </param>
      <param name="href">Diese Methode gibt einen Verweis auf ein Handle für die implementierte Schnittstelle zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeAttr(System.IntPtr@)">
      <summary>Ruft eine <see cref="T:System.Runtime.InteropServices.TYPEATTR" />-Struktur mit den Attributen der Typenbeschreibung ab.</summary>
      <param name="ppTypeAttr">Diese Methode gibt einen Verweis auf die Struktur zurück, die die Attribute dieser Typbeschreibung enthält.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Ruft die ITypeComp-Schnittstelle für die Typenbeschreibung ab, sodass ein Clientcompiler eine Bindung an die Member der Typenbeschreibung vornehmen kann.</summary>
      <param name="ppTComp">Diese Methode gibt einen Verweis auf ITypeComp der enthaltenden Typbibliothek zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeFlags(System.Int32@)">
      <summary>Gibt die Typflags zurück, ohne Reservierungen vorzunehmen.Von dieser Methode wird ein DWORD-Typflag zurückgegeben, wodurch die Typflags erweitert werden, ohne dass das TYPEATTR (Typattribut) zunimmt.</summary>
      <param name="pTypeFlags">Diese Methode gibt einen DWORD-Verweis auf ein TYPEFLAG zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeKind(System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Gibt schnell die TYPEKIND-Enumeration zurück, ohne Reservierungen vorzunehmen.</summary>
      <param name="pTypeKind">Diese Methode gibt einen Verweis auf eine TYPEKIND-Enumeration zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Ruft die Variable für die benutzerdefinierten Daten ab.</summary>
      <param name="index">Der Index der Variablen, für die die benutzerdefinierten Daten abgerufen werden. </param>
      <param name="guid">Die für die Bezeichnung der Daten verwendete GUID. </param>
      <param name="pVarVal">Diese Methode gibt ein Object zurück, das angibt, wo die abgerufenen Daten abzulegen sind.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Ruft eine VARDESC-Struktur ab, die die angegebene Variable beschreibt.</summary>
      <param name="index">Der Index der zurückzugebenden Variablenbeschreibung. </param>
      <param name="ppVarDesc">Diese Methode gibt einen Verweis auf die VARDESC-Struktur zurück, die die angegebene Variable beschreibt.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarIndexOfMemId(System.Int32,System.Int32@)">
      <summary>Bindet an einen bestimmten Member aufgrund einer bekannten DISPID, wobei der Membername nicht bekannt ist (z. B. beim Binden an einen Standardmember).</summary>
      <param name="memid">Der Memberbezeichner. </param>
      <param name="pVarIndex">Diese Methode gibt einen Index von <paramref name="memid" /> zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Ruft eine Methode auf bzw. greift auf die Eigenschaft eines Objekts zu, die bzw. das die von der Typenbeschreibung beschriebene Schnittstelle implementiert.</summary>
      <param name="pvInstance">Ein Verweis auf die von dieser Typbeschreibung beschriebene Schnittstelle. </param>
      <param name="memid">Bezeichner des Schnittstellenmembers. </param>
      <param name="wFlags">Flags, die den Kontext des Invoke-Aufrufs beschreiben. </param>
      <param name="pDispParams">Ein Verweis auf eine Struktur, die ein Array von Argumenten und ein Array von DISPIDs für benannte Argumente enthält und die Anzahl der Elemente in jedem Array zählt. </param>
      <param name="pVarResult">Ein Verweis auf den gewünschten Speicherort für das Ergebnis.Wenn <paramref name="wFlags" />DISPATCH_PROPERTYPUT oder DISPATCH_PROPERTYPUTREF angibt, wird <paramref name="pVarResult" /> ignoriert.Wenn kein Ergebnis erforderlich ist, wird dies auf null festgelegt.</param>
      <param name="pExcepInfo">Ein Zeiger auf eine Struktur für Ausnahmeinformationen, die nur bei Rückgabe von DISP_E_EXCEPTION aufgefüllt wird. </param>
      <param name="puArgErr">Wenn InvokeDISP_E_TYPEMISMATCH zurückgibt, gibt <paramref name="puArgErr" /> den Index des Arguments an, das nicht den korrekten Typ besitzt.Wenn mehrere Argumente einen Fehler zurückgeben, gibt <paramref name="puArgErr" /> nur das erste Argument mit einem Fehler an.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseFuncDesc(System.IntPtr)">
      <summary>Gibt eine <see cref="T:System.Runtime.InteropServices.FUNCDESC" />-Struktur frei, die zuvor von der <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />-Methode zurückgegeben wurde.</summary>
      <param name="pFuncDesc">Ein Verweis auf die freizugebende FUNCDESC-Struktur. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseTypeAttr(System.IntPtr)">
      <summary>Gibt eine <see cref="T:System.Runtime.InteropServices.TYPEATTR" />-Struktur frei, die zuvor von der <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />-Methode zurückgegeben wurde.</summary>
      <param name="pTypeAttr">Ein Verweis auf die freizugebende TYPEATTR-Struktur. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseVarDesc(System.IntPtr)">
      <summary>Gibt eine VARDESC-Struktur frei, die zuvor von der <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />-Methode zurückgegeben wurde.</summary>
      <param name="pVarDesc">Ein Verweis auf die freizugebende VARDESC-Struktur. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib">
      <summary>Stellt die verwaltete Definition der ITypeLib-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Sucht in einer Typbibliothek nach dem Vorkommen einer Typenbeschreibung.</summary>
      <param name="szNameBuf">Der zu suchende Name.Dies ist ein In/Out-Parameter.</param>
      <param name="lHashVal">Ein von der LHashValOfNameSys-Funktion berechneter Hashwert zum Beschleunigen der Suche.Wenn <paramref name="lHashVal" /> 0 ist, wird ein Wert berechnet.</param>
      <param name="ppTInfo">Diese Methode gibt ein Array von Zeigern auf die Typenbeschreibungen zurück, die den in <paramref name="szNameBuf" /> angegebenen Namen enthalten.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="rgMemId">Ein Array von MEMBERIDs der gefundenen Elemente. <paramref name="rgMemId" />[i] ist die MEMBERID, die eine Indizierung in der von <paramref name="ppTInfo" />[i] angegebenen Typenbeschreibung vornimmt.Kann nicht null sein.</param>
      <param name="pcFound">Zeigt bei einem Eintrag die Anzahl der zu suchenden Instanzen an.Das erste Auftreten wird z. B. durch Aufrufen von <paramref name="pcFound" /> = 1 gesucht.Die Suche hält an, sobald eine Instanz gefunden wird.Gibt bei Beendigung der Suche die Anzahl der gefundenen Instanzen an.Wenn der in-Wert und der out-Wert von <paramref name="pcFound" /> identisch sind, ist der Name möglicherweise in mehreren Typbeschreibungen enthalten.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Ruft die Dokumentationszeichenfolge der Bibliothek, den vollständigen Namen und Pfad der Hilfedatei sowie den Kontextbezeichner des Hilfethemas für die Bibliothek in der Hilfedatei ab.</summary>
      <param name="index">Der Index der Typenbeschreibung, deren Dokumentation zurückgegeben werden soll. </param>
      <param name="strName">Diese Methode gibt eine Zeichenfolge zurück, die den Namen des angegebenen Elements darstellt.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="strDocString">Diese Methode gibt eine Zeichenfolge zurück, die die Dokumentationszeichenfolge des angegebenen Elements darstellt.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="dwHelpContext">Diese Methode gibt den dem angegebenen Element zugeordneten Kontextbezeichner der Hilfe zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="strHelpFile">Diese Methode gibt eine Zeichenfolge zurück, die den vollqualifizierten Namen der Hilfedatei darstellt.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>Ruft die Struktur mit den Attributen der Bibliothek ab.</summary>
      <param name="ppTLibAttr">Diese Methode gibt eine Struktur zurück, die die Attribute der Bibliothek enthält.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Dadurch kann ein Clientcompiler Typen, Variablen, Konstanten und globale Funktionen einer Bibliothek binden.</summary>
      <param name="ppTComp">Diese Methode gibt eine Instanz einer ITypeComp-Instanz für diesen ITypeLib zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Ruft die in der Bibliothek angegebene Typenbeschreibung ab.</summary>
      <param name="index">Der Index der zurückzugebenden ITypeInfo-Schnittstelle. </param>
      <param name="ppTI">Diese Methode gibt eine ITypeInfo zurück, die den Typ beschreibt, auf den mit dem <paramref name="index" /> verwiesen wird.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoCount">
      <summary>Gibt die Anzahl von Typenbeschreibungen in der Typbibliothek zurück.</summary>
      <returns>Die Anzahl von Typenbeschreibungen in der Typbibliothek.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Ruft die Typenbeschreibung ab, die der angegebenen GUID entspricht.</summary>
      <param name="guid">Die IID für die Schnittstelle oder CLSID für die Klasse, deren Typinformationen angefordert sind. </param>
      <param name="ppTInfo">Diese Methode gibt die angeforderte ITypeInfo-Schnittstelle zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Ruft den Typ einer Typenbeschreibung ab.</summary>
      <param name="index">Der Index der Typenbeschreibung in der Typbibliothek. </param>
      <param name="pTKind">Diese Methode gibt einen Verweis auf die TYPEKIND-Enumeration für die Typenbeschreibung zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.IsName(System.String,System.Int32)">
      <summary>Gibt an, ob in einer übergebenen Zeichenfolge der Name eines in der Bibliothek beschriebenen Typs oder Members enthalten ist.</summary>
      <returns>true, wenn <paramref name="szNameBuf" /> in der Typbibliothek gefunden wurde, andernfalls false.</returns>
      <param name="szNameBuf">Die zu testende Zeichenfolge.Dies ist ein In/Out-Parameter.</param>
      <param name="lHashVal">Der Hashwert von <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>Gibt die mithilfe der <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />-Methode die ursprünglich abgerufene <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" />-Struktur frei.</summary>
      <param name="pTLibAttr">Die freizugebende TLIBATTR-Struktur. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib2">
      <summary>Stellt eine verwaltete Definition der ITypeLib2-Schnittstelle bereit.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Sucht in einer Typbibliothek nach dem Vorkommen einer Typenbeschreibung.</summary>
      <param name="szNameBuf">Der zu suchende Name. </param>
      <param name="lHashVal">Ein von der LHashValOfNameSys-Funktion berechneter Hashwert zum Beschleunigen der Suche.Wenn <paramref name="lHashVal" /> 0 ist, wird ein Wert berechnet.</param>
      <param name="ppTInfo">Diese Methode gibt ein Array von Zeigern auf die Typenbeschreibungen zurück, die den in <paramref name="szNameBuf" /> angegebenen Namen enthalten.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="rgMemId">Enthält nach dem Beenden dieser Methode ein Array von MEMBERID der gefundenen Elemente. <paramref name="rgMemId" />[i] ist die MEMBERID, die eine Indizierung in der von <paramref name="ppTInfo" />[i] angegebenen Typenbeschreibung vornimmt.Dieser Parameter darf nicht null sein.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pcFound">Zu Beginn ein als Verweis übergebener Wert, der angibt, wie viele Instanzen gesucht werden sollen.Das erste Auftreten wird z. B. durch Aufrufen von <paramref name="pcFound" /> = 1 gesucht.Die Suche hält an, sobald eine Instanz gefunden wird.Gibt bei Beendigung der Suche die Anzahl der gefundenen Instanzen an.Wenn der in-Wert und der out-Wert von <paramref name="pcFound" /> identisch sind, ist der Name möglicherweise in mehreren Typbeschreibungen enthalten.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetAllCustData(System.IntPtr)">
      <summary>Ruft alle benutzerdefinierten Datenelemente für die Bibliothek ab.</summary>
      <param name="pCustData">Ein Zeiger auf CUSTDATA, worin alle benutzerdefinierten Datenelemente enthalten sind. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetCustData(System.Guid@,System.Object@)">
      <summary>Ruft die benutzerdefinierten Daten ab.</summary>
      <param name="guid">Eine als Verweis übergebene <see cref="T:System.Guid" />, mit der die Daten identifiziert werden. </param>
      <param name="pVarVal">Enthält nach dem Beenden dieser Methode ein Objekt, das angibt, wo die abgerufenen Daten abzulegen sind.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Ruft die Dokumentationszeichenfolge der Bibliothek, den vollständigen Namen und Pfad der Hilfedatei sowie den Kontextbezeichner des Hilfethemas für die Bibliothek in der Hilfedatei ab.</summary>
      <param name="index">Ein Index der Typenbeschreibung, deren Dokumentation zurückgegeben werden soll. </param>
      <param name="strName">Enthält nach dem Beenden dieser Methode eine Zeichenfolge, die den Namen des angegebenen Elements angibt.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="strDocString">Enthält nach dem Beenden dieser Methode die Dokumentationszeichenfolge für das angegebene Element.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="dwHelpContext">Diese Methode gibt den dem angegebenen Element zugeordneten Kontextbezeichner der Hilfe zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="strHelpFile">Enthält nach dem Beenden dieser Methode eine Zeichenfolge, die den vollqualifizierten Namen der Hilfedatei angibt.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Ruft die Dokumentationszeichenfolge der Bibliothek, den vollständigen Namen und Pfad der Hilfedatei sowie den zu verwendenden Lokalisierungskontext und die Kontext-ID des Hilfethemas für die Bibliothek in der Hilfedatei ab.</summary>
      <param name="index">Ein Index der Typbeschreibung, deren Dokumentation zurückgegeben soll. Wenn <paramref name="index" /> den Wert -1 hat, wird die Dokumentation der Bibliothek zurückgegeben. </param>
      <param name="pbstrHelpString">Enthält nach dem Beenden dieser Methode einen BSTR, der den Namen des angegebenen Elements angibt.Wenn der Aufrufer den Elementnamen nicht benötigt, kann <paramref name="pbstrHelpString" />null sein.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pdwHelpStringContext">Enthält nach dem Beenden dieser Methode den Lokalisierungskontext der Hilfe.Wenn der Aufrufer den Hilfekontext nicht benötigt, kann <paramref name="pdwHelpStringContext" />null sein.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="pbstrHelpStringDll">Enthält nach dem Beenden dieser Methode einen BSTR, der den vollqualifizierten Namen der Datei angibt, die die für die Hilfedatei verwendete DLL enthält.Wenn der Aufrufer den Dateinamen nicht benötigt, kann <paramref name="pbstrHelpStringDll" />null sein.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibAttr(System.IntPtr@)">
      <summary>Ruft die Struktur mit den Attributen der Bibliothek ab.</summary>
      <param name="ppTLibAttr">Diese Methode gibt eine Struktur zurück, die die Attribute der Bibliothek enthält.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibStatistics(System.IntPtr,System.Int32@)">
      <summary>Gibt Statistiken über eine Typbibliothek zurück, die für die effiziente Größenanpassung von Hashtabellen erforderlich sind.</summary>
      <param name="pcUniqueNames">Ein Zeiger auf eine Anzahl eindeutiger Namen.Wenn der Aufrufer diese Informationen nicht benötigt, kann der Wert auf null festgelegt werden.</param>
      <param name="pcchUniqueNames">Enthält nach dem Beenden dieser Methode einen Zeiger auf eine Änderung in der Anzahl der eindeutigen Namen.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Dadurch kann ein Clientcompiler Typen, Variablen, Konstanten und globale Funktionen einer Bibliothek binden.</summary>
      <param name="ppTComp">Enthält nach dem Beenden dieser Methode eine ITypeComp-Instanz für diese ITypeLib.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Ruft die in der Bibliothek angegebene Typenbeschreibung ab.</summary>
      <param name="index">Ein Index der zurückzugebenden ITypeInfo-Schnittstelle. </param>
      <param name="ppTI">Diese Methode gibt eine ITypeInfo zurück, die den Typ beschreibt, auf den mit dem <paramref name="index" /> verwiesen wird.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoCount">
      <summary>Gibt die Anzahl von Typenbeschreibungen in der Typbibliothek zurück.</summary>
      <returns>Die Anzahl von Typenbeschreibungen in der Typbibliothek.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Ruft die Typenbeschreibung ab, die der angegebenen GUID entspricht.</summary>
      <param name="guid">Die als Verweis übergebene <see cref="T:System.Guid" />, die die IID der CLSID-Schnittstelle der Klasse darstellt, deren Typinformationen angefordert wurden. </param>
      <param name="ppTInfo">Diese Methode gibt die angeforderte ITypeInfo-Schnittstelle zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Ruft den Typ einer Typenbeschreibung ab.</summary>
      <param name="index">Der Index der Typenbeschreibung in der Typbibliothek. </param>
      <param name="pTKind">Diese Methode gibt einen Verweis auf die TYPEKIND-Enumeration für die Typenbeschreibung zurück.Dieser Parameter wird nicht initialisiert übergeben.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.IsName(System.String,System.Int32)">
      <summary>Gibt an, ob in einer übergebenen Zeichenfolge der Name eines in der Bibliothek beschriebenen Typs oder Members enthalten ist.</summary>
      <returns>true, wenn <paramref name="szNameBuf" /> in der Typbibliothek gefunden wurde, andernfalls false.</returns>
      <param name="szNameBuf">Die zu testende Zeichenfolge. </param>
      <param name="lHashVal">Der Hashwert von <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.ReleaseTLibAttr(System.IntPtr)">
      <summary>Gibt die mithilfe der <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />-Methode die ursprünglich abgerufene <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" />-Struktur frei.</summary>
      <param name="pTLibAttr">Die freizugebende TLIBATTR-Struktur. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.LIBFLAGS">
      <summary>Definiert Flags, die Typbibliotheken betreffen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FCONTROL">
      <summary>Die Typbibliothek beschreibt Steuerelemente und sollte nicht in Typbrowsern angezeigt werden, die für nichtvisuelle Objekte bestimmt sind.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHASDISKIMAGE">
      <summary>Die Typbibliothek steht auch dauerhaft auf der Festplatte zur Verfügung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHIDDEN">
      <summary>Die Typbibliothek sollte Benutzern nicht angezeigt werden, obwohl ihre Verwendung nicht eingeschränkt ist.Die Typbibliothek muss von Steuerelementen verwendet werden.Hosts müssen eine neue Typbibliothek erzeugen, die das Steuerelement mit erweiterten Eigenschaften umschließt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FRESTRICTED">
      <summary>Die Typbibliothek unterliegt Beschränkungen und sollte Benutzern nicht angezeigt werden.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMDESC">
      <summary>Enthält Informationen über die Übertragung eines Strukturelements, Parameters oder Rückgabewerts einer Funktion zwischen Prozessen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.lpVarValue">
      <summary>Stellt einen Zeiger auf einen Wert dar, der zwischen Prozessen übergeben wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.wParamFlags">
      <summary>Stellt Bitmaskenwerte dar, die das Strukturelement, den Parameter oder den Rückgabewert beschreiben.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMFLAG">
      <summary>Beschreibt die Übertragung eines Strukturelements, Parameters oder Rückgabewerts einer Funktion zwischen Prozessen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASCUSTDATA">
      <summary>Der Parameter enthält benutzerdefinierte Daten.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASDEFAULT">
      <summary>Für den Parameter sind Standardverhalten definiert.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FIN">
      <summary>Der Parameter übergibt Informationen vom Aufrufer zum Aufgerufenen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FLCID">
      <summary>Der Parameter ist der lokale Bezeichner einer Clientanwendung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOPT">
      <summary>Der Parameter ist optional.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOUT">
      <summary>Der Parameter gibt Informationen vom Aufgerufenen zum Aufrufer zurück.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FRETVAL">
      <summary>Der Parameter ist der Rückgabewert des Members.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_NONE">
      <summary>Es wird nicht angegeben, ob der Parameter Informationen weitergibt oder empfängt.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATDATA">
      <summary>Stellt die verwaltete Definition der STATDATA-Struktur bereit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advf">
      <summary>Stellt den <see cref="T:System.Runtime.InteropServices.ComTypes.ADVF" />-Enumerationswert dar, der bestimmt, wann die Advise-Senke über Änderungen an den Daten benachrichtigt wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advSink">
      <summary>Stellt die <see cref="T:System.Runtime.InteropServices.ComTypes.IAdviseSink" />-Schnittstelle dar, die Änderungsbenachrichtigungen empfängt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.connection">
      <summary>Stellt das Token dar, das die Advise-Verbindung eindeutig identifiziert.Dieses Token wird von der Methode zurückgegeben, die die Advise-Verbindung herstellt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.formatetc">
      <summary>Stellt die <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />-Struktur für die Daten dar, die die Advise-Senke benötigt.Die Advise-Senke empfängt Benachrichtigung über Änderungen an den von dieser <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />-Struktur angegebenen Daten.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATSTG">
      <summary>Enthält statistische Informationen über ein geöffnetes Speicher-, Stream- oder Bytearrayobjekt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.atime">
      <summary>Gibt für diesen Speicher, diesen Stream oder dieses Bytearray den Zeitpunkt des letzten Zugriffs an. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.cbSize">
      <summary>Gibt die Größe des Streams oder Bytearrays in Bytes an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.clsid">
      <summary>Gibt den Klassenbezeichner für das Speicherobjekt an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.ctime">
      <summary>Gibt für diesen Speicher, diesen Stream oder dieses Bytearray den Erstellungszeitpunkt an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfLocksSupported">
      <summary>Gibt die vom Stream oder Bytearray unterstützten Arten der Bereichssperrung an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfMode">
      <summary>Gibt den beim Öffnen des Objekts angegebenen Zugriffsmodus an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfStateBits">
      <summary>Gibt die aktuellen Zustandsbits des Speicherobjekts an (der von der IStorage::SetStateBits-Methode zuletzt festgelegte Wert).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.mtime">
      <summary>Gibt für diesen Speicher, diesen Stream oder dieses Bytearray den Zeitpunkt der letzten Änderung an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.pwcsName">
      <summary>Stellt einen Zeiger auf eine mit NULL endende Zeichenfolge dar, die den Namen des von dieser Struktur beschriebenen Objekts enthält.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.reserved">
      <summary>Für zukünftige Verwendung reserviert.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.type">
      <summary>Gibt den Typ des Speicherobjekts an, wobei es sich um einen der Werte der STGTY-Enumeration handelt.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM">
      <summary>Stellt die verwaltete Definition der STGMEDIUM-Struktur bereit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease">
      <summary>Stellt einen Zeiger auf eine Schnittstelleninstanz dar, die dem sendenden Prozess das Steuern der Freigabe des Speichers ermöglicht, wenn der empfangende Prozess die ReleaseStgMedium-Funktion aufruft.Wenn <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" />null ist, verwendet ReleaseStgMedium Standardprozeduren für das Freigeben von Speicher, andernfalls verwendet ReleaseStgMedium die angegebene IUnknown-Schnittstelle.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.tymed">
      <summary>Gibt den Typ des Speichermediums an.Die Routinen für das Marshalling und Unmarshalling verwenden diesen Wert, um das verwendete Union-Element zu bestimmen.Dieser Wert muss eines der Elemente der <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" />-Enumeration sein.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.unionmember">
      <summary>Stellt ein Handle, eine Zeichenfolge oder einen Schnittstellenzeiger dar, mit dem der empfangende Prozess auf die übertragenen Daten zuzugreifen kann.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.SYSKIND">
      <summary>Bezeichnet die Plattform des Zielbetriebssystems.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_MAC">
      <summary>Das Zielbetriebssystem für die Typbibliothek ist Apple Macintosh.In der Standardeinstellung sind sämtliche Datenfelder an geraden Bytegrenzen ausgerichtet.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN16">
      <summary>Zielbetriebssystem für die Typbibliothek sind 16-Bit-Windows-Systeme.In der Standardeinstellung sind die Datenfelder gepackt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN32">
      <summary>Zielbetriebssystem für die Typbibliothek sind 32-Bit-Windows-Systeme.In der Standardeinstellung sind Datenfelder natürlich ausgerichtet (beispielsweise sind 2-Byte-Ganzzahlen an geraden Bytegrenzen ausgerichtet, 4-Byte-Ganzzahlen an Quad-Word-Grenzen usw.).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN64">
      <summary>Zielbetriebssystem für die Typbibliothek sind 64-Bit-Windows-Systeme.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYMED">
      <summary>Stellt die verwaltete Definition der TYMED-Struktur bereit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ENHMF">
      <summary>Das Speichermedium ist eine erweiterte Metadatei.Wenn der <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" />-Member aus <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />null ist, sollte der Zielprozess für das Löschen der Bitmap DeleteEnhMetaFile verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_FILE">
      <summary>Das Speichermedium ist eine über einen Pfad gekennzeichnete Datenträgerdatei.Wenn der <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" />-Member aus STGMEDIUMnull ist, sollte der Zielprozess für das Löschen der Datei OpenFile verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_GDI">
      <summary>Das Speichermedium ist eine GDI (Graphics Device Interface)-Komponente (HBITMAP).Wenn der <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" />-Member aus <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />null ist, sollte der Zielprozess für das Löschen der Bitmap DeleteObject verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_HGLOBAL">
      <summary>Das Speichermedium ist ein globales Speicherhandle (HGLOBAL).Ordnen Sie das globale Handle dem GMEM_SHARE-Flag zu.Wenn der <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" />-Member aus <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />null ist, sollte der Zielprozess für das Freigeben des Speichers GlobalFree verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTORAGE">
      <summary>Das Speichermedium ist eine durch einen IStorage-Zeiger gekennzeichnete Speicherkomponente.Die Daten sind in den Streams und Speichern dieser IStorage-Instanz enthalten.Wenn der <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" />-Member aus <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /> nicht null ist, sollte der Zielprozess für das Freigeben der Speicherkomponente IStorage::Release verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTREAM">
      <summary>Das Speichermedium ist ein durch einen IStream-Zeiger gekennzeichnetes Streamobjekt.Verwenden Sie zum Lesen der Daten ISequentialStream::Read.Wenn der <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" />-Member aus <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /> nicht null ist, sollte der Zielprozess für das Freigeben der Streamkomponente IStream::Release verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_MFPICT">
      <summary>Das Speichermedium ist eine Metadatei (HMETAFILE).Verwenden Sie für den Zugriff auf die Daten der Metadatei die Windows- oder WIN32-Funktionen.Wenn der <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" />-Member aus <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />null ist, sollte der Zielprozess für das Löschen der Bitmap DeleteMetaFile verwenden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_NULL">
      <summary>Es werden keine Daten übergeben.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEATTR">
      <summary>Enthält Attribute einer UCOMITypeInfo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbAlignment">
      <summary>Gibt die Byteausrichtung für eine Instanz dieses Typs an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeInstance">
      <summary>Die Größe einer Instanz dieses Typs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeVft">
      <summary>Die Größe der Tabelle virtueller Methoden (Virtual Methods Table, VTBL) dieses Typs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cFuncs">
      <summary>Gibt die Anzahl der Funktionen für die von dieser Struktur beschriebene Schnittstelle an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cImplTypes">
      <summary>Gibt die Anzahl der implementierten Schnittstellen für die von dieser Struktur beschriebene Schnittstelle an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cVars">
      <summary>Gibt die Anzahl der Variablen und Datenfelder für die von dieser Struktur beschriebene Schnittstelle an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.dwReserved">
      <summary>Für zukünftige Verwendung reserviert.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.guid">
      <summary>Die GUID der Typinformationen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.idldescType">
      <summary>Die IDL-Attribute (Interface Definition Language) des beschriebenen Typs.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lcid">
      <summary>Gebietsschema der Membernamen und Dokumentationszeichenfolgen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lpstrSchema">
      <summary>Für zukünftige Verwendung reserviert.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.MEMBER_ID_NIL">
      <summary>Eine mit dem <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidConstructor" />-Feld und dem <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidDestructor" />-Feld verwendete Konstante.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidConstructor">
      <summary>ID des Konstruktors oder <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />, wenn nicht vorhanden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidDestructor">
      <summary>ID des Destruktors oder <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />, wenn nicht vorhanden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.tdescAlias">
      <summary>Wenn <see cref="F:System.Runtime.InteropServices.TYPEATTR.typekind" /> == <see cref="F:System.Runtime.InteropServices.TYPEKIND.TKIND_ALIAS" />: Gibt den Typ an, für den dieser Typ einen Alias darstellt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.typekind">
      <summary>Ein <see cref="T:System.Runtime.InteropServices.TYPEKIND" />-Wert, der den Typ beschreibt, der durch diese Informationen beschrieben wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMajorVerNum">
      <summary>Hauptversionsnummer.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMinorVerNum">
      <summary>Nebenversionsnummer.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wTypeFlags">
      <summary>Ein <see cref="T:System.Runtime.InteropServices.TYPEFLAGS" />-Wert, der diese Informationen beschreibt.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEDESC">
      <summary>Beschreibt den Typ einer Variablen, den Rückgabetyp einer Funktion oder den Typ eines Funktionsparameters.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.lpValue">
      <summary>Wenn die Variable VT_SAFEARRAY oder VT_PTR ist, enthält das lpValue-Feld einen Zeiger auf den TYPEDESC, mit dem der Elementtyp angegeben wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.vt">
      <summary>Gibt den Variant-Typ für das durch TYPEDESC beschriebene Element an.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEFLAGS">
      <summary>Definiert die Eigenschaften und Attribute einer Typenbeschreibung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAGGREGATABLE">
      <summary>Die Klasse unterstützt Aggregation.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAPPOBJECT">
      <summary>Ein Typbeschreibung, die ein Application-Objekt beschreibt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCANCREATE">
      <summary>Instanzen des Typs können durch ITypeInfo::CreateInstance erstellt werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCONTROL">
      <summary>Der Typ ist ein Steuerelement, von dem andere Typen abgeleitet werden, und darf Benutzern nicht angezeigt werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDISPATCHABLE">
      <summary>Gibt an, dass die Schnittstelle direkt oder indirekt aus IDispatch abgeleitet ist.Dieses Flag ist berechnet. Für das Flag gibt es keine Objektbeschreibungssprache.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDUAL">
      <summary>Die Schnittstelle stellt sowohl IDispatch als auch VTBL-Bindung bereit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FHIDDEN">
      <summary>Der Typ darf in Browsern nicht angezeigt werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FLICENSED">
      <summary>Der Typ ist lizenziert.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FNONEXTENSIBLE">
      <summary>Die Schnittstelle kann zur Laufzeit keine Member hinzufügen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FOLEAUTOMATION">
      <summary>Die in der Schnittstelle verwendeten Typen sind vollständig automatisierungskompatibel, wobei die Unterstützung von VTBL-Bindung eingeschlossen ist.Durch das Festlegen einer Schnittstelle als dual wird sowohl dieses Flag als auch <see cref="F:System.Runtime.InteropServices.TYPEFLAGS.TYPEFLAG_FDUAL" /> festgelegt.Dieses Flag ist für Dispatchschnittstellen nicht zulässig.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPREDECLID">
      <summary>Der Typ ist vordefiniert.Die Clientanwendung erstellt automatisch eine einzelne Instanz des Objekts, das über dieses Attribut verfügt.Der Name der auf das Objekt zeigenden Variablen ist derselbe wie der Klassenname des Objekts.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPROXY">
      <summary>Gibt an, dass die Schnittstelle eine Proxy-/Stub-Dynamic Link Library verwendet.Dieses Flag gibt an, dass die Registrierung des Proxys für die Typbibliothek nicht aufgehoben werden darf, wenn die Registrierung der Typbibliothek aufgehoben wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREPLACEABLE">
      <summary>Das Objekt unterstützt IConnectionPointWithDefault und verfügt über Standardverhalten.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FRESTRICTED">
      <summary>Der Zugriff darf für Makrosprachen nicht möglich sein.Dieses Flag ist für Typen auf Systemebene oder für Typen bestimmt, die von Typenbrowsern nicht angezeigt werden sollen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREVERSEBIND">
      <summary>Gibt an, dass die Basisschnittstellen vor dem Überprüfen untergeordneter Elemente auf Namensauflösung geprüft werden sollen. Dies stellt die Umkehrung des Standardverhaltens dar.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEKIND">
      <summary>Gibt verschiedene Typen von Daten und Funktionen an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ALIAS">
      <summary>Ein Typ, der einen Alias für einen anderen Typ darstellt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_COCLASS">
      <summary>Eine Gruppe von implementierten Komponentenschnittstellen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_DISPATCH">
      <summary>Eine Gruppe von Methoden und Eigenschaften, auf die über IDispatch::Invoke zugegriffen werden kann.In der Standardeinstellung geben duale Schnittstellen TKIND_DISPATCH zurück.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ENUM">
      <summary>Eine Gruppe von Enumeratoren.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_INTERFACE">
      <summary>Ein Typ, der über rein virtuelle Funktionen verfügt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MAX">
      <summary>Markierung für das Ende der Enumeration.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MODULE">
      <summary>Ein Modul, das nur statische Funktionen und Daten enthalten kann (z. B. eine DLL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_RECORD">
      <summary>Eine Struktur ohne Methoden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_UNION">
      <summary>Eine Gesamtmenge aller Member, die einen Offset von 0 aufweisen.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPELIBATTR">
      <summary>Gibt eine bestimmte Typbibliothek an und stellt Lokalisierungsunterstützung für Membernamen bereit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.guid">
      <summary>Stellt eine global eindeutige Bibliotheks-ID einer Typbibliothek dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.lcid">
      <summary>Stellt eine Gebietsschema-ID einer Typbibliothek dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.syskind">
      <summary>Stellt die Zielhardwareplattform für eine Typbibliothek dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wLibFlags">
      <summary>Stellt Bibliotheksflags dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMajorVerNum">
      <summary>Stellt die Hauptversionsnummer einer Typbibliothek dar.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMinorVerNum">
      <summary>Stellt die Nebenversionsnummer einer Typbibliothek dar.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC">
      <summary>Beschreibt eine Variable, eine Konstante oder einen Datenmember.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.desc">
      <summary>Enthält Informationen über eine Variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.elemdescVar">
      <summary>Der Typ des Containersteuerelements.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.lpstrSchema">
      <summary>Dieses Feld ist für eine spätere Verwendung vorgesehen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.memid">
      <summary>Gibt die Member-ID einer Variablen an.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.varkind">
      <summary>Definiert, wie eine Variable gemarshallt wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.wVarFlags">
      <summary>Definiert die Eigenschaften einer Variablen.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION">
      <summary>Enthält Informationen über eine Variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.lpvarValue">
      <summary>Beschreibt eine symbolische Konstante.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.oInst">
      <summary>Gibt den Offset dieser Variablen in der Instanz an.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARFLAGS">
      <summary>Gibt die Konstanten an, die die Eigenschaften einer Variablen definieren.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FBINDABLE">
      <summary>Die Variable unterstützt die Datenbindung.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTBIND">
      <summary>Die Variable ist die einzelne Eigenschaft, die das Objekt am besten darstellt.Nur eine Variable in den Typinformationen kann über dieses Attribut verfügen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTCOLLELEM">
      <summary>Lässt eine Optimierung zu, bei der der Compiler im Typ "abc" nach dem Member "xyz" sucht.Wenn ein solcher Member gefunden und als Accessorfunktion für ein Element der Standardauflistung markiert wird, wird ein Aufruf dieser Memberfunktion generiert.Zulässig für Member an Dispatchschnittstellen und Schnittstellen, nicht zulässig für Module.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDISPLAYBIND">
      <summary>Die dem Benutzer als bindungsfähig angezeigte Variable.<see cref="F:System.Runtime.InteropServices.VARFLAGS.VARFLAG_FBINDABLE" /> muss auch festgelegt werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FHIDDEN">
      <summary>Die Variable darf dem Benutzer nicht in einem Browser angezeigt werden, obwohl sie vorhanden und bindbar ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FIMMEDIATEBIND">
      <summary>Die Variable wird als einzelne Eigenschaft zugeordnet, die gebunden werden kann.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FNONBROWSABLE">
      <summary>Die Variable wird in einem Objektkatalog, jedoch nicht in einem Eigenschaftenbrowser angezeigt.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREADONLY">
      <summary>Zuweisungen zur Variablen sollten nicht zulässig sein.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREPLACEABLE">
      <summary>Markiert die Schnittstelle als Schnittstelle mit Standardverhalten.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREQUESTEDIT">
      <summary>Bei Festlegung hat jeder Versuch einer direkten Änderung der Eigenschaft einen Aufruf von IPropertyNotifySink::OnRequestEdit zur Folge.Die Implementierung von OnRequestEdit bestimmt, ob die Änderung angenommen wird.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FRESTRICTED">
      <summary>Der Zugriff auf die Variable durch Makrosprachen darf nicht möglich sein.Dieses Flag ist für Variablen auf Systemebene oder für Variablen bestimmt, die von Typbrowsern nicht angezeigt werden sollen.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FSOURCE">
      <summary>Die Variable gibt ein Objekt zurück, das eine Ereignisquelle ist.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FUIDEFAULT">
      <summary>Die Variable ist die Standardanzeige auf der Benutzeroberfläche.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARKIND">
      <summary>Definiert den Typ der Variable.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_CONST">
      <summary>Die VARDESC-Struktur beschreibt eine symbolische Konstante.Es gibt keinen ihr zugeordneten Arbeitsspeicher.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_DISPATCH">
      <summary>Auf die Variable kann nur anhand von IDispatch::Invoke zugegriffen werden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_PERINSTANCE">
      <summary>Die Variable ist ein Feld oder ein Member des Typs.Sie ist bei einem festen Offset innerhalb jeder Instanz des Typs vorhanden.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_STATIC">
      <summary>Es gibt nur eine Instanz der Variable.</summary>
    </member>
  </members>
</doc>