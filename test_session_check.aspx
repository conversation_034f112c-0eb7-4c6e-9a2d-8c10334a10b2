<%@ Page Language="C#" AutoEventWireup="true" CodeFile="test_session_check.aspx.cs" Inherits="test_session_check" %>

<!DOCTYPE html>
<html>

<head runat="server">
	<title>Session 檢查器</title>
	<style>
		.container {
			max-width: 600px;
			margin: 20px auto;
			padding: 20px;
		}

		.success {
			color: green;
			font-weight: bold;
		}

		.error {
			color: red;
			font-weight: bold;
		}

		.info {
			background: #e7f3ff;
			padding: 10px;
			margin: 10px 0;
			border-radius: 5px;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<div class="container">
			<h1>🔍 Session 狀態檢查器</h1>

			<div class="info">
				<strong>此頁面用於檢查當前 Session 狀態</strong><br />
				會顯示所有相關的 Session 變數值和權限狀態。
			</div>

			<asp:Button ID="btnRefresh" runat="server" Text="🔄 重新整理" OnClick="btnRefresh_Click" />
			<asp:Button ID="btnSetupSession" runat="server" Text="⚙️ 設定測試 Session" OnClick="btnSetupSession_Click" />
			<asp:Button ID="btnClearSession" runat="server" Text="🗑️ 清除 Session" OnClick="btnClearSession_Click" />

			<hr />

			<asp:Label ID="lblSessionInfo" runat="server" Text=""></asp:Label>

			<hr />

			<h3>🔗 快速連結：</h3>
			<a href="test_login.aspx" target="_blank">🔐 測試登入頁面</a><br />
			<a href="test_main_functionality.aspx" target="_blank">🧪 測試主功能頁面</a><br />
			<a href="Analyze_recowish01.aspx" target="_blank">📊 主分析頁面</a><br />
			<a href="debug_analyze.aspx" target="_blank">🐛 調試分析頁面</a>
		</div>
	</form>
</body>

</html>