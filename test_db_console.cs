using System;
using System.Data;
using System.Data.SqlClient;

class Program
{
	static void Main()
	{
		string connectionString = "Server=192.168.2.61;uid=feuisic2;pwd=**************************;Database=school;Connection Timeout=30;";

		Console.WriteLine("=== 資料庫連接測試 ===");

		try
		{
			using (SqlConnection conn = new SqlConnection(connectionString))
			{
				Console.WriteLine("🔗 嘗試連接到資料庫...");
				conn.Open();
				Console.WriteLine("✅ 資料庫連接成功！");
				Console.WriteLine($"📊 資料庫名稱: {conn.Database}");
				Console.WriteLine($"🖥️ 伺服器版本: {conn.ServerVersion}");
				Console.WriteLine($"⏱️ 連接逾時: {conn.ConnectionTimeout} 秒");

				Console.WriteLine("\n=== 檢查視圖存在性 ===");
				string[] viewNames = { "V_recowish1", "V_recowish3", "V_recowish5" };
				string[] descriptions = { "日間部", "碩士班", "進修部" };

				for (int i = 0; i < viewNames.Length; i++)
				{
					string checkViewSql = $@"
                        SELECT COUNT(*) 
                        FROM INFORMATION_SCHEMA.VIEWS 
                        WHERE TABLE_NAME = '{viewNames[i]}' AND TABLE_SCHEMA = 'dbo'";

					using (SqlCommand cmd = new SqlCommand(checkViewSql, conn))
					{
						int viewExists = (int)cmd.ExecuteScalar();

						if (viewExists > 0)
						{
							// 檢查視圖是否有數據
							string countSql = $"SELECT COUNT(*) FROM [dbo].[{viewNames[i]}]";
							using (SqlCommand countCmd = new SqlCommand(countSql, conn))
							{
								try
								{
									int rowCount = (int)countCmd.ExecuteScalar();
									Console.WriteLine($"✅ {viewNames[i]} ({descriptions[i]}): 存在，共 {rowCount} 筆記錄");

									// 顯示前3筆記錄的結構
									if (rowCount > 0)
									{
										string sampleSql = $"SELECT TOP 3 * FROM [dbo].[{viewNames[i]}]";
										using (SqlCommand sampleCmd = new SqlCommand(sampleSql, conn))
										{
											using (SqlDataReader reader = sampleCmd.ExecuteReader())
											{
												Console.WriteLine($"   📋 {viewNames[i]} 欄位結構:");
												for (int col = 0; col < reader.FieldCount; col++)
												{
													Console.WriteLine($"     • {reader.GetName(col)} ({reader.GetFieldType(col).Name})");
												}
											}
										}
									}
								}
								catch (Exception ex)
								{
									Console.WriteLine($"⚠️  {viewNames[i]} ({descriptions[i]}): 存在，但查詢數據時出錯: {ex.Message}");
								}
							}
						}
						else
						{
							Console.WriteLine($"❌ {viewNames[i]} ({descriptions[i]}): 不存在");
						}
					}
				}

				Console.WriteLine("\n=== 測試實際查詢 ===");
				string testSql = @"SELECT TOP 5 [學制], [科系], [實名制人數], [報到人數], [繳交畢業證書人數], [繳費人數]
                                  FROM [school].[dbo].[V_recowish1]";

				Console.WriteLine("🔍 執行測試查詢: " + testSql);

				using (SqlCommand cmd = new SqlCommand(testSql, conn))
				{
					using (SqlDataAdapter da = new SqlDataAdapter(cmd))
					{
						DataTable dt = new DataTable();
						da.Fill(dt);

						Console.WriteLine($"✅ 查詢成功，獲得 {dt.Rows.Count} 筆記錄");

						if (dt.Rows.Count > 0)
						{
							Console.WriteLine("\n📊 範例數據:");
							for (int i = 0; i < Math.Min(3, dt.Rows.Count); i++)
							{
								DataRow row = dt.Rows[i];
								Console.WriteLine($"   [{i + 1}] 學制: {row[0]}, 科系: {row[1]}, 實名制: {row[2]}, 報到: {row[3]}, 畢業證書: {row[4]}, 繳費: {row[5]}");
							}
						}
					}
				}
			}
		}
		catch (Exception ex)
		{
			Console.WriteLine($"❌ 錯誤: {ex.Message}");
			Console.WriteLine($"📝 詳細錯誤: {ex.ToString()}");
		}

		Console.WriteLine("\n按任意鍵繼續...");
		Console.ReadKey();
	}
}
