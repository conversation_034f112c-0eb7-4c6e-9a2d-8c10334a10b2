using System;
using System.Web.UI;

public partial class test_login : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			lblResult.Text = "✅ 測試登入頁面載入完成";
			UpdateSessionDisplay();
		}
	}

	protected void btnSetupAdmin_Click(object sender, EventArgs e)
	{
		try
		{
			// 設定管理者權限所需的 Session 變數
			Session["tech_no"] = "TEST001";           // 測試用戶代號
			Session["user"] = "測試管理者";              // 使用者姓名
			Session["master_single"] = "3";           // 管理者權限
			Session["office"] = "TEST";               // 單位代碼
			Session["ysem"] = "113";                  // 學年期 (如果需要)

			lblResult.Text = "✅ 管理者 Session 設定完成！<br/>" +
						   "• tech_no: " + Session["tech_no"] + "<br/>" +
						   "• user: " + Session["user"] + "<br/>" +
						   "• master_single: " + Session["master_single"] + "<br/>" +
						   "• office: " + Session["office"];
			lblResult.CssClass = "success";

			// 更新 Session 顯示
			UpdateSessionDisplay();
		}
		catch (Exception ex)
		{
			lblResult.Text = "❌ 設定 Session 時發生錯誤: " + ex.Message;
			lblResult.CssClass = "error";
		}
	}

	protected void btnCheckSession_Click(object sender, EventArgs e)
	{
		UpdateSessionDisplay();
	}

	protected void btnClearSession_Click(object sender, EventArgs e)
	{
		try
		{
			Session.Clear();
			lblResult.Text = "🗑️ 所有 Session 已清除";
			lblResult.CssClass = "info";
			UpdateSessionDisplay();
		}
		catch (Exception ex)
		{
			lblResult.Text = "❌ 清除 Session 時發生錯誤: " + ex.Message;
			lblResult.CssClass = "error";
		}
	}

	private void UpdateSessionDisplay()
	{
		string sessionInfo = "<strong>📊 當前 Session 狀態：</strong><br/>";

		sessionInfo += "• tech_no: " + (Session["tech_no"] != null ? Session["tech_no"].ToString() : "❌ 未設定") + "<br/>";
		sessionInfo += "• user: " + (Session["user"] != null ? Session["user"].ToString() : "❌ 未設定") + "<br/>";
		sessionInfo += "• master_single: " + (Session["master_single"] != null ? Session["master_single"].ToString() : "❌ 未設定") + "<br/>";
		sessionInfo += "• office: " + (Session["office"] != null ? Session["office"].ToString() : "❌ 未設定") + "<br/>";

		// 檢查權限狀態
		bool hasValidAuth = Session["tech_no"] != null && Session["master_single"] != null && Session["master_single"].ToString() == "3";

		if (hasValidAuth)
		{
			sessionInfo += "<br/>✅ <strong>權限檢查：可以存取主分析頁面</strong>";
		}
		else
		{
			sessionInfo += "<br/>❌ <strong>權限檢查：無法存取主分析頁面</strong>";
		}

		lblSessionStatus.Text = sessionInfo;
	}
}
