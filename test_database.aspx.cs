using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class test_database : System.Web.UI.Page
{
	dbconnection db = new dbconnection();
	SqlCommand cmd = new SqlCommand();
	SqlDataAdapter da = new SqlDataAdapter();
	DataSet ds = new DataSet();

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			lblResults.Text = "🚀 資料庫診斷工具已就緒，請選擇測試項目。";
		}
	}

	protected void TestConnection_Click(object sender, EventArgs e)
	{
		StringBuilder result = new StringBuilder();
		result.AppendLine("🔗 <strong>資料庫連接測試</strong><br/>");

		try
		{
			result.AppendLine($"📍 連接字串: {GetConnectionStringInfo()}<br/>");

			if (db.conn.State != ConnectionState.Open)
				db.conn.Open();

			result.AppendLine("✅ <span class='success'>資料庫連接成功！</span><br/>");
			result.AppendLine($"📊 資料庫名稱: {db.conn.Database}<br/>");
			result.AppendLine($"🖥️ 伺服器版本: {db.conn.ServerVersion}<br/>");
			result.AppendLine($"⏱️ 連接逾時: {db.conn.ConnectionTimeout} 秒<br/>");

			db.conn.Close();
		}
		catch (Exception ex)
		{
			result.AppendLine($"❌ <span class='error'>資料庫連接失敗: {ex.Message}</span><br/>");
			result.AppendLine($"📝 詳細錯誤: {ex.ToString()}<br/>");
		}

		lblResults.Text = result.ToString();
	}

	protected void TestViews_Click(object sender, EventArgs e)
	{
		StringBuilder result = new StringBuilder();
		result.AppendLine("📊 <strong>視圖存在性測試</strong><br/>");

		string[] viewNames = { "V_recowish1", "V_recowish3", "V_recowish5" };
		string[] descriptions = { "日間部數據", "碩士班數據", "進修部數據" };

		try
		{
			db.conn.Open();

			for (int i = 0; i < viewNames.Length; i++)
			{
				string checkViewSql = $@"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.VIEWS 
                    WHERE TABLE_NAME = '{viewNames[i]}' AND TABLE_SCHEMA = 'dbo'";

				cmd.Connection = db.conn;
				cmd.CommandText = checkViewSql;

				int viewExists = (int)cmd.ExecuteScalar();

				if (viewExists > 0)
				{
					// 檢查視圖是否有數據
					string countSql = $"SELECT COUNT(*) FROM [dbo].[{viewNames[i]}]";
					cmd.CommandText = countSql;
					int rowCount = (int)cmd.ExecuteScalar();

					result.AppendLine($"✅ {viewNames[i]} ({descriptions[i]}): 存在，共 {rowCount} 筆記錄<br/>");
				}
				else
				{
					result.AppendLine($"❌ <span class='error'>{viewNames[i]} ({descriptions[i]}): 不存在</span><br/>");
				}
			}

			db.conn.Close();
		}
		catch (Exception ex)
		{
			result.AppendLine($"❌ <span class='error'>視圖測試失敗: {ex.Message}</span><br/>");
		}

		lblResults.Text = result.ToString();
	}

	protected void TestData_Click(object sender, EventArgs e)
	{
		StringBuilder result = new StringBuilder();
		result.AppendLine("📋 <strong>數據查詢測試</strong><br/>");

		try
		{
			// 測試日間部數據查詢
			string sqlStr = @"SELECT TOP 5 [學制], [科系], [實名制人數], [報到人數], [繳交畢業證書人數], [繳費人數]
                             FROM [school].[dbo].[V_recowish1]";

			cmd.Connection = db.conn;
			cmd.CommandText = sqlStr;
			da.SelectCommand = cmd;
			da.Fill(ds, "test");

			DataTable testData = ds.Tables["test"];

			if (testData.Rows.Count > 0)
			{
				result.AppendLine($"✅ <span class='success'>成功查詢到 {testData.Rows.Count} 筆日間部數據</span><br/>");
				result.AppendLine("📊 <strong>欄位結構:</strong><br/>");

				foreach (DataColumn col in testData.Columns)
				{
					result.AppendLine($"&nbsp;&nbsp;• {col.ColumnName} ({col.DataType.Name})<br/>");
				}

				result.AppendLine("<br/>📋 <strong>範例數據 (前5筆):</strong><br/>");
				gvPreview.DataSource = testData;
				gvPreview.DataBind();
			}
			else
			{
				result.AppendLine("⚠️ <span class='info'>查詢成功但沒有數據</span><br/>");
				gvPreview.DataSource = null;
				gvPreview.DataBind();
			}
		}
		catch (Exception ex)
		{
			result.AppendLine($"❌ <span class='error'>數據查詢失敗: {ex.Message}</span><br/>");
			result.AppendLine($"📝 SQL錯誤詳細: {ex.ToString()}<br/>");
		}

		lblResults.Text = result.ToString();
	}

	protected void TestAll_Click(object sender, EventArgs e)
	{
		StringBuilder result = new StringBuilder();
		result.AppendLine("🧪 <strong>執行全部測試</strong><br/><hr/>");

		// 1. 連接測試
		TestConnectionInternal(result);
		result.AppendLine("<hr/>");

		// 2. 視圖測試
		TestViewsInternal(result);
		result.AppendLine("<hr/>");

		// 3. 數據測試
		TestDataInternal(result);

		lblResults.Text = result.ToString();
	}

	private void TestConnectionInternal(StringBuilder result)
	{
		result.AppendLine("🔗 <strong>1. 資料庫連接測試</strong><br/>");

		try
		{
			if (db.conn.State != ConnectionState.Open)
				db.conn.Open();

			result.AppendLine("✅ 資料庫連接成功<br/>");
			result.AppendLine($"📊 資料庫: {db.conn.Database}<br/>");

			db.conn.Close();
		}
		catch (Exception ex)
		{
			result.AppendLine($"❌ <span class='error'>連接失敗: {ex.Message}</span><br/>");
		}
	}

	private void TestViewsInternal(StringBuilder result)
	{
		result.AppendLine("📊 <strong>2. 視圖存在性測試</strong><br/>");

		string[] viewNames = { "V_recowish1", "V_recowish3", "V_recowish5" };

		try
		{
			db.conn.Open();

			foreach (string viewName in viewNames)
			{
				string checkSql = $"SELECT COUNT(*) FROM [dbo].[{viewName}]";
				cmd.Connection = db.conn;
				cmd.CommandText = checkSql;

				int count = (int)cmd.ExecuteScalar();
				result.AppendLine($"✅ {viewName}: {count} 筆記錄<br/>");
			}

			db.conn.Close();
		}
		catch (Exception ex)
		{
			result.AppendLine($"❌ <span class='error'>視圖測試失敗: {ex.Message}</span><br/>");
		}
	}

	private void TestDataInternal(StringBuilder result)
	{
		result.AppendLine("📋 <strong>3. 數據查詢測試</strong><br/>");

		try
		{
			string sqlStr = @"SELECT TOP 3 [學制], [科系], [實名制人數], [報到人數] 
                             FROM [school].[dbo].[V_recowish1]";

			DataSet testDs = new DataSet();
			SqlDataAdapter testDa = new SqlDataAdapter();
			SqlCommand testCmd = new SqlCommand();

			testCmd.Connection = db.conn;
			testCmd.CommandText = sqlStr;
			testDa.SelectCommand = testCmd;
			testDa.Fill(testDs, "test");

			DataTable testData = testDs.Tables["test"];
			result.AppendLine($"✅ 查詢成功，獲得 {testData.Rows.Count} 筆數據<br/>");

			gvPreview.DataSource = testData;
			gvPreview.DataBind();
		}
		catch (Exception ex)
		{
			result.AppendLine($"❌ <span class='error'>查詢失敗: {ex.Message}</span><br/>");
		}
	}

	private string GetConnectionStringInfo()
	{
		try
		{
			string connStr = db.conn.ConnectionString;
			// 隱藏密碼
			if (connStr.Contains("pwd="))
			{
				int pwdStart = connStr.IndexOf("pwd=") + 4;
				int pwdEnd = connStr.IndexOf(";", pwdStart);
				if (pwdEnd == -1) pwdEnd = connStr.Length;

				string hiddenPassword = new string('*', pwdEnd - pwdStart);
				connStr = connStr.Substring(0, pwdStart) + hiddenPassword + connStr.Substring(pwdEnd);
			}
			return connStr;
		}
		catch
		{
			return "無法獲取連接字串資訊";
		}
	}
}
