﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices</name>
  </assembly>
  <members>
    <member name="T:System.DataMisalignedException">
      <summary>L'eccezione generata quando viene eseguita la lettura o la scrittura di un'unità di dati da o in un indirizzo che non è un multiplo delle dimensioni dei dati.La classe non può essere ereditata.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DataMisalignedException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.DataMisalignedException" />. </summary>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.DataMisalignedException" /> utilizzando un messaggio di errore specificato.</summary>
      <param name="message">Oggetto <see cref="T:System.String" /> che descrive l'errore.Il contenuto di <paramref name="message" /> deve essere facilmente comprensibile.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.DataMisalignedException" /> utilizzando il messaggio di errore specificato e l'eccezione sottostante.</summary>
      <param name="message">Oggetto <see cref="T:System.String" /> che descrive l'errore.Il contenuto di <paramref name="message" /> deve essere facilmente comprensibile.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
      <param name="innerException">Eccezione che è la causa della generazione dell'oggetto <see cref="T:System.DataMisalignedException" /> corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
    </member>
    <member name="T:System.DllNotFoundException">
      <summary>Eccezione che viene generata quando non è possibile individuare un file DLL specificato in un'importazione di file DLL.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DllNotFoundException.#ctor">
      <summary>Consente l'inizializzazione di una nuova istanza della classe <see cref="T:System.DllNotFoundException" /> con le proprietà predefinite.</summary>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.DllNotFoundException" /> con un messaggio di errore specificato.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String,System.Exception)">
      <summary>Consente l'inizializzazione di una nuova istanza della classe <see cref="T:System.DllNotFoundException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="T:System.Reflection.Missing">
      <summary>Rappresenta un oggetto <see cref="T:System.Object" /> mancante.La classe non può essere ereditata.</summary>
    </member>
    <member name="F:System.Reflection.Missing.Value">
      <summary>Rappresenta l'unica istanza della classe <see cref="T:System.Reflection.Missing" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ArrayWithOffset">
      <summary>Incapsula una matrice e un offset all'interno della matrice specificata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.#ctor(System.Object,System.Int32)">
      <summary>Inizializza una nuova istanza della struttura <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <param name="array">Matrice gestita. </param>
      <param name="offset">Offset in byte dell'elemento da passare mediante chiamata alla piattaforma. </param>
      <exception cref="T:System.ArgumentException">La matrice è maggiore di 2 GB.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Object)">
      <summary>Indica se l'oggetto specificato corrisponde alla struttura <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> corrente.</summary>
      <returns>true se l'oggetto corrisponde a questa struttura <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />; in caso contrario, false.</returns>
      <param name="obj">Oggetto da confrontare con questa istanza. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Indica se l'oggetto specificato <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> corrisponde all'istanza corrente.</summary>
      <returns>true se l’oggetto specificato <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />corrisponde all'istanza corrente; in caso contrario false.</returns>
      <param name="obj">Oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> da confrontare con questa istanza.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetArray">
      <summary>Restituisce la matrice gestita cui questa struttura <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> fa riferimento.</summary>
      <returns>Matrice gestita cui questa istanza fa riferimento.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetHashCode">
      <summary>Restituisce un codice hash per questo tipo di valore.</summary>
      <returns>Codice hash per l'istanza.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetOffset">
      <summary>Restituisce l'offset fornito al momento della costruzione di questa struttura <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</summary>
      <returns>Offset per questa istanza.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Equality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Determina se due oggetti <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> specificati hanno lo stesso valore.</summary>
      <returns>true se il valore del parametro <paramref name="a" /> è uguale al valore del parametro <paramref name="b" />; in caso contrario, false.</returns>
      <param name="a">Oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> da confrontare con il parametro <paramref name="b" />. </param>
      <param name="b">Oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> da confrontare con il parametro <paramref name="a" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Inequality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>Determina se due oggetti <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> specificati non hanno lo stesso valore.</summary>
      <returns>true se il valore di <paramref name="a" /> non è uguale al valore di <paramref name="b" />; in caso contrario, false.</returns>
      <param name="a">Oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> da confrontare con il parametro <paramref name="b" />. </param>
      <param name="b">Oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> da confrontare con il parametro <paramref name="a" />.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.BestFitMappingAttribute">
      <summary>Controlla se i caratteri Unicode vengono convertiti nei caratteri ANSI che più si avvicinano ad essi.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BestFitMappingAttribute.#ctor(System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.BestFitMappingAttribute" /> impostata sul valore della proprietà <see cref="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping" />.</summary>
      <param name="BestFitMapping">true per indicare che è abilitato il mapping più appropriato; in caso contrario, false.Il valore predefinito è true.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping">
      <summary>Ottiene il comportamento di mapping più appropriato quando si convertono i caratteri Unicode in caratteri ANSI.</summary>
      <returns>true se è attivato il mapping più appropriato; in caso contrario, false.Il valore predefinito è true.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.BestFitMappingAttribute.ThrowOnUnmappableChar">
      <summary>Abilita o disabilita la generazione di un'eccezione su un carattere Unicode di cui non è possibile eseguire il mapping convertito in un carattere ANSI "?".</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.BStrWrapper">
      <summary>Esegue il marshalling dei dati di tipo VT_BSTR dal codice gestito al codice non gestito.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> con l'oggetto <see cref="T:System.Object" /> specificato.</summary>
      <param name="value">Oggetto da includere e sottoporre a marshalling come VT_BSTR.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> con l'oggetto <see cref="T:System.String" /> specificato.</summary>
      <param name="value">Oggetto da includere e sottoporre a marshalling come VT_BSTR.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BStrWrapper.WrappedObject">
      <summary>Ottiene l'oggetto <see cref="T:System.String" /> incluso da sottoporre a marshalling come tipo VT_BSTR.</summary>
      <returns>Oggetto di cui è stato eseguito il wrapping da <see cref="T:System.Runtime.InteropServices.BStrWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CallingConvention">
      <summary>Specifica la convenzione di chiamata necessaria per chiamare i metodi implementati nel codice non gestito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Cdecl">
      <summary>Il chiamante esegue la pulizia dello stack.Questa operazione attiva funzioni chiamanti con varargs, pertanto può essere utilizzata per metodi che accettano un numero variabile di parametri, ad esempio Printf.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.StdCall">
      <summary>Il chiamato esegue la pulizia dello stack.Si tratta della convenzione predefinita per chiamare funzioni non gestite tramite platform invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.ThisCall">
      <summary>Il primo parametro è il puntatore this ed è archiviato in ECX del Registro di sistema.Altri parametri vengono inseriti nello stack.Questa convenzione di chiamata viene utilizzata per chiamare metodi su classi esportate da una DLL non gestita.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Winapi">
      <summary>In realtà il membro non è una convenzione di chiamata, ma utilizza la convenzione di chiamata predefinita di platform invoke.In Windows, ad esempio, l'impostazione predefinita è <see cref="F:System.Runtime.InteropServices.CallingConvention.StdCall" />, mentre in Windows CE .NET è <see cref="F:System.Runtime.InteropServices.CallingConvention.Cdecl" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceAttribute">
      <summary>Indica il tipo di interfaccia di classe da generare per una classe esposta a COM, se viene generata un'interfaccia.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Int16)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> con il valore di enumerazione <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> specificato.</summary>
      <param name="classInterfaceType">Descrive il tipo di interfaccia generata per una classe. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Runtime.InteropServices.ClassInterfaceType)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> con il membro di enumerazione <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> specificato.</summary>
      <param name="classInterfaceType">Uno dei valori <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> che descrive il tipo di interfaccia generato per una classe. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ClassInterfaceAttribute.Value">
      <summary>Ottiene il valore <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> che descrive il tipo di interfaccia che deve essere generato per la classe.</summary>
      <returns>Valore <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> che descrive il tipo di interfaccia che deve essere generato per la classe.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceType">
      <summary>Identifica il tipo di interfaccia di classe generata per una classe.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDispatch">
      <summary>Indica che la classe supporta solo l'associazione tardiva dei client COM.Una dispinterface per la classe viene esposta automaticamente ai client COM su richiesta.La libreria dei tipi prodotta da Tlbexp.exe (utilità di esportazione della libreria dei tipi) non contiene informazioni sui tipi per la dispinterface allo scopo di impedire ai client di memorizzare nella cache i DISPID dell'interfaccia.La dispinterface non mostra i problemi di controllo delle versioni descritti in <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> perché i client possono eseguire soltanto l'associazione tardiva all'interfaccia.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDual">
      <summary>Indica che un'interfaccia di classe duale viene generata automaticamente per la classe ed esposta a COM.Le informazioni sul tipo sono prodotte per l'interfaccia di classe e pubblicate nella libreria dei tipi.L'utilizzo di AutoDual è fortemente sconsigliato a causa delle limitazioni nel controllo delle versioni descritte in <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.None">
      <summary>Indica che per la classe non viene generata alcuna interfaccia di classe.Se nessuna interfaccia viene implementata in modo esplicito, la classe può fornire solo accesso ad associazione tardiva tramite l'interfaccia IDispatch.Si tratta dell'impostazione consigliata per <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />.L'utilizzo di ClassInterfaceType.None è l'unico modo per l'esporre la funzionalità tramite interfacce implementate in modo esplicito dalla classe.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CoClassAttribute">
      <summary>Specifica l'identificatore di classe di una coclasse importata da una libreria dei tipi.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CoClassAttribute.#ctor(System.Type)">
      <summary>Inizializza la nuova istanza di <see cref="T:System.Runtime.InteropServices.CoClassAttribute" /> con l'ID di classe della coclasse originale.</summary>
      <param name="coClass">Oggetto <see cref="T:System.Type" /> che contiene l'identificatore di classe della coclasse originale. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.CoClassAttribute.CoClass">
      <summary>Ottiene l'identificatore di classe della coclasse originale.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che contiene l'identificatore di classe della coclasse originale.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComAwareEventInfo">
      <summary>Consente la registrazione ad associazione tardiva di un gestore dell'evento.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.#ctor(System.Type,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ComAwareEventInfo" /> utilizzando il tipo specificato e un nome dell'evento nel tipo.</summary>
      <param name="type">Tipo di oggetto. </param>
      <param name="eventName">Nome di un evento in <paramref name="type" />.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>Associa un gestore dell'evento a un oggetto COM.</summary>
      <param name="target">Oggetto di destinazione al quale deve essere associato il delegato dell'evento.</param>
      <param name="handler">Delegato dell'evento.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Attributes">
      <summary>Ottiene gli attributi per questo evento.</summary>
      <returns>Attributi di sola lettura per questo evento.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.DeclaringType">
      <summary>Ottiene la classe che dichiara questo membro.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> per la classe che dichiara questo membro.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Name">
      <summary>Ottiene il nome del membro corrente.</summary>
      <returns>Nome del membro.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>Dissocia un gestore dell'evento da un oggetto COM.</summary>
      <param name="target">Oggetto di destinazione al quale è associato il delegato dell'evento.</param>
      <param name="handler">Delegato dell'evento.</param>
      <exception cref="T:System.InvalidOperationException">L'evento non dispone di una funzione di accesso remove pubblica.</exception>
      <exception cref="T:System.ArgumentException">Non è possibile utilizzare il gestore che è stato passato.</exception>
      <exception cref="T:System.Reflection.TargetException">Nell'API.NET per le applicazioni Windows o nella Libreria di classi portabile, rilevare piuttosto <see cref="T:System.Exception" />.Il parametro <paramref name="target" /> non è null e l'evento non è static.- oppure - L'oggetto <see cref="T:System.Reflection.EventInfo" /> non è dichiarato nella destinazione.</exception>
      <exception cref="T:System.MethodAccessException">Nell'API.NET per le applicazioni Windows o nella Libreria di classi portabile, rilevare piuttosto l'eccezione della classe di base <see cref="T:System.MemberAccessException" />.Il chiamante non dispone dell'autorizzazione per accedere al membro.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute">
      <summary>Specifica un'interfaccia predefinita da esporre a COM.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute" /> con l'oggetto <see cref="T:System.Type" /> specificato come interfaccia predefinita esposta a COM.</summary>
      <param name="defaultInterface">Valore <see cref="T:System.Type" /> che indica l'interfaccia predefinita da esporre a COM. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.Value">
      <summary>Ottiene l'oggetto <see cref="T:System.Type" /> che specifica l'interfaccia predefinita da esporre a COM.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che specifica l'interfaccia predefinita da esporre a COM.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventInterfaceAttribute">
      <summary>Identifica l'interfaccia di origine e la classe che implementa i metodi dell'interfaccia eventi che viene generata in caso di importazione di una coclasse da una libreria dei tipi COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventInterfaceAttribute.#ctor(System.Type,System.Type)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ComEventInterfaceAttribute" /> con la classe dell'interfaccia di origine e del provider di eventi.</summary>
      <param name="SourceInterface">
        <see cref="T:System.Type" /> che contiene l'originale interfaccia di origine della libreria dei tipi.COM utilizza questa interfaccia per richiamare la classe gestita.</param>
      <param name="EventProvider">
        <see cref="T:System.Type" /> che contiene la classe che implementa i metodi dell'interfaccia eventi. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.EventProvider">
      <summary>Ottiene la classe che implementa i metodi dell'interfaccia eventi.</summary>
      <returns>
        <see cref="T:System.Type" /> che contiene la classe che implementa i metodi dell'interfaccia eventi.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.SourceInterface">
      <summary>Ottiene l'originale interfaccia di origine dalla libreria dei tipi.</summary>
      <returns>
        <see cref="T:System.Type" /> contenente l'interfaccia di origine.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventsHelper">
      <summary>Fornisce metodi che consentono a .NET Framework di delegare gli eventi di handle da aggiungere e rimuovere dagli oggetti COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Combine(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Aggiunge un delegato all'elenco chiamate di eventi che provengono da un oggetto COM.</summary>
      <param name="rcw">Oggetto COM che lancia gli eventi ai quali il chiamante vorrebbe rispondere.</param>
      <param name="iid">Identificatore dell'interfaccia di origine utilizzata dall'oggetto COM per lanciare gli eventi. </param>
      <param name="dispid">Identificatore di invio del metodo sull'interfaccia di origine.</param>
      <param name="d">Delegato da richiamare al lancio dell'evento COM.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Remove(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>Rimuove un delegato dall'elenco chiamate di un evento generato da un oggetto COM.</summary>
      <returns>Delegato rimosso dall'elenco delle chiamate.</returns>
      <param name="rcw">Oggetto COM a cui il delegato è associato.</param>
      <param name="iid">Identificatore dell'interfaccia di origine utilizzata dall'oggetto COM per lanciare gli eventi. </param>
      <param name="dispid">Identificatore di invio del metodo sull'interfaccia di origine.</param>
      <param name="d">Delegato da rimuovere dall'elenco delle chiamate.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.COMException">
      <summary>Eccezione generata quando un valore HRESULT non riconosciuto viene restituito da una chiamata al metodo COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.COMException" /> con valori predefiniti.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.COMException" /> con un messaggio specificato.</summary>
      <param name="message">Messaggio che indica la causa dell'eccezione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.COMException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.COMException" /> con un messaggio e il codice di errore specificati.</summary>
      <param name="message">Messaggio che indica il motivo per il quale è stata generata l'eccezione. </param>
      <param name="errorCode">Valore del codice di errore (HRESULT) associato a questa eccezione. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComImportAttribute">
      <summary>Indica che il tipo con attributi è già stato definito in COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComImportAttribute.#ctor">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Runtime.InteropServices.ComImportAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComInterfaceType">
      <summary>Identifica come esporre un'interfaccia a COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual">
      <summary>Indica che l'interfaccia è esposta a COM come un'interfaccia duale, ovvero un'interfaccia che consente sia l'associazione anticipata sia l'associazione tardiva.Il valore predefinito è <see cref="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIDispatch">
      <summary>Indica che un'interfaccia viene esposta a COM come interfaccia di tipo dispatch, che consente solo l'associazione tardiva.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIInspectable">
      <summary>Indica che un'interfaccia viene esposta a COM come interfaccia di Windows Runtime. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIUnknown">
      <summary>Indica che un'interfaccia è esposta a COM come una interfaccia derivata da IUnknown, che consente solo l'associazione anticipata.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComMemberType">
      <summary>Descrive il tipo di un membro COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.Method">
      <summary>Il membro è un metodo normale.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropGet">
      <summary>Il membro ottiene le proprietà.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropSet">
      <summary>Il membro imposta le proprietà.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute">
      <summary>Identifica un elenco di interfacce esposte come origini eventi COM per la classe con attributi.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> con il nome dell'interfaccia di origine eventi.</summary>
      <param name="sourceInterfaces">Elenco delimitato da valori null di nomi completi di interfacce di origine eventi. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> con il tipo da utilizzare come interfaccia di origine.</summary>
      <param name="sourceInterface">Tipo <see cref="T:System.Type" /> dell'interfaccia di origine. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> con i tipi da utilizzare come interfacce di origine.</summary>
      <param name="sourceInterface1">Tipo <see cref="T:System.Type" /> dell'interfaccia di origine predefinita. </param>
      <param name="sourceInterface2">Tipo <see cref="T:System.Type" /> di un'interfaccia di origine. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type)">
      <summary>Inizializza una nuova istanza della classe ComSourceInterfacesAttribute con i tipi da utilizzare come interfacce di origine.</summary>
      <param name="sourceInterface1">Tipo <see cref="T:System.Type" /> dell'interfaccia di origine predefinita. </param>
      <param name="sourceInterface2">Tipo <see cref="T:System.Type" /> di un'interfaccia di origine. </param>
      <param name="sourceInterface3">Tipo <see cref="T:System.Type" /> di un'interfaccia di origine. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type,System.Type)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> con i tipi da utilizzare come interfacce di origine.</summary>
      <param name="sourceInterface1">Tipo <see cref="T:System.Type" /> dell'interfaccia di origine predefinita. </param>
      <param name="sourceInterface2">Tipo <see cref="T:System.Type" /> di un'interfaccia di origine. </param>
      <param name="sourceInterface3">Tipo <see cref="T:System.Type" /> di un'interfaccia di origine. </param>
      <param name="sourceInterface4">Tipo <see cref="T:System.Type" /> di un'interfaccia di origine. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComSourceInterfacesAttribute.Value">
      <summary>Ottiene il nome completo dell'interfaccia di origine eventi.</summary>
      <returns>Nome completo dell'interfaccia di origine eventi.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CurrencyWrapper">
      <summary>Esegue il wrapping degli oggetti di cui il gestore di marshalling deve eseguire il marshalling come VT_CY.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Decimal)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> con il valore Decimal di cui eseguire il wrapping e da trasformare e sottoporre a marshalling come tipo VT_CY.</summary>
      <param name="obj">Valore Decimal di cui eseguire il wrapping e da sottoporre a marshalling come VT_CY. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> con l'oggetto contenente il valore Decimal di cui eseguire il wrapping e da sottoporre a marshalling come tipo VT_CY.</summary>
      <param name="obj">Oggetto contenente il valore Decimal di cui eseguire il wrapping e da sottoporre a marshalling come VT_CY. </param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="obj" /> non è di tipo <see cref="T:System.Decimal" />.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.CurrencyWrapper.WrappedObject">
      <summary>Ottiene l'oggetto di cui è stato eseguito il wrapping, da sottoporre a marshalling come tipo VT_CY.</summary>
      <returns>Oggetto di cui è stato eseguito il wrapping, da sottoporre a marshalling come tipo VT_CY.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceMode">
      <summary>Indica se le chiamate al metodo <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> di tipo IUnknown::QueryInterface possono utilizzare l'interfaccia <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Allow">
      <summary>Le chiamate del metodo IUnknown::QueryInterface possono utilizare l'interfaccia <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.Quando si utilizza questo valore, le funzioni di overload del metodo <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> preferiscono l'overload <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Ignore">
      <summary>Le chiamate del metodo IUnknown::QueryInterface dovrebbero ignorare l'interfaccia <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceResult">
      <summary>Fornisce i valori restituiti dal metodo <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Failed">
      <summary>L'interfaccia per un ID di interfaccia specifico non è disponibile.In questo caso l'interfaccia restituita è null.E_NOINTERFACE viene restituito al chiamante di IUnknown::QueryInterface.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Handled">
      <summary>Il puntatore a interfaccia restituito dal metodo <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> può essere utilizzato come risultato di IUnknown::QueryInterface.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.NotHandled">
      <summary>La QueryInterface personalizzata non è stata utilizzata.Al contrario, deve essere utilizzata l'implementazione predefinita di IUnknown::QueryInterface.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultCharSetAttribute">
      <summary>Specifica il valore dell'enumerazione <see cref="T:System.Runtime.InteropServices.CharSet" />.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultCharSetAttribute.#ctor(System.Runtime.InteropServices.CharSet)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.DefaultCharSetAttribute" /> con il valore <see cref="T:System.Runtime.InteropServices.CharSet" /> specificato.</summary>
      <param name="charSet">Uno dei valori <see cref="T:System.Runtime.InteropServices.CharSet" />.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultCharSetAttribute.CharSet">
      <summary>Ottiene il valore predefinito <see cref="T:System.Runtime.InteropServices.CharSet" /> per qualsiasi chiamata a <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />.</summary>
      <returns>Valore predefinito <see cref="T:System.Runtime.InteropServices.CharSet" /> per qualsiasi chiamata a <see cref="T:System.Runtime.InteropServices.DllImportAttribute" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute">
      <summary>Specifica i percorsi utilizzati per individuare le DLL che forniscono le funzioni per i platform invoke. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.#ctor(System.Runtime.InteropServices.DllImportSearchPath)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" />, specificando i percorsi da utilizzare nella ricerca delle destinazioni di platform invoke. </summary>
      <param name="paths">Combinazione bit per bit dei valori di enumerazione che specificano i percorsi di ricerca che la funzione LoadLibraryEx ricerca durante i pInvoke. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.Paths">
      <summary>Ottiene una combinazione bit per bit dei valori di enumerazione che specificano i percorsi in cui la funzione LoadLibraryEx effettua la ricerca durante le chiamate di pInvoke. </summary>
      <returns>Combinazione bit per bit dei valori di enumerazione che specificano i pInvoke. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultParameterValueAttribute">
      <summary>Imposta il valore predefinito di un parametro se chiamato da un linguaggio che supporta i parametri predefiniti.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultParameterValueAttribute.#ctor(System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.DefaultParameterValueAttribute" /> con il valore predefinito di un parametro.</summary>
      <param name="value">Oggetto che rappresenta il valore predefinito di un parametro.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultParameterValueAttribute.Value">
      <summary>Ottiene il valore predefinito di un parametro.</summary>
      <returns>Oggetto che rappresenta il valore predefinito di un parametro.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispatchWrapper">
      <summary>Esegue il wrapping degli oggetti di cui il gestore di marshalling deve eseguire il marshalling come VT_DISPATCH.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispatchWrapper.#ctor(System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> con l'oggetto di cui è stato eseguito il wrapping.</summary>
      <param name="obj">L'oggetto di cui è stato eseguito il wrapping e convertire in <see cref="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> non è una classe né una matrice.In alternativa <paramref name="obj" /> non supporta IDispatch. </exception>
      <exception cref="T:System.InvalidOperationException">Il parametro <paramref name="obj" /> è contrassegnato con un attributo <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> al quale è stato passato il valore false.In alternativaIl parametro <paramref name="obj" /> eredita da un tipo contrassegnato con un attributo <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> al quale è stato passato il valore false.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.DispatchWrapper.WrappedObject">
      <summary>Ottiene l'oggetto di cui è stato eseguito il wrapping da <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />.</summary>
      <returns>L'oggetto di cui è stato eseguito il wrapping da <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispIdAttribute">
      <summary>Specifica l'ID di invio (o DISPID) COM di un metodo, campo o proprietà.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispIdAttribute.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe DispIdAttribute con il DISPID specificato.</summary>
      <param name="dispId">DISPID del membro. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DispIdAttribute.Value">
      <summary>Ottiene il DISPID del membro.</summary>
      <returns>DISPID del membro.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportAttribute">
      <summary>Indica che il metodo con attributi viene esposto da una libreria a collegamento dinamico (DLL) non gestita come punto di ingresso statico.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DllImportAttribute.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> con il nome della DLL che contiene il metodo da importare.</summary>
      <param name="dllName">Nome della DLL che contiene il metodo non gestito.Può includere il nome visualizzato di un assembly, se la DLL è inclusa in un assembly.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.BestFitMapping">
      <summary>Abilita o disabilita il comportamento di mapping più appropriato per la conversione di caratteri Unicode in caratteri ANSI.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CallingConvention">
      <summary>Indica la convenzione di chiamata di un punto di ingresso.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CharSet">
      <summary>Indica come eseguire il marshalling dei parametri di stringa nel metodo e controlla l'alterazione dei nomi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.EntryPoint">
      <summary>Indica il nome o l'ordinale del punto di ingresso DLL da chiamare.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ExactSpelling">
      <summary>Controlla se il campo <see cref="F:System.Runtime.InteropServices.DllImportAttribute.CharSet" /> fa in modo che Common Language Runtime cerchi in una DLL non gestita i nomi dei punti di ingresso diversi da quello specificato.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.PreserveSig">
      <summary>Indica se i metodi non gestiti con valore restituito HRESULT o retval vengono convertiti direttamente o se il valore restituito HRESULT o retval viene convertito automaticamente in eccezioni.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError">
      <summary>Indica se il destinatario della chiamata chiama la funzione API Win32 SetLastError prima della restituzione di un risultato dal metodo con attributi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ThrowOnUnmappableChar">
      <summary>Abilita o disabilita la generazione di un'eccezione su un carattere Unicode di cui non è possibile eseguire il mapping convertito in un carattere ANSI "?".</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.DllImportAttribute.Value">
      <summary>Ottiene il nome del file DLL che contiene il punto di ingresso.</summary>
      <returns>Nome del file DLL che contiene il punto di ingresso.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportSearchPath">
      <summary>Specifica i percorsi utilizzati per individuare le DLL che forniscono le funzioni per i platform invoke. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.ApplicationDirectory">
      <summary>Includere la directory dell'applicazione nel percorso di ricerca della DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.AssemblyDirectory">
      <summary>Una volta cercate le dipendenze dell'assembly, includere la directory contenente l'assembly stesso e cercare tale directory per prima.Questo valore viene utilizzato da .NET Framework, prima che i percorsi vengano passati alla funzione LoadLibraryEx di Win32.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.LegacyBehavior">
      <summary>Effettuare la ricerca nella directory dell'applicazione, quindi chiamare la funzione Win32 LoadLibraryEx con il flag LOAD_WITH_ALTERED_SEARCH_PATH.Questo valore viene ignorato se viene specificato qualsiasi altro valore.I sistemi operativi che non supportano l'attributo <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> utilizzano questo valore e ignorano gli altri valori.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.SafeDirectories">
      <summary>Includere la directory dell'applicazione, la directory %WinDir%\System32, e le directory utente nel percorso di ricerca della DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.System32">
      <summary>Includere la directory %WinDir%\System32 nel percorso di ricerca della DLL. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UseDllDirectoryForDependencies">
      <summary>Cercare le dipendenze di una DLL nella cartella in cui si trova la DLL prima di cercare in altre cartelle. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UserDirectories">
      <summary>Includere qualsiasi percorso aggiunto in modo esplicito al percorso di ricerca a livello di processo utilizzando la funzione AddDllDirectory Win32. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ErrorWrapper">
      <summary>Esegue il wrapping degli oggetti di cui il gestore di marshalling deve eseguire il marshalling come VT_ERROR.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> con il valore HRESULT che corrisponde all'eccezione fornita.</summary>
      <param name="e">L'eccezione da convertire in codice di errore. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> con il valore HRESULT dell'errore.</summary>
      <param name="errorCode">Valore HRESULT dell'errore. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> con un oggetto contenente il valore HRESULT dell'errore.</summary>
      <param name="errorCode">L'oggetto contenente il valore HRESULT dell'errore. </param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="errorCode" /> non è di tipo <see cref="T:System.Int32" />.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.ErrorWrapper.ErrorCode">
      <summary>Ottiene il codice di errore del wrapper.</summary>
      <returns>Valore HRESULT dell'errore.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandle">
      <summary>Fornisce un modo per accedere a un oggetto gestito da una memoria non gestita.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.AddrOfPinnedObject">
      <summary>Recupera l'indirizzo di un oggetto in un handle <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />.</summary>
      <returns>Indirizzo dell'oggetto bloccato come <see cref="T:System.IntPtr" />. </returns>
      <exception cref="T:System.InvalidOperationException">The handle is any type other than <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object)">
      <summary>Alloca un handle <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" /> per l'oggetto specificato.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> che protegge l'oggetto dalla procedura di Garbage Collection.Questo oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> deve essere rilasciato con il metodo <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> quando non è più necessario.</returns>
      <param name="value">Oggetto che usa <see cref="T:System.Runtime.InteropServices.GCHandle" />. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object,System.Runtime.InteropServices.GCHandleType)">
      <summary>Alloca un handle del tipo specificato per l'oggetto specificato.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> del tipo specificato.Questo oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> deve essere rilasciato con il metodo <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> quando non è più necessario.</returns>
      <param name="value">Oggetto che usa <see cref="T:System.Runtime.InteropServices.GCHandle" />. </param>
      <param name="type">Uno dei valori di <see cref="T:System.Runtime.InteropServices.GCHandleType" /> che indica il tipo di <see cref="T:System.Runtime.InteropServices.GCHandle" /> da creare. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Equals(System.Object)">
      <summary>Determina se l'oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> specificato è uguale all'oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> corrente.</summary>
      <returns>true se l'oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> specificato è uguale all'oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> corrente; in caso contrario, false.</returns>
      <param name="o">Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> da confrontare con l'oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> corrente.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Free">
      <summary>Rilascia un oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <exception cref="T:System.InvalidOperationException">The handle was freed or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.FromIntPtr(System.IntPtr)">
      <summary>Restituisce un nuovo oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> creato da un handle per un oggetto gestito.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> corrispondente al parametro del valore.  </returns>
      <param name="value">Handle <see cref="T:System.IntPtr" /> per un oggetto gestito da cui creare un oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" />.</param>
      <exception cref="T:System.InvalidOperationException">The value of the <paramref name="value" /> parameter is <see cref="F:System.IntPtr.Zero" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.GetHashCode">
      <summary>Restituisce un identificatore per l'oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> corrente.</summary>
      <returns>Identificatore per l'oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> corrente.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.IsAllocated">
      <summary>Ottiene un valore che indica se l'handle è allocato.</summary>
      <returns>true se l'handle è allocato; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Equality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Restituisce un valore che indica se due oggetti <see cref="T:System.Runtime.InteropServices.GCHandle" /> sono uguali.</summary>
      <returns>true se i parametri <paramref name="a" /> e <paramref name="b" /> sono uguali; in caso contrario, false.</returns>
      <param name="a">Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> da confrontare con il parametro <paramref name="b" />. </param>
      <param name="b">Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> da confrontare con il parametro <paramref name="a" />.  </param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.IntPtr)~System.Runtime.InteropServices.GCHandle">
      <summary>Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> archiviato usando una rappresentazione interna di interi.</summary>
      <returns>Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> archiviato usando una rappresentazione interna di interi.</returns>
      <param name="value">Oggetto <see cref="T:System.IntPtr" /> che indica l'handle per il quale è richiesta la conversione. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.Runtime.InteropServices.GCHandle)~System.IntPtr">
      <summary>Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> archiviato usando una rappresentazione interna di interi.</summary>
      <returns>Valore intero.</returns>
      <param name="value">Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> per il quale è necessario l'intero. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Inequality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>Restituisce un valore che indica se due oggetti <see cref="T:System.Runtime.InteropServices.GCHandle" /> non sono uguali.</summary>
      <returns>true se i parametri <paramref name="a" /> e <paramref name="b" /> non sono uguali; in caso contrario, false.</returns>
      <param name="a">Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> da confrontare con il parametro <paramref name="b" />. </param>
      <param name="b">Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> da confrontare con il parametro <paramref name="a" />.  </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.Target">
      <summary>Ottiene o imposta l'oggetto rappresentato da questo handle.</summary>
      <returns>Oggetto rappresentato da questo handle.</returns>
      <exception cref="T:System.InvalidOperationException">The handle was freed, or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.ToIntPtr(System.Runtime.InteropServices.GCHandle)">
      <summary>Restituisce la rappresentazione interna di interi di un oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" />.</summary>
      <returns>Oggetto <see cref="T:System.IntPtr" /> che rappresenta un oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" />. </returns>
      <param name="value">Oggetto <see cref="T:System.Runtime.InteropServices.GCHandle" /> da cui recuperare una rappresentazione interna di interi.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandleType">
      <summary>Rappresenta i tipi di handle che la classe <see cref="T:System.Runtime.InteropServices.GCHandle" /> può allocare.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Normal">
      <summary>Questo tipo di handle rappresenta un handle opaco, ovvero non è possibile risolvere l'indirizzo dell'oggetto bloccato tramite l'handle.È possibile utilizzare questo tipo per controllare un oggetto e impedire che sia sottoposto alla procedura di Garbage Collection.Questo membro di enumerazione è utile quando un client non gestito contiene l'unico riferimento, non rilevabile dalla procedura di Garbage Collection, a un oggetto gestito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Pinned">
      <summary>Questo tipo di handle è simile a <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" />, ma consente di recuperare l'indirizzo dell'oggetto bloccato.In questo modo si evita che l'oggetto venga spostato tramite la procedura di Garbage Collection, riducendo così l'efficienza della procedura stessa.Utilizzare il metodo <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> per liberare l'handle allocato non appena possibile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Weak">
      <summary>Questo tipo di handle è utilizzato per controllare un oggetto, sebbene consenta di raccoglierlo.Quando un oggetto viene raccolto, il contenuto di <see cref="T:System.Runtime.InteropServices.GCHandle" /> viene azzerato.I riferimenti Weak vengono azzerati prima dell'esecuzione del finalizzatore, in modo che, anche se il finalizzatore recupera l'oggetto, il riferimento Weak rimane ugualmente azzerato.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.WeakTrackResurrection">
      <summary>Questo tipo di handle è simile a <see cref="F:System.Runtime.InteropServices.GCHandleType.Weak" /> ma l'handle non viene azzerato se l'oggetto viene ripristinato durante la finalizzazione.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.GuidAttribute">
      <summary>Fornisce un oggetto <see cref="T:System.Guid" /> esplicito quando un GUID automatico non è auspicabile.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GuidAttribute.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.GuidAttribute" /> con il GUID specificato.</summary>
      <param name="guid">Oggetto <see cref="T:System.Guid" /> da assegnare. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GuidAttribute.Value">
      <summary>Ottiene l'oggetto <see cref="T:System.Guid" /> della classe.</summary>
      <returns>Oggetto <see cref="T:System.Guid" /> della classe.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.HandleCollector">
      <summary>Tiene traccia degli handle in attesa e forza una procedura di Garbage Collection quando viene raggiunta la soglia specificata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.HandleCollector" /> utilizzando un nome e una soglia dopo la quale avviare la procedura di Garbage Collection per gli handle. </summary>
      <param name="name">Nome per lo strumento di raccolta.Questo parametro consente di denominare gli strumenti di raccolta che tengono traccia dei tipi di handle in modo separato.</param>
      <param name="initialThreshold">Valore che specifica il punto in cui le procedure di Garbage Collection devono essere avviate.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="initialThreshold" /> è minore di 0.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.HandleCollector" /> utilizzando un nome, una soglia dopo la quale avviare la procedura di Garbage Collection per gli handle e una soglia dopo la quale la procedura deve essere eseguita. </summary>
      <param name="name">Nome per lo strumento di raccolta.  Questo parametro consente di denominare gli strumenti di raccolta che tengono traccia dei tipi di handle in modo separato.</param>
      <param name="initialThreshold">Valore che specifica il punto in cui le procedure di Garbage Collection devono essere avviate.</param>
      <param name="maximumThreshold">Valore che specifica il punto in cui devono essere eseguite le procedure di Garbage Collection.Questo valore deve essere impostato sul numero massimo di handle disponibili.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="initialThreshold" /> è minore di 0.- oppure -Il parametro <paramref name="maximumThreshold" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="maximumThreshold" /> è minore del parametro <paramref name="initialThreshold" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Add">
      <summary>Incrementa il numero corrente di handle.</summary>
      <exception cref="T:System.InvalidOperationException">Il valore della proprietà <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> è minore di 0.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Count">
      <summary>Ottiene il numero di handle raccolti.</summary>
      <returns>Numero di handle raccolti.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.InitialThreshold">
      <summary>Ottiene un valore che specifica il punto in cui le procedure di Garbage Collection devono essere avviate.</summary>
      <returns>Valore che specifica il punto in cui le procedure di Garbage Collection devono essere avviate.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.MaximumThreshold">
      <summary>Ottiene un valore che specifica il punto in cui devono essere eseguite le procedure di Garbage Collection.</summary>
      <returns>Valore che specifica il punto in cui devono essere eseguite le procedure di Garbage Collection.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Name">
      <summary>Ottiene il nome di un oggetto <see cref="T:System.Runtime.InteropServices.HandleCollector" />.</summary>
      <returns>Questa proprietà <see cref="P:System.Runtime.InteropServices.HandleCollector.Name" /> consente di denominare gli strumenti di raccolta che tengono traccia dei tipi di handle in modo separato.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Remove">
      <summary>Decrementa il numero corrente di handle.</summary>
      <exception cref="T:System.InvalidOperationException">Il valore della proprietà <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> è minore di 0.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomAdapter">
      <summary>Fornisce ai client un modo per accedere all'oggetto vero e proprio, anziché all'oggetto adattatore distribuito da un gestore di marshalling personalizzato.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomAdapter.GetUnderlyingObject">
      <summary>Fornisce accesso all'oggetto sottostante di cui un gestore di marshalling personalizzato ha eseguito il wrapping.</summary>
      <returns>Oggetto contenuto dall'oggetto adattatore.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomQueryInterface">
      <summary>Consente agli sviluppatori di fornire una implementazione gestita e personalizzata del metodo IUnknown:: QueryInterface (REFIID riid, void **ppvObject).</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)">
      <summary>Restituisce un'interfaccia in base a un ID di interfaccia specificato.</summary>
      <returns>Uno dei valori di enumerazione che indica se è stata utilizzata un'implementazione personalizzata di IUnknown::QueryInterface.</returns>
      <param name="iid">GUID dell'interfaccia richiesta.</param>
      <param name="ppv">Riferimento all'interfaccia richiesta, al completamento del metodo.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InAttribute">
      <summary>Indica che è necessario eseguire il marshalling dei dati dal chiamante al destinatario della chiamata, ma non nuovamente al chiamante.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InAttribute.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.InAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.InterfaceTypeAttribute">
      <summary>Indica se un'interfaccia gestita è del tipo dual, dispatch-only o solo IUnknown durante l'esposizione a COM.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Int16)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> con il membro di enumerazione <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> specificato.</summary>
      <param name="interfaceType">Descrive come esporre l'interfaccia ai client COM. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Runtime.InteropServices.ComInterfaceType)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> con il membro di enumerazione <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> specificato.</summary>
      <param name="interfaceType">Uno dei valori <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> che descrive come esporre l'interfaccia a client COM. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.InterfaceTypeAttribute.Value">
      <summary>Ottiene il valore <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> che descrive come esporre l'interfaccia a COM.</summary>
      <returns>Il valore <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> che descrive come esporre l'interfaccia a COM.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidComObjectException">
      <summary>Eccezione generata quando si utilizza un oggetto COM non valido.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor">
      <summary>Inizializza un'istanza della classe InvalidComObjectException con le proprietà predefinite.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String)">
      <summary>Inizializza un'istanza della classe InvalidComObjectException con un messaggio.</summary>
      <param name="message">Messaggio che indica la causa dell'eccezione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.InvalidComObjectException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
      <summary>L'eccezione generata dal gestore di marshalling quando rileva un argomento di un tipo Variant del quale non è possibile eseguire il marshalling nel codice gestito.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor">
      <summary>Inizializza una nuova istanza della classe InvalidOleVariantTypeException con valori predefiniti.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe InvalidOleVariantTypeException con un messaggio specificato.</summary>
      <param name="message">Messaggio che indica la causa dell'eccezione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.Marshal">
      <summary>Fornisce una raccolta di metodi per l'allocazione della memoria non gestita, la copia di blocchi di memoria non gestita e la conversione di tipi gestiti in tipi non gestiti, oltre ad altri metodi usati durante l'interazione con codice non gestito.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AddRef(System.IntPtr)">
      <summary>Incrementa il numero di riferimenti nell'interfaccia specificata.</summary>
      <returns>Nuovo valore del conteggio dei riferimenti sul parametro <paramref name="pUnk" />.</returns>
      <param name="pUnk">Numero di riferimenti nell'interfaccia da incrementare.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)">
      <summary>Alloca un blocco di memoria delle dimensioni specificate dall'allocatore di memoria delle attività COM.</summary>
      <returns>Intero che rappresenta l'indirizzo del blocco di memoria allocato.Questa memoria deve essere liberata con <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />.</returns>
      <param name="cb">Dimensioni del blocco di memoria da allocare.</param>
      <exception cref="T:System.OutOfMemoryException">Memoria insufficiente per soddisfare la richiesta.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.Int32)">
      <summary>Alloca memoria dalla memoria non gestita del processo tramite il numero specificato di byte.</summary>
      <returns>Puntatore alla memoria appena allocata.Questa memoria deve essere liberata usando il metodo <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="cb">Numero di byte necessari nella memoria.</param>
      <exception cref="T:System.OutOfMemoryException">Memoria insufficiente per soddisfare la richiesta.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)">
      <summary>Alloca memoria dalla memoria non gestita del processo tramite il puntatore al numero specificato di byte.</summary>
      <returns>Puntatore alla memoria appena allocata.Questa memoria deve essere liberata usando il metodo <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="cb">Numero di byte necessari nella memoria.</param>
      <exception cref="T:System.OutOfMemoryException">Memoria insufficiente per soddisfare la richiesta.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AreComObjectsAvailableForCleanup">
      <summary>Indica se Runtime Callable Wrapper (RCW) provenienti da qualsiasi contesto sono disponibili per la pulizia.</summary>
      <returns>true se sono disponibili RCW per la pulizia. In caso contrario, false.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Byte[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia i dati da una matrice di interi senza segno a 8 bit gestita e unidimensionale a un puntatore di memoria non gestita.</summary>
      <param name="source">Matrice unidimensionale da cui copiare.</param>
      <param name="startIndex">Indice in base zero nella matrice di origine in corrispondenza del quale inizia la copia.</param>
      <param name="destination">Puntatore di memoria in cui copiare.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> e <paramref name="length" /> non sono validi.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Char[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia i dati da una matrice di caratteri gestita e unidimensionale a un puntatore di memoria non gestita.</summary>
      <param name="source">Matrice unidimensionale da cui copiare.</param>
      <param name="startIndex">Indice in base zero nella matrice di origine in corrispondenza del quale inizia la copia.</param>
      <param name="destination">Puntatore di memoria in cui copiare.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> e <paramref name="length" /> non sono validi.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Double[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia i dati da una matrice di numeri a virgola mobile a precisione doppia gestita e unidimensionale a un puntatore di memoria non gestita.</summary>
      <param name="source">Matrice unidimensionale da cui copiare.</param>
      <param name="startIndex">Indice in base zero nella matrice di origine in corrispondenza del quale inizia la copia.</param>
      <param name="destination">Puntatore di memoria in cui copiare.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> e <paramref name="length" /> non sono validi.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int16[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia i dati da una matrice di interi con segno a 16 bit gestita e unidimensionale a un puntatore di memoria non gestita.</summary>
      <param name="source">Matrice unidimensionale da cui copiare.</param>
      <param name="startIndex">Indice in base zero nella matrice di origine in corrispondenza del quale inizia la copia.</param>
      <param name="destination">Puntatore di memoria in cui copiare.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> e <paramref name="length" /> non sono validi.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int32[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia i dati da una matrice di interi con segno a 32 bit gestita e unidimensionale a un puntatore di memoria non gestita.</summary>
      <param name="source">Matrice unidimensionale da cui copiare.</param>
      <param name="startIndex">Indice in base zero nella matrice di origine in corrispondenza del quale inizia la copia.</param>
      <param name="destination">Puntatore di memoria in cui copiare.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> e <paramref name="length" /> non sono validi.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int64[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia i dati da una matrice di interi con segno a 64 bit gestita e unidimensionale a un puntatore di memoria non gestita.</summary>
      <param name="source">Matrice unidimensionale da cui copiare.</param>
      <param name="startIndex">Indice in base zero nella matrice di origine in corrispondenza del quale inizia la copia.</param>
      <param name="destination">Puntatore di memoria in cui copiare.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> e <paramref name="length" /> non sono validi.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Byte[],System.Int32,System.Int32)">
      <summary>Copia i dati da un puntatore di memoria non gestita a una matrice di interi senza segno a 8 bit gestita.</summary>
      <param name="source">Puntatore di memoria da cui copiare.</param>
      <param name="destination">Matrice in cui effettuare la copia.</param>
      <param name="startIndex">Indice in base zero nella matrice di destinazione in corrispondenza del quale inizia la copia.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Char[],System.Int32,System.Int32)">
      <summary>Copia i dati da un puntatore di memoria non gestita a una matrice di caratteri gestita.</summary>
      <param name="source">Puntatore di memoria da cui copiare.</param>
      <param name="destination">Matrice in cui effettuare la copia.</param>
      <param name="startIndex">Indice in base zero nella matrice di destinazione in corrispondenza del quale inizia la copia.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Double[],System.Int32,System.Int32)">
      <summary>Copia i dati da un puntatore di memoria non gestita a una matrice di numeri a virgola mobile a precisione doppia gestita.</summary>
      <param name="source">Puntatore di memoria da cui copiare.</param>
      <param name="destination">Matrice in cui effettuare la copia.</param>
      <param name="startIndex">Indice in base zero nella matrice di destinazione in corrispondenza del quale inizia la copia.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int16[],System.Int32,System.Int32)">
      <summary>Copia i dati da un puntatore di memoria non gestita a una matrice di interi con segno a 16 bit gestita.</summary>
      <param name="source">Puntatore di memoria da cui copiare.</param>
      <param name="destination">Matrice in cui effettuare la copia.</param>
      <param name="startIndex">Indice in base zero nella matrice di destinazione in corrispondenza del quale inizia la copia.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int32[],System.Int32,System.Int32)">
      <summary>Copia i dati da un puntatore di memoria non gestita a una matrice di interi con segno a 32 bit gestita.</summary>
      <param name="source">Puntatore di memoria da cui copiare.</param>
      <param name="destination">Matrice in cui effettuare la copia.</param>
      <param name="startIndex">Indice in base zero nella matrice di destinazione in corrispondenza del quale inizia la copia.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int64[],System.Int32,System.Int32)">
      <summary>Copia i dati da un puntatore di memoria non gestita a una matrice di interi con segno a 64 bit gestita.</summary>
      <param name="source">Puntatore di memoria da cui copiare.</param>
      <param name="destination">Matrice in cui effettuare la copia.</param>
      <param name="startIndex">Indice in base zero nella matrice di destinazione in corrispondenza del quale inizia la copia.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
      <summary>Copia i dati da un puntatore di memoria non gestita a una matrice <see cref="T:System.IntPtr" /> gestita.</summary>
      <param name="source">Puntatore di memoria da cui copiare. </param>
      <param name="destination">Matrice in cui effettuare la copia.</param>
      <param name="startIndex">Indice in base zero nella matrice di destinazione in corrispondenza del quale inizia la copia.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Single[],System.Int32,System.Int32)">
      <summary>Copia i dati da un puntatore di memoria non gestita a una matrice di numeri a virgola mobile a precisione singola gestita.</summary>
      <param name="source">Puntatore di memoria da cui copiare. </param>
      <param name="destination">Matrice in cui effettuare la copia. </param>
      <param name="startIndex">Indice in base zero nella matrice di destinazione in corrispondenza del quale inizia la copia. </param>
      <param name="length">Numero degli elementi di matrice da copiare. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" /> è null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia i dati da una matrice <see cref="T:System.IntPtr" /> unidimensionale gestita a un puntatore di memoria non gestita.</summary>
      <param name="source">Matrice unidimensionale da cui copiare.</param>
      <param name="startIndex">Indice in base zero nella matrice di origine in corrispondenza del quale inizia la copia.</param>
      <param name="destination">Puntatore di memoria in cui copiare.</param>
      <param name="length">Numero degli elementi di matrice da copiare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> o <paramref name="length" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Single[],System.Int32,System.IntPtr,System.Int32)">
      <summary>Copia i dati da una matrice di numeri a virgola mobile a precisione singola gestita unidimensionale a un puntatore di memoria non gestita.</summary>
      <param name="source">Matrice unidimensionale da cui copiare. </param>
      <param name="startIndex">Indice in base zero nella matrice di origine in corrispondenza del quale inizia la copia. </param>
      <param name="destination">Puntatore di memoria in cui copiare. </param>
      <param name="length">Numero degli elementi di matrice da copiare. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> e <paramref name="length" /> non sono validi. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> o <paramref name="length" /> è null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject(System.IntPtr,System.Object)">
      <summary>Aggrega un oggetto gestito all'oggetto COM specificato.</summary>
      <returns>Puntatore IUnknown interno dell'oggetto gestito.</returns>
      <param name="pOuter">Puntatore IUnknown esterno.</param>
      <param name="o">Oggetto da aggregare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> è un oggetto Windows Runtime.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject``1(System.IntPtr,``0)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Aggrega un oggetto gestito del tipo specificato con l'oggetto COM specificato. </summary>
      <returns>Puntatore IUnknown interno dell'oggetto gestito. </returns>
      <param name="pOuter">Puntatore IUnknown esterno. </param>
      <param name="o">Oggetto gestito da aggregare. </param>
      <typeparam name="T">Tipo dell'oggetto gestito da aggregare. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> è un oggetto Windows Runtime. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType(System.Object,System.Type)">
      <summary>Include l'oggetto COM specificato in un oggetto del tipo specificato.</summary>
      <returns>L'oggetto appena incluso che è un'istanza del tipo desiderato.</returns>
      <param name="o">Oggetto da includere. </param>
      <param name="t">Tipo di wrapper da creare. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> deve derivare da __ComObject. -oppure-<paramref name="t" /> è un tipo Windows Runtime.</exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="t" /> è null.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> non può essere convertito nel tipo di destinazione perché non supporta tutte le interfacce richieste. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType``2(``0)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Include l'oggetto COM specificato in un oggetto del tipo specificato.</summary>
      <returns>Oggetto appena incluso. </returns>
      <param name="o">Oggetto da includere. </param>
      <typeparam name="T">Tipo di oggetto di cui eseguire il wrapping. </typeparam>
      <typeparam name="TWrapper">Tipo di oggetto da restituire. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> deve derivare da __ComObject. -oppure-<paramref name="T" /> è un tipo Windows Runtime.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> non può essere convertito in <paramref name="TWrapper" /> perché non supporta tutte le interfacce richieste. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Libera tutte le sottostrutture di un tipo specificato a cui punta il blocco di memoria non gestita specificato. </summary>
      <param name="ptr">Puntatore a un blocco di memoria non gestita. </param>
      <typeparam name="T">Tipo della struttura formattata.Fornisce le informazioni di layout necessarie per eliminare il buffer o i buffer nel parametro <paramref name="ptr" />.</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> dispone di un layout automatico.Utilizzare invece il layout sequenziale o esplicito.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)">
      <summary>Libera tutte le sottostrutture a cui punta il blocco di memoria non gestita specificato.</summary>
      <param name="ptr">Puntatore a un blocco di memoria non gestita. </param>
      <param name="structuretype">Tipo di una classe formattata.Fornisce le informazioni di layout necessarie per eliminare il buffer o i buffer nel parametro <paramref name="ptr" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> dispone di un layout automatico.Utilizzare invece il layout sequenziale o esplicito.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FinalReleaseComObject(System.Object)">
      <summary>Rilascia tutti i riferimenti a un Runtime Callable Wrapper (RCW) impostando il suo conteggio dei riferimenti a 0.</summary>
      <returns>Nuovo valore del conteggio dei riferimenti dell'oggetto RCW associato al parametro <paramref name="o" />corrispondente a 0 (zero) se il rilascio è stato eseguito correttamente.</returns>
      <param name="o">Wrapper RCW da rilasciare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> non è un oggetto COM valido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)">
      <summary>Libera un BSTR usando la funzione COM SysFreeString.</summary>
      <param name="ptr">Indirizzo del BSTR da liberare. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)">
      <summary>Libera un blocco di memoria assegnato dall'allocatore di memoria non gestita delle attività COM.</summary>
      <param name="ptr">Indirizzo della memoria da liberare. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)">
      <summary>Libera la memoria precedentemente allocata dalla memoria non gestita del processo.</summary>
      <param name="hglobal">L'handle restituito dalla chiamata di corrispondenza originale a <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)">
      <summary>Restituisce un puntatore a interfaccia IUnknown che rappresenta l'interfaccia specificata sull'oggetto specificato.L'accesso all'interfaccia di query è abilitato per impostazione predefinita.</summary>
      <returns>Puntatore a interfaccia che rappresenta l'interfaccia specificata per l'oggetto.</returns>
      <param name="o">Oggetto che fornisce l'interfaccia. </param>
      <param name="T">Tipo di interfaccia richiesto. </param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="T" /> non è un'interfaccia.-oppure- Il tipo non è visibile a COM. -oppure-Il parametro <paramref name="T" /> è un tipo generico.</exception>
      <exception cref="T:System.InvalidCastException">Il parametro <paramref name="o" /> non supporta l'interfaccia richiesta. </exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="o" /> è null.-oppure- Il parametro <paramref name="T" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)">
      <summary>Restituisce un puntatore a interfaccia IUnknown che rappresenta l'interfaccia specificata sull'oggetto specificato.L'accesso all'interfaccia di query personalizzato viene controllato dalla modalità della personalizzazione specificata.</summary>
      <returns>Puntatore a interfaccia che rappresenta l'interfaccia per l'oggetto.</returns>
      <param name="o">Oggetto che fornisce l'interfaccia.</param>
      <param name="T">Tipo di interfaccia richiesto.</param>
      <param name="mode">Uno dei valori di enumerazione che indica se applicare una personalizzazione IUnknown::QueryInterface fornita da un oggetto <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="T" /> non è un'interfaccia.-oppure- Il tipo non è visibile a COM.-oppure-Il parametro <paramref name="T" /> è un tipo generico.</exception>
      <exception cref="T:System.InvalidCastException">Il parametro <paramref name="o" /> dell'oggetto non supporta l'interfaccia richiesta.</exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="o" /> è null.-oppure- Il parametro <paramref name="T" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject``2(``0)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Restituisce un puntatore a interfaccia IUnknown che rappresenta l'interfaccia specificata sull'oggetto del tipo specificato.L'accesso all'interfaccia di query è abilitato per impostazione predefinita.</summary>
      <returns>Puntatore a interfaccia che rappresenta l'interfaccia <paramref name="TInterface" />.</returns>
      <param name="o">Oggetto che fornisce l'interfaccia. </param>
      <typeparam name="T">Tipo di <paramref name="o" />. </typeparam>
      <typeparam name="TInterface">Tipo di interfaccia da restituire. </typeparam>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="TInterface" /> non è un'interfaccia.-oppure- Il tipo non è visibile a COM. -oppure-Il parametro <paramref name="T" /> è un tipo generico aperto.</exception>
      <exception cref="T:System.InvalidCastException">Il parametro <paramref name="o" /> non supporta l'interfaccia <paramref name="TInterface" />. </exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="o" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer``1(System.IntPtr)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Converte un puntatore a funzione non gestito in un delegato di un tipo specificato. </summary>
      <returns>Istanza del tipo di delegato specificato.</returns>
      <param name="ptr">Puntatore alla funzione non gestita da convertire. </param>
      <typeparam name="TDelegate">Tipo del delegato da restituire. </typeparam>
      <exception cref="T:System.ArgumentException">Il parametro generico <paramref name="TDelegate" /> non è un delegato oppure è un tipo generico aperto.</exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="ptr" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(System.IntPtr,System.Type)">
      <summary>Converte un puntatore a funzione non gestito in un delegato.</summary>
      <returns>Istanza di delegato di cui è possibile eseguire il cast al tipo di delegato appropriato.</returns>
      <param name="ptr">Puntatore alla funzione non gestita da convertire.</param>
      <param name="t">Tipo di delegato da restituire.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="t" /> non è un delegato o è generico.</exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="ptr" /> è null.-oppure-Il parametro <paramref name="t" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionCode">
      <summary>Recupera un codice che identifica il tipo dell'eccezione che si è verificata.</summary>
      <returns>Tipo dell'eccezione.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32)">
      <summary>Converte il codice errore HRESULT specificato in un oggetto <see cref="T:System.Exception" /> corrispondente.</summary>
      <returns>Oggetto che rappresenta l'HRESULT convertito.</returns>
      <param name="errorCode">Codice errore HRESULT da convertire.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Converte il codice errore HRESULT specificato in un oggetto <see cref="T:System.Exception" /> corrispondente, con ulteriori informazioni sull'errore passate in un'interfaccia IErrorInfo per l'oggetto eccezione.</summary>
      <returns>Oggetto che rappresenta HRESULT convertito e le informazioni ottenute da <paramref name="errorInfo" />.</returns>
      <param name="errorCode">Codice errore HRESULT da convertire.</param>
      <param name="errorInfo">Puntatore all'interfaccia IErrorInfo che fornisce ulteriori informazioni sull'errore.È possibile specificare IntPtr(0) per usare l'interfaccia IErrorInfo corrente oppure IntPtr(-1) per ignorare l'interfaccia IErrorInfo corrente e costruire l'eccezione unicamente dal codice errore.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(System.Delegate)">
      <summary>Converte un delegato in un puntatore a funzione disponibile per la chiamata dal codice non gestito.</summary>
      <returns>Valore che può essere passato al codice non gestito, che a sua volta può usarlo per chiamare il delegato gestito sottostante. </returns>
      <param name="d">Delegato da passare al codice non gestito.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="d" /> è un tipo generico.</exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="d" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate``1(``0)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Converte un delegato di un tipo specificato in un puntatore a funzione disponibile per la chiamata dal codice non gestito. </summary>
      <returns>Valore che può essere passato al codice non gestito, che a sua volta può usarlo per chiamare il delegato gestito sottostante. </returns>
      <param name="d">Delegato da passare al codice non gestito. </param>
      <typeparam name="TDelegate">Tipo di delegato da convertire. </typeparam>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="d" /> è null. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForException(System.Exception)">
      <summary>Converte l'eccezione specificata in valore HRESULT.</summary>
      <returns>Valore HRESULT mappato all'eccezione fornita.</returns>
      <param name="e">Eccezione da convertire in valore HRESULT.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForLastWin32Error">
      <summary>Restituisce il valore HRESULT corrispondente all'ultimo errore in cui è incorso il codice Win32 eseguito usando <see cref="T:System.Runtime.InteropServices.Marshal" />.</summary>
      <returns>HRESULT corrispondente all'ultimo codice errore Win32.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetIUnknownForObject(System.Object)">
      <summary>Restituisce un'interfaccia IUnknown da un oggetto gestito.</summary>
      <returns>Puntatore IUnknown per il parametro <paramref name="o" />.</returns>
      <param name="o">Oggetto di cui è richiesta l'interfaccia IUnknown.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error">
      <summary>Restituisce il codice errore restituito dall'ultima funzione non gestita chiamata mediante una chiamata platform invoke con il flag <see cref="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError" /> impostato.</summary>
      <returns>Ultimo codice errore impostato da una chiamata alla funzione Win32 SetLastError.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject(System.Object,System.IntPtr)">
      <summary>Converte un oggetto nel tipo COM VARIANT.</summary>
      <param name="obj">Oggetto per il quale ottenere un tipo COM VARIANT.</param>
      <param name="pDstNativeVariant">Puntatore per ricevere il VARIANT corrispondente al parametro <paramref name="obj" />.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="obj" /> è un tipo generico.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject``1(``0,System.IntPtr)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Esegue la conversione da un oggetto di tipo specificato in un oggetto COM VARIANT. </summary>
      <param name="obj">Oggetto per il quale ottenere un tipo COM VARIANT. </param>
      <param name="pDstNativeVariant">Puntatore per ricevere il VARIANT corrispondente al parametro <paramref name="obj" />. </param>
      <typeparam name="T">Tipo dell'oggetto da convertire. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForIUnknown(System.IntPtr)">
      <summary>Restituisce un'istanza di un tipo che rappresenta un oggetto COM da un puntatore alla sua interfaccia IUnknown.</summary>
      <returns>Oggetto che rappresenta l'oggetto COM non gestito specificato.</returns>
      <param name="pUnk">Puntatore all'interfaccia IUnknown. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant(System.IntPtr)">
      <summary>Converte un tipo COM VARIANT in un oggetto.</summary>
      <returns>Oggetto che corrisponde al parametro <paramref name="pSrcNativeVariant" />.</returns>
      <param name="pSrcNativeVariant">Puntatore a un COM VARIANT.</param>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> non è un tipo VARIANT valido.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> presenta un tipo non supportato.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant``1(System.IntPtr)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Esegue la conversione da un oggetto di tipo specificato a un oggetto COM VARIANT. </summary>
      <returns>Oggetto del tipo specificato che corrisponde al parametro <paramref name="pSrcNativeVariant" />. </returns>
      <param name="pSrcNativeVariant">Puntatore a un COM VARIANT. </param>
      <typeparam name="T">Tipo in cui convertire COM VARIANT. </typeparam>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> non è un tipo VARIANT valido. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> presenta un tipo non supportato. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants(System.IntPtr,System.Int32)">
      <summary>Converte una matrice di COM VARIANTs in una matrice di oggetti. </summary>
      <returns>Matrice di oggetti che corrisponde a <paramref name="aSrcNativeVariant" />.</returns>
      <param name="aSrcNativeVariant">Puntatore al primo elemento di una matrice di COM VARIANT.</param>
      <param name="cVars">Numero di tipi COM VARIANT in <paramref name="aSrcNativeVariant" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> è un numero negativo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants``1(System.IntPtr,System.Int32)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Converte una matrice di tipi COM VARIANT in una matrice di un tipo specificato. </summary>
      <returns>Matrice di oggetti <paramref name="T" /> che corrisponde a <paramref name="aSrcNativeVariant" />. </returns>
      <param name="aSrcNativeVariant">Puntatore al primo elemento di una matrice di COM VARIANT. </param>
      <param name="cVars">Numero di tipi COM VARIANT in <paramref name="aSrcNativeVariant" />. </param>
      <typeparam name="T">Tipo della matrice da restituire. </typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> è un numero negativo. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetStartComSlot(System.Type)">
      <summary>Ottiene il primo slot nella tabella di funzioni virtual (V-Table o VTBL) che contiene metodi definiti dall'utente.</summary>
      <returns>Primo slot VTBL che contiene metodi definiti dall'utente.Il primo slot è 3 se l'interfaccia è basata su IUnknown e 7 se l'interfaccia è basata su IDispatch.</returns>
      <param name="t">Tipo che rappresenta un'interfaccia.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> non è visibile da COM.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeFromCLSID(System.Guid)">
      <summary>Restituisce il tipo associato all'identificatore di classe specificato (CLSID). </summary>
      <returns>System.__ComObject indipendentemente dalla validità del CLSID. </returns>
      <param name="clsid">CLSID del tipo da restituire. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeInfoName(System.Runtime.InteropServices.ComTypes.ITypeInfo)">
      <summary>Recupera il nome del tipo rappresentato da un oggetto ITypeInfo.</summary>
      <returns>Nome del tipo a cui punta il parametro <paramref name="typeInfo" />.</returns>
      <param name="typeInfo">Oggetto che rappresenta un puntatore ITypeInfo.</param>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="typeInfo" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetUniqueObjectForIUnknown(System.IntPtr)">
      <summary>Crea un oggetto Runtime Callable Wrapper univoc (RCW) per una data interfaccia IUnknown.</summary>
      <returns>RCW univoco per l'interfaccia IUnknown specificata.</returns>
      <param name="unknown">Puntatore gestito a un'interfaccia IUnknown.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.IsComObject(System.Object)">
      <summary>Indica se un oggetto specificato rappresenta un oggetto COM.</summary>
      <returns>true se il parametro <paramref name="o" /> è un tipo COM. In caso contrario, false.</returns>
      <param name="o">Oggetto da controllare.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf``1(System.String)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Restituisce l'offset del campo della forma non gestita della classe gestita specificata.</summary>
      <returns>Offset, in byte, per il parametro <paramref name="fieldName" /> all'interno della classe specificata dichiarata tramite platform invoke. </returns>
      <param name="fieldName">Nome del campo nel tipo <paramref name="T" />. </param>
      <typeparam name="T">Tipo di valore definito o tipo di riferimento formattato.È necessario applicare l'attributo <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> alla classe.</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf(System.Type,System.String)">
      <summary>Restituisce l'offset del campo della forma non gestita della classe gestita.</summary>
      <returns>Offset, in byte, per il parametro <paramref name="fieldName" /> all'interno della classe specificata dichiarata tramite platform invoke.</returns>
      <param name="t">Tipo di valore o tipo di riferimento formattato che specifica la classe gestita.È necessario applicare <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> alla classe.</param>
      <param name="fieldName">Campo all'interno del parametro <paramref name="t" />.</param>
      <exception cref="T:System.ArgumentException">La classe non può essere esportata come struttura o il campo è non pubblico.A partire da .NET Framework versione 2.0, il campo può essere privato.</exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="t" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr)">
      <summary>Copia tutti i caratteri fino al primo carattere Null da una stringa ANSI non gestita a un oggetto <see cref="T:System.String" /> gestito e trasforma ogni carattere ANSI in Unicode.</summary>
      <returns>Stringa gestita che contiene una copia della stringa ANSI non gestita.Se <paramref name="ptr" /> è null, il metodo restituisce una stringa Null.</returns>
      <param name="ptr">L'indirizzo del primo carattere della stringa non gestita.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr,System.Int32)">
      <summary>Alloca un oggetto <see cref="T:System.String" /> gestito, vi copia un numero di caratteri specificato da una stringa ANSI non gestita e trasforma ciascun carattere ANSI nel formato Unicode.</summary>
      <returns>Stringa gestita contenente una copia della stringa ANSI nativa se il valore del parametro <paramref name="ptr" /> non è null; in caso contrario, il metodo restituisce null.</returns>
      <param name="ptr">L'indirizzo del primo carattere della stringa non gestita.</param>
      <param name="len">Conteggio dei byte della stringa di input da copiare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="len" /> è minore di zero.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringBSTR(System.IntPtr)">
      <summary>Alloca un oggetto <see cref="T:System.String" /> gestito e vi copia una stringa BSTR archiviata nella memoria non gestita.</summary>
      <returns>Stringa gestita che contiene una copia della stringa non gestita se il valore del parametro <paramref name="ptr" /> non è null; in caso contrario, questo metodo restituisce null.</returns>
      <param name="ptr">L'indirizzo del primo carattere della stringa non gestita.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr)">
      <summary>Alloca un oggetto <see cref="T:System.String" /> gestito e vi copia tutti i caratteri fino al primo carattere Null da una stringa Unicode non gestita.</summary>
      <returns>Stringa gestita che contiene una copia della stringa non gestita se il valore del parametro <paramref name="ptr" /> non è null; in caso contrario, questo metodo restituisce null.</returns>
      <param name="ptr">L'indirizzo del primo carattere della stringa non gestita.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr,System.Int32)">
      <summary>Alloca un oggetto <see cref="T:System.String" /> gestito e vi copia un numero di caratteri specificato da una stringa Unicode non gestita.</summary>
      <returns>Stringa gestita che contiene una copia della stringa non gestita se il valore del parametro <paramref name="ptr" /> non è null; in caso contrario, questo metodo restituisce null.</returns>
      <param name="ptr">L'indirizzo del primo carattere della stringa non gestita.</param>
      <param name="len">Numero di caratteri Unicode da copiare.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Effettua il marshalling di dati da un blocco di memoria non gestita a un oggetto gestito appena allocato del tipo specificato per un parametro del tipo generico. </summary>
      <returns>Oggetto gestito che contiene i dati a cui punta il parametro <paramref name="ptr" />. </returns>
      <param name="ptr">Puntatore a un blocco di memoria non gestita. </param>
      <typeparam name="T">Tipo dell'oggetto nel quale devono essere copiati i dati.Questo deve rappresentare una classe formattata o una struttura.</typeparam>
      <exception cref="T:System.ArgumentException">Il layout di <paramref name="T" /> non è sequenziale o esplicito.</exception>
      <exception cref="T:System.MissingMethodException">La classe specificata da <paramref name="T" /> non dispone di un costruttore predefinito accessibile. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Object)">
      <summary>Effettua il marshalling di dati da un blocco di memoria non gestita a un oggetto gestito.</summary>
      <param name="ptr">Puntatore a un blocco di memoria non gestita.</param>
      <param name="structure">Oggetto nel quale devono essere copiati i dati.Deve essere un'istanza di una classe formattata.</param>
      <exception cref="T:System.ArgumentException">Il layout della struttura non è sequenziale o esplicito.-oppure- La struttura è un tipo valore boxed.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Type)">
      <summary>Effettua il marshalling di dati da un blocco di memoria non gestita a un oggetto gestito appena allocato del tipo specificato.</summary>
      <returns>Oggetto gestito contenente i dati a cui punta il parametro <paramref name="ptr" />.</returns>
      <param name="ptr">Puntatore a un blocco di memoria non gestita.</param>
      <param name="structureType">Tipo di oggetto da creare.Questo oggetto deve rappresentare una classe formattata o una struttura.</param>
      <exception cref="T:System.ArgumentException">Il layout del parametro <paramref name="structureType" /> non è sequenziale o esplicito.-oppure-Il parametro <paramref name="structureType" /> è un tipo generico.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structureType" /> è null.</exception>
      <exception cref="T:System.MissingMethodException">La classe specificata da <paramref name="structureType" /> non dispone di un costruttore predefinito accessibile. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr,``0)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Effettua il marshalling di dati da un blocco di memoria non gestita a un oggetto gestito di un tipo specificato. </summary>
      <param name="ptr">Puntatore a un blocco di memoria non gestita. </param>
      <param name="structure">Oggetto nel quale devono essere copiati i dati. </param>
      <typeparam name="T">Tipo di <paramref name="structure" />.Deve essere una classe formattata.</typeparam>
      <exception cref="T:System.ArgumentException">Il layout della struttura non è sequenziale o esplicito. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.QueryInterface(System.IntPtr,System.Guid@,System.IntPtr@)">
      <summary>Richiede un puntatore a un'interfaccia specificata da un oggetto COM.</summary>
      <returns>HRESULT che indica l'esito positivo o negativo della chiamata.</returns>
      <param name="pUnk">Interfaccia su cui eseguire una query.</param>
      <param name="iid">Identificatore di interfaccia (IID) dell'interfaccia richiesta.</param>
      <param name="ppv">Quando questo metodo restituisce un risultato, contiene un riferimento all'interfaccia restituita.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr)">
      <summary>Legge un singolo byte da memoria non gestita.</summary>
      <returns>Byte letto da memoria non gestita.</returns>
      <param name="ptr">Indirizzo nella memoria non gestita da cui leggere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null. -oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr,System.Int32)">
      <summary>Legge un singolo byte in base a un determinato offset (o indice) dalla memoria non gestita.</summary>
      <returns>Byte letto da memoria non gestita all'offset fornito.</returns>
      <param name="ptr">Indirizzo di base nella memoria non gestita da cui leggere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.Object,System.Int32)">
      <summary>Legge un singolo byte in base a un determinato offset (o indice) dalla memoria non gestita. </summary>
      <returns>Byte letto da memoria non gestita all'offset fornito.</returns>
      <param name="ptr">Indirizzo di base dell'oggetto di origine nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr)">
      <summary>Legge un valore intero con segno a 16 bit dalla memoria non gestita.</summary>
      <returns>Intero con segno a 16 bit letto dalla memoria non gestita.</returns>
      <param name="ptr">Indirizzo nella memoria non gestita da cui leggere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null.-oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr,System.Int32)">
      <summary>Legge un Intero con segno a 16 bit a un offset specificato dalla memoria non gestita.</summary>
      <returns>Intero con segno a 16 bit letto dalla memoria non gestita a un offset specificato.</returns>
      <param name="ptr">Indirizzo di base nella memoria non gestita da cui leggere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.Object,System.Int32)">
      <summary>Legge un Intero con segno a 16 bit a un offset specificato dalla memoria non gestita.</summary>
      <returns>Intero con segno a 16 bit letto dalla memoria non gestita a un offset specificato.</returns>
      <param name="ptr">Indirizzo di base dell'oggetto di origine nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr)">
      <summary>Legge un valore intero con segno a 32 bit dalla memoria non gestita.</summary>
      <returns>Intero con segno a 32 bit letto dalla memoria non gestita.</returns>
      <param name="ptr">Indirizzo nella memoria non gestita da cui leggere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null.-oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr,System.Int32)">
      <summary>Legge un Intero con segno a 32 bit a un offset specificato dalla memoria non gestita.</summary>
      <returns>Intero con segno a 32 bit letto dalla memoria non gestita.</returns>
      <param name="ptr">Indirizzo di base nella memoria non gestita da cui leggere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.Object,System.Int32)">
      <summary>Legge un Intero con segno a 32 bit a un offset specificato dalla memoria non gestita.</summary>
      <returns>Intero con segno a 32 bit letto dalla memoria non gestita a un offset specificato.</returns>
      <param name="ptr">Indirizzo di base dell'oggetto di origine nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr)">
      <summary>Legge un valore intero con segno a 64 bit dalla memoria non gestita.</summary>
      <returns>Intero con segno a 64 bit letto dalla memoria non gestita.</returns>
      <param name="ptr">Indirizzo nella memoria non gestita da cui leggere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null.-oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr,System.Int32)">
      <summary>Legge un Intero con segno a 64 bit a un offset specificato dalla memoria non gestita.</summary>
      <returns>Intero con segno a 64 bit letto dalla memoria non gestita a un offset specificato.</returns>
      <param name="ptr">Indirizzo di base nella memoria non gestita da cui leggere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.Object,System.Int32)">
      <summary>Legge un Intero con segno a 64 bit a un offset specificato dalla memoria non gestita.</summary>
      <returns>Intero con segno a 64 bit letto dalla memoria non gestita a un offset specificato.</returns>
      <param name="ptr">Indirizzo di base dell'oggetto di origine nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr)">
      <summary>Legge un intero di dimensioni native del processore dalla memoria non gestita.</summary>
      <returns>Intero letto da memoria non gestita.In computer a 32 bit viene restituito un intero a 32 bit, mentre in computer a 64 bit viene restituito un intero a 64 bit.</returns>
      <param name="ptr">Indirizzo nella memoria non gestita da cui leggere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null. -oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr,System.Int32)">
      <summary>Legge un intero di dimensioni native del processore a un offset specificato dalla memoria non gestita.</summary>
      <returns>Intero letto da memoria non gestita all'offset fornito.</returns>
      <param name="ptr">Indirizzo di base nella memoria non gestita da cui leggere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.Object,System.Int32)">
      <summary>Legge un valore intero della dimensione nativa del processore dalla memoria non gestita.</summary>
      <returns>Intero letto da memoria non gestita all'offset fornito.</returns>
      <param name="ptr">Indirizzo di base dell'oggetto di origine nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della lettura.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocCoTaskMem(System.IntPtr,System.Int32)">
      <summary>Ridimensiona un blocco di memoria allocato in precedenza con <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />.</summary>
      <returns>Intero che rappresenta l'indirizzo del blocco di memoria riallocato.Questa memoria deve essere liberata con <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />.</returns>
      <param name="pv">Puntatore alla memoria allocata con <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />.</param>
      <param name="cb">Nuova dimensione del blocco allocato.</param>
      <exception cref="T:System.OutOfMemoryException">Memoria insufficiente per soddisfare la richiesta.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocHGlobal(System.IntPtr,System.IntPtr)">
      <summary>Ridimensiona un blocco di memoria allocato in precedenza con <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />.</summary>
      <returns>Puntatore alla memoria riallocata.Questa memoria deve essere liberata usando <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />.</returns>
      <param name="pv">Puntatore alla memoria allocata con <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />.</param>
      <param name="cb">Nuova dimensione del blocco allocato.Non si tratta di un puntatore; corrisponde al conteggio dei byte richiesti, di cui è stato eseguito il cast al tipo <see cref="T:System.IntPtr" />.Se si passa un puntatore, viene trattato come una dimensione.</param>
      <exception cref="T:System.OutOfMemoryException">Memoria insufficiente per soddisfare la richiesta.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Release(System.IntPtr)">
      <summary>Riduce il numero di riferimenti nell'interfaccia specificata.</summary>
      <returns>Nuovo valore del numero di riferimenti nell'interfaccia specificata dal parametro <paramref name="pUnk" />.</returns>
      <param name="pUnk">Interfaccia da liberare.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)">
      <summary>Decrementa il conteggio dei riferimenti del Runtime Callable Wrapper (RCW) specificato associato all'oggetto COM specificato.</summary>
      <returns>Nuovo valore del conteggio dei riferimenti dell'RCW associato a <paramref name="o" />.Questo valore in genere è zero poiché RCW conserva solo un riferimento all'oggetto COM incluso indipendentemente dal numero dei client gestiti che lo chiamano.</returns>
      <param name="o">Oggetto COM da rilasciare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> non è un oggetto COM valido.</exception>
      <exception cref="T:System.NullReferenceException">
        <paramref name="o" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Restituisce la dimensione di un tipo non gestito espressa in byte. </summary>
      <returns>Dimensione, in byte, del tipo specificato dal parametro di tipo generico <paramref name="T" />. </returns>
      <typeparam name="T">Tipo di cui verrà restituita la dimensione. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Object)">
      <summary>Restituisce la dimensione non gestita di un oggetto, espressa in byte.</summary>
      <returns>Dimensione dell'oggetto specificato nel codice non gestito.</returns>
      <param name="structure">Oggetto di cui verrà restituita la dimensione.</param>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="structure" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Type)">
      <summary>Restituisce la dimensione di un tipo non gestito espressa in byte.</summary>
      <returns>Dimensione del tipo specificato nel codice non gestito.</returns>
      <param name="t">Tipo di cui verrà restituita la dimensione.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="t" /> è un tipo generico.</exception>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="t" /> è null.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1(``0)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Restituisce la dimensione non gestita di un oggetto di un tipo specifico in byte. </summary>
      <returns>Dimensione, in byte, dell'oggetto specificato nel codice non gestito. </returns>
      <param name="structure">Oggetto di cui verrà restituita la dimensione. </param>
      <typeparam name="T">Tipo del parametro <paramref name="structure" />. </typeparam>
      <exception cref="T:System.ArgumentNullException">Il parametro <paramref name="structure" /> è null.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToBSTR(System.String)">
      <summary>Alloca un elemento BSTR e vi copia il contenuto di un oggetto <see cref="T:System.String" /> gestito.</summary>
      <returns>Puntatore non gestito a BSTR o 0 se <paramref name="s" /> è Null.</returns>
      <param name="s">Stringa gestita da copiare.</param>
      <exception cref="T:System.OutOfMemoryException">La memoria disponibile è insufficiente.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La lunghezza di <paramref name="s" />non è compresa nell'intervallo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemAnsi(System.String)">
      <summary>Copia il contenuto di un oggetto <see cref="T:System.String" /> gestito in un blocco di memoria allocato dall'allocatore di memoria delle attività COM non gestite.</summary>
      <returns>Intero che rappresenta un puntatore al blocco di memoria allocato per la stringa oppure 0 se <paramref name="s" /> è null.</returns>
      <param name="s">Stringa gestita da copiare.</param>
      <exception cref="T:System.OutOfMemoryException">La memoria disponibile è insufficiente.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="s" /> supera la lunghezza massima consentita dal sistema operativo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni(System.String)">
      <summary>Copia il contenuto di un oggetto <see cref="T:System.String" /> gestito in un blocco di memoria allocato dall'allocatore di memoria delle attività COM non gestite.</summary>
      <returns>Intero che rappresenta un puntatore al blocco di memoria allocato per la stringa oppure 0 se s è null.</returns>
      <param name="s">Stringa gestita da copiare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="s" /> supera la lunghezza massima consentita dal sistema operativo.</exception>
      <exception cref="T:System.OutOfMemoryException">La memoria disponibile è insufficiente.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalAnsi(System.String)">
      <summary>Copia il contenuto di un oggetto <see cref="T:System.String" /> gestito nella memoria non gestita, effettuando contemporaneamente la conversione nel formato ANSI.</summary>
      <returns>Indirizzo, nella memoria non gestita, in cui è stato copiato <paramref name="s" />, oppure 0 se <paramref name="s" /> è null.</returns>
      <param name="s">Stringa gestita da copiare.</param>
      <exception cref="T:System.OutOfMemoryException">La memoria disponibile è insufficiente.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="s" /> supera la lunghezza massima consentita dal sistema operativo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalUni(System.String)">
      <summary>Copia il contenuto di un oggetto <see cref="T:System.String" /> gestito nella memoria non gestita.</summary>
      <returns>Indirizzo, nella memoria non gestita, in cui è stato copiato <paramref name="s" />, oppure 0 se <paramref name="s" /> è null.</returns>
      <param name="s">Stringa gestita da copiare.</param>
      <exception cref="T:System.OutOfMemoryException">Il metodo non è riuscito ad allocare memoria dell'heap nativo sufficiente.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="s" /> supera la lunghezza massima consentita dal sistema operativo.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr(System.Object,System.IntPtr,System.Boolean)">
      <summary>Effettua il marshalling di dati da un oggetto gestito a un blocco di memoria non gestito.</summary>
      <param name="structure">Oggetto gestito che contiene i dati di cui effettuare il marshalling.L'oggetto deve essere una struttura o un'istanza di una classe formattata.</param>
      <param name="ptr">Puntatore a un blocco di memoria non gestito che deve essere allocato prima della chiamata al metodo.</param>
      <param name="fDeleteOld">true per chiamare il metodo <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)" /> sul parametro <paramref name="ptr" /> prima che il metodo esegui la copia dei dati.Il blocco deve contenere dati validi.Si noti che passare il valore false quando il blocco di memoria contiene già dei dati può causare una perdita di memoria.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> è un tipo di riferimento che non è una classe formattata. -oppure-L'oggetto <paramref name="structure" /> è un tipo generico. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr``1(``0,System.IntPtr,System.Boolean)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Effettua il marshalling di dati da un oggetto gestito di un tipo specificato a un blocco di memoria non gestita. </summary>
      <param name="structure">Oggetto gestito che contiene i dati di cui effettuare il marshalling.L'oggetto deve essere una struttura o un'istanza di una classe formattata.</param>
      <param name="ptr">Puntatore a un blocco di memoria non gestito che deve essere allocato prima della chiamata al metodo. </param>
      <param name="fDeleteOld">true per chiamare il metodo <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)" /> sul parametro <paramref name="ptr" /> prima che il metodo esegui la copia dei dati.Il blocco deve contenere dati validi.Si noti che passare il valore false quando il blocco di memoria contiene già dei dati può causare una perdita di memoria.</param>
      <typeparam name="T">Tipo dell'oggetto gestito. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> è un tipo di riferimento che non è una classe formattata. </exception>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemDefaultCharSize">
      <summary>Rappresenta la dimensione predefinita del carattere nel sistema. Il valore predefinito è 2 per i sistemi Unicode e 1 per i sistemi ANSI.Questo campo è di sola lettura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemMaxDBCSCharSize">
      <summary>Rappresenta la dimensione massima di un set di caratteri a doppio byte (DBCS, double-byte character set), in byte, per il sistema operativo corrente.Questo campo è di sola lettura.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32)">
      <summary>Genera un'eccezione con un valore HRESULT di un errore specifico.</summary>
      <param name="errorCode">Valore HRESULT corrispondente all'eccezione desiderata.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)">
      <summary>Genera un'eccezione con un HRESULT di errore specifico, basato sull'interfaccia IErrorInfo specificata.</summary>
      <param name="errorCode">Valore HRESULT corrispondente all'eccezione desiderata.</param>
      <param name="errorInfo">Puntatore all'interfaccia IErrorInfo che fornisce ulteriori informazioni sull'errore.È possibile specificare IntPtr(0) per usare l'interfaccia IErrorInfo corrente oppure IntPtr(-1) per ignorare l'interfaccia IErrorInfo corrente e costruire l'eccezione unicamente dal codice errore.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement(System.Array,System.Int32)">
      <summary>Ottiene l'indirizzo dell'elemento in corrispondenza dell'indice specificato all'interno della matrice specificata.</summary>
      <returns>Indirizzo di <paramref name="index" /> all'interno di <paramref name="arr" />.</returns>
      <param name="arr">Matrice che contiene l'elemento desiderato.</param>
      <param name="index">Indice nel parametro <paramref name="arr" /> dell'elemento desiderato.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement``1(``0[],System.Int32)">
      <summary>[Supportato in .NET Framework 4.5.1 e versioni successive] Ottiene l'indirizzo dell'elemento in corrispondenza dell'indice specificato in una matrice del tipo specificato. </summary>
      <returns>Indirizzo di <paramref name="index" /> all'interno di <paramref name="arr" />. </returns>
      <param name="arr">Matrice che contiene l'elemento desiderato. </param>
      <param name="index">Indice dell'elemento desiderato nella matrice <paramref name="arr" />. </param>
      <typeparam name="T">Tipo della matrice </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Byte)">
      <summary>Scrive un valore a singolo byte nella memoria non gestita.</summary>
      <param name="ptr">Indirizzo nella memoria non gestita sul quale scrivere.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null.-oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Int32,System.Byte)">
      <summary>Scrive un valore a singolo byte nella memoria non gestita a un offset specifico.</summary>
      <param name="ptr">Indirizzo di base nella memoria non gestita su cui scrivere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.Object,System.Int32,System.Byte)">
      <summary>Scrive un valore a singolo byte nella memoria non gestita a un offset specifico.</summary>
      <param name="ptr">L'indirizzo di base dell'oggetto di destinazione nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Char)">
      <summary>Scrive un carattere sotto forma di intero a 16 bit nella memoria non gestita.</summary>
      <param name="ptr">Indirizzo nella memoria non gestita sul quale scrivere.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null.-oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int16)">
      <summary>Scrive un valore intero a 16 bit nella memoria non gestita.</summary>
      <param name="ptr">Indirizzo nella memoria non gestita sul quale scrivere.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null.-oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Char)">
      <summary>Scrive un Intero con segno a 16 bit nella memoria non gestita in corrispondenza di un offset specificato.</summary>
      <param name="ptr">L'indirizzo di base nell'heap nativo su cui scrivere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Int16)">
      <summary>Scrive un Intero con segno a 16 bit nella memoria non gestita in corrispondenza di un offset specificato.</summary>
      <param name="ptr">Indirizzo di base nella memoria non gestita su cui scrivere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Char)">
      <summary>Scrive un Intero con segno a 16 bit nella memoria non gestita in corrispondenza di un offset specificato.</summary>
      <param name="ptr">L'indirizzo di base dell'oggetto di destinazione nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Int16)">
      <summary>Scrive un Intero con segno a 16 bit nella memoria non gestita in corrispondenza di un offset specificato.</summary>
      <param name="ptr">L'indirizzo di base dell'oggetto di destinazione nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura. </param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32)">
      <summary>Scrive un valore intero con segno a 32 bit nella memoria non gestita.</summary>
      <param name="ptr">Indirizzo nella memoria non gestita sul quale scrivere.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null. -oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32,System.Int32)">
      <summary>Scrive un Intero con segno a 32 bit nella memoria non gestita in corrispondenza di un offset specificato.</summary>
      <param name="ptr">Indirizzo di base nella memoria non gestita su cui scrivere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.Object,System.Int32,System.Int32)">
      <summary>Scrive un Intero con segno a 32 bit nella memoria non gestita in corrispondenza di un offset specificato.</summary>
      <param name="ptr">L'indirizzo di base dell'oggetto di destinazione nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int32,System.Int64)">
      <summary>Scrive un Intero con segno a 64 bit nella memoria non gestita in corrispondenza di un offset specificato.</summary>
      <param name="ptr">Indirizzo di base nella memoria non gestita da scrivere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int64)">
      <summary>Scrive un valore intero con segno a 64 bit nella memoria non gestita.</summary>
      <param name="ptr">Indirizzo nella memoria non gestita sul quale scrivere.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null.-oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.Object,System.Int32,System.Int64)">
      <summary>Scrive un Intero con segno a 64 bit nella memoria non gestita in corrispondenza di un offset specificato.</summary>
      <param name="ptr">L'indirizzo di base dell'oggetto di destinazione nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.Int32,System.IntPtr)">
      <summary>Scrive un intero di dimensioni native del processore nella memoria non gestita in corrispondenza di un offset specificato.</summary>
      <param name="ptr">Indirizzo di base nella memoria non gestita su cui scrivere.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.IntPtr)">
      <summary>Scrive un valore intero della dimensione nativa del processore nella memoria non gestita.</summary>
      <param name="ptr">Indirizzo nella memoria non gestita sul quale scrivere.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> non è un formato riconosciuto.-oppure-<paramref name="ptr" /> è null.-oppure-<paramref name="ptr" /> non è valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.Object,System.Int32,System.IntPtr)">
      <summary>Scrive un valore intero della dimensione nativa del processore nella memoria non gestita.</summary>
      <param name="ptr">L'indirizzo di base dell'oggetto di destinazione nella memoria non gestita.</param>
      <param name="ofs">Offset di byte supplementare, aggiunto al parametro <paramref name="ptr" /> prima della scrittura.</param>
      <param name="val">Valore da scrivere.</param>
      <exception cref="T:System.AccessViolationException">L'indirizzo di base (<paramref name="ptr" />) più il byte di offset (<paramref name="ofs" />) produce un indirizzo Null o non valido.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> è un oggetto <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.Questo metodo non accetta parametri <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(System.IntPtr)">
      <summary>Libera un puntatore BSTR allocato tramite il metodo <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToBSTR(System.Security.SecureString)" />.</summary>
      <param name="s">Indirizzo dell'elemento BSTR da liberare.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemAnsi(System.IntPtr)">
      <summary>Libera un puntatore a una stringa non gestita allocato tramite il metodo <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemAnsi(System.Security.SecureString)" />.</summary>
      <param name="s">Indirizzo della stringa non gestita da liberare.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemUnicode(System.IntPtr)">
      <summary>Libera un puntatore a una stringa non gestita allocato tramite il metodo <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemUnicode(System.Security.SecureString)" />.</summary>
      <param name="s">Indirizzo della stringa non gestita da liberare.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocAnsi(System.IntPtr)">
      <summary>Libera un puntatore a una stringa non gestita allocato tramite il metodo <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocAnsi(System.Security.SecureString)" />.</summary>
      <param name="s">Indirizzo della stringa non gestita da liberare.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocUnicode(System.IntPtr)">
      <summary>Libera un puntatore a una stringa non gestita allocato tramite il metodo <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocUnicode(System.Security.SecureString)" />.</summary>
      <param name="s">Indirizzo della stringa non gestita da liberare.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalAsAttribute">
      <summary>Indica come eseguire il marshalling dei dati tra codice gestito e codice non gestito.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Int16)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> con il valore <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> specificato.</summary>
      <param name="unmanagedType">Valore in base al quale viene eseguito il marshalling dei dati. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Runtime.InteropServices.UnmanagedType)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> con il membro di enumerazione <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> specificato.</summary>
      <param name="unmanagedType">Valore in base al quale viene eseguito il marshalling dei dati. </param>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType">
      <summary>Specifica il tipo di elemento dell'oggetto <see cref="F:System.Runtime.InteropServices.UnmanagedType.LPArray" /> o <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValArray" /> non gestito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.IidParameterIndex">
      <summary>Specifica l'indice di parametro dell'attributo iid_is non gestito utilizzato da COM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalCookie">
      <summary>Fornisce informazioni aggiuntive a un gestore di marshalling personalizzato.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType">
      <summary>Specifica il nome completo di un gestore di marshalling personalizzato.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalTypeRef">
      <summary>Implementa <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType" /> come tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType">
      <summary>Indica il tipo di elemento di <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> .</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArrayUserDefinedSubType">
      <summary>Indica il tipo di elemento di <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> definito dall'utente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst">
      <summary>Indica il numero di elementi nella matrice a lunghezza fissa o il numero di caratteri (non byte) in una stringa da importare.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex">
      <summary>Indica il parametro in base zero che contiene il numero di elementi della matrice, come size_is in COM.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.MarshalAsAttribute.Value">
      <summary>Ottiene il valore <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> in base al quale viene eseguito il marshalling dei dati.</summary>
      <returns>Valore <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> in base al quale viene eseguito il marshalling dei dati.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalDirectiveException">
      <summary>Eccezione generata dal gestore di marshalling quando rileva un oggetto <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> non supportato.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor">
      <summary>Inizializza una nuova istanza della classe MarshalDirectiveException con proprietà predefinite.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe MarshalDirectiveException con un messaggio di errore specificato.</summary>
      <param name="message">Messaggio di errore che specifica la causa dell'eccezione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.MarshalDirectiveException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.OptionalAttribute">
      <summary>Indica che un parametro è facoltativo.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.OptionalAttribute.#ctor">
      <summary>Inizializza una nuova istanza della classe OptionalAttribute con valori predefiniti.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.PreserveSigAttribute">
      <summary>Indica che la trasformazione della firma del valore HRESULT o retval eseguita durante le chiamate di interoperabilità COM, deve essere evitata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.PreserveSigAttribute.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.PreserveSigAttribute" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayRankMismatchException">
      <summary>Eccezione generata quando il numero di dimensioni di un SAFEARRAY in entrata non corrisponde al numero di dimensioni specificato nella firma gestita.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor">
      <summary>Inizializza una nuova istanza della classe SafeArrayTypeMismatchException con valori predefiniti.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe SafeArrayRankMismatchException con il messaggio specificato.</summary>
      <param name="message">Messaggio che indica la causa dell'eccezione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.SafeArrayRankMismatchException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException">
      <summary>Eccezione generata quando il tipo di SAFEARRAY in entrata non corrisponde al tipo specificato nella firma gestita.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor">
      <summary>Inizializza una nuova istanza della classe SafeArrayTypeMismatchException con valori predefiniti.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe SafeArrayTypeMismatchException con il messaggio specificato.</summary>
      <param name="message">Messaggio che indica la causa dell'eccezione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeBuffer">
      <summary>Fornisce un buffer di memoria controllato che può essere utilizzato per la lettura e la scrittura.Tenta di accedere a memoria fuori delle eccezioni generate dal buffer controllato (sottocarichi e sovraccarichi).</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.#ctor(System.Boolean)">
      <summary>Crea una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.SafeBuffer" />, specificando se l'handle del buffer deve essere rilasciato in modo affidabile. </summary>
      <param name="ownsHandle">true per rilasciare in modo affidabile l'handle durante la fase di finalizzazione; false per impedire il rilascio affidabile (non consigliato).</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)">
      <summary>Ottiene un puntatore da un oggetto <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> per un blocco di memoria.</summary>
      <param name="pointer">Puntatore di byte, passato per riferimento, per ricevere il puntatore dall'interno dell'oggetto <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.È necessario impostare questo puntatore a null prima di chiamare questo metodo.</param>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> non è stato chiamato. </exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.ByteLength">
      <summary>Ottiene la dimensione, in byte, del buffer.</summary>
      <returns>Numero di byte nel buffer di memoria.</returns>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> non è stato chiamato.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize``1(System.UInt32)">
      <summary>Definisce la dimensione di allocazione dell'area della memoria specificando il numero di tipi di valore.Questo metodo deve essere chiamato prima di utilizzare l'istanza <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numElements">Numero di elementi del tipo di valore per il quale allocare memoria.</param>
      <typeparam name="T">Tipo di valore per il quale allocare memoria.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> è minore di zero.- oppure -<paramref name="numElements" /> moltiplicato per le dimensioni di ogni elemento è maggiore dello spazio degli indirizzi disponibile.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt32,System.UInt32)">
      <summary>Specifica la dimensione di allocazione del buffer di memoria tramite il numero specificato di elementi e la dimensione dell'elemento.Questo metodo deve essere chiamato prima di utilizzare l'istanza <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numElements">Numero di elementi contenuti nel buffer.</param>
      <param name="sizeOfEachElement">Dimensioni di ogni elemento nel buffer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> è minore di zero. - oppure -<paramref name="sizeOfEachElement" /> è minore di zero.- oppure -<paramref name="numElements" /> moltiplicato per <paramref name="sizeOfEachElement" /> è maggiore dello spazio degli indirizzi disponibile.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt64)">
      <summary>Definisce la dimensione di allocazione, in byte, dell'area di memoria.Questo metodo deve essere chiamato prima di utilizzare l'istanza <see cref="T:System.Runtime.InteropServices.SafeBuffer" />.</summary>
      <param name="numBytes">Numero di byte nel buffer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numBytes" /> è minore di zero.- oppure -<paramref name="numBytes" /> è maggiore dello spazio degli indirizzi disponibile.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.IsInvalid"></member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Read``1(System.UInt64)">
      <summary>Legge un tipo di valore dalla memoria all'offset specificato.</summary>
      <returns>Il tipo di valore che è stato letto dalla memoria.</returns>
      <param name="byteOffset">Posizione da cui leggere il tipo di valore.È possibile che sia necessario considerare problemi di allineamento.</param>
      <typeparam name="T">Tipo di valore da leggere.</typeparam>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> non è stato chiamato.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReadArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Legge il numero specificato di tipi di valore dal buffer di memoria partendo dall'offset e li scrive in una matrice iniziando all'indice. </summary>
      <param name="byteOffset">Posizione dalla quale iniziare a leggere.</param>
      <param name="array">Matrice di output in cui scrivere.</param>
      <param name="index">Posizione all'interno della matrice di output da cui iniziare a scrivere.</param>
      <param name="count">Numero di tipi di valore da leggere dalla matrice di input e scrivere nella matrice di output.</param>
      <typeparam name="T">Tipo di valore da leggere.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.- oppure -<paramref name="count" /> è minore di zero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentException">La lunghezza della matrice meno l'indice è inferiore a <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> non è stato chiamato.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReleasePointer">
      <summary>Rilascia un puntatore ottenuto dal metodo <see cref="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)" />.</summary>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> non è stato chiamato.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Write``1(System.UInt64,``0)">
      <summary>Scrive un tipo di valore in memoria in una posizione specificata.</summary>
      <param name="byteOffset">Posizione dalla quale iniziare a scrivere.È possibile che sia necessario considerare problemi di allineamento.</param>
      <param name="value">Valore da scrivere.</param>
      <typeparam name="T">Tipo di valore da scrivere.</typeparam>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> non è stato chiamato.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.WriteArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>Scrive il numero specificato di tipi di valore in una posizione di memoria leggendo i byte a partire dalla posizione specificata nella matrice di input.</summary>
      <param name="byteOffset">Posizione nella memoria sulla quale scrivere.</param>
      <param name="array">Matrice di input.</param>
      <param name="index">Offset nella matrice da cui cominciare a leggere.</param>
      <param name="count">Numero di tipi di valore da scrivere.</param>
      <typeparam name="T">Tipo di valore da scrivere.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro <paramref name="index" /> o <paramref name="count" /> è minore di zero.</exception>
      <exception cref="T:System.ArgumentException">La lunghezza della matrice di input meno <paramref name="index" /> è inferiore a <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> non è stato chiamato.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SEHException">
      <summary>Rappresenta gli errori Structured Exception Handler (SEH). </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.SEHException" />. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.SEHException" /> con un messaggio specificato.</summary>
      <param name="message">Messaggio che indica la causa dell'eccezione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.SEHException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.CanResume">
      <summary>Indica se l'eccezione può essere recuperata e se è possibile continuare l'esecuzione del codice dal punto in cui è stata generata l'eccezione.</summary>
      <returns>Sempre false, perché non sono implementate eccezioni recuperabili.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.TypeIdentifierAttribute">
      <summary>Fornisce supporto per l'equivalenza del tipo.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor">
      <summary>Crea una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" />. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)">
      <summary>Crea una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> con ambito e identificatore specificati. </summary>
      <param name="scope">Prima stringa di equivalenza di tipo.</param>
      <param name="identifier">Seconda stringa di equivalenza di tipo.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Identifier">
      <summary>Ottiene il valore del parametro <paramref name="identifier" /> passato al costruttore <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />.</summary>
      <returns>Valore del parametro <paramref name="identifier" /> del costruttore.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Scope">
      <summary>Ottiene il valore del parametro <paramref name="scope" /> passato al costruttore <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" />.</summary>
      <returns>Valore del parametro <paramref name="scope" /> del costruttore.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnknownWrapper">
      <summary>Esegue il wrapping degli oggetti di cui il gestore di marshalling deve eseguire il marshalling come VT_UNKNOWN.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnknownWrapper.#ctor(System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.UnknownWrapper" /> con l'oggetto di cui eseguire il wrapping.</summary>
      <param name="obj">Oggetto di cui eseguire il wrapping. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.UnknownWrapper.WrappedObject">
      <summary>Ottiene l'oggetto contenuto da questo wrapper.</summary>
      <returns>Oggetto di cui è stato eseguito il wrapping.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute">
      <summary>Controlla il comportamento di marshalling di una firma del delegato passata come puntatore a funzione non gestito a o da codice non gestito.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute" /> con la convenzione di chiamata specificata. </summary>
      <param name="callingConvention">Convenzione di chiamata specificata.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.BestFitMapping">
      <summary>Abilita o disabilita il comportamento di mapping più appropriato per la conversione di caratteri Unicode in caratteri ANSI.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CallingConvention">
      <summary>Ottiene il valore della convenzione di chiamata.</summary>
      <returns>Valore della convenzione di chiamata specificato dal costruttore <see cref="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)" />.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CharSet">
      <summary>Indica come eseguire il marshalling dei parametri di stringa nel metodo e controlla l'alterazione dei nomi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.SetLastError">
      <summary>Indica se il destinatario della chiamata chiama la funzione API Win32 SetLastError prima della restituzione di un risultato dal metodo con attributi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.ThrowOnUnmappableChar">
      <summary>Abilita o disabilita la generazione di un'eccezione su un carattere Unicode di cui non è possibile eseguire il mapping convertito in un carattere ANSI "?".</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedType">
      <summary>Identifica come effettuare il marshalling di parametri o campi nel codice gestito. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AnsiBStr">
      <summary>Stringa di caratteri ANSI costituita da un singolo byte con prefisso di lunghezza.È possibile usare questo membro sul tipo di dati <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AsAny">
      <summary>Tipo dinamico che determina il tipo di un oggetto in fase di esecuzione ed effettua il marshalling dell'oggetto come quel tipo.Questo membro è valido solo per metodi di platform invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Bool">
      <summary>Valore booleano a 4 byte (true != 0, false = 0).Si tratta del tipo BOOL Win32.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.BStr">
      <summary>Stringa di caratteri Unicode costituita da un byte doppio con prefisso di lunghezza.È possibile usare questo membro, ovvero la stringa predefinita in COM, sul tipo di dati <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValArray">
      <summary>Quando la proprietà <see cref="P:System.Runtime.InteropServices.MarshalAsAttribute.Value" /> è impostata su ByValArray, il campo <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> deve essere impostato per indicare il numero di elementi nella matrice.Il campo <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType" /> può contenere facoltativamente l'oggetto <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> degli elementi della matrice quando è necessario distinguere tra più tipi di stringa.È possibile usare solo questo oggetto <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> su una matrice i cui elementi vengono visualizzati come campi in una struttura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr">
      <summary>Usato per matrici di caratteri di lunghezza fissa inline visualizzate all'interno di una struttura.Il tipo di carattere usato con <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr" /> è determinato dall'argomento <see cref="T:System.Runtime.InteropServices.CharSet" /> dell'attributo <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> applicato alla struttura che lo contiene.Usare sempre il campo <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> per indicare la dimensione della matrice.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Currency">
      <summary>Tipo di valuta.Usato su un oggetto <see cref="T:System.Decimal" /> per effettuare il marshalling del valore decimale come tipo di valuta COM anziché come Decimal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Error">
      <summary>Tipo nativo associato a <see cref="F:System.Runtime.InteropServices.UnmanagedType.I4" /> o a <see cref="F:System.Runtime.InteropServices.UnmanagedType.U4" /> e che determina l'esportazione del parametro come HRESULT nella libreria dei tipi esportata.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.FunctionPtr">
      <summary>Intero che può essere usato come puntatore a funzione di tipo C.È possibile usare questo membro su un tipo di dati <see cref="T:System.Delegate" /> o su un tipo che eredita da <see cref="T:System.Delegate" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.HString">
      <summary>Stringa Windows Runtime.È possibile usare questo membro sul tipo di dati <see cref="T:System.String" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I1">
      <summary>Intero con segno a 1 byte.È possibile usare questo membro per trasformare un valore booleano in un bool a 1 byte di tipo C (true = 1, false = 0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I2">
      <summary>Intero con segno a 2 byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I4">
      <summary>Intero con segno a 4 byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I8">
      <summary>Intero con segno a 8 byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IDispatch">
      <summary>Puntatore a IDispatch COM (Object in Microsoft Visual Basic 6.0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IInspectable">
      <summary>Puntatore a interfaccia Windows Runtime.È possibile usare questo membro sul tipo di dati <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Interface">
      <summary>Puntatore a interfaccia COM.L'oggetto <see cref="T:System.Guid" /> dell'interfaccia è ottenuto dai metadati della classe.Usare questo membro per specificare il tipo di interfaccia esatto o il tipo di interfaccia predefinito se lo si applica a una classe.Questo membro produce lo stesso comportamento di <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" /> quando lo si applica al tipo di dati <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IUnknown">
      <summary>Puntatore a IUnknown COM.È possibile usare questo membro sul tipo di dati <see cref="T:System.Object" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPArray">
      <summary>Puntatore al primo elemento di una matrice di tipo C.Quando si effettua il marshalling da codice gestito a non gestito, la lunghezza della matrice dipende dalla lunghezza della matrice gestita.Quando si effettua il marshalling da codice non gestito a gestito, la lunghezza della matrice dipende dai campi <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> e <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex" />, facoltativamente seguiti dal tipo non gestito degli elementi all'interno della matrice quando è necessario distinguere tra più tipi di stringa.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStr">
      <summary>Stringa di caratteri ANSI a un byte a terminazione Null.È possibile usare questo membro sui tipi di dati <see cref="T:System.String" /> e <see cref="T:System.Text.StringBuilder" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStruct">
      <summary>Puntatore a una struttura di tipo C usata per effettuare il marshalling di classi formattate gestite.Questo membro è valido solo per metodi di platform invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPTStr">
      <summary>Stringa di caratteri dipendente dalla piattaforma: ANSI su Windows 98 e Unicode su Windows NT e Windows XP.Questo valore è supportato solo per platform invoke, non per l'interoperabilità COM, perché l'esportazione di una stringa di tipo LPTStr non è supportata.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPWStr">
      <summary>Stringa di caratteri Unicode a 2 byte a terminazione Null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R4">
      <summary>Numero a virgola mobile a 4 byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R8">
      <summary>Numero a virgola mobile a 8 byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SafeArray">
      <summary>SafeArray è una matrice autodescrittiva che contiene il tipo, la dimensioni e i limiti dei dati della matrice associati.È possibile usare questo membro con il campo <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType" /> per eseguire l'override del tipo di elemento predefinito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Struct">
      <summary>VARIANT usato per effettuare il marshalling di tipi valore e classi formattate gestite.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysInt">
      <summary>Intero con segno dipendente dalla piattaforma: a 4 byte in Windows a 32 bit, a 8 byte in Windows a 64 bit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysUInt">
      <summary>Intero senza segno dipendente dalla piattaforma: a 4 byte in Windows a 32 bit, a 8 byte in Windows a 64 bit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.TBStr">
      <summary>Stringa char dipendente dalla piattaforma con prefisso di lunghezza: ANSI in Windows 98, Unicode in Windows NT.Questo membro simile a BSTR viene usato raramente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U1">
      <summary>Intero senza segno a 1 byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U2">
      <summary>Intero senza segno a 2 byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U4">
      <summary>Intero senza segno a 4 byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U8">
      <summary>Intero senza segno a 8 byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VariantBool">
      <summary>Tipo VARIANT_BOOL definito da OLE a 2 byte (true = -1, false = 0).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VBByRefStr">
      <summary>Valore che consente a Visual Basic di modificare una stringa in codice non gestito e riflettere i risultati in codice gestito.Questo valore è supportato solo per platform invoke.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VarEnum">
      <summary>Indica le modalità di esecuzione del marshalling degli elementi della matrice quando viene effettuato il marshalling di una matrice da codice gestito a codice non gestito come <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ARRAY">
      <summary>Indica un puntatore a SAFEARRAY.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB">
      <summary>Indica i byte per il prefisso di lunghezza.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB_OBJECT">
      <summary>Indica che un blob contiene un oggetto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BOOL">
      <summary>Indica un valore Boolean.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BSTR">
      <summary>Indica una stringa BSTR.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BYREF">
      <summary>Indica che un valore è un riferimento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CARRAY">
      <summary>Indica una matrice di tipo C.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CF">
      <summary>Indica il formato degli Appunti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CLSID">
      <summary>Indica un ID di classe.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CY">
      <summary>Indica un valore di valuta.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DATE">
      <summary>Indica un valore DATE.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DECIMAL">
      <summary>Indica un valore decimal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH">
      <summary>Indica un puntatore a IDispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_EMPTY">
      <summary>Indica che un valore non è stato specificato.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ERROR">
      <summary>Indica un SCODE.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_FILETIME">
      <summary>Indica un valore FILETIME.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_HRESULT">
      <summary>Indica un HRESULT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I1">
      <summary>Indica un valore char.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I2">
      <summary>Indica un intero short.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I4">
      <summary>Indica un intero long.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I8">
      <summary>Indica un intero a 64 bit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_INT">
      <summary>Indica un intero.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPSTR">
      <summary>Indica una stringa con terminazione di tipo Null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPWSTR">
      <summary>Indica una stringa di caratteri estesi con terminazione null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_NULL">
      <summary>Indica un valore null, simile a un valore null in SQL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_PTR">
      <summary>Indica un tipo puntatore.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R4">
      <summary>Indica un valore float.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R8">
      <summary>Indica un valore double.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_RECORD">
      <summary>Indica un tipo definito dall'utente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_SAFEARRAY">
      <summary>Indica un SAFEARRAY.Non valido in un VARIANT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORAGE">
      <summary>Indica che segue il nome di un archivio.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORED_OBJECT">
      <summary>Indica che un archivio contiene un oggetto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAM">
      <summary>Indica che segue il nome di un flusso.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAMED_OBJECT">
      <summary>Indica che un flusso contiene un oggetto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI1">
      <summary>Indica un valore byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI2">
      <summary>Indica un valore unsignedshort.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI4">
      <summary>Indica un valore unsignedlong.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI8">
      <summary>Rappresenta un intero senza segno a 64 bit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UINT">
      <summary>Indica un intero unsigned.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UNKNOWN">
      <summary>Indica un puntatore a IUnknown.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_USERDEFINED">
      <summary>Indica un tipo definito dall'utente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VARIANT">
      <summary>Indica un puntatore far VARIANT.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VECTOR">
      <summary>Indica una matrice a conteggio semplice.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VOID">
      <summary>Indica un void di tipo C.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VariantWrapper">
      <summary>Esegue il marshalling dei dati di tipo VT_VARIANT | VT_BYREF dal codice gestito al codice non gestito.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.VariantWrapper.#ctor(System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> per il parametro <see cref="T:System.Object" /> specificato.</summary>
      <param name="obj">Oggetto di cui eseguire il marshalling. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.VariantWrapper.WrappedObject">
      <summary>Ottiene l'oggetto incluso dall'oggetto <see cref="T:System.Runtime.InteropServices.VariantWrapper" />.</summary>
      <returns>Oggetto incluso dall'oggetto <see cref="T:System.Runtime.InteropServices.VariantWrapper" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ADVF">
      <summary>Specifica il comportamento richiesto per l'impostazione di un sink di notifica o una connessione di inserimento nella cache a un oggetto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_DATAONSTOP">
      <summary>Per connessioni consultive di dati, assicura l'accessibilità ai dati. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_NODATA">
      <summary>Per le connessioni consultive di dati (<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.DAdvise(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.ADVF,System.Runtime.InteropServices.ComTypes.IAdviseSink,System.Int32@)" /> o <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />), questo flag richiede che l'oggetto dati non invii i dati quando chiama <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_ONLYONCE">
      <summary>Richiede che l'oggetto effettui una sola notifica di modifica o aggiornamento della cache prima di eliminare la connessione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_PRIMEFIRST">
      <summary>Richiede che l'oggetto non attenda la modifica dei dati o della visualizzazione prima di effettuare una chiamata iniziale al metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> (per connessioni consultive di dati o visualizzazione) o aggiornare la cache (per connessioni cache).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN">
      <summary>Questo valore viene utilizzato dalle applicazioni di oggetti DLL e gestori di oggetti che disegnano i propri oggetti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_NOHANDLER">
      <summary>Sinonimo di <see cref="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN" />, utilizzato più spesso.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_ONSAVE">
      <summary>Per le connessioni cache, questo flag aggiorna la rappresentazione nella cache solo quando viene salvato l'oggetto contenente la cache.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BIND_OPTS">
      <summary>Archivia i parametri utilizzati durante un'operazione di associazione del moniker.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.cbStruct">
      <summary>Specifica la dimensione, in byte, della struttura BIND_OPTS.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.dwTickCountDeadline">
      <summary>Indica la quantità di tempo (tempo di clock in millisecondi, restituito dalla funzione GetTickCount) specificata dal chiamante per completare l'operazione di associazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfFlags">
      <summary>Controlla gli aspetti delle operazioni di associazione del moniker.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfMode">
      <summary>Rappresenta i flag da utilizzare all'apertura del file che contiene l'oggetto identificato dal moniker.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BINDPTR">
      <summary>Contiene un puntatore a una struttura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> o <see cref="T:System.Runtime.InteropServices.VARDESC" /> associata o a un'interfaccia ITypeComp.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpfuncdesc">
      <summary>Rappresenta un puntatore a una struttura <see cref="T:System.Runtime.InteropServices.FUNCDESC" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lptcomp">
      <summary>Rappresenta un puntatore a un'interfaccia <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeComp" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpvardesc">
      <summary>Rappresenta un puntatore a una struttura <see cref="T:System.Runtime.InteropServices.VARDESC" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CALLCONV">
      <summary>Identifica la convenzione di chiamata utilizzata da un metodo descritto in una struttura METHODDATA.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_CDECL">
      <summary>Indica che per un metodo è utilizzata la convenzione di chiamata CDECL (C declaration). </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MACPASCAL">
      <summary>Indica che per un metodo è utilizzata la convenzione di chiamata MACPASCAL (Macintosh Pascal).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MAX">
      <summary>Indica la fine dell'enumerazione <see cref="T:System.Runtime.InteropServices.ComTypes.CALLCONV" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWCDECL">
      <summary>Indica che per un metodo è utilizzata la convenzione di chiamata CDECL MPW (Macintosh Programmers' Workbench).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWPASCAL">
      <summary>Indica che per un metodo è utilizzata la convenzione di chiamata PASCAL MPW (Macintosh Programmers' Workbench).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MSCPASCAL">
      <summary>Indica che per un metodo è utilizzata la convenzione di chiamata MSCPASCAL (MSC Pascal).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_PASCAL">
      <summary>Indica che per un metodo è utilizzata la convenzione di chiamata Pascal.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_RESERVED">
      <summary>Questo valore è riservato per un utilizzo futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_STDCALL">
      <summary>Indica che per un metodo è utilizzata la convenzione di chiamata standard (STDCALL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_SYSCALL">
      <summary>Indica che per un metodo è utilizzata la convenzione di chiamata SYSCALL standard.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CONNECTDATA">
      <summary>Descrive una connessione esistente a un dato punto di connessione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.dwCookie">
      <summary>Rappresenta un token di connessione restituito da una chiamata a <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.pUnk">
      <summary>Rappresenta un puntatore all'interfaccia IUnknown su un sink consultivo connesso.Il chiamante deve chiamare IUnknown::Release su questo puntatore quando la struttura CONNECTDATA non è più necessaria.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DATADIR">
      <summary>Specifica la direzione del flusso di dati nel parametro <paramref name="dwDirection" /> del metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" />.Determina i formati che possono essere enumerati dall'enumeratore risultante.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_GET">
      <summary>Richiede che il metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> fornisca un enumeratore per i formati che è possibile specificare nel metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.GetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_SET">
      <summary>Richiede che il metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> fornisca un enumeratore per i formati che è possibile specificare nel metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.SetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@,System.Boolean)" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DESCKIND">
      <summary>Identifica la descrizione del tipo a cui viene effettuata l'associazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_FUNCDESC">
      <summary>Indica che è stata restituita una struttura <see cref="T:System.Runtime.InteropServices.FUNCDESC" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_IMPLICITAPPOBJ">
      <summary>Indica che è stato restituito un oggetto IMPLICITAPPOBJ.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_MAX">
      <summary>Indica un marcatore di fine enumerazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_NONE">
      <summary>Indica che non è stata trovata alcuna corrispondenza.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_TYPECOMP">
      <summary>Indica che è stato restituito un oggetto TYPECOMP.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_VARDESC">
      <summary>Indica che è stato restituito un oggetto VARDESC.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DISPPARAMS">
      <summary>Contiene gli argomenti passati a un metodo o a una proprietà da IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cArgs">
      <summary>Rappresenta il numero di argomenti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cNamedArgs">
      <summary>Rappresenta il numero di argomenti denominati </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgdispidNamedArgs">
      <summary>Rappresenta gli ID dispatch degli argomenti denominati.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgvarg">
      <summary>Rappresenta un riferimento alla matrice di argomenti.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DVASPECT">
      <summary>Specifica i dati desiderati o l'aspetto di visualizzazione dell'oggetto quando si disegnano o ottengono dati.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_CONTENT">
      <summary>Rappresentazione di un oggetto che consente la visualizzazione dell'oggetto stesso come incorporato all'interno di un contenitore.Questo valore è in genere specificato per gli oggetti documento compositi.La presentazione può essere fornita per lo schermo o la stampante.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_DOCPRINT">
      <summary>Rappresentazione di un oggetto sullo schermo come se fosse stampato da una stampante scegliendo Stampa dal menu File.I dati descritti possono rappresentare una sequenza di pagine.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_ICON">
      <summary>Rappresentazione in formato di icona di un oggetto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_THUMBNAIL">
      <summary>Rappresentazione in formato anteprima di un oggetto che consente la visualizzazione dell'oggetto stesso in uno strumento di esplorazione.L'anteprima è una bitmap di circa 120 per 120 pixel, a 16 colori (consigliato), indipendente dal dispositivo, di cui può essere eseguito il wrapping in un metafile.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC">
      <summary>Contiene la descrizione dei tipi e le informazioni sul trasferimento di processi per una variabile, una funzione o un parametro di funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.desc">
      <summary>Contiene informazioni su un elemento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.tdesc">
      <summary>Identifica il tipo dell'elemento.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION">
      <summary>Contiene informazioni su un elemento. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.idldesc">
      <summary>Contiene informazioni per la gestione remota dell'elemento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.paramdesc">
      <summary>Contiene informazioni relative al parametro.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.EXCEPINFO">
      <summary>Descrive le eccezioni che si verificano durante IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrDescription">
      <summary>Descrive l'errore destinato al cliente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrHelpFile">
      <summary>Contiene l'unità, il percorso e il nome di file completi di un file della Guida contenente ulteriori informazioni sull'errore.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrSource">
      <summary>Indica il nome dell'origine dell'eccezione.In genere, corrisponde al nome di un'applicazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.dwHelpContext">
      <summary>Indica l'ID del contesto dell'argomento all'interno del file della Guida.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pfnDeferredFillIn">
      <summary>Rappresenta un puntatore a una funzione che accetta una struttura <see cref="T:System.Runtime.InteropServices.EXCEPINFO" /> come argomento e restituisce un valore HRESULT.Se non è desiderato il riempimento rinviato, questo campo è impostato su null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pvReserved">
      <summary>Il campo è riservato e deve essere impostato su null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.scode">
      <summary>Valore restituito che descrive l'errore.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wCode">
      <summary>Rappresenta un codice di errore che identifica l'errore.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wReserved">
      <summary>Il campo è riservato e deve essere impostato su 0.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FILETIME">
      <summary>Rappresenta il numero di intervalli da 100 nanosecondi trascorsi dal 1 gennaio 1601.Questa struttura è un valore a 64 bit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwHighDateTime">
      <summary>Specifica i 32 bit alti di FILETIME.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwLowDateTime">
      <summary>Specifica i 32 bit bassi di FILETIME.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FORMATETC">
      <summary>Rappresenta un formato degli Appunti generalizzato. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.cfFormat">
      <summary>Specifica un determinato formato degli Appunti di interesse.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.dwAspect">
      <summary>Specifica una delle costanti di enumerazione <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> che indica la quantità di dettagli che deve essere contenuta nel rendering.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.lindex">
      <summary>Specifica parte dell'aspetto quando è necessario dividere i dati attraverso i limiti di pagina. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.ptd">
      <summary>Specifica un puntatore a una struttura DVTARGETDEVICE contenente informazioni sulla periferica di destinazione per la quale vengono composti i dati. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.tymed">
      <summary>Specifica una delle costanti di enumerazione <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" />, che indica il tipo di supporto di archiviazione utilizzato per trasferire i dati dell'oggetto. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCDESC">
      <summary>Definisce la descrizione di una funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.callconv">
      <summary>Specifica la convenzione di chiamata di una funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParams">
      <summary>Conta il numero totale di parametri.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParamsOpt">
      <summary>Conta i parametri opzionali.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cScodes">
      <summary>Conta i valori restituiti consentiti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.elemdescFunc">
      <summary>Contiene il tipo restituito della funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.funckind">
      <summary>Specifica se la funzione è virtual, static o solo dispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.invkind">
      <summary>Specifica il tipo di una funzione di proprietà.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgelemdescParam">
      <summary>Indica la dimensione di <see cref="F:System.Runtime.InteropServices.FUNCDESC.cParams" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgscode">
      <summary>Memorizza il numero di errori che una funzione può restituire su un sistema a 16 bit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.memid">
      <summary>Identifica l'ID del membro funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.oVft">
      <summary>Specifica l'offset nella VTBL per <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_VIRTUAL" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.wFuncFlags">
      <summary>Indica il <see cref="T:System.Runtime.InteropServices.FUNCFLAGS" /> di una funzione.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCFLAGS">
      <summary>Identifica le costanti che definiscono le proprietà di una funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FBINDABLE">
      <summary>Funzione che supporta l'associazione dati.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTBIND">
      <summary>Funzione che meglio rappresenta l'oggetto.L'attributo può essere applicato solo a una funzione in un tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTCOLLELEM">
      <summary>Consente un'ottimizzazione nella quale il compilatore ricerca un membro denominato "xyz" sul tipo di "abc".Se tale membro viene individuato e gli viene assegnato un flag come funzione di accesso per un elemento dell'insieme predefinito, viene generata una chiamata alla funzione del membro.È consentito sui membri nelle interfacce dispatch e nelle interfacce, ma non sui moduli.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDISPLAYBIND">
      <summary>Funzione che viene visualizzata dall'utente come associabile.È inoltre necessario impostare <see cref="F:System.Runtime.InteropServices.FUNCFLAGS.FUNCFLAG_FBINDABLE" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FHIDDEN">
      <summary>La funzione non deve essere visualizzata all'utente, anche se è disponibile ed è associabile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FIMMEDIATEBIND">
      <summary>Mappata come singole proprietà associabili.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FNONBROWSABLE">
      <summary>La proprietà viene visualizzata in un visualizzatore oggetti, ma non in un visualizzatore proprietà.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREPLACEABLE">
      <summary>Inserisce dei tag nell'interfaccia in modo che abbia comportamenti predefiniti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREQUESTEDIT">
      <summary>Quando è impostato, qualsiasi chiamata a un metodo che imposta la proprietà determina anzitutto una chiamata a IPropertyNotifySink::OnRequestEdit.L'implementazione di OnRequestEdit determina se alla chiamata è consentito impostare la proprietà.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FRESTRICTED">
      <summary>La funzione non deve essere accessibile dai linguaggi macro.Il flag è destinato alle funzioni a livello di sistema o alle funzioni che non devono essere visualizzate dai visualizzatori dei tipi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FSOURCE">
      <summary>La funzione restituisce un oggetto che rappresenta un'origine di eventi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUIDEFAULT">
      <summary>Il membro delle informazioni sul tipo è il membro predefinito per la visualizzazione nell'interfaccia utente.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUSESGETLASTERROR">
      <summary>La funzione supporta GetLastError.Se si verifica un errore durante la funzione, il chiamante può chiamare GetLastError per recuperare il codice di errore.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCKIND">
      <summary>Definisce la modalità di accesso a una funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_DISPATCH">
      <summary>Alla funzione è possibile accedere solo mediante IDispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_NONVIRTUAL">
      <summary>La funzione è accessibile tramite un indirizzo static e accetta un puntatore this implicito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_PUREVIRTUAL">
      <summary>La funzione è accessibile tramite la tabella di funzioni virtual (VTBL) e assume un puntatore this implicito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_STATIC">
      <summary>La funzione è accessibile tramite un indirizzo static e non accetta un puntatore this implicito.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_VIRTUAL">
      <summary>L'accesso alla funzione avviene in modo identico a <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_PUREVIRTUAL" />, ad eccezione del fatto che essa dispone di un'implementazione.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IAdviseSink">
      <summary>Fornisce una definizione gestita dell'interfaccia IAdviseSink.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnClose">
      <summary>Notifica a tutti i sink consultivi registrati la modifica dello stato dell'oggetto da in esecuzione a caricato.  Questo metodo viene chiamato da un server.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)">
      <summary>Notifica a tutti i sink consultivi attualmente registrati degli oggetti dati che i dati nell'oggetto sono stati modificati.</summary>
      <param name="format">Oggetto <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />, passato per riferimento, che descrive informazioni relative a formato, dispositivo di destinazione, rendering e archiviazione dell'oggetto dati chiamante.</param>
      <param name="stgmedium">Oggetto <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />, passato per riferimento, che definisce il supporto di archiviazione (memoria globale, file su disco, oggetto di archiviazione, oggetto flusso, oggetto GDI (Graphics Device Interface) o supporto non definito) e la proprietà di tale supporto per l'oggetto dati chiamante.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnRename(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Notifica a tutti i sink consultivi registrati che l'oggetto è stato rinominato.Questo metodo viene chiamato da un server.</summary>
      <param name="moniker">Puntatore all'interfaccia IMoniker sul nuovo moniker completo dell'oggetto.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnSave">
      <summary>Notifica a tutti i sink consultivi registrati che l'oggetto è stato salvato.Questo metodo viene chiamato da un server.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnViewChange(System.Int32,System.Int32)">
      <summary>Notifica ai sink consultivi registrati di un oggetto che la visualizzazione è stata modificata.Questo metodo viene chiamato da un server.</summary>
      <param name="aspect">Aspetto, o visualizzazione, dell'oggetto.Contiene un valore ricavato dall'enumerazione <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" />.</param>
      <param name="index">Parte della visualizzazione modificata.Attualmente, è valido solo il valore -1.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IBindCtx">
      <summary>Fornisce la definizione gestita dell'interfaccia IBindCtx.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.EnumObjectParam(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Enumera le stringhe che sono le chiavi della tabella gestita internamente dei parametri degli oggetti contestuali.</summary>
      <param name="ppenum">Quando termina, questo metodo contiene un riferimento all'enumeratore del parametro dell'oggetto.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Restituisce le opzioni di associazione correnti archiviate nel contesto di associazione corrente.</summary>
      <param name="pbindopts">Puntatore alla struttura per ricevere le opzioni di associazione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetObjectParam(System.String,System.Object@)">
      <summary>Cerca la chiave specificata nella tabella gestita internamente dei parametri degli oggetti contestuali e restituisce l'oggetto corrispondente, se disponibile.</summary>
      <param name="pszKey">Nome dell'oggetto di cui eseguire la ricerca. </param>
      <param name="ppunk">Quando termina, questo metodo contiene il puntatore all'interfaccia dell'oggetto.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetRunningObjectTable(System.Runtime.InteropServices.ComTypes.IRunningObjectTable@)">
      <summary>Restituisce l'accesso alla tabella degli oggetti in esecuzione (ROT, Running Object Table) relativa a questo processo di associazione.</summary>
      <param name="pprot">Quando termina, questo metodo contiene un riferimento alla tabella ROT.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)">
      <summary>Registra l'oggetto passato come uno degli oggetti associati durante un'operazione di moniker e che deve essere liberato al completamento dell'operazione.</summary>
      <param name="punk">Oggetto da registrare per il rilascio. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectParam(System.String,System.Object)">
      <summary>Registra il puntatore all'oggetto specificato sotto il nome specificato nella tabella gestita internamente dei puntatori all'oggetto.</summary>
      <param name="pszKey">Nome con il quale registrare il parametro <paramref name="punk" />. </param>
      <param name="punk">Oggetto da registrare. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.ReleaseBoundObjects">
      <summary>Libera tutti gli oggetti correntemente registrati con il contesto di associazione mediante il metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectBound(System.Object)">
      <summary>Rimuove l'oggetto dall'insieme di oggetti registrati che devono essere liberati.</summary>
      <param name="punk">Oggetto di cui annullare la registrazione per il rilascio. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectParam(System.String)">
      <summary>Revoca la registrazione dell'oggetto correntemente trovato sotto la chiave specificata nella tabella gestita internamente dei parametri degli oggetti contestuali, se tale chiave è attualmente registrata.</summary>
      <returns>Valore S_OKHRESULT se la rimozione della chiave specificata dalla tabella è stata completata. In caso contrario, un valore S_FALSEHRESULT.</returns>
      <param name="pszKey">Chiave di cui annullare la registrazione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.SetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>Archivia un blocco di parametri nel contesto di associazione.Questi parametri verranno applicati alle successive operazioni UCOMIMoniker che utilizzeranno questo contesto di associazione.</summary>
      <param name="pbindopts">Struttura contenente le opzioni di associazione da impostare. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPoint">
      <summary>Fornisce la definizione gestita dell'interfaccia IConnectionPoint.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)">
      <summary>Stabilisce una connessione consultiva tra il punto di connessione e l'oggetto sink del chiamante.</summary>
      <param name="pUnkSink">Riferimento al sink per ricevere le chiamate per l'interfaccia in uscita gestita da questo punto di connessione. </param>
      <param name="pdwCookie">Quando termina, questo metodo contiene il cookie di connessione.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.EnumConnections(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Crea un oggetto enumeratore per lo scorrimento delle connessioni esistenti fino a questo punto di connessione.</summary>
      <param name="ppEnum">Quando termina, questo metodo contiene l'enumeratore appena creato.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionInterface(System.Guid@)">
      <summary>Restituisce l'IID dell'interfaccia in uscita gestita da questo punto di connessione.</summary>
      <param name="pIID">Quando termina, questo parametro contiene l'IID dell'interfaccia in uscita gestita da questo punto di connessione.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionPointContainer(System.Runtime.InteropServices.ComTypes.IConnectionPointContainer@)">
      <summary>Recupera il puntatore dell'interfaccia IConnectionPointContainer all'oggetto collegabile che concettualmente è proprietario di questo punto di connessione.</summary>
      <param name="ppCPC">Quando termina, questo parametro contiene l'interfaccia IConnectionPointContainer dell'oggetto collegabile.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Unadvise(System.Int32)">
      <summary>Termina una connessione consultiva precedentemente stabilita tramite il metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />.</summary>
      <param name="dwCookie">Cookie di connessione precedentemente restituito dal metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer">
      <summary>Fornisce la definizione gestita dell'interfaccia IConnectionPointContainer.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.EnumConnectionPoints(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Crea un enumeratore di tutti i punti di connessione supportati nell'oggetto collegabile, un punto di connessione per ciascun IID.</summary>
      <param name="ppEnum">Quando termina, questo metodo contiene il puntatore a interfaccia dell'enumeratore.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.FindConnectionPoint(System.Guid@,System.Runtime.InteropServices.ComTypes.IConnectionPoint@)">
      <summary>Chiede all'oggetto collegabile se contiene un punto di connessione per un particolare IID e, in caso affermativo, restituisce al punto di connessione il puntatore all'interfaccia IConnectionPoint.</summary>
      <param name="riid">Riferimento all'IID di interfaccia in uscita il cui punto di connessione è richiesto. </param>
      <param name="ppCP">Quando termina, questo metodo contiene il punto di connessione che gestisce il parametro <paramref name="riid" /> dell'interfaccia in uscita.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLDESC">
      <summary>Contiene le informazioni necessarie per trasferire un elemento di struttura, un parametro o un valore restituito di una funzione tra processi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.dwReserved">
      <summary>Riservato; impostato su null.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.wIDLFlags">
      <summary>Indica un valore <see cref="T:System.Runtime.InteropServices.IDLFLAG" /> che descrive il tipo.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLFLAG">
      <summary>Descrive come trasferire tra diversi processi un elemento strutturale, un parametro o il valore restituito di una funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FIN">
      <summary>Il parametro passa informazioni dal chiamante al chiamato.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FLCID">
      <summary>Il parametro è l'identificatore locale di un'applicazione client.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FOUT">
      <summary>Il parametro restituisce informazioni dal chiamante al chiamato.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FRETVAL">
      <summary>Il parametro è il valore restituito del membro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_NONE">
      <summary>Non specifica se il parametro passa o riceve informazioni.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints">
      <summary>Gestisce la definizione dell'interfaccia IEnumConnectionPoints.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>Crea un nuovo enumeratore che contiene lo stesso stato di enumerazione di quello corrente.</summary>
      <param name="ppenum">Quando termina, questo metodo contiene un riferimento all'enumeratore appena creato.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IConnectionPoint[],System.IntPtr)">
      <summary>Recupera un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il parametro <paramref name="pceltFetched" /> è uguale al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di riferimenti IConnectionPointda restituire in <paramref name="rgelt" />. </param>
      <param name="rgelt">Quando termina, questo metodo contiene un riferimento alle connessioni enumerate.Questo parametro viene passato non inizializzato.</param>
      <param name="pceltFetched">Quando termina, questo metodo contiene un riferimento al numero effettivo di connessioni enumerate in <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Reset">
      <summary>Riporta all'inizio la sequenza di enumerazione.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Skip(System.Int32)">
      <summary>Ignora un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il numero di elementi ignorati è pari al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di elementi da saltare nell'enumerazione. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnections">
      <summary>Gestisce la definizione dell'interfaccia IEnumConnections.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>Crea un nuovo enumeratore che contiene lo stesso stato di enumerazione di quello corrente.</summary>
      <param name="ppenum">Quando termina, questo metodo contiene un riferimento all'enumeratore appena creato.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Next(System.Int32,System.Runtime.InteropServices.ComTypes.CONNECTDATA[],System.IntPtr)">
      <summary>Recupera un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il parametro <paramref name="pceltFetched" /> è uguale al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di strutture <see cref="T:System.Runtime.InteropServices.CONNECTDATA" /> da restituire in <paramref name="rgelt" />. </param>
      <param name="rgelt">Quando termina, questo metodo contiene un riferimento alle connessioni enumerate.Questo parametro viene passato non inizializzato.</param>
      <param name="pceltFetched">Quando termina, questo metodo contiene un riferimento al numero effettivo di connessioni enumerate in <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Reset">
      <summary>Riporta all'inizio la sequenza di enumerazione.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Skip(System.Int32)">
      <summary>Ignora un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il numero di elementi ignorati è pari al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di elementi da saltare nell'enumerazione. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC">
      <summary>Fornisce la definizione gestita dell'interfaccia IEnumFORMATETC.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Clone(System.Runtime.InteropServices.ComTypes.IEnumFORMATETC@)">
      <summary>Crea un nuovo enumeratore che contiene lo stesso stato di enumerazione dell'enumeratore corrente.</summary>
      <param name="newEnum">Quando termina, questo metodo contiene un riferimento all'enumeratore appena creato.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Next(System.Int32,System.Runtime.InteropServices.ComTypes.FORMATETC[],System.Int32[])">
      <summary>Recupera un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il parametro <paramref name="pceltFetched" /> è uguale al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di riferimenti <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />da restituire in <paramref name="rgelt" />.</param>
      <param name="rgelt">Quando termina, questo metodo contiene un riferimento ai riferimenti <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> enumerati.Questo parametro viene passato non inizializzato.</param>
      <param name="pceltFetched">Quando termina, questo metodo contiene un riferimento al numero effettivo di riferimenti enumerati in <paramref name="rgelt" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Reset">
      <summary>Riporta all'inizio la sequenza di enumerazione.</summary>
      <returns>HRESULT con il valore S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Skip(System.Int32)">
      <summary>Ignora un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il numero di elementi ignorati è pari al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di elementi da saltare nell'enumerazione.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumMoniker">
      <summary>Gestisce la definizione dell'interfaccia IEnumMoniker.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Clone(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Crea un nuovo enumeratore che contiene lo stesso stato di enumerazione di quello corrente.</summary>
      <param name="ppenum">Quando termina, questo metodo contiene un riferimento all'enumeratore appena creato.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker[],System.IntPtr)">
      <summary>Recupera un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il parametro <paramref name="pceltFetched" /> è uguale al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di moniker da restituire in <paramref name="rgelt" />. </param>
      <param name="rgelt">Quando termina, questo metodo contiene un riferimento ai moniker enumerati.Questo parametro viene passato non inizializzato.</param>
      <param name="pceltFetched">Quando termina, questo metodo contiene un riferimento al numero effettivo di moniker enumerati in <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Reset">
      <summary>Riporta all'inizio la sequenza di enumerazione.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Skip(System.Int32)">
      <summary>Ignora un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il numero di elementi ignorati è pari al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di elementi da saltare nell'enumerazione. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumString">
      <summary>Gestisce la definizione dell'interfaccia IEnumString.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Clone(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>Crea un nuovo enumeratore che contiene lo stesso stato di enumerazione di quello corrente.</summary>
      <param name="ppenum">Quando termina, questo metodo contiene un riferimento all'enumeratore appena creato.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Next(System.Int32,System.String[],System.IntPtr)">
      <summary>Recupera un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il parametro <paramref name="pceltFetched" /> è uguale al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di stringhe da restituire in <paramref name="rgelt" />. </param>
      <param name="rgelt">Quando termina, questo metodo contiene un riferimento alle stringhe enumerate.Questo parametro viene passato non inizializzato.</param>
      <param name="pceltFetched">Quando termina, questo metodo contiene un riferimento al numero effettivo di stringhe enumerate in <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Reset">
      <summary>Riporta all'inizio la sequenza di enumerazione.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Skip(System.Int32)">
      <summary>Ignora un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il numero di elementi ignorati è pari al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di elementi da saltare nell'enumerazione. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT">
      <summary>Gestisce la definizione dell'interfaccia IEnumVARIANT.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Clone">
      <summary>Crea un nuovo enumeratore che contiene lo stesso stato di enumerazione di quello corrente.</summary>
      <returns>Riferimento <see cref="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT" /> all'enumeratore appena creato.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Next(System.Int32,System.Object[],System.IntPtr)">
      <summary>Recupera un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il parametro <paramref name="pceltFetched" /> è uguale al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di elementi da restituire in <paramref name="rgelt" />. </param>
      <param name="rgVar">Quando termina, questo metodo contiene un riferimento agli elementi enumerati.Questo parametro viene passato non inizializzato.</param>
      <param name="pceltFetched">Quando termina, questo metodo contiene un riferimento al numero effettivo di elementi enumerati in <paramref name="rgelt" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Reset">
      <summary>Riporta all'inizio la sequenza di enumerazione.</summary>
      <returns>HRESULT con il valore S_OK.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Skip(System.Int32)">
      <summary>Ignora un determinato numero di elementi nella sequenza di enumerazione.</summary>
      <returns>S_OK se il numero di elementi ignorati è pari al parametro <paramref name="celt" />; in caso contrario, S_FALSE.</returns>
      <param name="celt">Numero di elementi da saltare nell'enumerazione. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMoniker">
      <summary>Fornisce la definizione gestita dell'interfaccia IMoniker, con funzionalità COM da IPersist e IPersistStream.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToObject(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Utilizza il moniker per l'associazione all'oggetto che identifica.</summary>
      <param name="pbc">Riferimento all'interfaccia IBindCtx sull'oggetto del contesto di associazione utilizzato in questa operazione di associazione. </param>
      <param name="pmkToLeft">Riferimento al moniker alla sinistra del moniker corrente, se fa parte di un moniker composto. </param>
      <param name="riidResult">Identificatore di interfaccia (IID) che il client intende utilizzare per comunicare con l'oggetto identificato dal moniker. </param>
      <param name="ppvResult">Quando termina, questo metodo contiene un riferimento all'interfaccia richiesta da <paramref name="riidResult" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToStorage(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>Recupera un puntatore di interfaccia all'archiviazione che contiene l'oggetto identificato dal moniker.</summary>
      <param name="pbc">Riferimento all'interfaccia IBindCtx sull'oggetto del contesto di associazione utilizzato in questa operazione di associazione. </param>
      <param name="pmkToLeft">Riferimento al moniker alla sinistra del moniker corrente, se fa parte di un moniker composto. </param>
      <param name="riid">Identificatore di interfaccia (IID) dell'interfaccia di memoria richiesta. </param>
      <param name="ppvObj">Quando termina, questo metodo contiene un riferimento all'interfaccia richiesta da <paramref name="riid" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.CommonPrefixWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Crea un nuovo moniker in base al prefisso comune che il moniker condivide con un altro.</summary>
      <param name="pmkOther">Riferimento all'interfaccia IMoniker su un altro moniker per il confronto con il moniker corrente per individuare un prefisso comune. </param>
      <param name="ppmkPrefix">Quando termina, questo metodo contiene il moniker con il prefisso comune del moniker corrente e <paramref name="pmkOther" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ComposeWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Boolean,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Combina il moniker corrente con un altro, creando un nuovo moniker composito.</summary>
      <param name="pmkRight">Riferimento all'interfaccia IMoniker su un moniker da aggiungere alla fine del moniker corrente. </param>
      <param name="fOnlyIfNotGeneric">true per indicare che il chiamante richiede una composizione non generica.L'operazione prosegue solo se <paramref name="pmkRight" /> è una classe di moniker con cui può essere combinato il moniker corrente in un modo che non comporti la creazione di un composto generico.false per indicare che il metodo può creare un composto generico, se necessario.</param>
      <param name="ppmkComposite">Quando termina, questo metodo contiene un riferimento al moniker composto risultante.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Enum(System.Boolean,System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Fornisce un puntatore a un enumeratore in grado di enumerare i componenti di un moniker composito.</summary>
      <param name="fForward">true per enumerare i moniker da sinistra a destra.false per enumerare i moniker da destra a sinistra.</param>
      <param name="ppenumMoniker">Quando termina, questo metodo contiene un riferimento all'oggetto enumeratore per il moniker.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetClassID(System.Guid@)">
      <summary>Recupera l'identificatore di classe (CLSID) di un oggetto.</summary>
      <param name="pClassID">Quando termina, questo metodo contiene il CLSID.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String@)">
      <summary>Ottiene il nome visualizzato, ossia una rappresentazione del moniker corrente leggibile dall'utente.</summary>
      <param name="pbc">Riferimento al contesto di associazione da utilizzare in questa operazione. </param>
      <param name="pmkToLeft">Riferimento al moniker alla sinistra del moniker corrente, se fa parte di un moniker composto. </param>
      <param name="ppszDisplayName">Quando termina, questo metodo contiene la stringa del nome visualizzato.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetSizeMax(System.Int64@)">
      <summary>Restituisce la dimensione, in byte, del flusso necessario per salvare l'oggetto.</summary>
      <param name="pcbSize">Quando termina, questo metodo contiene un valore long che indica la dimensione, in byte, del flusso necessario per salvare l'oggetto.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Fornisce un numero che rappresenta l'ora dell'ultima modifica apportata all'oggetto identificato dal moniker corrente.</summary>
      <param name="pbc">Riferimento al contesto di associazione da utilizzare in questa operazione. </param>
      <param name="pmkToLeft">Riferimento al moniker alla sinistra del moniker corrente, se fa parte di un moniker composto. </param>
      <param name="pFileTime">Quando termina, questo metodo contiene l'ora dell'ultima modifica.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Hash(System.Int32@)">
      <summary>Calcola un intero a 32 bit utilizzando lo stato interno del moniker.</summary>
      <param name="pdwHash">Quando termina, questo metodo contiene il valore hash del moniker.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Inverse(System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Fornisce un moniker che, quando composto a destra del moniker corrente o di uno con struttura analoga, risulta in una composizione nulla.</summary>
      <param name="ppmk">Quando termina, questo metodo contiene il moniker inverso del moniker corrente.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsDirty">
      <summary>Verifica se l'oggetto ha subito modifiche dall'ultimo salvataggio.</summary>
      <returns>Valore S_OKHRESULT se l'oggetto è stato modificato. In caso contrario, un valore S_FALSEHRESULT.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsEqual(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Confronta il moniker corrente con un moniker specificato e indica se sono identici.</summary>
      <returns>Valore S_OKHRESULT se i moniker sono identici. In caso contrario, un valore S_FALSEHRESULT.  </returns>
      <param name="pmkOtherMoniker">Riferimento al moniker da utilizzare per il confronto. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsRunning(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Determina se l'oggetto identificato dal moniker corrente è attualmente caricato e in esecuzione.</summary>
      <returns>Valore S_OKHRESULT se il moniker è in esecuzione. In caso contrario, un valore S_FALSEHRESULT oppure un valore E_UNEXPECTEDHRESULT.</returns>
      <param name="pbc">Riferimento al contesto di associazione da utilizzare in questa operazione. </param>
      <param name="pmkToLeft">Riferimento al moniker alla sinistra del moniker corrente, se il moniker corrente fa parte di un composto. </param>
      <param name="pmkNewlyRunning">Riferimento al moniker aggiunto di recente alla tabella degli oggetti in esecuzione (ROT, Running Object Table). </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsSystemMoniker(System.Int32@)">
      <summary>Indica se il moniker corrente è uno dei moniker delle classi di moniker fornite dal sistema.</summary>
      <returns>Valore S_OKHRESULT se si tratta di un moniker di sistema. In caso contrario, un valore S_FALSEHRESULT.</returns>
      <param name="pdwMksys">Quando termina, questo metodo contiene un puntatore a un intero che corrisponde a uno dei valori nell'enumerazione MKSYS e fa riferimento a una delle classi di moniker COM.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Load(System.Runtime.InteropServices.ComTypes.IStream)">
      <summary>Inizializza un oggetto dal flusso in cui è stato salvato in precedenza.</summary>
      <param name="pStm">Flusso dal quale viene caricato l'oggetto. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Legge il numero massimo di caratteri del nome visualizzato specificato che il metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)" /> è in grado di comprendere e compila un moniker corrispondente alla porzione letta.</summary>
      <param name="pbc">Riferimento al contesto di associazione da utilizzare in questa operazione. </param>
      <param name="pmkToLeft">Riferimento al moniker compilato dal nome visualizzato fino a questo punto. </param>
      <param name="pszDisplayName">Riferimento alla stringa contenente il nome visualizzato rimanente da analizzare. </param>
      <param name="pchEaten">Quando termina, questo metodo contiene il numero di caratteri utilizzati nell'analisi di <paramref name="pszDisplayName" />.Questo parametro viene passato non inizializzato.</param>
      <param name="ppmkOut">Quando termina, questo metodo contiene un riferimento al moniker compilato da <paramref name="pszDisplayName" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Reduce(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Restituisce un moniker ridotto, ovvero un altro moniker che fa riferimento allo stesso oggetto del moniker corrente, ma la cui associazione può avere pari o maggiore efficienza.</summary>
      <param name="pbc">Riferimento all'interfaccia IBindCtx nel contesto di associazione da utilizzare in questa operazione. </param>
      <param name="dwReduceHowFar">Valore che specifica il grado di riduzione del moniker corrente. </param>
      <param name="ppmkToLeft">Riferimento al moniker alla sinistra del moniker corrente. </param>
      <param name="ppmkReduced">Quando termina, questo metodo contiene un riferimento alla forma ridotta del moniker corrente, che può essere null se si verifica un errore o se il moniker corrente è ridotto a nulla.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.RelativePathTo(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>Fornisce un moniker che, quando aggiunto a quello corrente o a uno con struttura analoga, produce il moniker specificato.</summary>
      <param name="pmkOther">Riferimento al moniker per il quale deve essere adottato un percorso relativo. </param>
      <param name="ppmkRelPath">Quando termina, questo metodo contiene un riferimento al moniker relativo.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Save(System.Runtime.InteropServices.ComTypes.IStream,System.Boolean)">
      <summary>Salva un oggetto nel flusso specificato.</summary>
      <param name="pStm">Flusso nel quale viene salvato l'oggetto. </param>
      <param name="fClearDirty">true per cancellare il flag modificato al termine del salvataggio. In caso contrario, false</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS">
      <summary>Definisce gli attributi di un'interfaccia implementata o ereditata di un tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULT">
      <summary>L'interfaccia o interfaccia dispatch è quella predefinita per l'origine o sink.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULTVTABLE">
      <summary>I sink ricevono gli eventi tramite la tabella di funzioni virtual (VTBL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FRESTRICTED">
      <summary>Il membro non dovrebbe essere visualizzabile o programmabile dagli utenti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FSOURCE">
      <summary>Questo membro di una coclasse viene chiamato piuttosto che implementato.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND">
      <summary>Specifica come richiamare una funzione mediante IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_FUNC">
      <summary>Il membro viene chiamato utilizzando una normale sintassi di chiamata delle funzioni.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYGET">
      <summary>La funzione viene richiamata utilizzando una normale sintassi di accesso alle proprietà.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUT">
      <summary>La funzione viene richiamata utilizzando una sintassi di assegnazione di valori a una proprietà.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUTREF">
      <summary>La funzione viene richiamata utilizzando una sintassi di assegnazione di riferimenti a una proprietà.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IPersistFile">
      <summary>Fornisce la definizione gestita dell'interfaccia IPersistFile con funzionalità da IPersist.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetClassID(System.Guid@)">
      <summary>Recupera l'identificatore di classe (CLSID) di un oggetto.</summary>
      <param name="pClassID">Quando termina, questo metodo contiene un riferimento al CLSID.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetCurFile(System.String@)">
      <summary>Recupera il percorso assoluto del file di lavoro corrente dell'oggetto oppure, se non è presente alcun file di lavoro corrente, la richiesta del nome file predefinito dell'oggetto.</summary>
      <param name="ppszFileName">Quando termina, questo metodo contiene l'indirizzo di un puntatore a una stringa con terminazione zero contenente il percorso del file corrente oppure la richiesta del nome file predefinito (ad esempio *.txt).Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.IsDirty">
      <summary>Verifica se un oggetto è stato modificato dall'ultimo salvataggio nel file corrente.</summary>
      <returns>S_OK se il file è stato modificato dall'ultimo salvataggio; in caso contrario, S_FALSE.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Load(System.String,System.Int32)">
      <summary>Apre il file specificato e inizializza un oggetto dal contenuto del file.</summary>
      <param name="pszFileName">Stringa con terminazione zero che contiene il percorso assoluto del file da aprire. </param>
      <param name="dwMode">Combinazione di valori dall'enumerazione STGM per indicare la modalità di accesso con cui aprire <paramref name="pszFileName" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Save(System.String,System.Boolean)">
      <summary>Salva una copia dell'oggetto nel file specificato.</summary>
      <param name="pszFileName">Stringa con terminazione zero che contiene il percorso assoluto del file nel quale è salvato l'oggetto. </param>
      <param name="fRemember">true per utilizzare il parametro <paramref name="pszFileName" /> come file di lavoro corrente; in caso contrario, false. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.SaveCompleted(System.String)">
      <summary>Indica all'oggetto che può scrivere nel relativo file.</summary>
      <param name="pszFileName">Percorso assoluto del file nel quale l'oggetto è stato precedentemente salvato. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IRunningObjectTable">
      <summary>Fornisce la definizione gestita dell'interfaccia IRunningObjectTable.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.EnumRunning(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>Enumera gli oggetti correntemente registrati come oggetti in esecuzione.</summary>
      <param name="ppenumMoniker">Quando termina, questo metodo contiene il nuovo enumeratore per la tabella degli oggetti in esecuzione (ROT, Running Object Table).Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetObject(System.Runtime.InteropServices.ComTypes.IMoniker,System.Object@)">
      <summary>Restituisce l'oggetto registrato se il nome dell'oggetto fornito è registrato come oggetto in esecuzione.</summary>
      <returns>Un HRESULT che indica l'esito positivo o negativo dell'operazione. </returns>
      <param name="pmkObjectName">Riferimento al moniker da cercare nella tabella ROT. </param>
      <param name="ppunkObject">Quando termina, questo metodo contiene l'oggetto in esecuzione richiesto.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Cerca questo moniker nella tabella ROT e riporta l'ora registrata della modifica, se presente.</summary>
      <returns>Un HRESULT che indica l'esito positivo o negativo dell'operazione.</returns>
      <param name="pmkObjectName">Riferimento al moniker da cercare nella tabella ROT. </param>
      <param name="pfiletime">Quando termina, questo oggetto contiene l'ora dell'ultima modifica degli oggetti.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.IsRunning(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Determina se il moniker specificato è correntemente registrato nella tabella ROT.</summary>
      <returns>Un HRESULT che indica l'esito positivo o negativo dell'operazione.</returns>
      <param name="pmkObjectName">Riferimento al moniker da cercare nella tabella ROT. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>Annota l'ora di modifica di un determinato oggetto per consentire all'interfaccia IMoniker::GetTimeOfLastChange di riportare un'ora di modifica corretta.</summary>
      <param name="dwRegister">Voce della tabella ROT dell'oggetto modificato. </param>
      <param name="pfiletime">Riferimento all'ora dell'ultima modifica dell'oggetto. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Register(System.Int32,System.Object,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>Registra l'ingresso dell'oggetto fornito nello stato di esecuzione.</summary>
      <returns>Valore che può essere utilizzato per identificare questa voce della ROT in successive chiamate al metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)" /> o <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)" />.</returns>
      <param name="grfFlags">Specifica se il riferimento della tabella degli oggetti in esecuzione (ROT, Running Object Table) a <paramref name="punkObject" /> è debole o forte e controlla l'accesso all'oggetto tramite la corrispondente voce nella ROT. </param>
      <param name="punkObject">Riferimento all'oggetto da registrare come oggetto in esecuzione. </param>
      <param name="pmkObjectName">Riferimento al moniker che identifica <paramref name="punkObject" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)">
      <summary>Annulla la registrazione dell'oggetto specificato dalla tabella ROT.</summary>
      <param name="dwRegister">Voce della tabella ROT da revocare. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IStream">
      <summary>Fornisce la definizione gestita dell'interfaccia IStream con funzionalità ISequentialStream.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
      <summary>Crea un nuovo oggetto flusso con il proprio puntatore di ricerca che fa riferimento agli stessi byte del flusso originale.</summary>
      <param name="ppstm">Quando termina, questo metodo contiene il nuovo oggetto flusso.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)">
      <summary>Assicura che eventuali modifiche apportate a un oggetto flusso aperto in modalità di transazione siano riflesse nell'archivio padre.</summary>
      <param name="grfCommitFlags">Valore che controlla la modalità di esecuzione del commit delle modifiche dell'oggetto flusso. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
      <summary>Copia un numero specificato di byte dal puntatore di ricerca corrente del flusso al puntatore di ricerca corrente in un altro flusso.</summary>
      <param name="pstm">Riferimento al flusso di destinazione. </param>
      <param name="cb">Numero di byte da copiare dal flusso di origine. </param>
      <param name="pcbRead">In caso di esito positivo, contiene il numero effettivo di byte letti dall'origine. </param>
      <param name="pcbWritten">In caso di esito positivo, contiene il numero effettivo di byte scritti nella destinazione. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Limita l'accesso a un intervallo specificato di byte nel flusso.</summary>
      <param name="libOffset">Offset di byte per l'inizio dell'intervallo. </param>
      <param name="cb">Lunghezza in byte dell'intervallo da limitare. </param>
      <param name="dwLockType">Limitazioni richieste nell'accesso all'intervallo. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Read(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Legge un numero specificato di byte dall'oggetto flusso nella memoria a partire dal puntatore di ricerca corrente.</summary>
      <param name="pv">Quando termina, questo metodo contiene i dati letti dal flusso.Questo parametro viene passato non inizializzato.</param>
      <param name="cb">Numero di byte da leggere dall'oggetto flusso. </param>
      <param name="pcbRead">Puntatore a una variabile ULONG che riceve il numero effettivo di byte letti dall'oggetto flusso. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Revert">
      <summary>Elimina tutte le modifiche apportate a un flusso sottoposto a transazione dall'ultima chiamata a <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Seek(System.Int64,System.Int32,System.IntPtr)">
      <summary>Modifica il puntatore di ricerca in una nuova posizione relativa all'inizio del flusso, alla fine del flusso o al puntatore di ricerca corrente.</summary>
      <param name="dlibMove">Spostamento da aggiungere a <paramref name="dwOrigin" />. </param>
      <param name="dwOrigin">Origine della ricerca.L'origine può essere l'inizio del file, il puntatore di ricerca corrente o la fine del file.</param>
      <param name="plibNewPosition">In caso di esito positivo, contiene l'offset del puntatore di ricerca dall'inizio del flusso. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.SetSize(System.Int64)">
      <summary>Modifica la dimensione dell'oggetto flusso.</summary>
      <param name="libNewSize">Nuova dimensione del flusso come numero di byte. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
      <summary>Recupera la struttura <see cref="T:System.Runtime.InteropServices.STATSTG" /> per questo flusso.</summary>
      <param name="pstatstg">Quando termina, questo metodo contiene una struttura STATSTG che descrive l'oggetto flusso.Questo parametro viene passato non inizializzato.</param>
      <param name="grfStatFlag">Membri nella struttura STATSTG che non vengono restituiti da questo metodo, salvando in tal modo alcune operazioni di allocazione della memoria. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>Rimuove le restrizioni di accesso in un intervallo di byte precedentemente limitato con il metodo <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)" />.</summary>
      <param name="libOffset">Offset di byte per l'inizio dell'intervallo. </param>
      <param name="cb">Lunghezza in byte dell'intervallo da limitare. </param>
      <param name="dwLockType">Limitazioni di accesso precedentemente imposte all'intervallo. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Write(System.Byte[],System.Int32,System.IntPtr)">
      <summary>Scrive un numero specificato di byte nell'oggetto flusso a partire dal puntatore di ricerca corrente.</summary>
      <param name="pv">Buffer in cui scrivere questo flusso. </param>
      <param name="cb">Numero di byte da scrivere nel flusso. </param>
      <param name="pcbWritten">In caso di esito positivo, contiene il numero effettivo di byte scritti nell'oggetto flusso.Se il chiamante imposta questo puntatore su <see cref="F:System.IntPtr.Zero" />, questo metodo non fornirà il numero effettivo di byte scritti.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeComp">
      <summary>Fornisce la definizione gestita dell'interfaccia ITypeComp.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.Bind(System.String,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.DESCKIND@,System.Runtime.InteropServices.ComTypes.BINDPTR@)">
      <summary>Esegue il mapping di un nome a un membro di un tipo o associa le variabili e le funzioni globali presenti in una libreria dei tipi.</summary>
      <param name="szName">Nome da associare. </param>
      <param name="lHashVal">Valore hash per <paramref name="szName" /> calcolato da LHashValOfNameSys. </param>
      <param name="wFlags">Parola flag contenente uno o più flag di chiamata definiti nell'enumerazione INVOKEKIND. </param>
      <param name="ppTInfo">Quando termina, questo metodo contiene un riferimento alla descrizione del tipo che contiene l'elemento a cui è associato, se è stato restituito FUNCDESC o VARDESC.Questo parametro viene passato non inizializzato.</param>
      <param name="pDescKind">Quando termina, questo metodo contiene un riferimento a un enumeratore DESCKIND che indica se il nome associato è VARDESC, FUNCDESC o TYPECOMP.Questo parametro viene passato non inizializzato.</param>
      <param name="pBindPtr">Quando termina, questo metodo contiene un riferimento all'interfaccia VARDESC, FUNCDESC o ITypeComp associata.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.BindType(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Effettua l'associazione alle descrizioni di tipi contenute all'interno di una libreria dei tipi.</summary>
      <param name="szName">Nome da associare. </param>
      <param name="lHashVal">Valore hash per <paramref name="szName" /> determinato da LHashValOfNameSys. </param>
      <param name="ppTInfo">Quando termina, questo metodo contiene un riferimento a un ITypeInfo del tipo a cui è stato associato <paramref name="szName" />.Questo parametro viene passato non inizializzato.</param>
      <param name="ppTComp">Quando termina, questo metodo contiene un riferimento a una variabile ITypeComp.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo">
      <summary>Fornisce la definizione gestita dell'interfaccia Component Automation ITypeInfo.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Recupera gli indirizzi delle funzioni o delle variabili statiche, come quelle definite in una DLL.</summary>
      <param name="memid">ID membro dell'indirizzo del membro static da recuperare. </param>
      <param name="invKind">Uno dei valori <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> che specifica se il membro è una proprietà e, in caso positivo, ne indica il tipo. </param>
      <param name="ppv">Quando termina, questo metodo contiene un riferimento al membro static.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Crea una nuova istanza di un tipo che descrive una classe di componenti (coclass).</summary>
      <param name="pUnkOuter">Oggetto che funge da interfaccia IUnknown di controllo. </param>
      <param name="riid">IID dell'interfaccia utilizzata dal chiamante per comunicare con l'oggetto risultante. </param>
      <param name="ppvObj">Quando termina, questo metodo contiene un riferimento all'oggetto creato.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Recupera la libreria dei tipi che contiene questa descrizione del tipo e il relativo indice all'interno della libreria.</summary>
      <param name="ppTLB">Quando termina, questo metodo contiene un riferimento alla libreria dei tipi contenitore.Questo parametro viene passato non inizializzato.</param>
      <param name="pIndex">Quando termina, questo metodo contiene un riferimento all'indice della descrizione dei tipi all'interno della libreria dei tipi contenitore.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Recupera una descrizione o specifica di un punto di ingresso per una funzione in una DLL.</summary>
      <param name="memid">ID della funzione membro di cui restituire la descrizione del punto di ingresso della DLL. </param>
      <param name="invKind">Uno dei valori <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> che specifica il tipo di membro identificato da <paramref name="memid" />. </param>
      <param name="pBstrDllName">Se non è null, la funzione imposterà <paramref name="pBstrDllName" /> su un BSTR che contiene il nome della DLL. </param>
      <param name="pBstrName">Se non è null, la funzione imposterà <paramref name="lpbstrName" /> su un BSTR che contiene il nome del punto di ingresso. </param>
      <param name="pwOrdinal">Se non è null e la funzione è definita da un numero ordinale, <paramref name="lpwOrdinal" /> verrà impostato in modo da puntare all'ordinale. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la stringa di documentazione, il nome e il percorso completo del file della Guida e l'ID del contesto per l'argomento della Guida relativo a una descrizione del tipo specifica.</summary>
      <param name="index">ID del membro di cui restituire la documentazione. </param>
      <param name="strName">Quando termina, questo metodo contiene il nome del metodo dell'elemento.Questo parametro viene passato non inizializzato.</param>
      <param name="strDocString">Quando termina, questo metodo contiene la stringa di documentazione per l'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="dwHelpContext">Quando termina, questo metodo contiene un riferimento al contesto della Guida associato all'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="strHelpFile">Quando termina, questo metodo contiene il nome completo del file della Guida.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Recupera la struttura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> che contiene le informazioni relative a una funzione specificata.</summary>
      <param name="index">Indice della descrizione della funzione da restituire. </param>
      <param name="ppFuncDesc">Quando termina, questo metodo contiene un riferimento a una struttura FUNCDESC che descrive la funzione specificata.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Esegue il mapping tra i nomi e gli ID dei membri e tra i nomi e gli ID dei parametri.</summary>
      <param name="rgszNames">Matrice di nomi di cui eseguire il mapping. </param>
      <param name="cNames">Conteggio dei nomi di cui eseguire il mapping. </param>
      <param name="pMemId">Quando termina, questo metodo contiene un riferimento a una matrice in cui si trovano i mapping dei nomi.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Recupera il valore <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> per un'interfaccia implementata o un'interfaccia di base in una descrizione del tipo.</summary>
      <param name="index">Indice dell'interfaccia implementata o dell'interfaccia di base. </param>
      <param name="pImplTypeFlags">Quando termina, questo metodo contiene un riferimento all'enumerazione IMPLTYPEFLAGS.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)">
      <summary>Recupera le informazioni di marshalling.</summary>
      <param name="memid">ID del membro che indica le informazioni di marshalling necessarie. </param>
      <param name="pBstrMops">Quando termina, questo metodo contiene un riferimento alla stringa opcode utilizzata nel marshaling dei campi della struttura descritta dalla descrizione del tipo a cui viene fatto riferimento oppure restituisce null se non sono disponibili informazioni da restituire.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Recupera la variabile con l'ID del membro specificato (o il nome della proprietà o del metodo e i relativi parametri) che corrisponde all'ID di funzione specificato.</summary>
      <param name="memid">ID del membro di cui restituire il cui nome o i nomi. </param>
      <param name="rgBstrNames">Quando termina, questo metodo contiene il nome o i nomi associati al membro.Questo parametro viene passato non inizializzato.</param>
      <param name="cMaxNames">Lunghezza della matrice <paramref name="rgBstrNames" />. </param>
      <param name="pcNames">Quando termina, questo metodo contiene il numero di nomi nella matrice <paramref name="rgBstrNames" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera le descrizioni del tipo cui viene fatto riferimento, se una descrizione del tipo fa riferimento ad altre.</summary>
      <param name="hRef">Handle alla descrizione del tipo cui viene fatto riferimento da restituire. </param>
      <param name="ppTI">Quando termina, questo metodo contiene la descrizione del tipo cui viene fatto riferimento.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Recupera la descrizione dei tipi di interfaccia implementati, se una descrizione del tipo descrive una classe COM.</summary>
      <param name="index">Indice del tipo implementato di cui viene restituito l'handle. </param>
      <param name="href">Quando termina, questo metodo contiene un riferimento a un handle per l'interfaccia implementata.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>Recupera una struttura <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> che contiene gli attributi della descrizione del tipo.</summary>
      <param name="ppTypeAttr">Quando termina, questo metodo contiene un riferimento alla struttura che contiene gli attributi della descrizione del tipo.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Recupera l'interfaccia ITypeComp per la descrizione del tipo, che consente a un compilatore client di eseguire l'associazione ai membri della descrizione del tipo.</summary>
      <param name="ppTComp">Quando termina, questo metodo contiene un riferimento all'interfaccia ITypeComp della libreria dei tipi contenitore.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Recupera una struttura VARDESC che descrive la variabile specificata.</summary>
      <param name="index">Indice della descrizione della variabile da restituire. </param>
      <param name="ppVarDesc">Quando termina, questo metodo contiene un riferimento alla struttura VARDESC che descrive la variabile specificata.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Richiama un metodo o accede a una proprietà di un oggetto, che implementa l'interfaccia descritta dalla descrizione del tipo.</summary>
      <param name="pvInstance">Riferimento all'interfaccia descritta da questa descrizione del tipo. </param>
      <param name="memid">Valore che identifica il membro di interfaccia. </param>
      <param name="wFlags">Flag che descrivono il contesto della chiamata Invoke. </param>
      <param name="pDispParams">Riferimento a una struttura che contiene una matrice di argomenti, una matrice di DISPID per argomenti denominati e i conteggi del numero di elementi in ogni matrice. </param>
      <param name="pVarResult">Riferimento al percorso in cui archiviare il risultato.Se <paramref name="wFlags" /> specifica DISPATCH_PROPERTYPUT o DISPATCH_PROPERTYPUTREF, <paramref name="pVarResult" /> verrà ignorato.Impostare su null se non si desidera alcun risultato.</param>
      <param name="pExcepInfo">Puntatore a una struttura di informazioni sulle eccezioni, compilata solo se viene restituito DISP_E_EXCEPTION. </param>
      <param name="puArgErr">Se Invoke restituisce DISP_E_TYPEMISMATCH, <paramref name="puArgErr" /> indica l'indice all'interno del parametro <paramref name="rgvarg" /> dell'argomento con il tipo non corretto.Se più argomenti restituiscono un errore, <paramref name="puArgErr" /> indicherà solo il primo argomento con l'errore.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>Libera una struttura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> precedentemente restituita dal metodo <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pFuncDesc">Riferimento alla struttura FUNCDESC da liberare. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>Libera una struttura <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> precedentemente restituita dal metodo <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />.</summary>
      <param name="pTypeAttr">Riferimento alla struttura TYPEATTR da liberare. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>Libera una struttura VARDESC precedentemente restituita dal metodo <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pVarDesc">Riferimento alla struttura VARDESC da liberare. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo2">
      <summary>Fornisce la definizione gestita dell'interfaccia ITypeInfo2.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>Recupera gli indirizzi delle funzioni o delle variabili statiche, come quelle definite in una DLL.</summary>
      <param name="memid">ID membro dell'indirizzo del membro static da recuperare. </param>
      <param name="invKind">Uno dei valori <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> che specifica se il membro è una proprietà e, in caso positivo, ne indica il tipo. </param>
      <param name="ppv">Quando termina, questo metodo contiene un riferimento al membro static.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>Crea una nuova istanza di un tipo che descrive una classe di componenti (coclass).</summary>
      <param name="pUnkOuter">Oggetto che funge da interfaccia IUnknown di controllo. </param>
      <param name="riid">IID dell'interfaccia utilizzata dal chiamante per comunicare con l'oggetto risultante. </param>
      <param name="ppvObj">Quando termina, questo metodo contiene un riferimento all'oggetto creato.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllCustData(System.IntPtr)">
      <summary>Ottiene tutti gli elementi dati personalizzati per la libreria.</summary>
      <param name="pCustData">Puntatore a CUSTDATA, che contiene tutti gli elementi dati personalizzati. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllFuncCustData(System.Int32,System.IntPtr)">
      <summary>Ottiene tutti i dati personalizzati dalla funzione specificata.</summary>
      <param name="index">Indice della funzione per cui ottenere i dati personalizzati. </param>
      <param name="pCustData">Puntatore a CUSTDATA, che contiene tutti gli elementi dati personalizzati. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllImplTypeCustData(System.Int32,System.IntPtr)">
      <summary>Ottiene tutti i dati personalizzati per il tipo di implementazione specificato.</summary>
      <param name="index">Indice del tipo di implementazione per i dati personalizzati. </param>
      <param name="pCustData">Puntatore a CUSTDATA, che contiene tutti gli elementi dati personalizzati. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllParamCustData(System.Int32,System.Int32,System.IntPtr)">
      <summary>Ottiene tutti i dati personalizzati per il parametro di funzione specificato.</summary>
      <param name="indexFunc">Indice della funzione per cui ottenere i dati personalizzati. </param>
      <param name="indexParam">Indice del parametro della funzione per cui ottenere i dati personalizzati. </param>
      <param name="pCustData">Puntatore a CUSTDATA, che contiene tutti gli elementi dati personalizzati. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllVarCustData(System.Int32,System.IntPtr)">
      <summary>Ottiene la variabile per i dati personalizzati.</summary>
      <param name="index">Indice della variabile per cui ottenere i dati personalizzati. </param>
      <param name="pCustData">Puntatore a CUSTDATA, che contiene tutti gli elementi dati personalizzati. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>Recupera la libreria dei tipi che contiene questa descrizione del tipo e il relativo indice all'interno della libreria.</summary>
      <param name="ppTLB">Quando termina, questo metodo contiene un riferimento alla libreria dei tipi contenitore.Questo parametro viene passato non inizializzato.</param>
      <param name="pIndex">Quando termina, questo metodo contiene un riferimento all'indice della descrizione dei tipi all'interno della libreria dei tipi contenitore.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetCustData(System.Guid@,System.Object@)">
      <summary>Ottiene i dati personalizzati.</summary>
      <param name="guid">GUID utilizzato per identificare i dati. </param>
      <param name="pVarVal">Quando termina, questo metodo contiene un Object che specifica dove inserire i dati recuperati.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>Recupera una descrizione o specifica di un punto di ingresso per una funzione in una DLL.</summary>
      <param name="memid">ID della funzione membro di cui restituire la descrizione del punto di ingresso della DLL. </param>
      <param name="invKind">Uno dei valori <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> che specifica il tipo di membro identificato da <paramref name="memid" />. </param>
      <param name="pBstrDllName">Se non è null, la funzione imposterà <paramref name="pBstrDllName" /> su un BSTR che contiene il nome della DLL. </param>
      <param name="pBstrName">Se non è null, la funzione imposterà <paramref name="lpbstrName" /> su un BSTR che contiene il nome del punto di ingresso. </param>
      <param name="pwOrdinal">Se non è null e la funzione è definita da un numero ordinale, <paramref name="lpwOrdinal" /> verrà impostato in modo da puntare all'ordinale. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la stringa di documentazione, il nome e il percorso completo del file della Guida e l'ID del contesto per l'argomento della Guida relativo a una descrizione del tipo specifica.</summary>
      <param name="index">ID del membro di cui restituire la documentazione. </param>
      <param name="strName">Quando termina, questo metodo contiene il nome del metodo dell'elemento.Questo parametro viene passato non inizializzato.</param>
      <param name="strDocString">Quando termina, questo metodo contiene la stringa di documentazione per l'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="dwHelpContext">Quando termina, questo metodo contiene un riferimento al contesto della Guida associato all'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="strHelpFile">Quando termina, questo metodo contiene il nome completo del file della Guida.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la stringa di documentazione, il nome e il percorso completo del file della Guida, il contesto di localizzazione da utilizzare e l'ID di contesto per l'argomento della Guida della libreria nel file della Guida.</summary>
      <param name="memid">Identificatore del membro per la descrizione del tipo. </param>
      <param name="pbstrHelpString">Quando termina, questo metodo contiene un BSTR che contiene il nome dell'elemento specificato.Se il chiamante non necessita del nome dell'elemento, <paramref name="pbstrHelpString" /> può essere null.Questo parametro viene passato non inizializzato.</param>
      <param name="pdwHelpStringContext">Quando termina, questo metodo contiene il contesto di localizzazione della Guida.Se il chiamante non necessita del contesto della Guida, <paramref name="pdwHelpStringContext" /> può essere null.Questo parametro viene passato non inizializzato.</param>
      <param name="pbstrHelpStringDll">Quando termina, questo metodo contiene un BSTR che contiene il nome completo del file contenente la DLL utilizzata per il file della Guida.Se il chiamante non necessita del nome file, <paramref name="pbstrHelpStringDll" /> può essere null.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Ottiene i dati personalizzati dalla funzione specificata.</summary>
      <param name="index">Indice della funzione per cui ottenere i dati personalizzati. </param>
      <param name="guid">GUID utilizzato per identificare i dati. </param>
      <param name="pVarVal">Quando termina, questo metodo contiene un Object che specifica dove inserire i dati.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>Recupera la struttura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> che contiene le informazioni relative a una funzione specificata.</summary>
      <param name="index">Indice della descrizione della funzione da restituire. </param>
      <param name="ppFuncDesc">Quando termina, questo metodo contiene un riferimento a una struttura FUNCDESC che descrive la funzione specificata.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncIndexOfMemId(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.Int32@)">
      <summary>Si associa a un membro specifico in base a un DISPID noto, dove il nome del membro è sconosciuto, ad esempio in caso di associazione a un membro predefinito.</summary>
      <param name="memid">Identificatore del membro. </param>
      <param name="invKind">Uno dei valori <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> che specifica il tipo di membro identificato da memid.</param>
      <param name="pFuncIndex">Quando termina, questo metodo contiene un indice nella funzione.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>Esegue il mapping tra i nomi e gli ID dei membri e tra i nomi e gli ID dei parametri.</summary>
      <param name="rgszNames">Matrice di nomi di cui eseguire il mapping. </param>
      <param name="cNames">Conteggio dei nomi di cui eseguire il mapping. </param>
      <param name="pMemId">Quando termina, questo metodo contiene un riferimento a una matrice in cui si trovano i mapping dei nomi.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Ottiene il tipo di implementazione dei dati personalizzati.</summary>
      <param name="index">Indice del tipo di implementazione per i dati personalizzati. </param>
      <param name="guid">GUID utilizzato per identificare i dati. </param>
      <param name="pVarVal">Quando termina, questo metodo contiene un Object che specifica dove inserire i dati recuperati.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>Recupera il valore <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> per un'interfaccia implementata o un'interfaccia di base in una descrizione del tipo.</summary>
      <param name="index">Indice dell'interfaccia implementata o dell'interfaccia di base. </param>
      <param name="pImplTypeFlags">Quando termina, questo metodo contiene un riferimento all'enumerazione IMPLTYPEFLAGS.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetMops(System.Int32,System.String@)">
      <summary>Recupera le informazioni di marshalling.</summary>
      <param name="memid">ID del membro che indica le informazioni di marshalling necessarie. </param>
      <param name="pBstrMops">Quando termina, questo metodo contiene un riferimento alla stringa opcode utilizzata nel marshaling dei campi della struttura descritta dalla descrizione del tipo a cui viene fatto riferimento oppure restituisce null se non sono disponibili informazioni da restituire.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>Recupera la variabile con l'ID del membro specificato (o il nome della proprietà o del metodo e i relativi parametri) che corrisponde all'ID di funzione specificato.</summary>
      <param name="memid">ID del membro di cui restituire il cui nome o i nomi. </param>
      <param name="rgBstrNames">Quando termina, questo metodo contiene il nome o i nomi associati al membro.Questo parametro viene passato non inizializzato.</param>
      <param name="cMaxNames">Lunghezza della matrice <paramref name="rgBstrNames" />. </param>
      <param name="pcNames">Quando termina, questo metodo contiene il numero di nomi nella matrice <paramref name="rgBstrNames" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetParamCustData(System.Int32,System.Int32,System.Guid@,System.Object@)">
      <summary>Ottiene il parametro specificato per i dati personalizzati.</summary>
      <param name="indexFunc">Indice della funzione per cui ottenere i dati personalizzati. </param>
      <param name="indexParam">Indice del parametro della funzione per cui ottenere i dati personalizzati. </param>
      <param name="guid">GUID utilizzato per identificare i dati. </param>
      <param name="pVarVal">Quando termina, questo metodo contiene un Object che specifica dove inserire i dati recuperati.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera le descrizioni di tipo cui viene fatto riferimento, se una descrizione del tipo fa riferimento ad altre.</summary>
      <param name="hRef">Handle alla descrizione del tipo cui viene fatto riferimento da restituire. </param>
      <param name="ppTI">Quando termina, questo metodo contiene la descrizione del tipo cui viene fatto riferimento.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>Recupera la descrizione dei tipi di interfaccia implementati, se una descrizione del tipo descrive una classe COM.</summary>
      <param name="index">Indice del tipo implementato di cui viene restituito l'handle. </param>
      <param name="href">Quando termina, questo metodo contiene un riferimento a un handle per l'interfaccia implementata.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeAttr(System.IntPtr@)">
      <summary>Recupera una struttura <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> che contiene gli attributi della descrizione del tipo.</summary>
      <param name="ppTypeAttr">Quando termina, questo metodo contiene un riferimento alla struttura che contiene gli attributi della descrizione del tipo.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Recupera l'interfaccia ITypeComp per la descrizione del tipo, che consente a un compilatore client di eseguire l'associazione ai membri della descrizione del tipo.</summary>
      <param name="ppTComp">Quando termina, questo metodo contiene un riferimento all'interfaccia ITypeComp della libreria dei tipi contenitore.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeFlags(System.Int32@)">
      <summary>Restituisce i flag di tipo senza allocazioni.Questo metodo restituisce un flag di tipo DWORD, che espande i flag di tipo senza accrescere l'attributo di tipo TYPEATTR.</summary>
      <param name="pTypeFlags">Quando termina, questo metodo contiene un riferimento DWORD a un TYPEFLAG.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeKind(System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Restituisce rapidamente l'enumerazione TYPEKIND, senza effettuare allocazioni.</summary>
      <param name="pTypeKind">Quando termina, questo metodo contiene un riferimento a un'enumerazione TYPEKIND.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>Ottiene la variabile per i dati personalizzati.</summary>
      <param name="index">Indice della variabile per cui ottenere i dati personalizzati. </param>
      <param name="guid">GUID utilizzato per identificare i dati. </param>
      <param name="pVarVal">Quando termina, questo metodo contiene un Object che specifica dove inserire i dati recuperati.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>Recupera una struttura VARDESC che descrive la variabile specificata.</summary>
      <param name="index">Indice della descrizione della variabile da restituire. </param>
      <param name="ppVarDesc">Quando termina, questo metodo contiene un riferimento alla struttura VARDESC che descrive la variabile specificata.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarIndexOfMemId(System.Int32,System.Int32@)">
      <summary>Si associa a un membro specifico in base a un DISPID noto, dove il nome del membro è sconosciuto, ad esempio in caso di associazione a un membro predefinito.</summary>
      <param name="memid">Identificatore del membro. </param>
      <param name="pVarIndex">Quando termina, questo metodo contiene un indice di <paramref name="memid" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>Richiama un metodo o accede a una proprietà di un oggetto, che implementa l'interfaccia descritta dalla descrizione del tipo.</summary>
      <param name="pvInstance">Riferimento all'interfaccia descritta da questa descrizione del tipo. </param>
      <param name="memid">Identificatore del membro di interfaccia. </param>
      <param name="wFlags">Flag che descrivono il contesto della chiamata Invoke. </param>
      <param name="pDispParams">Riferimento a una struttura che contiene una matrice di argomenti, una matrice di DISPID per argomenti denominati e i conteggi del numero di elementi in ogni matrice. </param>
      <param name="pVarResult">Riferimento al percorso in cui archiviare il risultato.Se <paramref name="wFlags" /> specifica DISPATCH_PROPERTYPUT o DISPATCH_PROPERTYPUTREF, <paramref name="pVarResult" /> verrà ignorato.Impostare su null se non si desidera alcun risultato.</param>
      <param name="pExcepInfo">Puntatore a una struttura di informazioni sulle eccezioni, compilata solo se viene restituito DISP_E_EXCEPTION. </param>
      <param name="puArgErr">Se Invoke restituisce DISP_E_TYPEMISMATCH, <paramref name="puArgErr" /> indica l'indice dell'argomento con il tipo non corretto.Se più argomenti restituiscono un errore, <paramref name="puArgErr" /> indicherà solo il primo argomento con l'errore.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseFuncDesc(System.IntPtr)">
      <summary>Libera una struttura <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> precedentemente restituita dal metodo <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pFuncDesc">Riferimento alla struttura FUNCDESC da liberare. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseTypeAttr(System.IntPtr)">
      <summary>Libera una struttura <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> precedentemente restituita dal metodo <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" />.</summary>
      <param name="pTypeAttr">Riferimento alla struttura TYPEATTR da liberare. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseVarDesc(System.IntPtr)">
      <summary>Libera una struttura VARDESC precedentemente restituita dal metodo <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" />.</summary>
      <param name="pVarDesc">Riferimento alla struttura VARDESC da liberare. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib">
      <summary>Fornisce la definizione gestita dell'interfaccia ITypeLib.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Individua le occorrenze di una descrizione dei tipi in una libreria dei tipi.</summary>
      <param name="szNameBuf">Nome da cercare.Parametro in/out.</param>
      <param name="lHashVal">Valore hash per accelerare la ricerca, calcolato dalla funzione LHashValOfNameSys.Se <paramref name="lHashVal" /> è 0, verrà calcolato un valore.</param>
      <param name="ppTInfo">Quando termina, questo metodo restituisce una matrice di puntatori alle descrizioni dei tipi che contengono il nome specificato in <paramref name="szNameBuf" />.Questo parametro viene passato non inizializzato.</param>
      <param name="rgMemId">Matrice dell'oggetto MEMBERID degli elementi individuati; <paramref name="rgMemId" />[i] è l'oggetto MEMBERID che crea indici nella descrizione dei tipi specificata da <paramref name="ppTInfo" />[i].Non può essere null.</param>
      <param name="pcFound">In ingresso, indica il numero di istanze da ricercare.<paramref name="pcFound" /> = 1, ad esempio, può essere chiamato per cercare la prima occorrenza.La ricerca termina quando viene trovata un'istanza.In uscita, indica il numero delle istanze trovate.Se i valori in e out di <paramref name="pcFound" /> sono identici, è possibile che siano disponibili più descrizioni dei tipi che contengono il nome.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la stringa di documentazione della libreria, il nome e il percorso completo del file della Guida e l'identificatore del contesto per l'argomento della Guida della libreria nel file della Guida.</summary>
      <param name="index">Indice della descrizione del tipo di cui restituire la documentazione. </param>
      <param name="strName">Quando termina, questo metodo contiene una stringa che rappresenta il nome dell'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="strDocString">Quando termina, questo metodo contiene una stringa che rappresenta la stringa di documentazione per l'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="dwHelpContext">Quando termina, questo metodo contiene l'identificatore di contesto della Guida associato all'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="strHelpFile">Quando termina, questo metodo contiene una stringa che rappresenta il nome completo del file della Guida.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>Recupera la struttura che contiene gli attributi della libreria.</summary>
      <param name="ppTLibAttr">Quando termina, questo metodo contiene una struttura che contiene gli attributi della libreria.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Consente a un compilatore client di effettuare l'associazione ai tipi, alle variabili, alle costanti e alle funzioni globali di una libreria.</summary>
      <param name="ppTComp">Quando termina, questo metodo contiene un'istanza di un'istanza di ITypeComp per ITypeLib.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera la descrizione del tipo specificata nella libreria.</summary>
      <param name="index">Indice dell'interfaccia ITypeInfo da restituire. </param>
      <param name="ppTI">Quando termina, questo metodo contiene un'interfaccia ITypeInfo che descrive il tipo a cui fa riferimento <paramref name="index" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoCount">
      <summary>Restituisce il numero delle descrizioni dei tipi nella libreria dei tipi.</summary>
      <returns>Numero delle descrizioni dei tipi nella libreria dei tipi.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera la descrizione del tipo che corrisponde al GUID specificato.</summary>
      <param name="guid">IID dell'interfaccia o CLSID della classe di cui vengono richieste le informazioni sul tipo. </param>
      <param name="ppTInfo">Quando termina, questo metodo contiene l'interfaccia ITypeInfo richiesta.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Recupera il tipo di una descrizione del tipo.</summary>
      <param name="index">Indice della descrizione dei tipi all'interno della libreria dei tipi. </param>
      <param name="pTKind">Quando termina, questo metodo contiene un riferimento all'enumerazione TYPEKIND per la descrizione del tipo.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.IsName(System.String,System.Int32)">
      <summary>Indica se una stringa passata contiene il nome di un tipo o di un membro descritto nella libreria.</summary>
      <returns>true se <paramref name="szNameBuf" /> viene trovato nella libreria dei tipi; in caso contrario, false.</returns>
      <param name="szNameBuf">Stringa per cui eseguire il test.Parametro in/out.</param>
      <param name="lHashVal">Valore hash di <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>Libera la struttura <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> originariamente ottenuta dal metodo <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />.</summary>
      <param name="pTLibAttr">Struttura TLIBATTR da liberare. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib2">
      <summary>Fornisce una definizione gestita dell'interfaccia ITypeLib2.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>Individua le occorrenze di una descrizione dei tipi in una libreria dei tipi.</summary>
      <param name="szNameBuf">Nome da cercare. </param>
      <param name="lHashVal">Valore hash per accelerare la ricerca, calcolato dalla funzione LHashValOfNameSys.Se <paramref name="lHashVal" /> è 0, verrà calcolato un valore.</param>
      <param name="ppTInfo">Quando termina, questo metodo restituisce una matrice di puntatori alle descrizioni dei tipi che contengono il nome specificato in <paramref name="szNameBuf" />.Questo parametro viene passato non inizializzato.</param>
      <param name="rgMemId">Quando termina, questo metodo contiene una matrice di MEMBERID degli elementi trovati; <paramref name="rgMemId" /> [i] è l'oggetto MEMBERID che indicizza nella descrizione dei tipi specificata da <paramref name="ppTInfo" /> [i].Questo parametro non può essere null.Questo parametro viene passato non inizializzato.</param>
      <param name="pcFound">In ingresso, valore passato per riferimento che indica quante istanze cercare.<paramref name="pcFound" /> = 1, ad esempio, può essere chiamato per cercare la prima occorrenza.La ricerca termina quando viene trovata un'istanza.In uscita, indica il numero delle istanze trovate.Se i valori in e out di <paramref name="pcFound" /> sono identici, è possibile che siano disponibili più descrizioni dei tipi che contengono il nome.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetAllCustData(System.IntPtr)">
      <summary>Ottiene tutti gli elementi dati personalizzati per la libreria.</summary>
      <param name="pCustData">Puntatore a CUSTDATA, che contiene tutti gli elementi dati personalizzati. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetCustData(System.Guid@,System.Object@)">
      <summary>Ottiene i dati personalizzati.</summary>
      <param name="guid">Oggetto <see cref="T:System.Guid" />, passato per riferimento, utilizzato per identificare i dati. </param>
      <param name="pVarVal">Quando termina, questo metodo contiene un oggetto che specifica dove inserire i dati recuperati.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la stringa di documentazione della libreria, il nome e il percorso completo del file della Guida e l'identificatore del contesto per l'argomento della Guida della libreria nel file della Guida.</summary>
      <param name="index">Indice della descrizione del tipo di cui restituire la documentazione. </param>
      <param name="strName">Quando termina, questo metodo contiene una stringa che specifica il nome dell'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="strDocString">Quando termina, questo metodo contiene la stringa di documentazione per l'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="dwHelpContext">Quando termina, questo metodo contiene l'identificatore di contesto della Guida associato all'elemento specificato.Questo parametro viene passato non inizializzato.</param>
      <param name="strHelpFile">Quando termina, questo metodo contiene una stringa che specifica il nome completo del file della Guida.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>Recupera la stringa di documentazione della libreria, il nome e il percorso completo del file della Guida, il contesto di localizzazione da utilizzare e l'ID di contesto per l'argomento della Guida della libreria nel file della Guida.</summary>
      <param name="index">Indice della descrizione del tipo di cui restituire la documentazione; se <paramref name="index" /> è -1, viene restituita la documentazione per la libreria. </param>
      <param name="pbstrHelpString">Quando termina, questo metodo contiene un BSTR che specifica il nome dell'elemento specificato.Se il chiamante non necessita del nome dell'elemento, <paramref name="pbstrHelpString" /> può essere null.Questo parametro viene passato non inizializzato.</param>
      <param name="pdwHelpStringContext">Quando termina, questo metodo contiene il contesto di localizzazione della Guida.Se il chiamante non necessita del contesto della Guida, <paramref name="pdwHelpStringContext" /> può essere null.Questo parametro viene passato non inizializzato.</param>
      <param name="pbstrHelpStringDll">Quando termina, questo metodo contiene un BSTR che specifica il nome completo del file contenente la DLL utilizzata per il file della Guida.Se il chiamante non necessita del nome file, <paramref name="pbstrHelpStringDll" /> può essere null.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibAttr(System.IntPtr@)">
      <summary>Recupera la struttura che contiene gli attributi della libreria.</summary>
      <param name="ppTLibAttr">Quando termina, questo metodo contiene una struttura che contiene gli attributi della libreria.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibStatistics(System.IntPtr,System.Int32@)">
      <summary>Restituisce statistiche su una libreria dei tipi richieste per l'efficiente dimensionamento di tabelle hash.</summary>
      <param name="pcUniqueNames">Puntatore a un conteggio di nomi univoci.Se il chiamante non necessita di queste informazioni, impostare su null.</param>
      <param name="pcchUniqueNames">Quando termina, questo metodo contiene un puntatore a una modifica nel conteggio di nomi univoci.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>Consente a un compilatore client di effettuare l'associazione ai tipi, alle variabili, alle costanti e alle funzioni globali di una libreria.</summary>
      <param name="ppTComp">Quando termina, questo metodo contiene un'istanza di ITypeComp per questo ITypeLib.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera la descrizione del tipo specificata nella libreria.</summary>
      <param name="index">Indice dell'interfaccia ITypeInfo da restituire. </param>
      <param name="ppTI">Quando termina, questo metodo contiene un'interfaccia ITypeInfo che descrive il tipo a cui fa riferimento <paramref name="index" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoCount">
      <summary>Restituisce il numero delle descrizioni dei tipi nella libreria dei tipi.</summary>
      <returns>Numero delle descrizioni dei tipi nella libreria dei tipi.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>Recupera la descrizione del tipo che corrisponde al GUID specificato.</summary>
      <param name="guid">Oggetto <see cref="T:System.Guid" />, passato per riferimento, che rappresenta l'IID dell'interfaccia CLSID della classe di cui sono richieste informazioni sul tipo. </param>
      <param name="ppTInfo">Quando termina, questo metodo contiene l'interfaccia ITypeInfo richiesta.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>Recupera il tipo di una descrizione del tipo.</summary>
      <param name="index">Indice della descrizione dei tipi all'interno della libreria dei tipi. </param>
      <param name="pTKind">Quando termina, questo metodo contiene un riferimento all'enumerazione TYPEKIND per la descrizione del tipo.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.IsName(System.String,System.Int32)">
      <summary>Indica se una stringa passata contiene il nome di un tipo o di un membro descritto nella libreria.</summary>
      <returns>true se <paramref name="szNameBuf" /> viene trovato nella libreria dei tipi; in caso contrario, false.</returns>
      <param name="szNameBuf">Stringa per cui eseguire il test. </param>
      <param name="lHashVal">Valore hash di <paramref name="szNameBuf" />. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.ReleaseTLibAttr(System.IntPtr)">
      <summary>Libera la struttura <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> originariamente ottenuta dal metodo <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" />.</summary>
      <param name="pTLibAttr">Struttura TLIBATTR da liberare. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.LIBFLAGS">
      <summary>Definisce i flag applicabili alle librerie dei tipi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FCONTROL">
      <summary>La libreria dei tipi descrive i controlli e non deve essere visualizzata nei visualizzatori dei tipi destinati agli oggetti non visibili.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHASDISKIMAGE">
      <summary>La libreria dei tipi viene mantenuta in modo persistente sul disco.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHIDDEN">
      <summary>La libreria dei tipi non deve essere visibile agli utenti, anche se l'utilizzo non è limitato.La libreria dei tipi deve essere utilizzata da controlli.Gli host devono creare una nuova libreria dei tipi che esegua il wrapping del controllo con proprietà estese.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FRESTRICTED">
      <summary>L'utilizzo della libreria dei tipi è limitato e questa non deve essere visibile agli utenti.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMDESC">
      <summary>Contiene informazioni sul trasferimento di un elemento strutturale, di un parametro o del valore restituito di una funzione tra diversi processi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.lpVarValue">
      <summary>Rappresenta un puntatore a un valore passato da un processo a un altro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.wParamFlags">
      <summary>Rappresenta valori bitmask che descrivono l'elemento strutturale, il parametro o il valore restituito.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMFLAG">
      <summary>Descrive come trasferire tra diversi processi un elemento strutturale, un parametro o il valore restituito di una funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASCUSTDATA">
      <summary>Il parametro contiene dati personalizzati.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASDEFAULT">
      <summary>	Il parametro contiene comportamenti predefiniti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FIN">
      <summary>Il parametro passa informazioni dal chiamante al chiamato.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FLCID">
      <summary>Il parametro è l'identificatore locale di un'applicazione client.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOPT">
      <summary>Il parametro è facoltativo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOUT">
      <summary>Il parametro restituisce informazioni dal chiamante al chiamato.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FRETVAL">
      <summary>Il parametro è il valore restituito del membro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_NONE">
      <summary>Non specifica se il parametro passa o riceve informazioni.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATDATA">
      <summary>Fornisce la definizione gestita della struttura STATDATA.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advf">
      <summary>Rappresenta il valore di enumerazione di <see cref="T:System.Runtime.InteropServices.ComTypes.ADVF" /> che determina in quale momento il sink consultivo riceve la notifica delle modifiche apportate ai dati.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advSink">
      <summary>Rappresenta l'interfaccia <see cref="T:System.Runtime.InteropServices.ComTypes.IAdviseSink" /> che riceverà le notifiche delle modifiche.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.connection">
      <summary>Rappresenta il token che identifica in modo univoco la connessione consultiva.Questo token viene restituito dal metodo che imposta la connessione consultiva.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.formatetc">
      <summary>Rappresenta la struttura <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> per i dati di interesse per il sink di notifica.Il sink di notifica riceve la notifica delle modifiche apportate ai dati specificati da questa struttura <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATSTG">
      <summary>Contiene le informazioni statistiche relative a un'archiviazione aperta, a un flusso oppure a un oggetto matrice di byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.atime">
      <summary>Specifica l'ora dell'ultimo accesso di questa archiviazione, flusso o matrice di byte. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.cbSize">
      <summary>Specifica la dimensione in byte del flusso o della matrice di byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.clsid">
      <summary>Indica l'identificatore di classe per l'oggetto di archiviazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.ctime">
      <summary>Indica l'ora di creazione di questo archivio, flusso o matrice di byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfLocksSupported">
      <summary>Indica i tipi di blocco di area supportati dal flusso o dalla matrice di byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfMode">
      <summary>Indica la modalità di accesso specificata quando l'oggetto è stato aperto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfStateBits">
      <summary>Indica i bit di stato correnti dell'oggetto di archiviazione (il valore impostato più di recente dal metodo IStorage::SetStateBits).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.mtime">
      <summary>Indica l'ora dell'ultima modifica di questo archivio, flusso o matrice di byte.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.pwcsName">
      <summary>Rappresenta un puntatore a una stringa con terminazione null contenente il nome dell'oggetto descritto da questa struttura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.reserved">
      <summary>Riservato per un utilizzo futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.type">
      <summary>Indica il tipo di oggetto di archiviazione che rappresenta uno dei valori dell'enumerazione di STGTY.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM">
      <summary>Fornisce la definizione gestita della struttura STGMEDIUM.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease">
      <summary>Rappresenta un puntatore a un'istanza di interfaccia che consente il controllo da parte del processo di invio della modalità di rilascio dell'archiviazione quando il processo ricevente chiama la funzione ReleaseStgMedium.Se <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> è null, ReleaseStgMedium utilizza le routine predefinite per rilasciare l'archiviazione; in caso contrario ReleaseStgMedium utilizza l'interfaccia IUnknown specificata.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.tymed">
      <summary>Specifica il tipo di supporto di archiviazione.Nelle routine di marshaling e unmarshaling questo valore viene utilizzato per determinare quale membro di union è stato utilizzato.Questo valore deve essere uno degli elementi dell'enumerazione <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.unionmember">
      <summary>Rappresenta un handle, una stringa o un puntatore a interfaccia utilizzabile dal processo ricevente per accedere ai dati di cui è in corso il trasferimento.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.SYSKIND">
      <summary>Identifica la piattaforma del sistema operativo di destinazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_MAC">
      <summary>Il sistema operativo di destinazione per la libreria dei tipi è Apple Macintosh.Per impostazione predefinita, tutti i campi dati sono allineati secondo limiti con byte pari.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN16">
      <summary>Il sistema operativo di destinazione per la libreria dei tipi è il sistema Windows a 16 bit.Per impostazione predefinita, i campi dati sono compressi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN32">
      <summary>Il sistema operativo di destinazione per la libreria dei tipi è il sistema Windows a 32 bit.Per impostazione predefinita, i campi dati sono allineati naturalmente (gli interi a 2 byte, ad esempio, sono allineati secondo limiti a byte pari; gli interi a 4 byte sono allineati secondo limiti a quattro parole e così via).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN64">
      <summary>Il sistema operativo di destinazione per la libreria dei tipi è il sistema Windows a 64 bit.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYMED">
      <summary>Fornisce la definizione gestita della struttura TYMED.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ENHMF">
      <summary>Il supporto di archiviazione è un enhanced metafile.Se il membro <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> è null, il processo di destinazione deve utilizzare DeleteEnhMetaFile per eliminare la bitmap.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_FILE">
      <summary>Il supporto di archiviazione è un file su disco identificato da un percorso.Se il membro STGMEDIUM<see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> è null, il processo di destinazione deve utilizzare OpenFile per eliminare il file.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_GDI">
      <summary>Il supporto di archiviazione è un componente GDI (Graphics Device Interface, HBITMAP).Se il membro <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> è null, il processo di destinazione deve utilizzare DeleteObject per eliminare la bitmap.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_HGLOBAL">
      <summary>Il supporto di archiviazione è un handle di memoria globale (HGLOBAL).Allocare il flag GMEM_SHARE all'handle globale.Se il membro <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> è null, il processo di destinazione deve utilizzare GlobalFree per rilasciare la memoria.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTORAGE">
      <summary>Il supporto di archiviazione è un componente di archiviazione identificato da un puntatore IStorage.I dati si trovano nei flussi e negli archivi contenuti da questa istanza di IStorage.Se il membro <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> non è null, il processo di destinazione deve utilizzare IStorage::Release per rilasciare il componente di archiviazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTREAM">
      <summary>Il supporto di archiviazione è un oggetto di flusso identificato da un puntatore IStream.Utilizzare ISequentialStream::Read per leggere i dati.Se il membro <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> non è null, il processo di destinazione deve utilizzare IStream::Release per rilasciare il componente di flusso.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_MFPICT">
      <summary>Il supporto di archiviazione è un metafile (HMETAFILE).Utilizzare le funzioni Windows o WIN32 per accedere ai dati del metafile.Se il membro <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> è null, il processo di destinazione deve utilizzare DeleteMetaFile per eliminare la bitmap.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_NULL">
      <summary>Non vengono passati dati.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEATTR">
      <summary>Contiene gli attributi di un UCOMITypeInfo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbAlignment">
      <summary>Specifica l'allineamento dei byte per un'istanza di questo tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeInstance">
      <summary>Dimensione di un'istanza di questo tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeVft">
      <summary>Dimensione della tabella del metodo virtual (VTBL) di questo tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cFuncs">
      <summary>Indica il numero delle funzioni sull'interfaccia descritta da questa struttura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cImplTypes">
      <summary>Indica il numero delle interfacce implementate sull'interfaccia descritta da questa struttura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cVars">
      <summary>Indica il numero delle variabili e dei campi dati sull'interfaccia descritta da questa struttura.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.dwReserved">
      <summary>Riservato per un utilizzo futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.guid">
      <summary>GUID delle informazioni sul tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.idldescType">
      <summary>Attributi IDL del tipo descritto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lcid">
      <summary>Impostazioni locali dei nomi dei membri e delle stringhe della documentazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lpstrSchema">
      <summary>Riservato per un utilizzo futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.MEMBER_ID_NIL">
      <summary>Costante utilizzata con i campi <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidConstructor" /> e <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidDestructor" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidConstructor">
      <summary>ID del costruttore oppure <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" /> se non ne esiste nessuno.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidDestructor">
      <summary>ID del distruttore oppure <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" /> se non ne esiste nessuno.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.tdescAlias">
      <summary>Se <see cref="F:System.Runtime.InteropServices.TYPEATTR.typekind" /> == <see cref="F:System.Runtime.InteropServices.TYPEKIND.TKIND_ALIAS" />, specifica il tipo per il quale questo tipo è un alias.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.typekind">
      <summary>Valore di <see cref="T:System.Runtime.InteropServices.TYPEKIND" /> che descrive il tipo descritto da queste informazioni.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMajorVerNum">
      <summary>Numero di versione principale.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMinorVerNum">
      <summary>Numero di versione secondario.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wTypeFlags">
      <summary>Valore di <see cref="T:System.Runtime.InteropServices.TYPEFLAGS" /> che descrive queste informazioni.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEDESC">
      <summary>Descrive il tipo di una variabile, il tipo restituito di una funzione oppure il tipo del parametro di una funzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.lpValue">
      <summary>Se la variabile è VT_SAFEARRAY oppure VT_PTR, il campo lpValue conterrà un puntatore a un oggetto TYPEDESC che specifica il tipo di elemento.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.vt">
      <summary>Indica il tipo Variant per l'elemento descritto da questo TYPEDESC.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEFLAGS">
      <summary>Definisce le proprietà e gli attributi della descrizione di un tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAGGREGATABLE">
      <summary>La classe supporta l'aggregazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAPPOBJECT">
      <summary>Descrizione di tipo relativa a un oggetto Application.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCANCREATE">
      <summary>Le istanze del tipo possono essere create da ITypeInfo::CreateInstance.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCONTROL">
      <summary>Il tipo è un controllo da cui saranno derivati altri tipi e non deve essere visibile agli utenti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDISPATCHABLE">
      <summary>Indica che l'interfaccia deriva da IDispatch, direttamente o indirettamente.Il flag viene calcolato; pertanto non esiste alcun ODL (Object Description Language) per il flag.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDUAL">
      <summary>L'interfaccia fornisce l'associazione a IDispatch e a VTBL.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FHIDDEN">
      <summary>Il tipo non deve essere visibile ai visualizzatori.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FLICENSED">
      <summary>Il tipo è concesso in licenza.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FNONEXTENSIBLE">
      <summary>L'interfaccia non può aggiungere membri in fase di esecuzione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FOLEAUTOMATION">
      <summary>I tipi utilizzati nell'interfaccia sono completamente compatibili con l'automazione, compreso il supporto per l'associazione VTBL.Se un'interfaccia viene impostata come duale, verrà impostato questo flag e <see cref="F:System.Runtime.InteropServices.TYPEFLAGS.TYPEFLAG_FDUAL" />.Questo flag non è consentito su interfacce dispatch.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPREDECLID">
      <summary>Tipo già definito.È necessario che nell'applicazione client venga creata automaticamente una singola istanza dell'oggetto che contiene questo attributo.Il nome della variabile che punta all'oggetto corrisponde a quello della classe dell'oggetto.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPROXY">
      <summary>Indica che l'interfaccia utilizzerà una libreria a collegamento dinamico proxy/stub.Il flag specifica che la registrazione del proxy della libreria dei tipi non deve essere annullata quando viene annullata la registrazione della libreria dei tipi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREPLACEABLE">
      <summary>L'oggetto supporta IConnectionPointWithDefault e dispone di comportamenti predefiniti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FRESTRICTED">
      <summary>Non deve essere accessibile dai linguaggi macro.Il flag viene utilizzato per i tipi a livello di sistema o che non devono essere visualizzati dai visualizzatori dei tipi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREVERSEBIND">
      <summary>Indica che la risoluzione dei nomi delle interfacce di base deve essere verificata prima di verificare gli elementi figlio, ovvero il contrario del comportamento predefinito.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEKIND">
      <summary>Specifica vari tipi di dati e funzioni.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ALIAS">
      <summary>Tipo che costituisce un alias per un altro tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_COCLASS">
      <summary>Insieme di interfacce di componenti implementate.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_DISPATCH">
      <summary>Insieme di metodi e proprietà accessibili mediante IDispatch::Invoke.Per impostazione predefinita, le interfacce duali restituiscono TKIND_DISPATCH.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ENUM">
      <summary>Insieme di enumeratori.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_INTERFACE">
      <summary>Tipo con funzioni virtual, che sono tutte pure.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MAX">
      <summary>Marcatore di fine numerazione.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MODULE">
      <summary>Modulo che può avere solo dati e funzioni static (ad esempio, una DLL).</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_RECORD">
      <summary>Struttura senza metodi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_UNION">
      <summary>Unione di tutti i membri con offset zero.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPELIBATTR">
      <summary>Identifica una particolare libreria dei tipi e fornisce supporto per la localizzazione per i nomi dei membri.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.guid">
      <summary>Rappresenta un ID di libreria univoco globale per una libreria di tipi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.lcid">
      <summary>Rappresenta un ID delle impostazioni locali di una libreria di tipi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.syskind">
      <summary>Rappresenta la piattaforma hardware di destinazione di una libreria di tipi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wLibFlags">
      <summary>Rappresenta flag di librerie.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMajorVerNum">
      <summary>Rappresenta il numero di versione principale di una libreria dei tipi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMinorVerNum">
      <summary>Rappresenta il numero di versione secondario di una libreria dei tipi.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC">
      <summary>Descrive un membro di una variabile, di una costante o di dati.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.desc">
      <summary>Contiene informazioni relative a una variabile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.elemdescVar">
      <summary>Contiene il tipo della variabile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.lpstrSchema">
      <summary>Questo campo è riservato per un utilizzo futuro.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.memid">
      <summary>Indica l'ID del membro di una variabile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.varkind">
      <summary>Definisce la modalità di marshalling di una variabile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.wVarFlags">
      <summary>Definisce le proprietà di una variabile.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION">
      <summary>Contiene informazioni relative a una variabile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.lpvarValue">
      <summary>Descrive una costante simbolica.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.oInst">
      <summary>Indica l'offset della variabile all'interno dell'istanza.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARFLAGS">
      <summary>Identifica le costanti che definiscono le proprietà di una variabile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FBINDABLE">
      <summary>La variabile supporta l'associazione dati.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTBIND">
      <summary>La variabile è la singola proprietà che meglio rappresenta l'oggetto.Solo una variabile nelle informazioni sul tipo può disporre di questo attributo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTCOLLELEM">
      <summary>Consente un'ottimizzazione nella quale il compilatore ricerca un membro denominato "xyz" sul tipo di "abc".Se tale membro viene individuato e gli viene assegnato un flag come funzione della funzione di accesso per un elemento dell'insieme predefinito, verrà generata una chiamata alla funzione di tale membro.È consentito sui membri nelle interfacce dispatch e nelle interfacce, ma non sui moduli.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDISPLAYBIND">
      <summary>Variabile che viene visualizzata dall'utente come associabile.È inoltre necessario impostare <see cref="F:System.Runtime.InteropServices.VARFLAGS.VARFLAG_FBINDABLE" />.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FHIDDEN">
      <summary>La variabile non deve essere visualizzata all'utente in un visualizzatore, anche se esiste ed è associabile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FIMMEDIATEBIND">
      <summary>La variabile viene mappata come singole proprietà associabili.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FNONBROWSABLE">
      <summary>La variabile viene visualizzata in un visualizzatore oggetti, ma non in un visualizzatore proprietà.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREADONLY">
      <summary>L'assegnazione alla variabile non deve essere consentita.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREPLACEABLE">
      <summary>Inserisce dei tag nell'interfaccia in modo che abbia comportamenti predefiniti.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREQUESTEDIT">
      <summary>Quando è impostata, qualunque tentativo di modifica diretta della proprietà genererà una chiamata a IPropertyNotifySink::OnRequestEdit.L'accettazione della modifica dipende dall'implementazione di OnRequestEdit.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FRESTRICTED">
      <summary>La variabile non deve essere accessibile dai linguaggi macro.Il flag è destinato alle variabili a livello di sistema o alle variabili che non devono essere visualizzate dai visualizzatori di tipi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FSOURCE">
      <summary>La variabile restituisce un oggetto che rappresenta un'origine di eventi.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FUIDEFAULT">
      <summary>La variabile è la visualizzazione predefinita nell'interfaccia utente.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARKIND">
      <summary>Definisce il tipo di variabile.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_CONST">
      <summary>La struttura VARDESC descrive una costante simbolica.Non vi è memoria associata.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_DISPATCH">
      <summary>La variabile è accessibile solo mediante IDispatch::Invoke.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_PERINSTANCE">
      <summary>La variabile è un campo o un membro del tipo.È presente in corrispondenza di un offset fisso all'interno di ciascuna istanza del tipo.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_STATIC">
      <summary>Esiste una sola istanza della variabile.</summary>
    </member>
  </members>
</doc>