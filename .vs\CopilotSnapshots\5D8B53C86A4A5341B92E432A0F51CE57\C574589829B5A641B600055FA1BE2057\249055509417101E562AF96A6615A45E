﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text.RegularExpressions;

public partial class Site : System.Web.UI.MasterPage
{
    App_Func appfun = new App_Func();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["tech_no"] != null)
        {
            string masterSingle = Session["master_single"] != null ? Session["master_single"].ToString() : "";

            // 設定「設定老師權限」連結的 NavigateUrl
            // 假設在 Site.Master 中有一個 HyperLink 控制項 ID 為 hlTeacherAuthLink
            // 這個連結可能存在於多個選單中，或者是一個共用的連結，其可見性由父容器控制
            HyperLink hlTeacherAuthLink = FindControl("hlTeacherAuthLink") as HyperLink; // 嘗試直接從 MasterPage 查找

            // 如果是放在特定的選單 Panel 內，例如 adminMenu 或 departmentAssistantMenu
            if (hlTeacherAuthLink == null && adminMenu != null)
            {
                hlTeacherAuthLink = adminMenu.FindControl("hlTeacherAuthLink") as HyperLink;
            }
            if (hlTeacherAuthLink == null && departmentAssistantMenu != null)
            {
                hlTeacherAuthLink = departmentAssistantMenu.FindControl("hlTeacherAuthLink") as HyperLink;
            }
            // 可以繼續為其他可能的選單容器查找

            if (hlTeacherAuthLink != null)
            {
                if (masterSingle == "3") // 管理者
                {
                    hlTeacherAuthLink.NavigateUrl = "~/AdminSelectDepartment.aspx";
                }
                else // 系助理或其他有權限的角色
                {
                    hlTeacherAuthLink.NavigateUrl = "~/departmentAuthority.aspx";
                }
            }

            // 控制選單顯示的邏輯需要在每次頁面載入時都執行，以確保 PostBack 後狀態正確
            if (!string.IsNullOrEmpty(masterSingle))
            {
                // 根據 master_single 的值顯示對應的選單
                // ASPX 中選單預設為 hidden="hidden"，這裡移除該屬性以顯示
                switch (masterSingle)
                {
                    case "3": // 管理者
                        if (adminMenu != null) adminMenu.Visible = true;
                        // 系助選單對管理者可能也需要可見，以便他們能看到系助的介面
                        //if (departmentAssistantMenu != null) departmentAssistantMenu.Visible = true;
                        break;
                    case "1": // 系助理
                        if (departmentAssistantMenu != null) departmentAssistantMenu.Visible = true;
                        break;
                    case "0": // 教師
                        if (teacherMenu != null) teacherMenu.Visible = true;
                        break;
                    default:
                        break;
                }
            }
        }
        else
        {
            // 若使用者尚未登入，重定向到登入頁面
            Session.Clear();
            Response.Redirect("Login.aspx");
        }
    }



    //判斷Session["TempUser"]是否有值，沒有則回到登入頁
    public bool CheckUserSession()
    {
        if (Session["tech_no"] == null)
        {
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('作業逾時或尚未登入,請登入後再繼續作業!');location.href='/Logout.aspx';", true);
            return false;
        }
        else
        {
            return true;
        }
    }
    public string FilterGdName(string orgString)
    {
        string newString = string.Empty;
        MatchCollection matches = Regex.Matches(orgString, @"[^\W']+", RegexOptions.IgnoreCase);
        foreach (Match match in matches)
        {
            newString += match.Value;
        }
        return newString;
    }
}
