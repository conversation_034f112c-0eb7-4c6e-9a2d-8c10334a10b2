﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices</name>
  </assembly>
  <members>
    <member name="T:System.DataMisalignedException">
      <summary>当在某个地址读取或写入一个单元的数据，但该地址的数据大小不是该数据单元的整数倍时引发的异常。此类不能被继承。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DataMisalignedException.#ctor">
      <summary>初始化 <see cref="T:System.DataMisalignedException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String)">
      <summary>使用指定的错误信息初始化 <see cref="T:System.DataMisalignedException" /> 类的新实例。</summary>
      <param name="message">描述错误的 <see cref="T:System.String" /> 对象。<paramref name="message" /> 的内容被设计为人可理解的形式。此构造函数的调用方需要确保此字符串已针对当前系统区域性进行了本地化。</param>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String,System.Exception)">
      <summary>用指定的错误信息和基础异常初始化 <see cref="T:System.DataMisalignedException" /> 类的新实例。</summary>
      <param name="message">描述错误的 <see cref="T:System.String" /> 对象。<paramref name="message" /> 的内容被设计为人可理解的形式。此构造函数的调用方需要确保此字符串已针对当前系统区域性进行了本地化。</param>
      <param name="innerException">导致当前 <see cref="T:System.DataMisalignedException" /> 的异常。如果 <paramref name="innerException" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="T:System.DllNotFoundException">
      <summary>当未找到在 DLL 导入中指定的 DLL 时所引发的异常。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DllNotFoundException.#ctor">
      <summary>使用默认属性初始化 <see cref="T:System.DllNotFoundException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String)">
      <summary>使用指定的错误消息初始化 <see cref="T:System.DllNotFoundException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.DllNotFoundException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="inner">导致当前异常的异常。如果 <paramref name="inner" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="T:System.Reflection.Missing">
      <summary>表示缺少的 <see cref="T:System.Object" />。此类不能被继承。</summary>
    </member>
    <member name="F:System.Reflection.Missing.Value">
      <summary>表示 <see cref="T:System.Reflection.Missing" /> 类的唯一实例。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ArrayWithOffset">
      <summary>在指定的数组中封装数组和偏移量。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.#ctor(System.Object,System.Int32)">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 结构的新实例。</summary>
      <param name="array">托管数组。</param>
      <param name="offset">要通过平台调用传递的元素的偏移量（以字节为单位）。</param>
      <exception cref="T:System.ArgumentException">数组大于 2 GB。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Object)">
      <summary>指示指定的对象是否与当前的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象匹配。</summary>
      <returns>如果对象与此 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 匹配，则为 true；否则，为 false。</returns>
      <param name="obj">要与该实例进行比较的对象。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>指示指定的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象是否与当前实例匹配。</summary>
      <returns>如果指定的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象与当前实例相匹配，则为 true；否则为 false。</returns>
      <param name="obj">与此实例进行比较的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetArray">
      <summary>返回此 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 引用的托管数组。</summary>
      <returns>此实例引用的托管数组。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetHashCode">
      <summary>返回此值类型的哈希代码。</summary>
      <returns>此实例的哈希代码。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetOffset">
      <summary>返回当构造此 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 时提供的偏移量。</summary>
      <returns>此实例的偏移量。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Equality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>确定两个指定的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象是否具有相同的值。</summary>
      <returns>如果 <paramref name="a" /> 的值与 <paramref name="b" /> 的值相同，则为 true；否则为 false。</returns>
      <param name="a">与 <paramref name="b" /> 参数进行比较的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。</param>
      <param name="b">与 <paramref name="a" /> 参数进行比较的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Inequality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>确定两个指定的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象是否具有不同值。</summary>
      <returns>如果 <paramref name="a" /> 的值与 <paramref name="b" /> 的值不同，则为 true；否则为 false。</returns>
      <param name="a">与 <paramref name="b" /> 参数进行比较的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。</param>
      <param name="b">与 <paramref name="a" /> 参数进行比较的 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.BestFitMappingAttribute">
      <summary>控制是否将 Unicode 字符转换为最接近的匹配 ANSI 字符。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BestFitMappingAttribute.#ctor(System.Boolean)">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.BestFitMappingAttribute" /> 类的新实例，并将其设置为 <see cref="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping" /> 属性的值。</summary>
      <param name="BestFitMapping">true 指示启用最佳映射；否则为 false。默认值为 true。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping">
      <summary>获取将 Unicode 字符转换为 ANSI 字符时的最佳映射行为。</summary>
      <returns>如果启用最佳映射则为 true；否则为 false。默认值为 true。</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.BestFitMappingAttribute.ThrowOnUnmappableChar">
      <summary>启用或禁用在遇到已被转换为 ANSI“?”字符的无法映射的 Unicode 字符时引发异常。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.BStrWrapper">
      <summary>将 VT_BSTR 类型的数据从托管代码封送到非托管代码。此类不能被继承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.Object)">
      <summary>用指定的 <see cref="T:System.Object" /> 对象初始化 <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> 类的新实例。</summary>
      <param name="value">要包装并作为 VT_BSTR 进行封送的对象。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.String)">
      <summary>用指定的 <see cref="T:System.String" /> 对象初始化 <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> 类的新实例。</summary>
      <param name="value">要包装并作为 VT_BSTR 进行封送的对象。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BStrWrapper.WrappedObject">
      <summary>获取将作为 VT_BSTR 类型进行封送的包装的 <see cref="T:System.String" /> 对象。</summary>
      <returns>由 <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> 包装的对象。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CallingConvention">
      <summary>指定调用在非托管代码中实现的方法所需的调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Cdecl">
      <summary>调用方清理堆栈。这使您能够调用具有 varargs 的函数（如 Printf），使之可用于接受可变数目的参数的方法。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.StdCall">
      <summary>被调用方清理堆栈。这是使用平台 invoke 调用非托管函数的默认约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.ThisCall">
      <summary>第一个参数是 this 指针，它存储在寄存器 ECX 中。其他参数被推送到堆栈上。此调用约定用于对从非托管 DLL 导出的类调用方法。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Winapi">
      <summary>此成员实际上不是调用约定，而是使用了默认平台调用约定。例如，在 Windows 上默认为 <see cref="F:System.Runtime.InteropServices.CallingConvention.StdCall" />，在 Windows CE.NET 上默认为 <see cref="F:System.Runtime.InteropServices.CallingConvention.Cdecl" />。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceAttribute">
      <summary>为公开给 COM 的类指定要生成的类接口的类型（如果有接口生成）。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Int16)">
      <summary>用指定的 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 枚举值初始化 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 类的新实例。</summary>
      <param name="classInterfaceType">描述为类生成的接口的类型。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Runtime.InteropServices.ClassInterfaceType)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 枚举成员初始化 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 类的新实例。</summary>
      <param name="classInterfaceType">
        <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 值之一，描述为类生成的接口的类型。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ClassInterfaceAttribute.Value">
      <summary>获取 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 值，该值描述应该为该类生成哪种类型的接口。</summary>
      <returns>描述应该为该类生成哪种类型的接口的 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 值。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceType">
      <summary>标识为某个类生成的类接口的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDispatch">
      <summary>指示该类只支持 COM 客户端的后期绑定。在请求时，该类的 dispinterface 将自动向 COM 客户端公开。Tlbexp.exe（类型库导出程序） 生成的类型库不包含 dispinterface 的类型信息，以防止客户端缓存接口的 DISPID。由于客户端只能后期绑定到接口，因此 dispinterface 不会出现 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 中所述的版本控制问题。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDual">
      <summary>指示自动为类生成双重类接口并向 COM 公开。为该类接口生成类型信息并在类型库中发布。由于 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 中描述的版本控制方面的限制，极力建议不要使用 AutoDual。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.None">
      <summary>指示不为类生成类接口。如果未显式实现任何接口，则该类将只能通过 IDispatch 接口提供后期绑定访问。这是 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 的推荐设置。要通过由类显式实现的接口来公开功能，唯一的方法是使用 ClassInterfaceType.None。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CoClassAttribute">
      <summary>指定从类型库中导入的 coclass 的类标识符。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CoClassAttribute.#ctor(System.Type)">
      <summary>用原始 coclass 的类标识符初始化 <see cref="T:System.Runtime.InteropServices.CoClassAttribute" /> 的新实例。</summary>
      <param name="coClass">一个 <see cref="T:System.Type" />，它包含原始 coclass 的类标识符。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.CoClassAttribute.CoClass">
      <summary>获取原始 coclass 的类标识符。</summary>
      <returns>一个 <see cref="T:System.Type" />，它包含原始 coclass 的类标识符。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComAwareEventInfo">
      <summary>允许对事件处理程序进行后期绑定注册。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.#ctor(System.Type,System.String)">
      <summary>使用指定类型和该类型的事件名称来初始化 <see cref="T:System.Runtime.InteropServices.ComAwareEventInfo" /> 类的新实例。</summary>
      <param name="type">对象的类型。</param>
      <param name="eventName">有关 <paramref name="type" /> 的事件的名称。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>将事件处理程序附加到 COM 对象。</summary>
      <param name="target">事件委托应绑定到的目标对象。</param>
      <param name="handler">事件委托。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Attributes">
      <summary>获取此事件的属性。</summary>
      <returns>此事件的只读特性。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.DeclaringType">
      <summary>获取声明该成员的类。</summary>
      <returns>声明该成员的类的 <see cref="T:System.Type" /> 对象。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Name">
      <summary>获取当前成员的名称。</summary>
      <returns>此成员的名称。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>从 COM 对象分离事件处理程序。</summary>
      <param name="target">事件委托所绑定到的目标对象。</param>
      <param name="handler">事件委托。</param>
      <exception cref="T:System.InvalidOperationException">该事件没有公共的 remove 访问器。</exception>
      <exception cref="T:System.ArgumentException">传入的处理程序无法使用。</exception>
      <exception cref="T:System.Reflection.TargetException">在 .NET for Windows Store 应用程序 或 可移植类库 中，请改为捕获 <see cref="T:System.Exception" />。<paramref name="target" /> 参数为 null 并且该事件不是静态的。- 或 -目标上没有声明 <see cref="T:System.Reflection.EventInfo" />。</exception>
      <exception cref="T:System.MethodAccessException">在 .NET for Windows Store 应用程序 或 可移植类库 中，请改为捕获基类异常 <see cref="T:System.MemberAccessException" />。调用方无权访问该成员。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute">
      <summary>指定要向 COM 公开的默认接口。此类不能被继承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>以指定的 <see cref="T:System.Type" /> 对象作为向 COM 公开的默认接口初始化 <see cref="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute" /> 类的新实例。</summary>
      <param name="defaultInterface">一个 <see cref="T:System.Type" /> 值，指示要向 COM 公开的默认接口。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.Value">
      <summary>获取 <see cref="T:System.Type" /> 对象，该对象指定要向 COM 公开的默认接口。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，该对象指定要向 COM 公开的默认接口。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventInterfaceAttribute">
      <summary>标识源接口和实现事件接口（从 COM 类型库导入 coclass 时生成）的方法的类。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventInterfaceAttribute.#ctor(System.Type,System.Type)">
      <summary>用源接口和事件提供程序类初始化 <see cref="T:System.Runtime.InteropServices.ComEventInterfaceAttribute" /> 类的新实例。</summary>
      <param name="SourceInterface">一个 <see cref="T:System.Type" />，它包含类型库中的原始源接口。COM 使用此接口回调到托管类。</param>
      <param name="EventProvider">一个 <see cref="T:System.Type" />，它包含实现事件接口的方法的类。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.EventProvider">
      <summary>获取实现事件接口的方法的类。</summary>
      <returns>一个 <see cref="T:System.Type" />，它包含实现事件接口的方法的类。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.SourceInterface">
      <summary>从类型库获取原始源接口。</summary>
      <returns>一个包含源接口的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventsHelper">
      <summary>提供允许将处理事件的 .NET Framework 委托添加到 COM 对象和从 COM 对象中删除这些委托的方法。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Combine(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>将委托添加到源自 COM 对象的事件的调用列表。</summary>
      <param name="rcw">触发事件的 COM 对象，调用方希望响应这些事件。</param>
      <param name="iid">COM 对象用来触发事件的源接口的标识符。</param>
      <param name="dispid">源接口上的方法的调度标识符。</param>
      <param name="d">要在激发 COM 事件时调用的委托。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Remove(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>从源自 COM 对象的事件的调用列表中移除委托。</summary>
      <returns>已从调用列表中移除的委托。</returns>
      <param name="rcw">委托附加到的 COM 对象。</param>
      <param name="iid">COM 对象用来触发事件的源接口的标识符。</param>
      <param name="dispid">源接口上的方法的调度标识符。</param>
      <param name="d">要从调用列表中移除的委托。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.COMException">
      <summary>当从 COM 方法调用返回无法识别的 HRESULT 时引发的异常。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor">
      <summary>使用默认值初始化 <see cref="T:System.Runtime.InteropServices.COMException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String)">
      <summary>用指定的消息初始化 <see cref="T:System.Runtime.InteropServices.COMException" /> 类的新实例。</summary>
      <param name="message">指示异常原因的消息。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Exception)">
      <summary>使用指定的错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Runtime.InteropServices.COMException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="inner">导致当前异常的异常。如果 <paramref name="inner" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Int32)">
      <summary>使用指定的消息和错误代码初始化 <see cref="T:System.Runtime.InteropServices.COMException" /> 类的新实例。</summary>
      <param name="message">指示所发生异常的原因的消息。</param>
      <param name="errorCode">与该异常关联的错误代码 (HRESULT) 值。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComImportAttribute">
      <summary>指示该属性化类型是以前在 COM 中定义的。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComImportAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.ComImportAttribute" /> 的新实例。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComInterfaceType">
      <summary>标识如何向 COM 公开接口。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual">
      <summary>指示接口将作为双重接口向 COM 公开，这将支持早期绑定和后期绑定。<see cref="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual" /> 为默认值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIDispatch">
      <summary>指示接口将作为dispinterface向 COM 公开，这将仅启用后期绑定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIInspectable">
      <summary>指示作为 Windows 运行时 接口向 COM 公开的接口。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIUnknown">
      <summary>指示接口将作为 IUnknown 派生的接口向 COM 公开，这将仅启用早期绑定。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComMemberType">
      <summary>描述 COM 成员的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.Method">
      <summary>该成员是一个一般方法。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropGet">
      <summary>该成员获取属性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropSet">
      <summary>该成员设置属性。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute">
      <summary>为属性化类标识公开为 COM 事件源的一组接口。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.String)">
      <summary>使用事件源接口的名称初始化 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 类的新实例。</summary>
      <param name="sourceInterfaces">事件源接口的完全限定名列表，名称之间用 null 分隔。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type)">
      <summary>使用要用作源接口的类型初始化 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 类的新实例。</summary>
      <param name="sourceInterface">源接口的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type)">
      <summary>在以要使用的类型作为源接口的情况下初始化 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 类的新实例。</summary>
      <param name="sourceInterface1">默认源接口的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface2">源接口的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type)">
      <summary>在以要使用的类型作为源接口的情况下初始化 ComSourceInterfacesAttribute 类的新实例。</summary>
      <param name="sourceInterface1">默认源接口的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface2">源接口的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface3">源接口的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type,System.Type)">
      <summary>在以要使用的类型作为源接口的情况下初始化 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 类的新实例。</summary>
      <param name="sourceInterface1">默认源接口的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface2">源接口的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface3">源接口的 <see cref="T:System.Type" />。</param>
      <param name="sourceInterface4">源接口的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComSourceInterfacesAttribute.Value">
      <summary>获取事件源接口的完全限定名。</summary>
      <returns>事件源接口的完全限定名。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CurrencyWrapper">
      <summary>对封送拆收器应该将其作为 VT_CY 封送的对象进行包装。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Decimal)">
      <summary>用要包装并作为 VT_CY 类型进行封送的 Decimal 来初始化 <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> 类的新实例。</summary>
      <param name="obj">要包装并作为 VT_CY 进行封送的 Decimal。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Object)">
      <summary>用包含要包装并作为 VT_CY 类型进行封送的 Decimal 的对象来初始化 <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> 类的新实例。</summary>
      <param name="obj">包含要包装并作为 VT_CY 进行封送的 Decimal 的对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> 参数不是 <see cref="T:System.Decimal" /> 类型。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.CurrencyWrapper.WrappedObject">
      <summary>获取将作为 VT_CY 类型进行封送的包装对象。</summary>
      <returns>将作为 VT_CY 类型进行封送的包装对象。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceMode">
      <summary>指示 <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> 方法的 IUnknown::QueryInterface 调用是否可以使用 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 接口。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Allow">
      <summary>IUnknown::QueryInterface 方法调用可以使用 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 接口。使用此值时，<see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> 方法重载的功能与 <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)" /> 重载的功能类似。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Ignore">
      <summary>IUnknown::QueryInterface 方法调用应忽略 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 接口。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceResult">
      <summary>提供 <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> 方法的返回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Failed">
      <summary>特定接口 ID 的接口不可用。在这种情况下，返回的接口为 null。E_NOINTERFACE 将返回给 IUnknown::QueryInterface 的调用方。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Handled">
      <summary>从 <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> 方法返回的接口指针可用作 IUnknown::QueryInterface 的结果。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.NotHandled">
      <summary>未使用自定义的 QueryInterface。相反，应该使用 IUnknown::QueryInterface 的默认实现。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultCharSetAttribute">
      <summary>指定 <see cref="T:System.Runtime.InteropServices.CharSet" /> 枚举的值。此类不能被继承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultCharSetAttribute.#ctor(System.Runtime.InteropServices.CharSet)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.CharSet" /> 值初始化 <see cref="T:System.Runtime.InteropServices.DefaultCharSetAttribute" /> 类的新实例。</summary>
      <param name="charSet">
        <see cref="T:System.Runtime.InteropServices.CharSet" /> 值之一。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultCharSetAttribute.CharSet">
      <summary>获取对 <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> 的任何调用的 <see cref="T:System.Runtime.InteropServices.CharSet" /> 的默认值。</summary>
      <returns>对 <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> 的任何调用的 <see cref="T:System.Runtime.InteropServices.CharSet" /> 的默认值。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute">
      <summary>指定用于搜索提供平台调用功能的 DLL 的路径。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.#ctor(System.Runtime.InteropServices.DllImportSearchPath)">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> 类的新实例，该实例指定用于当搜索目标平台调用时的路径。</summary>
      <param name="paths">指定平台调用期间 LoadLibraryEx 函数搜索路径的枚举值的按位组合。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.Paths">
      <summary>获取指定路径的枚举值的按位组合，路径在平台调用期间由 LoadLibraryEx 函数搜索。</summary>
      <returns>指定平台调用搜索路径的枚举值的按位组合。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultParameterValueAttribute">
      <summary>当从支持默认参数的语言执行调用时，设置参数的默认值。此类不能被继承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultParameterValueAttribute.#ctor(System.Object)">
      <summary>用参数的默认值初始化 <see cref="T:System.Runtime.InteropServices.DefaultParameterValueAttribute" /> 类的新实例。</summary>
      <param name="value">表示参数默认值的对象。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultParameterValueAttribute.Value">
      <summary>获取参数的默认值。</summary>
      <returns>表示参数默认值的对象。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispatchWrapper">
      <summary>对封送拆收器应该将其作为 VT_DISPATCH 封送的对象进行包装。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispatchWrapper.#ctor(System.Object)">
      <summary>使用正在包装的对象初始化 <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> 类的新实例。</summary>
      <param name="obj">要包装并转换成 <see cref="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH" /> 的对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> 不是类或数组。- 或 -<paramref name="obj" /> 不支持 IDispatch。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="obj" /> 参数是用被传递了一个 false 值的 <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> 特性标记的。- 或 -<paramref name="obj" /> 参数继承自一个类型，该类型是用一个被传递了 false 值的 <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> 特性标记的。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.DispatchWrapper.WrappedObject">
      <summary>获取由 <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> 包装的对象。</summary>
      <returns>由 <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> 包装的对象。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispIdAttribute">
      <summary>指定方法、字段或属性的 COM 调度标识符 (DISPID)。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispIdAttribute.#ctor(System.Int32)">
      <summary>用指定的 DISPID 初始化 DispIdAttribute 类的新实例。</summary>
      <param name="dispId">成员的 DISPID。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DispIdAttribute.Value">
      <summary>获取成员的 DISPID。</summary>
      <returns>成员的 DISPID。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportAttribute">
      <summary>指示该属性化方法由非托管动态链接库 (DLL) 作为静态入口点公开。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DllImportAttribute.#ctor(System.String)">
      <summary>使用包含要导入的方法的 DLL 的名称初始化 <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> 类的新实例。</summary>
      <param name="dllName">包含非托管方法的 DLL 的名称。如果 DLL 包含在某个程序集中，则可以包含程序集显示名称。</param>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.BestFitMapping">
      <summary>将 Unicode 字符转换为 ANSI 字符时，启用或禁用最佳映射行为。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CallingConvention">
      <summary>指示入口点的调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CharSet">
      <summary>指示如何向方法封送字符串参数，并控制名称重整。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.EntryPoint">
      <summary>指示要调用的 DLL 入口点的名称或序号。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ExactSpelling">
      <summary>控制 <see cref="F:System.Runtime.InteropServices.DllImportAttribute.CharSet" /> 字段是否使公共语言运行时在非托管 DLL 中搜索入口点名称，而不使用指定的入口点名称。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.PreserveSig">
      <summary>指示是否直接转换具有 HRESULT 或 retval 返回值的非托管方法，或是否自动将 HRESULT 或 retval 返回值转换为异常。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError">
      <summary>指示被调用方在从属性化方法返回之前是否调用 SetLastError Win32 API 函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ThrowOnUnmappableChar">
      <summary>启用或禁用在遇到已被转换为 ANSI“?”字符的无法映射的 Unicode 字符时引发异常。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.DllImportAttribute.Value">
      <summary>获取包含入口点的 DLL 文件的名称。</summary>
      <returns>包含入口点的 DLL 文件的名称。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportSearchPath">
      <summary>指定用于搜索提供平台调用功能的 DLL 的路径。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.ApplicationDirectory">
      <summary>在 DLL 的搜索路径中包含了应用程序目录。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.AssemblyDirectory">
      <summary>当搜索程序集依赖项时，请包括包含程序集本身的目录并首先查找该目录。在路径传递到 Win32 LoadLibraryEx 函数之前，通过 .NET Framework 使用此值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.LegacyBehavior">
      <summary>搜索应用程序目录，然后调用有 LOAD_WITH_ALTERED_SEARCH_PATH 标志的 Win32 LoadLibraryEx 函数。如果指定任何其他值，则忽略该值。不支持 <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> 特性使用此值并忽略其他值的操作系统。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.SafeDirectories">
      <summary>在 DLL 的搜索路径中包含应用程序目录，%WinDir%\System32 目录和用户目录。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.System32">
      <summary>在 DLL 的搜索路径中包含了 %WinDir%\System32 目录。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UseDllDirectoryForDependencies">
      <summary>搜索在搜索其他文件夹之前 DLL 所在文件夹中的依赖项。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UserDirectories">
      <summary>通过使用 Win32 AddDllDirectory 函数，包含了显式添加了过程搜索路径的所有路径。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ErrorWrapper">
      <summary>对封送拆收器应该将其作为 VT_ERROR 封送的对象进行包装。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Exception)">
      <summary>使用与所提供的异常相对应的 HRESULT 初始化 <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> 类的新实例。</summary>
      <param name="e">要转换为错误代码的异常。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Int32)">
      <summary>使用错误的 HRESULT 初始化 <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> 类的新实例。</summary>
      <param name="errorCode">错误的 HRESULT。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Object)">
      <summary>用包含错误的 HRESULT 的对象初始化 <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> 类的新实例。</summary>
      <param name="errorCode">包含错误的 HRESULT 的对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="errorCode" /> 参数不是 <see cref="T:System.Int32" /> 类型。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.ErrorWrapper.ErrorCode">
      <summary>获取包装的错误代码。</summary>
      <returns>错误的 HRESULT。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandle">
      <summary>提供用于从非托管内存访问托管对象的方法。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.AddrOfPinnedObject">
      <summary>在 <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" /> 句柄中检索对象的地址。</summary>
      <returns>
        <see cref="T:System.IntPtr" /> 形式的固定对象的地址。</returns>
      <exception cref="T:System.InvalidOperationException">The handle is any type other than <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object)">
      <summary>为指定的对象分配 <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" /> 句柄。</summary>
      <returns>一个新的 <see cref="T:System.Runtime.InteropServices.GCHandle" />，它保护对象不被垃圾回收。当不再需要 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 时，必须通过 <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> 将其释放。</returns>
      <param name="value">使用 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 的对象。</param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object,System.Runtime.InteropServices.GCHandleType)">
      <summary>为指定的对象分配指定类型的句柄。</summary>
      <returns>指定的类型的新 <see cref="T:System.Runtime.InteropServices.GCHandle" />。当不再需要 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 时，必须通过 <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> 将其释放。</returns>
      <param name="value">使用 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 的对象。</param>
      <param name="type">
        <see cref="T:System.Runtime.InteropServices.GCHandleType" /> 值之一，指示要创建的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 的类型。</param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Equals(System.Object)">
      <summary>确定指定的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象是否等于当前的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。</summary>
      <returns>如果指定的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象等于当前的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象，则为 true；否则为 false。</returns>
      <param name="o">将与当前 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象进行比较的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Free">
      <summary>释放 <see cref="T:System.Runtime.InteropServices.GCHandle" />。</summary>
      <exception cref="T:System.InvalidOperationException">The handle was freed or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.FromIntPtr(System.IntPtr)">
      <summary>返回从某个托管对象的句柄创建的新 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。</summary>
      <returns>对应于值参数的新的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。</returns>
      <param name="value">某个托管对象的 <see cref="T:System.IntPtr" /> 句柄，将从该句柄创建 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。</param>
      <exception cref="T:System.InvalidOperationException">The value of the <paramref name="value" /> parameter is <see cref="F:System.IntPtr.Zero" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.GetHashCode">
      <summary>返回当前 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象的一个标识符。</summary>
      <returns>当前 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象的一个标识符。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.IsAllocated">
      <summary>获取一个值，该值指示是否分配了句柄。</summary>
      <returns>如果分配了句柄，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Equality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>返回一个值，该值指示两个 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象是否相等。</summary>
      <returns>如果 <paramref name="a" /> 和 <paramref name="b" /> 参数相等，则为 true；否则为 false。</returns>
      <param name="a">要与 <paramref name="b" /> 参数进行比较的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。</param>
      <param name="b">要与 <paramref name="a" /> 参数进行比较的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。  </param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.IntPtr)~System.Runtime.InteropServices.GCHandle">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> 以内部整数表示形式存储。</summary>
      <returns>使用内部整数表示形式的已存储 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。</returns>
      <param name="value">一个 <see cref="T:System.IntPtr" />，它指示需要该转换的句柄。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.Runtime.InteropServices.GCHandle)~System.IntPtr">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> 以内部整数表示形式存储。</summary>
      <returns>整数值。</returns>
      <param name="value">需要该整数的 <see cref="T:System.Runtime.InteropServices.GCHandle" />。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Inequality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>返回一个值，该值指示两个 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象是否不相等。</summary>
      <returns>如果 <paramref name="a" /> 和 <paramref name="b" /> 参数不相等，则为 true；否则为 false。</returns>
      <param name="a">要与 <paramref name="b" /> 参数进行比较的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。</param>
      <param name="b">要与 <paramref name="a" /> 参数进行比较的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。  </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.Target">
      <summary>获取或设置该句柄表示的对象。</summary>
      <returns>该句柄表示的对象。</returns>
      <exception cref="T:System.InvalidOperationException">The handle was freed, or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.ToIntPtr(System.Runtime.InteropServices.GCHandle)">
      <summary>返回 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象的内部整数表示形式。</summary>
      <returns>表示 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象的 <see cref="T:System.IntPtr" /> 对象。 </returns>
      <param name="value">要从其中检索内部整数表示形式的 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 对象。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandleType">
      <summary>表示 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 类可以分配的句柄的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Normal">
      <summary>此句柄类型表示不透明句柄，这意味着无法通过此句柄解析固定对象的地址。可以使用此类型跟踪对象，并防止它被垃圾回收器回收。当非托管客户端持有对托管对象的唯一引用（从垃圾回收器检测不到该引用）时，此枚举成员很有用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Pinned">
      <summary>此句柄类型类似于 <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" />，但允许使用固定对象的地址。这将防止垃圾回收器移动对象，因此将降低垃圾回收器的效率。使用 <see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> 方法可尽快释放已分配的句柄。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Weak">
      <summary>此句柄类型用于跟踪对象，但允许回收该对象。当回收某个对象时，<see cref="T:System.Runtime.InteropServices.GCHandle" /> 的内容归零。在终结器运行之前，Weak 引用归零，因此即使终结器使该对象复活，Weak 引用仍然是归零的。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.WeakTrackResurrection">
      <summary>该句柄类型类似于 <see cref="F:System.Runtime.InteropServices.GCHandleType.Weak" />，但如果对象在终结过程中复活，此句柄不归零。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.GuidAttribute">
      <summary>当不需要自动 GUID 时提供显式的 <see cref="T:System.Guid" />。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GuidAttribute.#ctor(System.String)">
      <summary>用指定的 GUID 初始化 <see cref="T:System.Runtime.InteropServices.GuidAttribute" /> 类的新实例。</summary>
      <param name="guid">要分配的 <see cref="T:System.Guid" />。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.GuidAttribute.Value">
      <summary>获取类的 <see cref="T:System.Guid" />。</summary>
      <returns>类的 <see cref="T:System.Guid" />。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.HandleCollector">
      <summary>跟踪未处理的句柄，并在达到指定阈值时强制执行垃圾回收。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32)">
      <summary>使用一个名称以及一个阈值（在达到该值时开始执行句柄回收）初始化 <see cref="T:System.Runtime.InteropServices.HandleCollector" /> 类的新实例。</summary>
      <param name="name">回收器的名称。此参数允许您为跟踪句柄类型的回收器分别命名。</param>
      <param name="initialThreshold">指定何时开始执行回收的值。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialThreshold" /> 参数小于 0。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32,System.Int32)">
      <summary>使用一个名称、一个指定何时开始执行句柄回收的阈值，以及一个指定必须进行句柄回收的阈值初始化 <see cref="T:System.Runtime.InteropServices.HandleCollector" /> 类的新实例。</summary>
      <param name="name">回收器的名称。此参数允许您为跟踪句柄类型的回收器分别命名。</param>
      <param name="initialThreshold">指定何时开始执行回收的值。</param>
      <param name="maximumThreshold">指定必须开始进行回收的值。此值应设置为可用句柄的最大数量。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialThreshold" /> 参数小于 0。- 或 -<paramref name="maximumThreshold" /> 参数小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="maximumThreshold" /> 参数小于 <paramref name="initialThreshold" /> 参数。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Add">
      <summary>增加当前句柄计数。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> 属性小于 0。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Count">
      <summary>获取回收的句柄的数量。</summary>
      <returns>回收的句柄的数量。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.InitialThreshold">
      <summary>获取一个值，该值指定了何时开始执行回收。</summary>
      <returns>指定何时开始执行回收的值。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.MaximumThreshold">
      <summary>获取指定必须开始进行回收的值。</summary>
      <returns>指定必须开始进行回收的值。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Name">
      <summary>获取 <see cref="T:System.Runtime.InteropServices.HandleCollector" /> 对象的名称。</summary>
      <returns>此 <see cref="P:System.Runtime.InteropServices.HandleCollector.Name" /> 属性允许您为跟踪句柄类型的回收器分别命名。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Remove">
      <summary>减少当前句柄计数。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> 属性小于 0。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomAdapter">
      <summary>为客户端访问实际对象（而不是自定义封送拆收器分发的适配器对象）提供了一种方式。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomAdapter.GetUnderlyingObject">
      <summary>提供对自定义封送拆收器包装的基础对象的访问权限。</summary>
      <returns>适配器对象包含的对象。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomQueryInterface">
      <summary>允许开发人员提供 IUnknown::QueryInterface(REFIID riid, void **ppvObject) 方法的自定义托管实现。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)">
      <summary>根据指定的接口 ID 返回接口。</summary>
      <returns>枚举值之一，指示是否使用了 IUnknown::QueryInterface 的自定义实现。</returns>
      <param name="iid">请求的接口的 GUID。</param>
      <param name="ppv">此方法返回时，对所请求的接口的引用。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InAttribute">
      <summary>指示应将数据从调用方封送到被调用方，而不返回到调用方。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.InAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.InterfaceTypeAttribute">
      <summary>指示向 COM 公开时，托管接口是双重的、仅支持调度的、还是仅支持 IUnknown 的。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Int16)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 枚举成员初始化 <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> 类的新实例。</summary>
      <param name="interfaceType">描述应如何向 COM 客户端公开接口。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Runtime.InteropServices.ComInterfaceType)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 枚举成员初始化 <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> 类的新实例。</summary>
      <param name="interfaceType">
        <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 值之一，指定如何向 COM 客户端公开接口。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.InterfaceTypeAttribute.Value">
      <summary>获取 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 值，该值描述应如何向 COM 公开接口。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 值，该值描述应如何向 COM 公开接口。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidComObjectException">
      <summary>当使用无效的 COM 对象时引发的异常。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor">
      <summary>用默认属性初始化 InvalidComObjectException 的实例。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String)">
      <summary>用消息初始化 InvalidComObjectException 的实例。</summary>
      <param name="message">指示异常原因的消息。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Runtime.InteropServices.InvalidComObjectException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="inner">导致当前异常的异常。如果 <paramref name="inner" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
      <summary>封送拆收器在遇到不能封送到托管代码的 Variant 类型的参数时引发的异常。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor">
      <summary>使用默认值初始化 InvalidOleVariantTypeException 类的新实例。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String)">
      <summary>用指定的消息初始化 InvalidOleVariantTypeException 类的新实例。</summary>
      <param name="message">指示异常原因的消息。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="inner">导致当前异常的异常。如果 <paramref name="inner" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.Marshal">
      <summary>提供了一个方法集合，这些方法用于分配非托管内存、复制非托管内存块、将托管类型转换为非托管类型，此外还提供了在与非托管代码交互时使用的其他杂项方法。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AddRef(System.IntPtr)">
      <summary>递增指定接口上的引用计数。</summary>
      <returns>
        <paramref name="pUnk" /> 参数上的引用计数的新值。</returns>
      <param name="pUnk">要递增的接口引用计数。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)">
      <summary>从 COM 任务内存分配器分配指定大小的内存块。</summary>
      <returns>一个整数，表示分配的内存块的地址。该内存必须用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" /> 来释放。</returns>
      <param name="cb">要分配的内存块的大小。</param>
      <exception cref="T:System.OutOfMemoryException">内存不足，无法满足请求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.Int32)">
      <summary>通过使用指定的字节数，从进程的非托管内存中分配内存。</summary>
      <returns>指向新分配的内存的指针。必须使用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> 方法释放此内存。</returns>
      <param name="cb">内存中的所需字节数。</param>
      <exception cref="T:System.OutOfMemoryException">内存不足，无法满足请求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)">
      <summary>通过使用指向指定字节数的指针，从进程的非托管内存中分配内存。</summary>
      <returns>指向新分配的内存的指针。必须使用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> 方法释放此内存。</returns>
      <param name="cb">内存中的所需字节数。</param>
      <exception cref="T:System.OutOfMemoryException">内存不足，无法满足请求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AreComObjectsAvailableForCleanup">
      <summary>指示是否可以清除任何上下文中的运行时可调用包装器 (RCW)。</summary>
      <returns>如果存在任何可清除的 RCW，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Byte[],System.Int32,System.IntPtr,System.Int32)">
      <summary>将数据从一维托管 8 位无符号整数数组复制到非托管内存指针。</summary>
      <param name="source">从中进行复制的一维数组。</param>
      <param name="startIndex">源数组中从零开始的索引，在此处开始复制。</param>
      <param name="destination">要复制到的内存指针。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 无效。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Char[],System.Int32,System.IntPtr,System.Int32)">
      <summary>将数据从一维托管字符数组复制到非托管内存指针。</summary>
      <param name="source">从中进行复制的一维数组。</param>
      <param name="startIndex">源数组中从零开始的索引，在此处开始复制。</param>
      <param name="destination">要复制到的内存指针。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 无效。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Double[],System.Int32,System.IntPtr,System.Int32)">
      <summary>将数据从一维托管双精度浮点数数组复制到非托管内存指针。</summary>
      <param name="source">从中进行复制的一维数组。</param>
      <param name="startIndex">源数组中从零开始的索引，在此处开始复制。</param>
      <param name="destination">要复制到的内存指针。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 无效。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int16[],System.Int32,System.IntPtr,System.Int32)">
      <summary>将数据从一维托管 16 位带符号整数数组复制到非托管内存指针。</summary>
      <param name="source">从中进行复制的一维数组。</param>
      <param name="startIndex">源数组中从零开始的索引，在此处开始复制。</param>
      <param name="destination">要复制到的内存指针。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 无效。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int32[],System.Int32,System.IntPtr,System.Int32)">
      <summary>将数据从一维托管 32 位带符号整数数组复制到非托管内存指针。</summary>
      <param name="source">从中进行复制的一维数组。</param>
      <param name="startIndex">源数组中从零开始的索引，在此处开始复制。</param>
      <param name="destination">要复制到的内存指针。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 无效。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int64[],System.Int32,System.IntPtr,System.Int32)">
      <summary>将数据从一维托管 64 位带符号整数数组复制到非托管内存指针。</summary>
      <param name="source">从中进行复制的一维数组。</param>
      <param name="startIndex">源数组中从零开始的索引，在此处开始复制。</param>
      <param name="destination">要复制到的内存指针。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 无效。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Byte[],System.Int32,System.Int32)">
      <summary>将数据从非托管内存指针复制到托管 8 位无符号整数数组。</summary>
      <param name="source">从中进行复制的内存指针。</param>
      <param name="destination">要复制到的数组。</param>
      <param name="startIndex">目标数组中从零开始的索引，在此处开始复制。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Char[],System.Int32,System.Int32)">
      <summary>将数据从非托管内存指针复制到托管字符数组。</summary>
      <param name="source">从中进行复制的内存指针。</param>
      <param name="destination">要复制到的数组。</param>
      <param name="startIndex">目标数组中从零开始的索引，在此处开始复制。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Double[],System.Int32,System.Int32)">
      <summary>将数据从非托管内存指针复制到托管双精度浮点数数组。</summary>
      <param name="source">从中进行复制的内存指针。</param>
      <param name="destination">要复制到的数组。</param>
      <param name="startIndex">目标数组中从零开始的索引，在此处开始复制。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int16[],System.Int32,System.Int32)">
      <summary>将数据从非托管内存指针复制到托管 16 位带符号整数数组。</summary>
      <param name="source">从中进行复制的内存指针。</param>
      <param name="destination">要复制到的数组。</param>
      <param name="startIndex">目标数组中从零开始的索引，在此处开始复制。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int32[],System.Int32,System.Int32)">
      <summary>将数据从非托管内存指针复制到托管 32 位带符号整数数组。</summary>
      <param name="source">从中进行复制的内存指针。</param>
      <param name="destination">要复制到的数组。</param>
      <param name="startIndex">目标数组中从零开始的索引，在此处开始复制。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int64[],System.Int32,System.Int32)">
      <summary>将数据从非托管内存指针复制到托管 64 位带符号整数数组。</summary>
      <param name="source">从中进行复制的内存指针。</param>
      <param name="destination">要复制到的数组。</param>
      <param name="startIndex">目标数组中从零开始的索引，在此处开始复制。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
      <summary>将数据从非托管内存指针复制到托管 <see cref="T:System.IntPtr" /> 数组。</summary>
      <param name="source">从中进行复制的内存指针。</param>
      <param name="destination">要复制到的数组。</param>
      <param name="startIndex">目标数组中从零开始的索引，在此处开始复制。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Single[],System.Int32,System.Int32)">
      <summary>将数据从非托管内存指针复制到托管单精度浮点数数组。</summary>
      <param name="source">从中进行复制的内存指针。</param>
      <param name="destination">要复制到的数组。</param>
      <param name="startIndex">目标数组中从零开始的索引，在此处开始复制。</param>
      <param name="length">要复制的数组元素的数目。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr[],System.Int32,System.IntPtr,System.Int32)">
      <summary>将数据从一维托管 <see cref="T:System.IntPtr" /> 数组复制到非托管内存指针。</summary>
      <param name="source">从中进行复制的一维数组。</param>
      <param name="startIndex">源数组中从零开始的索引，在此处开始复制。</param>
      <param name="destination">要复制到的内存指针。</param>
      <param name="length">要复制的数组元素的数目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="destination" />、<paramref name="startIndex" /> 或 <paramref name="length" /> 为 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Single[],System.Int32,System.IntPtr,System.Int32)">
      <summary>将数据从一维托管单精度浮点数数组复制到非托管内存指针。</summary>
      <param name="source">从中进行复制的一维数组。</param>
      <param name="startIndex">源数组中从零开始的索引，在此处开始复制。</param>
      <param name="destination">要复制到的内存指针。</param>
      <param name="length">要复制的数组元素的数目。 </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 无效。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="startIndex" />、<paramref name="destination" /> 或 <paramref name="length" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject(System.IntPtr,System.Object)">
      <summary>聚合托管对象和指定的 COM 对象。</summary>
      <returns>托管对象的内部 IUnknown 指针。</returns>
      <param name="pOuter">外部 IUnknown 指针。</param>
      <param name="o">要聚合的对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> 是 Windows 运行时 对象。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject``1(System.IntPtr,``0)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]聚合指定类型的托管对象和指定的 COM 对象。</summary>
      <returns>托管对象的内部 IUnknown 指针。 </returns>
      <param name="pOuter">外部 IUnknown 指针。</param>
      <param name="o">要集合的托管对象。</param>
      <typeparam name="T">要聚合的托管对象的类型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> 是 Windows 运行时 对象。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType(System.Object,System.Type)">
      <summary>在指定类型的对象中包装指定的 COM 对象。</summary>
      <returns>新包装的对象，该对象是所需类型的实例。</returns>
      <param name="o">要包装的对象。</param>
      <param name="t">要创建的包装器的类型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 必须从 __ComObject 派生。- 或 -<paramref name="t" /> 是一种 Windows 运行时 类型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> 参数为 null。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> 无法转换为目标类型，因为它不支持所有所需的接口。 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType``2(``0)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]在指定类型的对象中包装指定的 COM 对象。</summary>
      <returns>新包装的对象。 </returns>
      <param name="o">要包装的对象。</param>
      <typeparam name="T">要包装的对象的类型。</typeparam>
      <typeparam name="TWrapper">要返回的对象的类型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 必须从 __ComObject 派生。- 或 -<paramref name="T" /> 是一种 Windows 运行时 类型。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> 无法转换为 <paramref name="TWrapper" />，因为它不支持所有需要的接口。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]释放指定的非托管内存块所指向的所有指定类型的子结构。</summary>
      <param name="ptr">指向非托管内存块的指针。 </param>
      <typeparam name="T">格式化结构的类型。该类型提供删除 <paramref name="ptr" /> 参数指向的缓冲区时必需的布局信息。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 具有自动布局。但请使用连续或显式布局。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)">
      <summary>释放指定的非托管内存块所指向的所有子结构。</summary>
      <param name="ptr">指向非托管内存块的指针。</param>
      <param name="structuretype">格式化类的类型。该类型提供删除 <paramref name="ptr" /> 参数指向的缓冲区时必需的布局信息。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> 具有自动布局。但请使用连续或显式布局。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FinalReleaseComObject(System.Object)">
      <summary>通过将 运行时可调用包装 (RCW) 的引用计数设置为 0，释放对它的所有引用。</summary>
      <returns>与 <paramref name="o" /> 参数关联的 RCW 的引用计数的新值，如果释放成功，则为 0（零）。</returns>
      <param name="o">要释放的 RCW。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> 不是一个有效的 COM 对象。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)">
      <summary>使用 COM SysFreeString 函数释放 BSTR。</summary>
      <param name="ptr">要释放的 BSTR 的地址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)">
      <summary>释放由非托管 COM 任务内存分配器分配的内存块。</summary>
      <param name="ptr">要释放的内存的地址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)">
      <summary>释放以前从进程的非托管内存中分配的内存。</summary>
      <param name="hglobal">由对 <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> 的原始匹配调用返回的句柄。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)">
      <summary>返回一个指向 IUnknown 接口的指针，该指针表示指定对象上的指定接口。默认情况下，启用自定义查询接口访问。</summary>
      <returns>表示对象的指定接口的接口指针。</returns>
      <param name="o">提供接口的对象。</param>
      <param name="T">所请求接口的类型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 参数不是接口。- 或 -该类型对 COM 不可见。- 或 -<paramref name="T" /> 参数是泛型类型。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> 参数不支持请求的接口。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 参数为 null。- 或 -<paramref name="T" /> 参数为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)">
      <summary>返回一个指向 IUnknown 接口的指针，该指针表示指定对象上的指定接口。自定义查询接口访问由指定的自定义模式控制。</summary>
      <returns>表示对象的接口的接口指针。</returns>
      <param name="o">提供接口的对象。</param>
      <param name="T">所请求接口的类型。</param>
      <param name="mode">指示是否要应用 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 提供的 IUnknown::QueryInterface 自定义的枚举值之一。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 参数不是接口。- 或 -该类型对 COM 不可见。- 或 -<paramref name="T" /> 参数是泛型类型。</exception>
      <exception cref="T:System.InvalidCastException">对象 <paramref name="o" /> 不支持请求的接口。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 参数为 null。- 或 -<paramref name="T" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject``2(``0)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]返回指向 IUnknown 接口的指针，该指针表示指定类型的对象上的指定接口。默认情况下，启用自定义查询接口访问。</summary>
      <returns>表示 <paramref name="TInterface" /> 接口的接口指针。</returns>
      <param name="o">提供接口的对象。</param>
      <typeparam name="T">
        <paramref name="o" /> 的类型。</typeparam>
      <typeparam name="TInterface">要返回的接口的类型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="TInterface" /> 参数不是接口。- 或 -该类型对 COM 不可见。- 或 -<paramref name="T" /> 参数是开放式泛型类型。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> 参数不支持 <paramref name="TInterface" /> 接口。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer``1(System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]将非托管函数指针转换为指定类型的委托。</summary>
      <returns>指定委托类型的实例。</returns>
      <param name="ptr">要转换的非托管函数指针。</param>
      <typeparam name="TDelegate">要返回的委托的类型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="TDelegate" /> 泛型参数不是代理，或者它是开放式泛型类型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ptr" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(System.IntPtr,System.Type)">
      <summary>将非托管函数指针转换为委托。</summary>
      <returns>可转换为适当的委托类型的委托实例。</returns>
      <param name="ptr">要转换的非托管函数指针。</param>
      <param name="t">要返回的委托的类型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 参数不是委托或泛型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ptr" /> 参数为 null。- 或 -<paramref name="t" /> 参数为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionCode">
      <summary>检索标识所发生异常的类型的代码。</summary>
      <returns>异常的类型。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32)">
      <summary>将指定的 HRESULT 错误代码转换为对应的 <see cref="T:System.Exception" /> 对象。</summary>
      <returns>表示转换后的 HRESULT 的对象。</returns>
      <param name="errorCode">要转换的 HRESULT。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32,System.IntPtr)">
      <summary>通过传入异常对象的 IErrorInfo 接口的附加错误消息，将指定的 HRESULT 错误代码转换为对应的 <see cref="T:System.Exception" /> 对象。</summary>
      <returns>一个对象，表示转换后的 HRESULT 以及从 <paramref name="errorInfo" /> 获取的信息。</returns>
      <param name="errorCode">要转换的 HRESULT。</param>
      <param name="errorInfo">指向 IErrorInfo 接口的指针，该接口提供有关错误的更多信息。您可以指定 IntPtr(0) 以使用当前 IErrorInfo 接口，或者指定 IntPtr(-1) 以忽略当前 IErrorInfo 接口，并仅从错误代码构造异常。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(System.Delegate)">
      <summary>将委托转换为可从非托管代码调用的函数指针。</summary>
      <returns>一个可传递给非托管代码的值，非托管代码使用该值来调用基础托管委托。</returns>
      <param name="d">要传递给非托管代码的委托。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="d" /> 参数是泛型类型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 参数为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate``1(``0)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]将指定类型的委托转换为可从非托管代码调用的函数指针。</summary>
      <returns>一个可传递给非托管代码的值，非托管代码使用该值来调用基础托管委托。</returns>
      <param name="d">要传递给非托管代码的委托。</param>
      <typeparam name="TDelegate">要转换的委托的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForException(System.Exception)">
      <summary>将指定异常转换为 HRESULT。</summary>
      <returns>映射到所提供的异常的 HRESULT。</returns>
      <param name="e">要转换为 HRESULT 的异常。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForLastWin32Error">
      <summary>返回 HRESULT，它对应于使用 <see cref="T:System.Runtime.InteropServices.Marshal" /> 执行的 Win32 代码引起的最后一个错误。</summary>
      <returns>对应于最后一个 Win32 错误代码的 HRESULT。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetIUnknownForObject(System.Object)">
      <summary>从托管对象返回 IUnknown 接口。</summary>
      <returns>
        <paramref name="o" /> 参数的 IUnknown 指针。</returns>
      <param name="o">其 IUnknown 接口被请求的对象。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error">
      <summary>返回由上一个非托管函数返回的错误代码，该函数是使用设置了 <see cref="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError" /> 标志的平台调用来调用的。</summary>
      <returns>通过调用 Win32 SetLastError 函数设置的最后一个错误代码。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject(System.Object,System.IntPtr)">
      <summary>将对象转换为 COM VARIANT。</summary>
      <param name="obj">为其获取 COM VARIANT 的对象。</param>
      <param name="pDstNativeVariant">一个指针，接收对应于 <paramref name="obj" /> 参数的 VARIANT。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> 参数是泛型类型。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject``1(``0,System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]将指定类型的对象转换为 COM VARIANT。</summary>
      <param name="obj">为其获取 COM VARIANT 的对象。</param>
      <param name="pDstNativeVariant">一个指针，接收对应于 <paramref name="obj" /> 参数的 VARIANT。</param>
      <typeparam name="T">要转换的对象的类型。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForIUnknown(System.IntPtr)">
      <summary>返回一个类型实例，该实例通过指向 COM 对象的 IUnknown 接口的指针表示该对象。</summary>
      <returns>一个对象，表示指定的非托管 COM 对象。</returns>
      <param name="pUnk">指向 IUnknown 接口的指针。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant(System.IntPtr)">
      <summary>将 COM VARIANT 转换为对象。</summary>
      <returns>一个对象，对应于 <paramref name="pSrcNativeVariant" /> 参数。</returns>
      <param name="pSrcNativeVariant">指向 COM VARIANT 的指针。</param>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> 不是有效的 VARIANT 类型。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> 包含不受支持的类型。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant``1(System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]将 COM VARIANT 转换为指定类型的对象。</summary>
      <returns>一个指定类型的对象，它与 <paramref name="pSrcNativeVariant" /> 参数对应。 </returns>
      <param name="pSrcNativeVariant">指向 COM VARIANT 的指针。</param>
      <typeparam name="T">要将 COM VARIANT 转换为的类型。</typeparam>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" /> 不是有效的 VARIANT 类型。 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" /> 包含不受支持的类型。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants(System.IntPtr,System.Int32)">
      <summary>将 COM VARIANTs 数组转换为对象数组。</summary>
      <returns>一个对象数组，对应于 <paramref name="aSrcNativeVariant" />。</returns>
      <param name="aSrcNativeVariant">指向 COM VARIANT 数组中第一个元素的指针。</param>
      <param name="cVars">
        <paramref name="aSrcNativeVariant" /> 中的 COM VARIANT 的计数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> 是一个负数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants``1(System.IntPtr,System.Int32)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]将 COM VARIANT 数组转换为指定类型的数组。</summary>
      <returns>对应于 <paramref name="aSrcNativeVariant" /> 的 <paramref name="T" /> 对象的数组。 </returns>
      <param name="aSrcNativeVariant">指向 COM VARIANT 数组中第一个元素的指针。</param>
      <param name="cVars">
        <paramref name="aSrcNativeVariant" /> 中的 COM VARIANT 的计数。</param>
      <typeparam name="T">要返回的数组的类型。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" /> 是一个负数。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetStartComSlot(System.Type)">
      <summary>获取虚拟功能表（v 表或 VTBL）中包含用户定义的方法的第一个槽。</summary>
      <returns>包含用户定义的方法的第一个 VTBL 槽。如果接口基于 IUnknown，则第一个槽为 3；如果接口基于 IDispatch，则为 7。</returns>
      <param name="t">表示接口的类型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 在 COM 中不可见。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeFromCLSID(System.Guid)">
      <summary>返回与指定类标识符 (CLSID) 关联的类型。</summary>
      <returns>System.__ComObject，无论 CLSID 是否有效。</returns>
      <param name="clsid">返回的类型的 CLSID。 </param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeInfoName(System.Runtime.InteropServices.ComTypes.ITypeInfo)">
      <summary>检索由 ITypeInfo 对象表示的类型的名称。</summary>
      <returns>
        <paramref name="typeInfo" /> 参数指向的类型的名称。</returns>
      <param name="typeInfo">表示 ITypeInfo 指针的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeInfo" /> 参数为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetUniqueObjectForIUnknown(System.IntPtr)">
      <summary>为给定的 IUnknown 接口创建唯一的 运行时可调用包装 (RCW) 对象。</summary>
      <returns>指定的 IUnknown 接口的唯一 RCW。</returns>
      <param name="unknown">指向 IUnknown 接口的托管指针。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.IsComObject(System.Object)">
      <summary>指示指定对象是否表示 COM 对象。</summary>
      <returns>如果 <paramref name="o" /> 参数是 COM 类型，则为 true；否则为 false。</returns>
      <param name="o">要检查的对象。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf``1(System.String)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]返回指定托管类的非托管形式的字段偏移量。</summary>
      <returns>平台调用声明的指定类中 <paramref name="fieldName" /> 参数的偏移量（以字节为单位）。 </returns>
      <param name="fieldName">
        <paramref name="T" /> 类型中字段的名称。</param>
      <typeparam name="T">托管值类型或格式化引用类型。必须将 <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 特性应用于该类。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf(System.Type,System.String)">
      <summary>返回托管类的非托管形式的字段偏移量。</summary>
      <returns>平台调用声明的指定类中 <paramref name="fieldName" /> 参数的偏移量（以字节为单位）。</returns>
      <param name="t">指定托管类的值类型或格式化引用类型。必须将 <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 应用于该类。</param>
      <param name="fieldName">
        <paramref name="t" /> 参数中的字段。</param>
      <exception cref="T:System.ArgumentException">该类无法作为结构导出，或者字段为非公共字段。从 .NET Framework 2.0 版开始，该字段可以是私有的。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> 参数为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr)">
      <summary>将非托管 ANSI 字符串中第一个 null 字符之前的所有字符复制到托管 <see cref="T:System.String" />，并将每个 ANSI 字符扩展为 Unicode 字符。</summary>
      <returns>包含非托管 ANSI 字符串的副本的托管字符串。如果 <paramref name="ptr" /> 为 null，则该方法返回空字符串。</returns>
      <param name="ptr">非托管字符串的第一个字符的地址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr,System.Int32)">
      <summary>分配托管 <see cref="T:System.String" />，然后从非托管 ANSI 字符串向其复制指定数目的字符，并将每个 ANSI 字符扩展为 Unicode 字符。</summary>
      <returns>如果 <paramref name="ptr" /> 参数的值不是 null，则为包含本机 ANSI 字符串副本的托管字符串；否则，此方法返回 null。</returns>
      <param name="ptr">非托管字符串的第一个字符的地址。</param>
      <param name="len">要复制的输入字符串的字节数。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="len" /> 小于零。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringBSTR(System.IntPtr)">
      <summary>分配托管 <see cref="T:System.String" />，并向其复制存储在非托管内存中的 BSTR 字符串。</summary>
      <returns>如果 <paramref name="ptr" /> 参数的值不是 null，则为包含非托管字符串副本的托管字符串；否则，此方法返回 null。</returns>
      <param name="ptr">非托管字符串的第一个字符的地址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr)">
      <summary>分配托管 <see cref="T:System.String" />，并从非托管 Unicode 字符串向其复制第一个空字符之前的所有字符。</summary>
      <returns>如果 <paramref name="ptr" /> 参数的值不是 null，则为包含非托管字符串副本的托管字符串；否则，此方法返回 null。</returns>
      <param name="ptr">非托管字符串的第一个字符的地址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr,System.Int32)">
      <summary>分配托管 <see cref="T:System.String" />，并从非托管 Unicode 字符串向其复制指定数目的字符。</summary>
      <returns>如果 <paramref name="ptr" /> 参数的值不是 null，则为包含非托管字符串副本的托管字符串；否则，此方法返回 null。</returns>
      <param name="ptr">非托管字符串的第一个字符的地址。</param>
      <param name="len">要复制的 Unicode 字符数。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]将数据从非托管内存块封送到泛型类型参数指定的类型的新分配托管对象。</summary>
      <returns>一个托管对象，包含 <paramref name="ptr" /> 参数指向的数据。</returns>
      <param name="ptr">指向非托管内存块的指针。</param>
      <typeparam name="T">要将数据复制到其中的对象的类型。这必须是格式化类或结构。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 的布局不是连续或显式的。</exception>
      <exception cref="T:System.MissingMethodException">
        <paramref name="T" /> 指定的类没有可访问的默认值构造函数。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Object)">
      <summary>将数据从非托管内存块封送到托管对象。</summary>
      <param name="ptr">指向非托管内存块的指针。</param>
      <param name="structure">将数据复制到其中的对象。这必须是格式化类的实例。</param>
      <exception cref="T:System.ArgumentException">结构布局不是连续或显式的。- 或 -结构为装箱的值类型。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Type)">
      <summary>将数据从非托管内存块封送到新分配的指定类型的托管对象。</summary>
      <returns>一个托管对象，包含 <paramref name="ptr" /> 参数指向的数据。</returns>
      <param name="ptr">指向非托管内存块的指针。</param>
      <param name="structureType">要创建的对象的类型。此对象必须表示格式化类或结构。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> 参数布局不是连续或显式的。- 或 -<paramref name="structureType" /> 参数是泛型类型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structureType" /> 为 null。</exception>
      <exception cref="T:System.MissingMethodException">
        <paramref name="structureType" /> 指定的类没有可访问的默认值构造函数。 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr,``0)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]将数据从非托管内存块封送到指定类型的托管内存对象。</summary>
      <param name="ptr">指向非托管内存块的指针。</param>
      <param name="structure">将数据复制到其中的对象。</param>
      <typeparam name="T">
        <paramref name="structure" /> 的类型。这必须是格式化的类。</typeparam>
      <exception cref="T:System.ArgumentException">结构布局不是连续或显式的。 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.QueryInterface(System.IntPtr,System.Guid@,System.IntPtr@)">
      <summary>从 COM 对象请求指向指定接口的指针。</summary>
      <returns>一个 HRESULT，指示调用成功还是失败。</returns>
      <param name="pUnk">要查询的接口。</param>
      <param name="iid">所请求的接口的接口标识符 (IID)。</param>
      <param name="ppv">此方法返回时，包含对返回接口的引用。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr)">
      <summary>从非托管内存读取单个字节。</summary>
      <returns>从非托管内存读取的字节。</returns>
      <param name="ptr">非托管内存中开始读取的地址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr,System.Int32)">
      <summary>从非托管内存按给定的偏移量（或索引）读取单个字节。</summary>
      <returns>从非托管内存按给定的偏移量读取的字节。</returns>
      <param name="ptr">非托管内存中开始读取的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.Object,System.Int32)">
      <summary>从非托管内存按给定的偏移量（或索引）读取单个字节。</summary>
      <returns>从非托管内存按给定的偏移量读取的字节。</returns>
      <param name="ptr">非托管内存中源对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr)">
      <summary>从非托管内存中读取一个 16 位带符号整数。</summary>
      <returns>从非托管内存中读取的 16 位带符号整数。</returns>
      <param name="ptr">非托管内存中开始读取的地址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr,System.Int32)">
      <summary>从非托管内存按给定的偏移量读取一个 16 位带符号整数。</summary>
      <returns>从非托管内存按给定的偏移量读取的 16 位带符号整数。</returns>
      <param name="ptr">非托管内存中开始读取的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.Object,System.Int32)">
      <summary>从非托管内存按给定的偏移量读取一个 16 位带符号整数。</summary>
      <returns>从非托管内存按给定的偏移量读取的 16 位带符号整数。</returns>
      <param name="ptr">非托管内存中源对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr)">
      <summary>从非托管内存中读取一个 32 位带符号整数。</summary>
      <returns>从非托管内存中读取的 32 位带符号整数。</returns>
      <param name="ptr">非托管内存中开始读取的地址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr,System.Int32)">
      <summary>从非托管内存按给定的偏移量读取一个 32 位带符号整数。</summary>
      <returns>从非托管内存中读取的 32 位带符号整数。</returns>
      <param name="ptr">非托管内存中开始读取的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.Object,System.Int32)">
      <summary>从非托管内存按给定的偏移量读取一个 32 位带符号整数。</summary>
      <returns>从非托管内存按给定的偏移量读取的 32 位带符号整数。</returns>
      <param name="ptr">非托管内存中源对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr)">
      <summary>从非托管内存中读取一个 64 位带符号整数。</summary>
      <returns>从非托管内存中读取的 64 位带符号整数。</returns>
      <param name="ptr">非托管内存中开始读取的地址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr,System.Int32)">
      <summary>从非托管内存按给定的偏移量读取一个 64 位带符号整数。</summary>
      <returns>从非托管内存按给定的偏移量读取的 64 位带符号整数。</returns>
      <param name="ptr">非托管内存中开始读取的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.Object,System.Int32)">
      <summary>从非托管内存按给定的偏移量读取一个 64 位带符号整数。</summary>
      <returns>从非托管内存按给定的偏移量读取的 64 位带符号整数。</returns>
      <param name="ptr">非托管内存中源对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr)">
      <summary>从非托管内存读取处理器本机大小的整数。</summary>
      <returns>从非托管内存读取的整数。在 32 位计算机上返回 32 位整数，在 64 位计算机上返回 64 位整数。</returns>
      <param name="ptr">非托管内存中开始读取的地址。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr,System.Int32)">
      <summary>从非托管内存按给定的偏移量读取处理器本机大小的整数。</summary>
      <returns>从非托管内存按给定的偏移量读取的整数。</returns>
      <param name="ptr">非托管内存中开始读取的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.Object,System.Int32)">
      <summary>从非托管内存读取处理器本机大小的整数。</summary>
      <returns>从非托管内存按给定的偏移量读取的整数。</returns>
      <param name="ptr">非托管内存中源对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在读取前添加到 <paramref name="ptr" /> 参数中。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocCoTaskMem(System.IntPtr,System.Int32)">
      <summary>调整以前用 <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" /> 分配的内存块的大小。</summary>
      <returns>一个整数，表示重新分配的内存块的地址。该内存必须用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" /> 来释放。</returns>
      <param name="pv">指向用 <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" /> 分配的内存的指针。</param>
      <param name="cb">已分配块的新大小。</param>
      <exception cref="T:System.OutOfMemoryException">内存不足，无法满足请求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocHGlobal(System.IntPtr,System.IntPtr)">
      <summary>调整以前用 <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> 分配的内存块的大小。</summary>
      <returns>指向重新分配的内存的指针。该内存必须用 <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> 来释放。</returns>
      <param name="pv">指向用 <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> 分配的内存的指针。</param>
      <param name="cb">已分配块的新大小。这不是指针；它是您请求的字节数，转换为类型 <see cref="T:System.IntPtr" />。如果你传递指针，则将其视为大小。</param>
      <exception cref="T:System.OutOfMemoryException">内存不足，无法满足请求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Release(System.IntPtr)">
      <summary>递减指定接口上的引用计数。</summary>
      <returns>
        <paramref name="pUnk" /> 参数指定的接口上引用计数的新值。</returns>
      <param name="pUnk">要释放的接口。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)">
      <summary>递减与指定的 COM 对象关联的指定 运行时可调用包装 (RCW) 的引用计数。</summary>
      <returns>与 <paramref name="o" /> 关联的 RCW 的引用计数的新值。此值通常为零，因为无论调用包装 COM 对象的托管客户端有多少，RCW 仅保留对该对象的一次引用。</returns>
      <param name="o">要释放的 COM 对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" /> 不是一个有效的 COM 对象。</exception>
      <exception cref="T:System.NullReferenceException">
        <paramref name="o" /> 为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]返回非托管类型的大小（以字节为单位）。</summary>
      <returns>
        <paramref name="T" /> 泛型类型参数指定的类型的大小（以字节为单位）。</returns>
      <typeparam name="T">要返回其大小的类型。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Object)">
      <summary>返回对象的非托管大小（以字节为单位）。</summary>
      <returns>非托管代码中指定对象的大小。</returns>
      <param name="structure">要返回其大小的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structure" /> 参数为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Type)">
      <summary>返回非托管类型的大小（以字节为单位）。</summary>
      <returns>非托管代码中指定类型的大小。</returns>
      <param name="t">要返回其大小的类型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 参数是泛型类型。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> 参数为 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1(``0)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]返回指定类型的对象的非托管大小（以字节为单位）。</summary>
      <returns>非托管代码中指定对象的大小（以字节为单位）。</returns>
      <param name="structure">要返回其大小的对象。</param>
      <typeparam name="T">
        <paramref name="structure" /> 参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structure" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToBSTR(System.String)">
      <summary>分配 BSTR 并向其复制托管 <see cref="T:System.String" /> 的内容。</summary>
      <returns>指向 BSTR 的非托管指针；如果 <paramref name="s" /> 为 null，则为 0。</returns>
      <param name="s">要复制的托管字符串。</param>
      <exception cref="T:System.OutOfMemoryException">没有足够的可用内存。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 的长度超出范围。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemAnsi(System.String)">
      <summary>将托管 <see cref="T:System.String" /> 的内容复制到从非托管 COM 任务分配器分配的内存块。</summary>
      <returns>一个整数，表示指向为字符串分配的内存块的指针；如果 <paramref name="s" /> 为 null，则为 0。</returns>
      <param name="s">要复制的托管字符串。</param>
      <exception cref="T:System.OutOfMemoryException">没有足够的可用内存。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 参数超过了操作系统所允许的最大长度。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni(System.String)">
      <summary>将托管 <see cref="T:System.String" /> 的内容复制到从非托管 COM 任务分配器分配的内存块。</summary>
      <returns>一个整数，表示指向为字符串分配的内存块的指针；如果 s 为 null，则为 0。</returns>
      <param name="s">要复制的托管字符串。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 参数超过了操作系统所允许的最大长度。</exception>
      <exception cref="T:System.OutOfMemoryException">没有足够的可用内存。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalAnsi(System.String)">
      <summary>将托管 <see cref="T:System.String" /> 中的内容复制到非托管内存，并在复制时转换为 ANSI 格式。</summary>
      <returns>非托管内存中将 <paramref name="s" /> 复制到的地址；如果 <paramref name="s" /> 为 null，则为 0。</returns>
      <param name="s">要复制的托管字符串。</param>
      <exception cref="T:System.OutOfMemoryException">没有足够的可用内存。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 参数超过了操作系统所允许的最大长度。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalUni(System.String)">
      <summary>向非托管内存复制托管 <see cref="T:System.String" /> 的内容。</summary>
      <returns>非托管内存中将 <paramref name="s" /> 复制到的地址；如果 <paramref name="s" /> 为 null，则为 0。</returns>
      <param name="s">要复制的托管字符串。</param>
      <exception cref="T:System.OutOfMemoryException">此方法未能分配足够的本机堆内存。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 参数超过了操作系统所允许的最大长度。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr(System.Object,System.IntPtr,System.Boolean)">
      <summary>将数据从托管对象封送到非托管内存块。</summary>
      <param name="structure">包含要封送的数据的托管对象。该对象必须是格式化类的结构或实例。</param>
      <param name="ptr">指向非托管内存块的指针，必须在调用此方法之前分配该指针。</param>
      <param name="fDeleteOld">如果在此方法复制该数据前在 <paramref name="ptr" /> 参数上调用 <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)" />， 则为true 。该块必须包含有效的数据。请注意，在内存块已包含数据时传递 false 可能会导致内存泄漏。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> 一个不是格式化类的引用类型。- 或 -<paramref name="structure" /> 是一个泛型类型。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr``1(``0,System.IntPtr,System.Boolean)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]将数据从指定类型的托管内存块封送到非托管内存对象。</summary>
      <param name="structure">包含要封送的数据的托管对象。该对象必须是格式化类的结构或实例。</param>
      <param name="ptr">指向非托管内存块的指针，必须在调用此方法之前分配该指针。 </param>
      <param name="fDeleteOld">如果在此方法复制该数据前在 <paramref name="ptr" /> 参数上调用 <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)" />， 则为true 。该块必须包含有效的数据。请注意，在内存块已包含数据时传递 false 可能会导致内存泄漏。</param>
      <typeparam name="T">托管对象的类型。</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" /> 一个不是格式化类的引用类型。</exception>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemDefaultCharSize">
      <summary>表示系统上的默认字符大小；Unicode 系统上默认值为 2，ANSI 系统上默认值为 1。此字段为只读。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemMaxDBCSCharSize">
      <summary>表示用于当前操作系统的双字节字符集 (DBCS) 的最大大小（以字节为单位）。此字段为只读。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32)">
      <summary>用特定的失败 HRESULT 值引发异常。</summary>
      <param name="errorCode">与所需异常相对应的 HRESULT。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)">
      <summary>基于指定的 IErrorInfo 接口，以特定的失败 HRESULT 引发异常。</summary>
      <param name="errorCode">与所需异常相对应的 HRESULT。</param>
      <param name="errorInfo">指向 IErrorInfo 接口的指针，该接口提供有关错误的更多信息。您可以指定 IntPtr(0) 以使用当前 IErrorInfo 接口，或者指定 IntPtr(-1) 以忽略当前 IErrorInfo 接口，并仅从错误代码构造异常。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement(System.Array,System.Int32)">
      <summary>获取指定数组中指定索引处的元素的地址。</summary>
      <returns>
        <paramref name="arr" /> 内的 <paramref name="index" /> 的地址。</returns>
      <param name="arr">包含所需元素的数组。</param>
      <param name="index">所需元素的 <paramref name="arr" /> 参数中的索引。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement``1(``0[],System.Int32)">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]获取指定类型的数组中指定索引处的元素的地址。</summary>
      <returns>
        <paramref name="arr" /> 中的 <paramref name="index" /> 的地址。</returns>
      <param name="arr">包含所需元素的数组。</param>
      <param name="index">
        <paramref name="arr" /> 数组中所需元素的索引。</param>
      <typeparam name="T">数组类型。</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Byte)">
      <summary>将单个字节值写入到非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的地址。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Int32,System.Byte)">
      <summary>按指定偏移量将单字节值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.Object,System.Int32,System.Byte)">
      <summary>按指定偏移量将单字节值写入非托管内存。</summary>
      <param name="ptr">非托管内存中目标对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Char)">
      <summary>将一个字符作为 16 位整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的地址。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int16)">
      <summary>将 16 位整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的地址。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Char)">
      <summary>按指定偏移量将 16 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">本机堆中要写入的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Int16)">
      <summary>按指定偏移量将 16 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Char)">
      <summary>按指定偏移量将 16 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中目标对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Int16)">
      <summary>按指定偏移量将 16 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中目标对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32)">
      <summary>将 32 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的地址。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32,System.Int32)">
      <summary>按指定偏移量将 32 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.Object,System.Int32,System.Int32)">
      <summary>按指定偏移量将 32 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中目标对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int32,System.Int64)">
      <summary>按指定偏移量将 64 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int64)">
      <summary>将 64 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的地址。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.Object,System.Int32,System.Int64)">
      <summary>按指定偏移量将 64 位带符号整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中目标对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.Int32,System.IntPtr)">
      <summary>按指定的偏移量将一个处理器本机大小的整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.IntPtr)">
      <summary>将一个处理器本机大小的整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中要写入的地址。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" /> 不是识别的格式。- 或 -<paramref name="ptr" /> 为 null。- 或 -<paramref name="ptr" /> 无效。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.Object,System.Int32,System.IntPtr)">
      <summary>将一个处理器本机大小的整数值写入非托管内存。</summary>
      <param name="ptr">非托管内存中目标对象的基址。</param>
      <param name="ofs">额外的字节偏移量，在写入前添加到 <paramref name="ptr" /> 参数中。</param>
      <param name="val">要写入的值。</param>
      <exception cref="T:System.AccessViolationException">基址 (<paramref name="ptr" />) 加上偏移字节(<paramref name="ofs" />) 可产生空或无效地址。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" /> 是 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 对象。此方法不接受 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 参数。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(System.IntPtr)">
      <summary>释放 BSTR 指针，该指针是使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToBSTR(System.Security.SecureString)" /> 方法分配的。</summary>
      <param name="s">要释放的 BSTR 的地址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemAnsi(System.IntPtr)">
      <summary>释放非托管字符串指针，该指针是使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemAnsi(System.Security.SecureString)" /> 方法分配的。</summary>
      <param name="s">要释放的非托管字符串的地址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemUnicode(System.IntPtr)">
      <summary>释放非托管字符串指针，该指针是使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemUnicode(System.Security.SecureString)" /> 方法分配的。</summary>
      <param name="s">要释放的非托管字符串的地址。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocAnsi(System.IntPtr)">
      <summary>释放非托管字符串指针，该指针是使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocAnsi(System.Security.SecureString)" /> 方法分配的。</summary>
      <param name="s">要释放的非托管字符串的地址。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocUnicode(System.IntPtr)">
      <summary>释放非托管字符串指针，该指针是使用 <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocUnicode(System.Security.SecureString)" /> 方法分配的。</summary>
      <param name="s">要释放的非托管字符串的地址。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalAsAttribute">
      <summary>指示如何在托管代码和非托管代码之间封送数据。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Int16)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 值初始化 <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> 类的新实例。</summary>
      <param name="unmanagedType">数据将封送为的值。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Runtime.InteropServices.UnmanagedType)">
      <summary>使用指定的 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 枚举成员初始化 <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> 类的新实例。</summary>
      <param name="unmanagedType">数据将封送为的值。</param>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType">
      <summary>指定非托管 <see cref="F:System.Runtime.InteropServices.UnmanagedType.LPArray" /> 或 <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValArray" /> 的元素类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.IidParameterIndex">
      <summary>指定 COM 使用的非托管 iid_is 属性的参数索引。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalCookie">
      <summary>向自定义封送拆收器提供附加信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType">
      <summary>指定自定义封送拆收器的完全限定名。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalTypeRef">
      <summary>将 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType" /> 作为类型实现。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType">
      <summary>指示 <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> 的元素类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArrayUserDefinedSubType">
      <summary>指示用户定义的 <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> 元素类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst">
      <summary>指示固定长度数组中的元素数，或要导入的字符串中的字符（不是字节）数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex">
      <summary>指示从零开始的参数，该参数包含数组元素的计数，与 COM 中的 size_is 类似。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.MarshalAsAttribute.Value">
      <summary>获取 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 值，数据将被作为该值封送。</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 值，数据将被作为该值封送。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalDirectiveException">
      <summary>当封送拆收器遇到它不支持的 <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> 时由该封送拆收器引发的异常。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor">
      <summary>使用默认属性初始化 MarshalDirectiveException 类的新实例。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String)">
      <summary>使用指定的错误信息初始化 MarshalDirectiveException 类的新实例。</summary>
      <param name="message">指定异常原因的错误信息。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String,System.Exception)">
      <summary>使用指定的错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Runtime.InteropServices.MarshalDirectiveException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="inner">导致当前异常的异常。如果 <paramref name="inner" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.OptionalAttribute">
      <summary>指示参数是可选的。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.OptionalAttribute.#ctor">
      <summary>使用默认值初始化 OptionalAttribute 类的新实例。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.PreserveSigAttribute">
      <summary>指示应取消在 COM 互操作调用期间发生的 HRESULT 或 retval 签名转换。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.PreserveSigAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.PreserveSigAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayRankMismatchException">
      <summary>当传入的 SAFEARRAY 的秩与托管签名中指定的秩不匹配时引发的异常。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor">
      <summary>使用默认值初始化 SafeArrayTypeMismatchException 类的新实例。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String)">
      <summary>用指定消息初始化 SafeArrayRankMismatchException 类的新实例。</summary>
      <param name="message">指示异常原因的消息。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Runtime.InteropServices.SafeArrayRankMismatchException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="inner">导致当前异常的异常。如果 <paramref name="inner" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException">
      <summary>当传入的 SAFEARRAY 的类型与托管签名中指定的类型不匹配时引发的异常。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor">
      <summary>使用默认值初始化 SafeArrayTypeMismatchException 类的新实例。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String)">
      <summary>用指定消息初始化 SafeArrayTypeMismatchException 类的新实例。</summary>
      <param name="message">指示异常原因的消息。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="inner">导致当前异常的异常。如果 <paramref name="inner" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeBuffer">
      <summary>提供可用于读写的受控内存缓冲区。尝试访问受控缓冲区（不足和溢出）之外的访问内存将引发异常。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.#ctor(System.Boolean)">
      <summary>创建 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 类的新实例，并指定是否可靠地释放缓冲区句柄。</summary>
      <param name="ownsHandle">如果为 true，则在完成阶段可靠地释放句柄；如果为 false，则阻止可靠释放（建议不要这样做）。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)">
      <summary>从内存块的 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 对象中获取一个指针。</summary>
      <param name="pointer">通过引用传递的字节指针，用于从 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 对象内接收指针。您必须在调用此方法之前将此指针设置为 null。</param>
      <exception cref="T:System.InvalidOperationException">未调用 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.ByteLength">
      <summary>获取缓冲区的大小（以字节为单位）。</summary>
      <returns>内存缓冲区中的字节数。</returns>
      <exception cref="T:System.InvalidOperationException">未调用 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize``1(System.UInt32)">
      <summary>通过指定值类型的数目，定义内存区域的分配大小。在使用 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 实例之前，必须调用此方法。</summary>
      <param name="numElements">要为其分配内存的值类型的元素数。</param>
      <typeparam name="T">要为其分配内存的值类型。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> 小于零。- 或 -<paramref name="numElements" /> 与每个元素大小的乘积大于可用地址空间。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt32,System.UInt32)">
      <summary>通过使用指定的元素数和元素大小，指定内存缓冲区的分配大小。在使用 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 实例之前，必须调用此方法。</summary>
      <param name="numElements">缓冲区中元素的数目。</param>
      <param name="sizeOfEachElement">缓冲区中每个元素的大小。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" /> 小于零。- 或 -<paramref name="sizeOfEachElement" /> 小于零。- 或 -<paramref name="numElements" /> 与 <paramref name="sizeOfEachElement" /> 的乘积大于可用地址空间。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt64)">
      <summary>定义内存区域的分配大小（以字节为单位）。在使用 <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 实例之前，必须调用此方法。</summary>
      <param name="numBytes">缓冲区中的字节数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numBytes" /> 小于零。- 或 -<paramref name="numBytes" /> 大于可用地址空间。</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.IsInvalid"></member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Read``1(System.UInt64)">
      <summary>按指定的偏移量从内存中读取值类型。</summary>
      <returns>从内存中读取的值类型。</returns>
      <param name="byteOffset">从中读取值类型的位置。可能必须考虑对齐问题。</param>
      <typeparam name="T">要读取的值类型。</typeparam>
      <exception cref="T:System.InvalidOperationException">未调用 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReadArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>从自偏移量开始的内存中读取指定数量的值类型，并将它们写入从索引开始的数组中。</summary>
      <param name="byteOffset">从其开始读取的位置。</param>
      <param name="array">要写入的输出数组。</param>
      <param name="index">输出数组中要开始写入的位置。</param>
      <param name="count">要从输入数组中读取并写入输出数组的值类型的数目。</param>
      <typeparam name="T">要读取的值类型。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。- 或 -<paramref name="count" /> 小于零。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">数组的长度减去索引小于 <paramref name="count" /> 。</exception>
      <exception cref="T:System.InvalidOperationException">未调用 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReleasePointer">
      <summary>释放由 <see cref="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)" /> 方法获取的指针。</summary>
      <exception cref="T:System.InvalidOperationException">未调用 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Write``1(System.UInt64,``0)">
      <summary>将值类型写入内存中的给定位置。</summary>
      <param name="byteOffset">开始写入的位置。可能必须考虑对齐问题。</param>
      <param name="value">要写入的值。</param>
      <typeparam name="T">要写入的值类型。</typeparam>
      <exception cref="T:System.InvalidOperationException">未调用 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.WriteArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>通过读取从输入数组中指定位置开始的字节，将指定数目的值类型写入内存位置。</summary>
      <param name="byteOffset">内存中要写入的位置。</param>
      <param name="array">输入数组。</param>
      <param name="index">数组中从其开始读取的偏移量。</param>
      <param name="count">要写入的值类型的数目。</param>
      <typeparam name="T">要写入的值类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 或 <paramref name="count" /> 小于零。</exception>
      <exception cref="T:System.ArgumentException">输入数组的长度减去 <paramref name="index" /> 小于 <paramref name="count" />。</exception>
      <exception cref="T:System.InvalidOperationException">未调用 <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 方法。</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SEHException">
      <summary>表示结构化异常处理程序 (SEH) 错误。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.InteropServices.SEHException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String)">
      <summary>用指定的消息初始化 <see cref="T:System.Runtime.InteropServices.SEHException" /> 类的新实例。</summary>
      <param name="message">指示异常原因的消息。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String,System.Exception)">
      <summary>使用指定的错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Runtime.InteropServices.SEHException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="inner">导致当前异常的异常。如果 <paramref name="inner" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.CanResume">
      <summary>指示是否可以从异常中恢复，以及代码是否可以从引发异常的地方继续。</summary>
      <returns>始终为 false，因为未实现可恢复的异常。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.TypeIdentifierAttribute">
      <summary>提供对类型等效性的支持。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor">
      <summary>创建 <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)">
      <summary>用指定的范围和标识符创建 <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> 类的新实例。</summary>
      <param name="scope">第一个类型等效性字符串。</param>
      <param name="identifier">第二个类型等效性字符串。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Identifier">
      <summary>获取传递给 <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" /> 构造函数的 <paramref name="identifier" /> 参数的值。</summary>
      <returns>构造函数的 <paramref name="identifier" /> 参数的值。</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Scope">
      <summary>获取传递给 <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" /> 构造函数的 <paramref name="scope" /> 参数的值。</summary>
      <returns>构造函数的 <paramref name="scope" /> 参数的值。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnknownWrapper">
      <summary>对封送拆收器应该将其作为 VT_UNKNOWN 封送的对象进行包装。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnknownWrapper.#ctor(System.Object)">
      <summary>使用要被包装的对象初始化 <see cref="T:System.Runtime.InteropServices.UnknownWrapper" /> 类的新实例。</summary>
      <param name="obj">被包装的对象。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.UnknownWrapper.WrappedObject">
      <summary>获取此包装包含的对象。</summary>
      <returns>被包装的对象。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute">
      <summary>控制作为非托管函数指针传入或传出非托管代码的委托签名的封送行为。此类不能被继承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)">
      <summary>使用指定的调用约定初始化 <see cref="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute" /> 类的新实例。</summary>
      <param name="callingConvention">指定的调用约定。</param>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.BestFitMapping">
      <summary>将 Unicode 字符转换为 ANSI 字符时，启用或禁用最佳映射行为。</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CallingConvention">
      <summary>获取调用约定的值。</summary>
      <returns>
        <see cref="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)" /> 构造函数指定的调用约定的值。</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CharSet">
      <summary>指示如何向方法封送字符串参数，并控制名称重整。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.SetLastError">
      <summary>指示被调用方在从属性化方法返回之前是否调用 SetLastError Win32 API 函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.ThrowOnUnmappableChar">
      <summary>启用或禁用在遇到已被转换为 ANSI“?”字符的无法映射的 Unicode 字符时引发异常。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedType">
      <summary>指定如何将参数或字段封送到非托管代码。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AnsiBStr">
      <summary>ANSI 字符串是一个带有长度前缀的单字节字符串。可以在 <see cref="T:System.String" /> 数据类型上使用此成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AsAny">
      <summary>一个动态类型，将在运行时确定对象的类型，并将该对象作为所确定的类型进行封送处理。该成员仅对平台调用方法有效。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Bool">
      <summary>4 字节布尔值（true != 0, false = 0）。这是 Win32 BOOL 类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.BStr">
      <summary>长度前缀为双字节的 Unicode 字符串。可以在 <see cref="T:System.String" /> 数据类型上使用此成员（它是 COM 中的默认字符串）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValArray">
      <summary>当 <see cref="P:System.Runtime.InteropServices.MarshalAsAttribute.Value" /> 属性被设置为 ByValArray 时，必须设置 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> 字段以指示该数组中的元素数。当需要区分字符串类型时，<see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType" /> 字段可以选择包含数组元素的 <see cref="T:System.Runtime.InteropServices.UnmanagedType" />。此 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 只可用于作为结构中其元素作为字段出现的数组。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr">
      <summary>用于在结构中出现的内联定长字符数组。与 <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr" /> 一起使用的字符类型由应用于包含结构的 <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 特性的 <see cref="T:System.Runtime.InteropServices.CharSet" /> 参数确定。应始终使用 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> 字段来指示数组的大小。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Currency">
      <summary>货币类型。在 <see cref="T:System.Decimal" /> 上使用，以将十进制数值作为 COM 货币类型而不是 Decimal 封送。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Error">
      <summary>此与 <see cref="F:System.Runtime.InteropServices.UnmanagedType.I4" /> 或 <see cref="F:System.Runtime.InteropServices.UnmanagedType.U4" /> 关联的本机类型将导致参数作为导出类型库中的 HRESULT 导出。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.FunctionPtr">
      <summary>一个可用作 C 样式函数指针的整数。可将此成员用于 <see cref="T:System.Delegate" /> 数据类型或从 <see cref="T:System.Delegate" /> 继承的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.HString">
      <summary>一个 Windows 运行时 字符串。可以在 <see cref="T:System.String" /> 数据类型上使用此成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I1">
      <summary>1 字节有符号整数。可使用此成员将布尔值转换为 1 字节、C 样式的 bool (true = 1, false = 0)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I2">
      <summary>2 字节有符号整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I4">
      <summary>4 字节有符号整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I8">
      <summary>8 字节有符号整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IDispatch">
      <summary>一个 COM IDispatch 指针（在 Microsoft Visual Basic 6.0 中为 Object）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IInspectable">
      <summary>一个 Windows 运行时 接口指针。可以在 <see cref="T:System.Object" /> 数据类型上使用此成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Interface">
      <summary>COM 接口指针。从类元数据获得接口的 <see cref="T:System.Guid" />。如果将此成员应用于类，则可以使用该成员指定确切的接口类型或默认的接口类型。当应用于 <see cref="T:System.Object" /> 数据类型时，此成员将产生与 <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" /> 相同的行为。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IUnknown">
      <summary>COM IUnknown 指针。可以在 <see cref="T:System.Object" /> 数据类型上使用此成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPArray">
      <summary>指向 C 样式数组的第一个元素的指针。当从托管到非托管代码进行封送处理时，该数组的长度由托管数组的长度确定。当从非托管到托管代码进行封送处理时，将根据 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> 和 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex" /> 字段确定该数组的长度，当需要区分字符串类型时，还可以后跟数组中元素的非托管类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStr">
      <summary>单字节、以 null 结尾的 ANSI 字符串。可在 <see cref="T:System.String" /> 和 <see cref="T:System.Text.StringBuilder" /> 数据类型上使用此成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStruct">
      <summary>一个指针，它指向用于封送托管格式化类的 C 样式结构。该成员仅对平台调用方法有效。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPTStr">
      <summary>与平台相关的字符串：在 Windows 98 上为 ANSI，在 Windows NT 和 Windows XP 上为 Unicode。该值仅支持平台调用而不支持 COM 互操作，因为导出 LPTStr 类型的字符串不受支持。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPWStr">
      <summary>一个 2 字节、以 null 结尾的 Unicode 字符串。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R4">
      <summary>4 字节浮点数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R8">
      <summary>8 字节浮点数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SafeArray">
      <summary>SafeArray 是自我描述的数组，它带有关联数组数据的类型、秩和界限。可将此成员与 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType" /> 字段一起使用，以重写默认元素类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Struct">
      <summary>一个用于封送托管格式化类和值类型的 VARIANT。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysInt">
      <summary>与平台相关的有符号整数：在 32 位 Windows 上为 4 个字节，在 64 位 Windows 上为 8 个字节。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysUInt">
      <summary>与平台相关的无符号整数：在 32 位 Windows 上为 4 个字节，在 64 位 Windows 上为 8 个字节。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.TBStr">
      <summary>一个有长度前缀的与平台相关的 char 字符串：在 Windows 98 上为 ANSI，在 Windows NT 上为 Unicode。很少用到这个类似于 BSTR 的成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U1">
      <summary>1 字节无符号整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U2">
      <summary>2 字节无符号整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U4">
      <summary>4 字节无符号整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U8">
      <summary>8 字节无符号整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VariantBool">
      <summary>2 字节、OLE 定义的 VARIANT_BOOL 类型 (true = -1, false = 0)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VBByRefStr">
      <summary>一个值，该值使 Visual Basic 能够更改非托管代码中的字符串，并使结果在托管代码中反映出来。该值仅支持平台调用。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VarEnum">
      <summary>指示当数组作为 <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" /> 从托管代码封送到非托管代码时，如何封送该数组的元素。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ARRAY">
      <summary>指示 SAFEARRAY 指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB">
      <summary>指示以长度为前缀的字节。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB_OBJECT">
      <summary>指示 Blob 包含对象。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BOOL">
      <summary>指示一个布尔值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BSTR">
      <summary>指示 BSTR 字符串。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BYREF">
      <summary>指示值为引用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CARRAY">
      <summary>指示 C 样式数组。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CF">
      <summary>指示剪贴板格式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CLSID">
      <summary>指示类 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CY">
      <summary>指示货币值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DATE">
      <summary>指示 DATE 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DECIMAL">
      <summary>指示 decimal 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH">
      <summary>指示 IDispatch 指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_EMPTY">
      <summary>指示未指定值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ERROR">
      <summary>指示 SCODE。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_FILETIME">
      <summary>指示 FILETIME 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_HRESULT">
      <summary>指示 HRESULT。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I1">
      <summary>指示 char 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I2">
      <summary>指示 short 整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I4">
      <summary>指示 long 整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I8">
      <summary>指示 64 位整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_INT">
      <summary>指示整数值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPSTR">
      <summary>指示一个以 NULL 结尾的字符串。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPWSTR">
      <summary>指示由 null 终止的宽字符串。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_NULL">
      <summary>指示空值（类似于 SQL 中的空值）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_PTR">
      <summary>指示指针类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R4">
      <summary>指示 float 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R8">
      <summary>指示 double 值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_RECORD">
      <summary>指示用户定义的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_SAFEARRAY">
      <summary>指示 SAFEARRAY。在 VARIANT 中无效。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORAGE">
      <summary>指示随后是存储的名称。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORED_OBJECT">
      <summary>指示存储包含对象。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAM">
      <summary>指示随后是流的名称。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAMED_OBJECT">
      <summary>指示流包含对象。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI1">
      <summary>指示 byte。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI2">
      <summary>指示 unsignedshort。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI4">
      <summary>指示 unsignedlong。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI8">
      <summary>指示 64 位无符号整数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UINT">
      <summary>指示 unsigned 整数值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UNKNOWN">
      <summary>指示 IUnknown 指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_USERDEFINED">
      <summary>指示用户定义的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VARIANT">
      <summary>指示 VARIANTfar 指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VECTOR">
      <summary>指示简单的已计数数组。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VOID">
      <summary>指示 C 样式 void。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VariantWrapper">
      <summary>将 VT_VARIANT | VT_BYREF 类型的数据从托管代码封送到非托管代码。此类不能被继承。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.VariantWrapper.#ctor(System.Object)">
      <summary>使用指定的 <see cref="T:System.Object" /> 参数初始化 <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> 类的新实例。</summary>
      <param name="obj">要封送的对象。</param>
    </member>
    <member name="P:System.Runtime.InteropServices.VariantWrapper.WrappedObject">
      <summary>获取由 <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> 对象包装的对象。</summary>
      <returns>由 <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> 对象包装的对象。</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ADVF">
      <summary>在设置通知接收器或缓存与对象的连接时指定请求的行为。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_DATAONSTOP">
      <summary>对于数据通知连接，请确保数据的可访问性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_NODATA">
      <summary>对于数据通知连接（<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.DAdvise(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.ADVF,System.Runtime.InteropServices.ComTypes.IAdviseSink,System.Int32@)" /> 或 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />），此标志请求数据对象在它调用 <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> 时不要发送数据。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_ONLYONCE">
      <summary>请求对象在删除连接前只进行一次更改通知或缓存更新。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_PRIMEFIRST">
      <summary>请求对象在对 <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> 进行初始调用（对于数据或视图通知连接）或更新缓存（对于缓存连接）之前不要等待数据或视图更改。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN">
      <summary>此值由执行对象绘制的 DLL 对象应用程序和对象处理程序使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_NOHANDLER">
      <summary>
        <see cref="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN" /> 的同义词，该词更为常用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_ONSAVE">
      <summary>对于缓存连接，此标志只在保存包含缓存的对象时更新缓存的表示形式。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BIND_OPTS">
      <summary>存储在名字对象绑定操作期间使用的参数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.cbStruct">
      <summary>指定 BIND_OPTS 结构的大小（以字节为单位）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.dwTickCountDeadline">
      <summary>指示调用方指定的用于完成绑定操作的时间（由 GetTickCount 函数返回的以毫秒为单位的时钟时间）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfFlags">
      <summary>控制名字对象绑定操作的各个方面。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfMode">
      <summary>表示当打开包含由该名字对象标识的对象的文件时应使用的标志。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BINDPTR">
      <summary>包含指向绑定到 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 结构、<see cref="T:System.Runtime.InteropServices.VARDESC" /> 结构或 ITypeComp 接口的指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpfuncdesc">
      <summary>表示指向 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 结构的指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lptcomp">
      <summary>表示指向 <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeComp" /> 接口的指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpvardesc">
      <summary>表示指向 <see cref="T:System.Runtime.InteropServices.VARDESC" /> 结构的指针。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CALLCONV">
      <summary>标识 METHODDATA 结构中描述的方法所使用的调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_CDECL">
      <summary>指示对方法使用 C 声明 (CDECL) 调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MACPASCAL">
      <summary>指示对方法使用 Macintosh Pascal (MACPASCAL) 调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MAX">
      <summary>指示 <see cref="T:System.Runtime.InteropServices.ComTypes.CALLCONV" /> 枚举的结尾。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWCDECL">
      <summary>指示对方法使用 Macintosh Programmers' Workbench (MPW) CDECL 调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWPASCAL">
      <summary>指示对方法使用 Macintosh Programmers' Workbench (MPW) PASCAL 调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MSCPASCAL">
      <summary>指示对方法使用 MSC Pascal (MSCPASCAL) 调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_PASCAL">
      <summary>指示对方法使用 Pascal 调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_RESERVED">
      <summary>保留此值供将来使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_STDCALL">
      <summary>指示对方法使用标准调用约定 (STDCALL)。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_SYSCALL">
      <summary>指示对方法使用标准 SYSCALL 调用约定。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CONNECTDATA">
      <summary>描述现有的到给定连接点的连接。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.dwCookie">
      <summary>表示从 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> 调用中返回的连接标记。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.pUnk">
      <summary>表示指向已连接的通知接收器上的 IUnknown 接口的指针。当不再需要 CONNECTDATA 结构时，调用方必须在此指针上调用 IUnknown::Release。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DATADIR">
      <summary>在 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> 方法的 <paramref name="dwDirection" /> 参数中指定数据流的方向。这将确定生成的枚举数可以枚举的格式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_GET">
      <summary>请求 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> 为可在 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.GetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" /> 中指定的格式提供枚举数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_SET">
      <summary>请求 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> 为可在 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.SetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@,System.Boolean)" /> 中指定的格式提供枚举数。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DESCKIND">
      <summary>标识绑定到的类型说明。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_FUNCDESC">
      <summary>指示返回了 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 结构。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_IMPLICITAPPOBJ">
      <summary>指示返回了 IMPLICITAPPOBJ。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_MAX">
      <summary>指示枚举结尾标记。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_NONE">
      <summary>指示未找到匹配。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_TYPECOMP">
      <summary>指示返回了 TYPECOMP。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_VARDESC">
      <summary>指示返回了 VARDESC。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DISPPARAMS">
      <summary>包含通过 IDispatch::Invoke 传递给方法或属性的参数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cArgs">
      <summary>表示参数的计数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cNamedArgs">
      <summary>表示命名参数的计数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgdispidNamedArgs">
      <summary>表示命名参数的调度 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgvarg">
      <summary>表示对参数数组的引用。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DVASPECT">
      <summary>在绘制或获取数据时指定对象所需的数据或视图方位。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_CONTENT">
      <summary>对象的表示形式，它使该对象在容器内显示为嵌入的对象。通常为复合文档对象指定此值。该演示可提供给屏幕或打印机。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_DOCPRINT">
      <summary>对象在屏幕上的表示形式，与使用“文件”菜单上的“打印”命令在打印机上打印出的效果相同。该描述数据可以表示页序列。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_ICON">
      <summary>对象的图标表示形式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_THUMBNAIL">
      <summary>对象的缩略图表示形式，使对象可在浏览工具中显示。该缩略图大约为 120 x 120 像素、16 色（推荐）、独立于设备的位图，该位图可能包含在图元文件中。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC">
      <summary>包含变量、函数或函数参数的类型说明以及进程传输信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.desc">
      <summary>包含有关元素的信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.tdesc">
      <summary>标识元素的类型。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION">
      <summary>包含有关元素的信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.idldesc">
      <summary>包含有关远程处理该元素的信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.paramdesc">
      <summary>包含有关参数的信息。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.EXCEPINFO">
      <summary>描述在 IDispatch::Invoke 过程中发生的异常。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrDescription">
      <summary>描述用户可能遇到的错误。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrHelpFile">
      <summary>包含帮助文件的完全限定驱动器、路径和文件名，该帮助文件包含有关错误的更多信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrSource">
      <summary>指示异常源的名称。该名称通常是一个应用程序名称。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.dwHelpContext">
      <summary>指示该主题在帮助文件中的帮助上下文 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pfnDeferredFillIn">
      <summary>表示指向一个函数的指针，该函数采用 <see cref="T:System.Runtime.InteropServices.EXCEPINFO" /> 结构作为参数并返回 HRESULT 值。如果不想推迟填充，则将此字段设置为 null。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pvReserved">
      <summary>此字段为保留字段；必须将其设置为 null。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.scode">
      <summary>描述错误的返回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wCode">
      <summary>表示用于标识错误的错误代码。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wReserved">
      <summary>此字段为保留字段；必须将其设置为 0。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FILETIME">
      <summary>表示从 1601 年 1 月 1 日起的 100 毫微秒间隔数。此结构是一个 64 位值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwHighDateTime">
      <summary>指定 FILETIME 的高 32 位。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwLowDateTime">
      <summary>指定 FILETIME 的低 32 位。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FORMATETC">
      <summary>表示通用剪贴板格式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.cfFormat">
      <summary>指定有意义的特殊剪贴板格式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.dwAspect">
      <summary>指定一个 <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> 枚举常数，用以确定呈现中应包含多少详细信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.lindex">
      <summary>在必须跨页边界拆分数据时指定方位的部分。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.ptd">
      <summary>指定指向 DVTARGETDEVICE 结构的指针，该结构中包含有关该数据所构成的目标设备的信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.tymed">
      <summary>指定一个 <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" /> 枚举常数，该常数指示用于传输对象数据的存储介质的类型。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCDESC">
      <summary>定义函数说明。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.callconv">
      <summary>指定函数的调用约定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParams">
      <summary>计算参数的总数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParamsOpt">
      <summary>计算可选参数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cScodes">
      <summary>计算允许的返回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.elemdescFunc">
      <summary>包含函数的返回类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.funckind">
      <summary>指定函数是虚拟的、静态的还是仅支持调度的。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.invkind">
      <summary>指定属性函数的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgelemdescParam">
      <summary>指示 <see cref="F:System.Runtime.InteropServices.FUNCDESC.cParams" /> 的大小。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgscode">
      <summary>存储函数可在 16 位系统中返回的错误的计数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.memid">
      <summary>标识函数成员 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.oVft">
      <summary>指定 <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_VIRTUAL" /> 在 VTBL 中的偏移量。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.wFuncFlags">
      <summary>指示函数的 <see cref="T:System.Runtime.InteropServices.FUNCFLAGS" />。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCFLAGS">
      <summary>标识定义函数属性的常数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FBINDABLE">
      <summary>支持数据绑定的函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTBIND">
      <summary>最佳表示此对象的函数。一个类型中只能有一个函数可以具有此特性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTCOLLELEM">
      <summary>允许进行一种优化，使编译器查找“abc”类型上名为“xyz”的成员。如果找到这样的成员，而且该成员标志为默认集合的某元素的访问器函数，则生成对该成员函数的调用。可以对调度接口和接口中的成员使用；不能对模块使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDISPLAYBIND">
      <summary>作为可绑定函数显示给用户的函数。还必须设置 <see cref="F:System.Runtime.InteropServices.FUNCFLAGS.FUNCFLAG_FBINDABLE" />。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FHIDDEN">
      <summary>不应将此函数显示给用户，尽管它存在并且为可绑定函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FIMMEDIATEBIND">
      <summary>作为单独的可绑定属性映射。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FNONBROWSABLE">
      <summary>该属性出现在对象浏览器而非属性浏览器中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREPLACEABLE">
      <summary>将该接口标记为具有默认行为。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREQUESTEDIT">
      <summary>设置后，任何对设置此属性的方法的调用都首先导致对 IPropertyNotifySink::OnRequestEdit 的调用。OnRequestEdit 的实现确定是否允许该调用设置属性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FRESTRICTED">
      <summary>此函数不应该是可从宏语言访问的。此标志用于系统级函数或类型浏览器不应显示的函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FSOURCE">
      <summary>该函数返回一个对象，此对象为事件的源。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUIDEFAULT">
      <summary>类型信息成员是在用户界面中显示的默认成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUSESGETLASTERROR">
      <summary>该函数支持 GetLastError。如果在函数过程中发生错误，则调用方可以调用 GetLastError 来检索错误代码。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCKIND">
      <summary>定义如何访问函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_DISPATCH">
      <summary>该函数只能通过 IDispatch 访问。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_NONVIRTUAL">
      <summary>该函数通过 static 地址访问，并采用隐式 this 指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_PUREVIRTUAL">
      <summary>该函数通过虚函数表 (VTBL) 访问，并采用隐式 this 指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_STATIC">
      <summary>该函数通过 static 地址访问，不采用隐式 this 指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_VIRTUAL">
      <summary>该函数与 <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_PUREVIRTUAL" /> 的访问方式相同，不同之处在于该函数具有实现。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IAdviseSink">
      <summary>提供 IAdviseSink 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnClose">
      <summary>向所有已注册的通知接收器通知以下情况：对象已从运行状态更改为加载状态。此方法由服务器调用。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)">
      <summary>向当前已注册通知接收器的所有数据对象通知以下情况：对象中的数据已更改。</summary>
      <param name="format">一个由引用传递的 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />，它描述调用数据对象的格式、目标设备、呈现和存储信息。</param>
      <param name="stgmedium">一个由引用传递的 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />，它为调用数据对象定义存储介质（共用内存、磁盘文件、存储对象、流对象、图形设备接口 (GDI) 对象或未定义介质）和该介质的所属权。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnRename(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>向所有已注册的通知接收器通知以下情况：对象已重命名。此方法由服务器调用。</summary>
      <param name="moniker">一个指针，它指向对象的新的、完整的名字对象上的 IMoniker 接口。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnSave">
      <summary>向所有已注册的通知接收器通知以下情况：对象已保存。此方法由服务器调用。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnViewChange(System.Int32,System.Int32)">
      <summary>向对象的已注册通知接收器通知以下情况：其视图已更改。此方法由服务器调用。</summary>
      <param name="aspect">对象的方位或视图。包含一个从 <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> 枚举中提取的值。</param>
      <param name="index">已更改的视图部分。当前，只有 -1 有效。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IBindCtx">
      <summary>提供 IBindCtx 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.EnumObjectParam(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>枚举字符串，这些字符串是在内部维护的上下文对象参数表的项。</summary>
      <param name="ppenum">此方法返回时，包含对对象参数枚举数的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>返回当前存储在当前绑定上下文中的绑定选项。</summary>
      <param name="pbindopts">指向接收绑定选项的结构的指针。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetObjectParam(System.String,System.Object@)">
      <summary>在内部维护的上下文对象参数表中查找给定的项并返回相应的对象（如果此对象存在的话）。</summary>
      <param name="pszKey">要搜索的对象名称。</param>
      <param name="ppunk">此方法返回时，包含对象接口指针。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetRunningObjectTable(System.Runtime.InteropServices.ComTypes.IRunningObjectTable@)">
      <summary>返回对与此绑定进程相关的运行对象表 (ROT) 的访问权。</summary>
      <param name="pprot">此方法返回时，包含对运行对象表 (ROT) 的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)">
      <summary>将传递的对象注册为已在名字对象操作期间绑定且应在此操作完成之后释放的对象之一。</summary>
      <param name="punk">要为释放而注册的对象。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectParam(System.String,System.Object)">
      <summary>以内部维护的对象指针表中的指定名称注册指定的对象指针。</summary>
      <param name="pszKey">用于注册 <paramref name="punk" /> 的名称。</param>
      <param name="punk">要注册的对象。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.ReleaseBoundObjects">
      <summary>释放所有当前由 <see cref="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)" /> 方法用绑定上下文注册的对象。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectBound(System.Object)">
      <summary>从需要释放的注册对象集中移除该对象。</summary>
      <param name="punk">要为释放而注销的对象。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectParam(System.String)">
      <summary>撤消当前在指定项（位于内部维护的上下文对象参数表中）下可找到的对象的注册（如果该项当前已注册）。</summary>
      <returns>如果从表中成功移除指定键，则为 S_OKHRESULT 值；否则为 S_FALSEHRESULT 值。</returns>
      <param name="pszKey">要注销的项。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.SetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>在绑定上下文中存储参数块。这些参数将应用于稍后使用此绑定上下文的 UCOMIMoniker 操作。</summary>
      <param name="pbindopts">包含要设置的绑定选项的结构。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPoint">
      <summary>提供 IConnectionPoint 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)">
      <summary>在连接点和调用方的接收器对象之间建立一个通知连接。</summary>
      <param name="pUnkSink">对接收器的引用，该接收器为此连接点所管理的输出接口接收调用。</param>
      <param name="pdwCookie">此方法返回时，包含连接 Cookie。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.EnumConnections(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>创建枚举数对象，以便循环访问到此连接点的现有连接。</summary>
      <param name="ppEnum">此方法返回时，包含新创建的枚举数。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionInterface(System.Guid@)">
      <summary>返回由此连接点管理的输出接口的 IID。</summary>
      <param name="pIID">此参数返回时，包含由此连接点管理的输出接口的 IID。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionPointContainer(System.Runtime.InteropServices.ComTypes.IConnectionPointContainer@)">
      <summary>检索指向在概念上拥有此连接点的可连接对象的 IConnectionPointContainer 接口指针。</summary>
      <param name="ppCPC">此参数返回时，包含可连接对象的 IConnectionPointContainer 接口。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Unadvise(System.Int32)">
      <summary>终止先前通过 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> 方法建立的顾问连接。</summary>
      <param name="dwCookie">先前从 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> 方法返回的连接 Cookie。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer">
      <summary>提供 IConnectionPointContainer 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.EnumConnectionPoints(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>创建在可连接对象中支持的所有连接点的枚举数，每个 IID 一个连接点。</summary>
      <param name="ppEnum">此方法返回时，包含枚举数的接口指针。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.FindConnectionPoint(System.Guid@,System.Runtime.InteropServices.ComTypes.IConnectionPoint@)">
      <summary>询问可连接对象是否具有某个特定 IID 的连接点，如果是，则返回指向此连接点的 IConnectionPoint 接口指针。</summary>
      <param name="riid">对输出接口 IID 的引用，此输出接口 IID 的连接点正在被请求。</param>
      <param name="ppCP">此方法返回时，包含管理输出接口 <paramref name="riid" /> 的连接点。该参数未经初始化即被传递。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLDESC">
      <summary>包含在进程之间传输结构元素、参数或函数返回值时所需的信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.dwReserved">
      <summary>保留；设置为 null。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.wIDLFlags">
      <summary>指示描述类型的 <see cref="T:System.Runtime.InteropServices.IDLFLAG" /> 值。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLFLAG">
      <summary>描述如何在进程之间传输结构元素、参数或函数返回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FIN">
      <summary>参数将信息从调用方传递到被调用方。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FLCID">
      <summary>参数是客户端应用程序的本地标识符。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FOUT">
      <summary>参数将信息从被调用方返回到调用方。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FRETVAL">
      <summary>参数是成员的返回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_NONE">
      <summary>不会指定该参数传递还是接收信息。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints">
      <summary>管理 IEnumConnectionPoints 接口的定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>创建与当前枚举数包含相同枚举状态的一个新枚举数。</summary>
      <param name="ppenum">此方法返回时，包含对该新创建的枚举数的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IConnectionPoint[],System.IntPtr)">
      <summary>检索枚举序列中指定数目的项。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 参数与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中返回的 IConnectionPoint 引用的数目。</param>
      <param name="rgelt">此方法返回时，包含对枚举连接的引用。该参数未经初始化即被传递。</param>
      <param name="pceltFetched">此方法返回时，包含对 <paramref name="rgelt" /> 中枚举的连接的实际数目的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Reset">
      <summary>将枚举序列重置到开始处。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Skip(System.Int32)">
      <summary>跳过枚举序列中指定数目的项。</summary>
      <returns>如果跳过的元素数目与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">枚举中要跳过的元素数目。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnections">
      <summary>管理 IEnumConnections 接口的定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>创建与当前枚举数包含相同枚举状态的一个新枚举数。</summary>
      <param name="ppenum">此方法返回时，包含对该新创建的枚举数的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Next(System.Int32,System.Runtime.InteropServices.ComTypes.CONNECTDATA[],System.IntPtr)">
      <summary>检索枚举序列中指定数目的项。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 参数与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中返回的 <see cref="T:System.Runtime.InteropServices.CONNECTDATA" /> 结构的数目。</param>
      <param name="rgelt">此方法返回时，包含对枚举连接的引用。该参数未经初始化即被传递。</param>
      <param name="pceltFetched">此方法返回时，包含对 <paramref name="rgelt" /> 中枚举的连接的实际数目的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Reset">
      <summary>将枚举序列重置到开始处。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Skip(System.Int32)">
      <summary>跳过枚举序列中指定数目的项。</summary>
      <returns>如果跳过的元素数目与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">枚举中要跳过的元素数目。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC">
      <summary>提供 IEnumFORMATETC 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Clone(System.Runtime.InteropServices.ComTypes.IEnumFORMATETC@)">
      <summary>创建与当前枚举数包含相同枚举状态的一个新枚举数。</summary>
      <param name="newEnum">此方法返回时，包含对该新创建的枚举数的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Next(System.Int32,System.Runtime.InteropServices.ComTypes.FORMATETC[],System.Int32[])">
      <summary>检索枚举序列中指定数目的项。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 参数与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中返回的 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 引用的数目。</param>
      <param name="rgelt">此方法返回时，包含对枚举的 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 引用的引用。该参数未经初始化即被传递。</param>
      <param name="pceltFetched">此方法返回时，包含对 <paramref name="rgelt" /> 中枚举的实际引用数的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Reset">
      <summary>将枚举序列重置到开始处。</summary>
      <returns>具有值 S_OK 的 HRESULT。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Skip(System.Int32)">
      <summary>跳过枚举序列中指定数目的项。</summary>
      <returns>如果跳过的元素数目与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">枚举中要跳过的元素数目。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumMoniker">
      <summary>管理 IEnumMoniker 接口的定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Clone(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>创建与当前枚举数包含相同枚举状态的一个新枚举数。</summary>
      <param name="ppenum">此方法返回时，包含对该新创建的枚举数的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker[],System.IntPtr)">
      <summary>检索枚举序列中指定数目的项。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 参数与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中返回的名字对象的数目。</param>
      <param name="rgelt">此方法返回时，包含对枚举的名字对象的引用。该参数未经初始化即被传递。</param>
      <param name="pceltFetched">此方法返回时，包含对在 <paramref name="rgelt" /> 中枚举的名字对象的实际数目的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Reset">
      <summary>将枚举序列重置到开始处。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Skip(System.Int32)">
      <summary>跳过枚举序列中指定数目的项。</summary>
      <returns>如果跳过的元素数目与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">枚举中要跳过的元素数目。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumString">
      <summary>管理 IEnumString 接口的定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Clone(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>创建与当前枚举数包含相同枚举状态的一个新枚举数。</summary>
      <param name="ppenum">此方法返回时，包含对该新创建的枚举数的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Next(System.Int32,System.String[],System.IntPtr)">
      <summary>检索枚举序列中指定数目的项。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 参数与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中返回的字符串的数目。</param>
      <param name="rgelt">此方法返回时，包含对枚举字符串的引用。该参数未经初始化即被传递。</param>
      <param name="pceltFetched">此方法返回时，包含对在 <paramref name="rgelt" /> 中枚举的字符串的实际数目的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Reset">
      <summary>将枚举序列重置到开始处。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Skip(System.Int32)">
      <summary>跳过枚举序列中指定数目的项。</summary>
      <returns>如果跳过的元素数目与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">枚举中要跳过的元素数目。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT">
      <summary>管理 IEnumVARIANT 接口的定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Clone">
      <summary>创建与当前枚举数包含相同枚举状态的一个新枚举数。</summary>
      <returns>对新创建枚举数的 <see cref="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT" /> 引用。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Next(System.Int32,System.Object[],System.IntPtr)">
      <summary>检索枚举序列中指定数目的项。</summary>
      <returns>如果 <paramref name="pceltFetched" /> 参数与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">要在 <paramref name="rgelt" /> 中返回的元素的数目。</param>
      <param name="rgVar">此方法返回时，包含对枚举元素的引用。该参数未经初始化即被传递。</param>
      <param name="pceltFetched">此方法返回时，包含对在 <paramref name="rgelt" /> 中枚举的元素的实际数目的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Reset">
      <summary>将枚举序列重置到开始处。</summary>
      <returns>具有值 S_OK 的 HRESULT。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Skip(System.Int32)">
      <summary>跳过枚举序列中指定数目的项。</summary>
      <returns>如果跳过的元素的数目与 <paramref name="celt" /> 参数相等，则为 S_OK；否则为 S_FALSE。</returns>
      <param name="celt">枚举中要跳过的元素数目。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMoniker">
      <summary>提供 IMoniker 接口的托管定义，具有 IPersist 和 IPersistStream 中的 COM 功能。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToObject(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>使用名字对象绑定到它所标识的对象。</summary>
      <param name="pbc">对在此绑定操作中使用的绑定上下文对象上的 IBindCtx 接口的引用。</param>
      <param name="pmkToLeft">如果名字对象是复合名字对象的一部分，则为对当前名字对象左边的名字对象的引用。</param>
      <param name="riidResult">接口的接口标识符 (IID)，客户端打算使用该接口与名字对象标识的对象进行通信。</param>
      <param name="ppvResult">此方法返回时，包含对 <paramref name="riidResult" /> 请求的接口的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToStorage(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>检索指向存储（该存储包含名字对象所标识的对象）的接口指针。</summary>
      <param name="pbc">对在此绑定操作过程中使用的绑定上下文对象上的 IBindCtx 接口的引用。</param>
      <param name="pmkToLeft">如果名字对象是复合名字对象的一部分，则为对当前名字对象左边的名字对象的引用。</param>
      <param name="riid">所请求的存储接口的接口标识符 (IID)。</param>
      <param name="ppvObj">此方法返回时，包含对 <paramref name="riid" /> 请求的接口的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.CommonPrefixWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>基于此名字对象与另一名字对象共有的公共前缀创建新的名字对象。</summary>
      <param name="pmkOther">对另一名字对象上的 IMoniker 接口的引用，将使用该名字对象与当前名字对象进行比较，以获得公共前缀。</param>
      <param name="ppmkPrefix">此方法返回时，包含作为当前名字对象和 <paramref name="pmkOther" /> 的公共前缀的名字对象。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ComposeWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Boolean,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>将当前名字对象与另一名字对象组合，创建一个新的复合名字对象。</summary>
      <param name="pmkRight">对名字对象上的 IMoniker 接口的引用，该引用将追加到当前名字对象的末尾。</param>
      <param name="fOnlyIfNotGeneric">true 指示调用方需要非通用复合。仅当 <paramref name="pmkRight" /> 为当前名字对象可以采用不同于构成通用复合的方式与其组合的名字对象类时，该操作才继续。而 false 指示该方法可以在必要时创建通用复合。</param>
      <param name="ppmkComposite">此方法返回时，包含对结果复合名字对象的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Enum(System.Boolean,System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>提供一个指向枚举器（该枚举器可枚举复合名字对象的组件）的指针。</summary>
      <param name="fForward">true 表示按从左到右的顺序枚举名字对象。而 false 表示按从右到左的顺序枚举。</param>
      <param name="ppenumMoniker">此方法返回时，包含对名字对象的枚举数对象的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetClassID(System.Guid@)">
      <summary>检索对象的类标识符 (CLSID)。</summary>
      <param name="pClassID">此方法返回时，包含 CLSID。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String@)">
      <summary>获取显示名称，该名称是当前名字对象的用户可读表示形式。</summary>
      <param name="pbc">对在此操作中使用的绑定上下文的引用。</param>
      <param name="pmkToLeft">如果名字对象是复合名字对象的一部分，则为对当前名字对象左边的名字对象的引用。</param>
      <param name="ppszDisplayName">此方法返回时，包含显示名称字符串。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetSizeMax(System.Int64@)">
      <summary>返回保存该对象所需的流的大小（以字节为单位）。</summary>
      <param name="pcbSize">此方法返回时，包含 long 值，该值指示保存此对象所需的流的大小（以字节为单位）。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>提供一个数字，该数字表示当前名字对象所标识的对象的上次更改时间。</summary>
      <param name="pbc">对要在此绑定操作中使用的绑定上下文的引用。</param>
      <param name="pmkToLeft">如果名字对象是复合名字对象的一部分，则为对当前名字对象左边的名字对象的引用。</param>
      <param name="pFileTime">此方法返回时，包含上次更改时间。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Hash(System.Int32@)">
      <summary>使用该名字对象的内部状态计算 32 位整数。</summary>
      <param name="pdwHash">此方法返回时，包含此名字对象的哈希值。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Inverse(System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>提供一个名字对象，该名字对象在被复合到当前名字对象或一个结构相似的名字对象的右边时，将不复合到任何对象。</summary>
      <param name="ppmk">此方法返回时，包含一个名字对象，它是当前名字对象的逆命题。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsDirty">
      <summary>检查该对象自上次保存以来所发生的更改。</summary>
      <returns>如果该对象已更改，则为 S_OKHRESULT 值；否则为 S_FALSEHRESULT 值。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsEqual(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>将当前名字对象与指定的名字对象进行比较，并指示它们是否相同。</summary>
      <returns>如果名字对象相同，则为 S_OKHRESULT 值；否则为 S_FALSEHRESULT 值。</returns>
      <param name="pmkOtherMoniker">对用于比较的名字对象的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsRunning(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>确定由当前名字对象标识的对象当前是否已加载并正在运行。</summary>
      <returns>如果名字对象处于运行状态，则为 S_OKHRESULT 值；如果名字对象不处于运行状态，则为 S_FALSEHRESULT 值；否则为 E_UNEXPECTEDHRESULT 值。</returns>
      <param name="pbc">对要在此绑定操作中使用的绑定上下文的引用。</param>
      <param name="pmkToLeft">如果当前名字对象是复合名字对象的一部分，则为对当前名字对象左边的名字对象的引用。</param>
      <param name="pmkNewlyRunning">对最近添加到运行对象表 (ROT) 的名字对象的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsSystemMoniker(System.Int32@)">
      <summary>指示该名字对象是否是系统提供的名字对象类之一的对象。</summary>
      <returns>如果名字对象为系统名字对象，则为 S_OKHRESULT 值；否则为 S_FALSEHRESULT 值。</returns>
      <param name="pdwMksys">此方法返回时，包含指向一个整数的指针，该整数是 MKSYS 枚举值之一并引用 COM 名字对象类之一。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Load(System.Runtime.InteropServices.ComTypes.IStream)">
      <summary>从以前保存对象的流中初始化对象。</summary>
      <param name="pStm">从中加载对象的流。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>读取指定的显示名称中 <see cref="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)" /> 能够理解的全部字符并生成一个与读取的部分相对应的名字对象。</summary>
      <param name="pbc">对要在此绑定操作中使用的绑定上下文的引用。</param>
      <param name="pmkToLeft">对迄今为止已经从显示名称生成的名字对象的引用。</param>
      <param name="pszDisplayName">对包含要分析的剩余显示名称的字符串的引用。</param>
      <param name="pchEaten">此方法返回时，包含分析 <paramref name="pszDisplayName" /> 时所使用的字符数。该参数未经初始化即被传递。</param>
      <param name="ppmkOut">此方法返回时，包含对从 <paramref name="pszDisplayName" /> 生成的名字对象的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Reduce(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>返回简化的名字对象，它是与当前名字对象引用同一对象但能够用相等或更高的效率绑定的另一个名字对象。</summary>
      <param name="pbc">对在此绑定操作中使用的绑定上下文中的 IBindCtx 接口的引用。</param>
      <param name="dwReduceHowFar">指定当前名字对象简化程度的值。</param>
      <param name="ppmkToLeft">对当前名字对象左边的名字对象的引用。</param>
      <param name="ppmkReduced">此方法返回时，包含对当前名字对象的简化形式的引用（如果发生错误或当前名字对象被简化为无，则可以为 null）。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.RelativePathTo(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>提供一个名字对象，该名字对象在被追加到当前名字对象（或一个有相似结构的名字对象）时生成指定名字对象。</summary>
      <param name="pmkOther">对应该对其采用相对路径的名字对象的引用。</param>
      <param name="ppmkRelPath">此方法返回时，包含对相关名字对象的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Save(System.Runtime.InteropServices.ComTypes.IStream,System.Boolean)">
      <summary>将对象保存到指定流。</summary>
      <param name="pStm">将对象保存到的流。</param>
      <param name="fClearDirty">如果要在保存完成之后清除修改后的标志，则为 true；否则为 false</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS">
      <summary>定义类型的实现或继承接口的特性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULT">
      <summary>该接口或调度接口表示源或接收器的默认值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULTVTABLE">
      <summary>接收器通过虚函数表 (VTBL) 接收事件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FRESTRICTED">
      <summary>不应显示该成员，用户也不应可对该成员进行编程。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FSOURCE">
      <summary>调用而不是实现 coclass 的此成员。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND">
      <summary>指定如何通过 IDispatch::Invoke 来调用函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_FUNC">
      <summary>使用常规函数调用语法来调用该成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYGET">
      <summary>使用常规属性访问语法来调用该函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUT">
      <summary>使用属性值赋值语法来调用该函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUTREF">
      <summary>使用属性引用赋值语法来调用该函数。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IPersistFile">
      <summary>提供具有 IPersist 中的功能的 IPersistFile 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetClassID(System.Guid@)">
      <summary>检索对象的类标识符 (CLSID)。</summary>
      <param name="pClassID">此方法返回时，包含对 CLSID 的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetCurFile(System.String@)">
      <summary>检索该对象的当前工作文件的绝对路径，或者，如果没有当前工作文件，则检索该对象的默认文件名提示。</summary>
      <param name="ppszFileName">此方法返回时，包含指向一个以零终止的字符串的指针的地址，该字符串中包含当前文件的路径或者默认的文件名提示（如 *.txt）。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.IsDirty">
      <summary>检查对象自上次保存到其当前文件以来是否更改。</summary>
      <returns>如果文件自上次保存以来已经更改，则为 S_OK；如果文件自上次保存以来尚未更改，则为 S_FALSE。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Load(System.String,System.Int32)">
      <summary>打开指定文件并从文件内容初始化对象。</summary>
      <param name="pszFileName">以零结尾的字符串，包含要打开的文件的绝对路径。</param>
      <param name="dwMode">STGM 枚举中值的组合，指示用来打开 <paramref name="pszFileName" /> 的访问模式。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Save(System.String,System.Boolean)">
      <summary>将该对象的副本保存到指定文件。</summary>
      <param name="pszFileName">以零结尾的字符串，包含将该对象保存到的文件的绝对路径。</param>
      <param name="fRemember">将 <paramref name="pszFileName" /> 参数用作当前工作文件时为 true；否则为 false。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.SaveCompleted(System.String)">
      <summary>通知该对象它可以写入它的文件。</summary>
      <param name="pszFileName">以前保存该对象的文件的绝对路径。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IRunningObjectTable">
      <summary>提供 IRunningObjectTable 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.EnumRunning(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>枚举当前注册为运行对象的对象。</summary>
      <param name="ppenumMoniker">此方法返回时，包含运行对象表 (ROT) 的新枚举器。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetObject(System.Runtime.InteropServices.ComTypes.IMoniker,System.Object@)">
      <summary>如果提供的对象名注册为运行对象，则返回该注册对象。</summary>
      <returns>一个 HRESULT 值，该值指示操作是成功还是失败。</returns>
      <param name="pmkObjectName">对要在运行对象表 (ROT) 中搜索的名字对象的引用。</param>
      <param name="ppunkObject">此方法返回时，包含请求的运行对象。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>在运行对象表 (ROT) 中搜索此名字对象并报告所记录的更改时间（如果存在的话）。</summary>
      <returns>一个 HRESULT 值，该值指示操作是成功还是失败。</returns>
      <param name="pmkObjectName">对要在运行对象表 (ROT) 中搜索的名字对象的引用。</param>
      <param name="pfiletime">此对象返回时，包含对象的上次更改时间。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.IsRunning(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>确定指定名字对象当前是否在运行对象表 (ROT) 中注册。</summary>
      <returns>一个 HRESULT 值，该值指示操作是成功还是失败。</returns>
      <param name="pmkObjectName">对要在运行对象表 (ROT) 中搜索的名字对象的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>记录特定对象发生更改的时间，以便 IMoniker::GetTimeOfLastChange 可以报告相应更改时间。</summary>
      <param name="dwRegister">已更改对象的运行对象表 (ROT) 项。</param>
      <param name="pfiletime">对对象的上次更改时间的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Register(System.Int32,System.Object,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>注册提供的对象已进入运行状态。</summary>
      <returns>一个值，该值可用于在随后对 <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)" /> 或 <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)" /> 的调用中标识此 ROT 项。</returns>
      <param name="grfFlags">指定运行对象表 (ROT) 对 <paramref name="punkObject" /> 的引用是弱引用还是强引用，并通过对象在 ROT 中的项控制对它的访问。</param>
      <param name="punkObject">对注册为运行对象的对象的引用。</param>
      <param name="pmkObjectName">对标识 <paramref name="punkObject" /> 的名字对象的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)">
      <summary>从运行对象表 (ROT) 中注销指定的对象。</summary>
      <param name="dwRegister">要撤消的运行对象表 (ROT) 项。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IStream">
      <summary>提供具有 ISequentialStream 功能的 IStream 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
      <summary>创建一个新的流对象，该流对象具有自己的查找指针且该指针与原始流引用相同的字节。</summary>
      <param name="ppstm">此方法返回时，包含新的流对象。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)">
      <summary>确保对在事务处理模式下打开的流对象所做的任何更改都能反映在父级存储中。</summary>
      <param name="grfCommitFlags">控制流对象更改的提交方式的值。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
      <summary>将指定数量的字节从该流中的当前查找指针复制到另一个流中的当前查找指针。</summary>
      <param name="pstm">对目标流的引用。</param>
      <param name="cb">要从源流复制的字节数。</param>
      <param name="pcbRead">成功返回时包含从源读取的实际字节数。</param>
      <param name="pcbWritten">成功返回时包含写入到目标的实际字节数。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>限制对流中指定字节范围的访问。</summary>
      <param name="libOffset">范围开始位置的字节偏移量。</param>
      <param name="cb">要限制的范围的长度（以字节为单位）。</param>
      <param name="dwLockType">所请求的对访问该范围的限制。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Read(System.Byte[],System.Int32,System.IntPtr)">
      <summary>将指定的字节数从流对象读入从当前查找指针开始的内存。</summary>
      <param name="pv">此方法返回时，包含从流中读取的数据。该参数未经初始化即被传递。</param>
      <param name="cb">要从流对象中读取的字节数。</param>
      <param name="pcbRead">指向 ULONG 变量的指针，该变量接收从流对象中读取的实际字节数。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Revert">
      <summary>放弃自从上次 <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)" /> 调用以来对事务处理流所做的所有更改。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Seek(System.Int64,System.Int32,System.IntPtr)">
      <summary>将查找指针更改到相对于流的开头、流的结尾或当前查找指针的新位置。</summary>
      <param name="dlibMove">要添加到 <paramref name="dwOrigin" /> 的置换。</param>
      <param name="dwOrigin">查找的起始地址。该起始地址可以是文件的开头、当前查找指针或文件的结尾。</param>
      <param name="plibNewPosition">成功返回时包含从流的开头算起的查找指针的偏移量。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.SetSize(System.Int64)">
      <summary>更改流对象的大小。</summary>
      <param name="libNewSize">流的新大小以字节数表示。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
      <summary>检索此流的 <see cref="T:System.Runtime.InteropServices.STATSTG" /> 结构。</summary>
      <param name="pstatstg">此方法返回时，包含描述此流对象的 STATSTG 结构。该参数未经初始化即被传递。</param>
      <param name="grfStatFlag">在 STATSTG 结构中指定此方法不返回的成员，这样就省去了一些内存分配操作。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>移除对先前使用 <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)" /> 方法限制的字节范围的访问限制。</summary>
      <param name="libOffset">范围开始位置的字节偏移量。</param>
      <param name="cb">要限制的范围的长度（以字节为单位）。</param>
      <param name="dwLockType">先前设置在范围上的访问限制。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Write(System.Byte[],System.Int32,System.IntPtr)">
      <summary>将指定数量的字节写入从当前查找指针开始的流对象。</summary>
      <param name="pv">要将此流写入的缓冲区。</param>
      <param name="cb">要写入此流的字节数。</param>
      <param name="pcbWritten">成功返回时包含写入此流对象的实际的字节数。如果调用方将此指针设置为 <see cref="F:System.IntPtr.Zero" />，则此方法不提供写入的实际字节数。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeComp">
      <summary>提供 ITypeComp 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.Bind(System.String,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.DESCKIND@,System.Runtime.InteropServices.ComTypes.BINDPTR@)">
      <summary>将名称映射到类型的成员，或者绑定类型库中包含的全局变量和函数。</summary>
      <param name="szName">要绑定的名称。</param>
      <param name="lHashVal">由 LHashValOfNameSys 计算的 <paramref name="szName" /> 的哈希值。</param>
      <param name="wFlags">标志字，包含一个或多个在 INVOKEKIND 枚举中定义的调用标志。</param>
      <param name="ppTInfo">此方法返回时，包含对类型说明（包含将其绑定到的项）的引用（如果返回了 FUNCDESC 或 VARDESC）。该参数未经初始化即被传递。</param>
      <param name="pDescKind">此方法返回时，包含对 DESCKIND 枚举数的引用，该枚举数指示绑定到的名称是 VARDESC、FUNCDESC 还是 TYPECOMP。该参数未经初始化即被传递。</param>
      <param name="pBindPtr">此方法返回时，包含对绑定到的 VARDESC、FUNCDESC 或 ITypeComp 接口的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.BindType(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>绑定到包含在类型库中的类型说明。</summary>
      <param name="szName">要绑定的名称。</param>
      <param name="lHashVal">由 LHashValOfNameSys 确定的 <paramref name="szName" /> 的哈希值。</param>
      <param name="ppTInfo">此方法返回时，包含对将 <paramref name="szName" /> 绑定到的类型的 ITypeInfo 的引用。该参数未经初始化即被传递。</param>
      <param name="ppTComp">此方法返回时，包含对 ITypeComp 变量的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo">
      <summary>提供组件自动化 ITypeInfo 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>检索静态函数或变量（如那些在 DLL 中定义的静态函数或变量）的地址。</summary>
      <param name="memid">要检索的 static 成员地址的成员 ID。</param>
      <param name="invKind">指定该成员是否为属性（如果是，还将指定它属于哪种属性）的 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值之一。</param>
      <param name="ppv">此方法返回时，包含对 static 成员的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>创建描述组件类 (coclass) 的类型的新实例。</summary>
      <param name="pUnkOuter">作为控制 IUnknown 的对象。</param>
      <param name="riid">接口的 IID，调用方将使用该接口与结果对象进行通信。</param>
      <param name="ppvObj">此方法返回时，包含对已创建对象的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>检索类型库，该类型库包含此类型说明和它在该类型库中的索引。</summary>
      <param name="ppTLB">此方法返回时，包含对包含类型库的引用。该参数未经初始化即被传递。</param>
      <param name="pIndex">此方法返回时，包含对包含类型库中的类型说明的索引的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>检索 DLL 中函数的入口点的说明或规范。</summary>
      <param name="memid">成员函数的 ID，要返回该成员函数的 DLL 入口说明。</param>
      <param name="invKind">指定由 <paramref name="memid" /> 标识的成员种类的 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值之一。</param>
      <param name="pBstrDllName">如果不为 null，则此函数将 <paramref name="pBstrDllName" /> 设置为包含 DLL 名称的 BSTR。</param>
      <param name="pBstrName">如果不为 null，则此函数将 <paramref name="lpbstrName" /> 设置为包含入口点名称的 BSTR。</param>
      <param name="pwOrdinal">如果不为 null，并且此函数是按序号定义的，则 <paramref name="lpwOrdinal" /> 被设置为指向该序号。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>从文档字符串、完整的帮助文件名和路径以及帮助主题的上下文 ID 中检索指定的类型说明。</summary>
      <param name="index">要返回其文档的成员的 ID。</param>
      <param name="strName">此方法返回时，包含项方法的名称。该参数未经初始化即被传递。</param>
      <param name="strDocString">此方法返回时，包含指定项的文档字符串。该参数未经初始化即被传递。</param>
      <param name="dwHelpContext">此方法返回时，包含对与指定项相关联的帮助上下文的引用。该参数未经初始化即被传递。</param>
      <param name="strHelpFile">此方法返回时，包含帮助文件的完全限定名。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>检索包含有关指定函数的信息的 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 结构。</summary>
      <param name="index">要返回的函数说明的索引。</param>
      <param name="ppFuncDesc">此方法返回时，包含对描述指定函数的 FUNCDESC 结构的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>在成员名和成员 ID 之间以及参数名和参数 ID 之间映射。</summary>
      <param name="rgszNames">要映射的名称数组。</param>
      <param name="cNames">要映射的名称计数。</param>
      <param name="pMemId">此方法返回时，包含对在其中放置名称映射的数组的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>在类型说明中检索某个已实现的接口或基接口的 <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> 值。</summary>
      <param name="index">已实现的接口或基接口的索引。</param>
      <param name="pImplTypeFlags">此方法返回时，包含对 IMPLTYPEFLAGS 枚举的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)">
      <summary>检索封送处理信息。</summary>
      <param name="memid">成员 ID，它指示需要哪些封送处理信息。</param>
      <param name="pBstrMops">此方法返回时，包含对 opcode 字符串的引用，该字符串用于封送处理由引用类型说明描述的结构的字段；如果没有要返回的信息，则返回 null。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>检索具有与指定函数 ID 相对应的指定成员 ID（或者属性或方法的名称及其参数）的变量。</summary>
      <param name="memid">要返回其名称的成员的 ID。</param>
      <param name="rgBstrNames">此方法返回时，包含与成员相关联的名称。该参数未经初始化即被传递。</param>
      <param name="cMaxNames">
        <paramref name="rgBstrNames" /> 数组的长度。</param>
      <param name="pcNames">此方法返回时，包含 <paramref name="rgBstrNames" /> 数组中的名称数。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>检索被引用的类型说明（如果该类型说明引用其他类型说明）。</summary>
      <param name="hRef">要返回的被引用类型说明的句柄。</param>
      <param name="ppTI">此方法返回时，包含被引用的类型说明。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>检索实现的接口类型的类型说明（如果类型说明描述 COM 类）。</summary>
      <param name="index">返回其句柄的已实现类型的索引。</param>
      <param name="href">此方法返回时，包含对已实现接口的句柄的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>检索包含类型说明的特性的 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 结构。</summary>
      <param name="ppTypeAttr">此方法返回时，包含对包含此类型说明的特性的结构的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>检索类型说明的 ITypeComp 接口，此接口使客户端编译器可以绑定到类型说明的成员。</summary>
      <param name="ppTComp">此方法返回时，包含对包含类型库的 ITypeComp 接口的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>检索描述指定变量的 VARDESC 结构。</summary>
      <param name="index">要返回的变量说明的索引。</param>
      <param name="ppVarDesc">此方法返回时，包含对描述指定变量的 VARDESC 结构的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>调用对象的方法或访问对象的属性，该方法或属性实现由类型说明描述的接口。</summary>
      <param name="pvInstance">对由此类型说明描述的接口的引用。</param>
      <param name="memid">用于标识接口成员的值。</param>
      <param name="wFlags">描述 Invoke 调用的上下文的标志。</param>
      <param name="pDispParams">对结构的引用，该结构包含一个参数数组、一个命名参数的 DISPID 数组和每个数组中元素数的计数。</param>
      <param name="pVarResult">对用于存储结果的位置的引用。如果 <paramref name="wFlags" /> 指定 DISPATCH_PROPERTYPUT 或 DISPATCH_PROPERTYPUTREF，则忽略 <paramref name="pVarResult" />。如果不需要任何结果，则设置为 null。</param>
      <param name="pExcepInfo">指向异常信息结构的指针，该结构仅在返回 DISP_E_EXCEPTION 时才被填充。</param>
      <param name="puArgErr">如果 Invoke 返回 DISP_E_TYPEMISMATCH，则 <paramref name="puArgErr" /> 指示具有错误类型的参数的 <paramref name="rgvarg" /> 中的索引。如果多个参数返回错误，则 <paramref name="puArgErr" /> 仅指示第一个具有错误的参数。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>释放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" /> 方法返回的一个 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 结构。</summary>
      <param name="pFuncDesc">对要释放的 FUNCDESC 结构的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>释放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" /> 方法返回的一个 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 结构。</summary>
      <param name="pTypeAttr">对要释放的 TYPEATTR 结构的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>释放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" /> 方法返回的一个 VARDESC 结构。</summary>
      <param name="pVarDesc">对要释放的 VARDESC 结构的引用。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo2">
      <summary>提供 ITypeInfo2 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>检索静态函数或变量（如那些在 DLL 中定义的静态函数或变量）的地址。</summary>
      <param name="memid">要检索的 static 成员地址的成员 ID。</param>
      <param name="invKind">指定该成员是否为属性（如果是，还将指定它属于哪种属性）的 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值之一。</param>
      <param name="ppv">此方法返回时，包含对 static 成员的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>创建描述组件类 (coclass) 的类型的新实例。</summary>
      <param name="pUnkOuter">作为控制 IUnknown 的对象。</param>
      <param name="riid">接口的 IID，调用方将使用该接口与结果对象进行通信。</param>
      <param name="ppvObj">此方法返回时，包含对已创建对象的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllCustData(System.IntPtr)">
      <summary>获取库的所有自定义数据项。</summary>
      <param name="pCustData">一个指向包含所有自定义数据项的 CUSTDATA 的指针。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllFuncCustData(System.Int32,System.IntPtr)">
      <summary>从指定函数中获取所有自定义数据。</summary>
      <param name="index">用于为其获取自定义数据的函数的索引。</param>
      <param name="pCustData">一个指向包含所有自定义数据项的 CUSTDATA 的指针。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllImplTypeCustData(System.Int32,System.IntPtr)">
      <summary>为指定的实现类型获取所有自定义数据。</summary>
      <param name="index">自定义数据的实现类型的索引。</param>
      <param name="pCustData">一个指向包含所有自定义数据项的 CUSTDATA 的指针。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllParamCustData(System.Int32,System.Int32,System.IntPtr)">
      <summary>为指定的函数参数获取所有自定义数据。</summary>
      <param name="indexFunc">用于为其获取自定义数据的函数的索引。</param>
      <param name="indexParam">用于为其获取自定义数据的此函数的参数的索引。</param>
      <param name="pCustData">一个指向包含所有自定义数据项的 CUSTDATA 的指针。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllVarCustData(System.Int32,System.IntPtr)">
      <summary>为自定义数据获取变量。</summary>
      <param name="index">用于为其获取自定义数据的变量的索引。</param>
      <param name="pCustData">一个指向包含所有自定义数据项的 CUSTDATA 的指针。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>检索类型库，该类型库包含此类型说明和它在该类型库中的索引。</summary>
      <param name="ppTLB">此方法返回时，包含对包含类型库的引用。该参数未经初始化即被传递。</param>
      <param name="pIndex">此方法返回时，包含对包含类型库中的类型说明的索引的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetCustData(System.Guid@,System.Object@)">
      <summary>获取自定义数据。</summary>
      <param name="guid">用于标识数据的 GUID。</param>
      <param name="pVarVal">此方法返回时，包含一个指定在何处放置检索到的数据的 Object。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>检索 DLL 中函数的入口点的说明或规范。</summary>
      <param name="memid">成员函数的 ID，要返回该成员函数的 DLL 入口说明。</param>
      <param name="invKind">指定由 <paramref name="memid" /> 标识的成员种类的 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值之一。</param>
      <param name="pBstrDllName">如果不为 null，则此函数将 <paramref name="pBstrDllName" /> 设置为包含 DLL 名称的 BSTR。</param>
      <param name="pBstrName">如果不为 null，则此函数将 <paramref name="lpbstrName" /> 设置为包含入口点名称的 BSTR。</param>
      <param name="pwOrdinal">如果不为 null，并且此函数是按序号定义的，则 <paramref name="lpwOrdinal" /> 被设置为指向该序号。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>从文档字符串、完整的帮助文件名和路径以及帮助主题的上下文 ID 中检索指定的类型说明。</summary>
      <param name="index">要返回其文档的成员的 ID。</param>
      <param name="strName">此方法返回时，包含项方法的名称。该参数未经初始化即被传递。</param>
      <param name="strDocString">此方法返回时，包含指定项的文档字符串。该参数未经初始化即被传递。</param>
      <param name="dwHelpContext">此方法返回时，包含对与指定项相关联的帮助上下文的引用。该参数未经初始化即被传递。</param>
      <param name="strHelpFile">此方法返回时，包含帮助文件的完全限定名。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>检索文档字符串、完整的帮助文件名和路径、要使用的本地化上下文，以及帮助文件中的库帮助主题的上下文 ID。</summary>
      <param name="memid">类型说明的成员标识符。</param>
      <param name="pbstrHelpString">此方法返回时，包含一个 BSTR，其中包含指定项的名称。如果调用方不需要该项名称，则 <paramref name="pbstrHelpString" /> 可以为 null。该参数未经初始化即被传递。</param>
      <param name="pdwHelpStringContext">此方法返回时，包含帮助本地化上下文。如果调用方不需要该帮助上下文，则 <paramref name="pdwHelpStringContext" /> 可以为 null。该参数未经初始化即被传递。</param>
      <param name="pbstrHelpStringDll">此方法返回时，包含一个 BSTR，其中包含特定文件（包含帮助文件所使用的 DLL）的完全限定名。如果调用方不需要该文件名，则 <paramref name="pbstrHelpStringDll" /> 可以为 null。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>从指定函数中获取自定义数据。</summary>
      <param name="index">用于为其获取自定义数据的函数的索引。</param>
      <param name="guid">用于标识数据的 GUID。</param>
      <param name="pVarVal">此方法返回时，包含一个指定在何处放置数据的 Object。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>检索包含有关指定函数的信息的 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 结构。</summary>
      <param name="index">要返回的函数说明的索引。</param>
      <param name="ppFuncDesc">此方法返回时，包含对描述指定函数的 FUNCDESC 结构的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncIndexOfMemId(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.Int32@)">
      <summary>基于已知 DISPID 绑定至特定成员，其中成员名称是未知的（例如，绑定至默认成员时）。</summary>
      <param name="memid">成员标识符。</param>
      <param name="invKind">指定由 memid 标识的成员种类的 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 值之一。</param>
      <param name="pFuncIndex">此方法返回时，将索引纳入函数中。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>在成员名和成员 ID 之间以及参数名和参数 ID 之间映射。</summary>
      <param name="rgszNames">要映射的名称数组。</param>
      <param name="cNames">要映射的名称计数。</param>
      <param name="pMemId">此方法返回时，包含对在其中放置名称映射的数组的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>获取自定义数据的实现类型。</summary>
      <param name="index">自定义数据的实现类型的索引。</param>
      <param name="guid">用于标识数据的 GUID。</param>
      <param name="pVarVal">此方法返回时，包含一个指定在何处放置检索到的数据的 Object。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>在类型说明中检索某个已实现的接口或基接口的 <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> 值。</summary>
      <param name="index">已实现的接口或基接口的索引。</param>
      <param name="pImplTypeFlags">此方法返回时，包含对 IMPLTYPEFLAGS 枚举的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetMops(System.Int32,System.String@)">
      <summary>检索封送处理信息。</summary>
      <param name="memid">成员 ID，它指示需要哪些封送处理信息。</param>
      <param name="pBstrMops">此方法返回时，包含对 opcode 字符串的引用，该字符串用于封送处理由引用类型说明描述的结构的字段；如果没有要返回的信息，则返回 null。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>检索具有与指定函数 ID 相对应的指定成员 ID（或者属性或方法的名称及其参数）的变量。</summary>
      <param name="memid">要返回其名称的成员的 ID。</param>
      <param name="rgBstrNames">此方法返回时，包含与成员相关联的名称。该参数未经初始化即被传递。</param>
      <param name="cMaxNames">
        <paramref name="rgBstrNames" /> 数组的长度。</param>
      <param name="pcNames">此方法返回时，包含 <paramref name="rgBstrNames" /> 数组中的名称数。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetParamCustData(System.Int32,System.Int32,System.Guid@,System.Object@)">
      <summary>获取指定的自定义数据参数。</summary>
      <param name="indexFunc">用于为其获取自定义数据的函数的索引。</param>
      <param name="indexParam">用于为其获取自定义数据的此函数的参数的索引。</param>
      <param name="guid">用于标识数据的 GUID。</param>
      <param name="pVarVal">此方法返回时，包含一个指定在何处放置检索到的数据的 Object。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>检索被引用的类型说明（如果该类型说明引用其他类型说明）。</summary>
      <param name="hRef">要返回的被引用类型说明的句柄。</param>
      <param name="ppTI">此方法返回时，包含被引用的类型说明。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>检索已实现接口类型的类型说明（如果类型说明描述 COM 类）。</summary>
      <param name="index">返回其句柄的已实现类型的索引。</param>
      <param name="href">此方法返回时，包含对已实现接口的句柄的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeAttr(System.IntPtr@)">
      <summary>检索包含类型说明的特性的 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 结构。</summary>
      <param name="ppTypeAttr">此方法返回时，包含对包含此类型说明的特性的结构的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>检索类型说明的 ITypeComp 接口，此接口使客户端编译器可以绑定到类型说明的成员。</summary>
      <param name="ppTComp">此方法返回时，包含对包含类型库的 ITypeComp 的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeFlags(System.Int32@)">
      <summary>返回类型标志，而不进行任何分配。此方法返回 DWORD 类型标志，该标志将扩展类型标志，而不会增加 TYPEATTR（类型特性）。</summary>
      <param name="pTypeFlags">此方法返回时，包含对 TYPEFLAG 的 DWORD 引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeKind(System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>快速返回 TYPEKIND 枚举，而不进行任何分配。</summary>
      <param name="pTypeKind">此方法返回时，包含对 TYPEKIND 枚举的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>为自定义数据获取变量。</summary>
      <param name="index">用于为其获取自定义数据的变量的索引。</param>
      <param name="guid">用于标识数据的 GUID。</param>
      <param name="pVarVal">此方法返回时，包含一个指定在何处放置检索到的数据的 Object。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>检索描述指定变量的 VARDESC 结构。</summary>
      <param name="index">要返回的变量说明的索引。</param>
      <param name="ppVarDesc">此方法返回时，包含对描述指定变量的 VARDESC 结构的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarIndexOfMemId(System.Int32,System.Int32@)">
      <summary>基于已知 DISPID 绑定至特定成员，其中成员名称是未知的（例如，绑定至默认成员时）。</summary>
      <param name="memid">成员标识符。</param>
      <param name="pVarIndex">此方法返回时，包含 <paramref name="memid" /> 的索引。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>调用对象的方法或访问对象的属性，该方法或属性实现由类型说明描述的接口。</summary>
      <param name="pvInstance">对由此类型说明描述的接口的引用。</param>
      <param name="memid">接口成员的标识符。</param>
      <param name="wFlags">描述 Invoke 调用的上下文的标志。</param>
      <param name="pDispParams">对结构的引用，该结构包含一个参数数组、一个命名参数的 DISPID 数组和每个数组中元素数的计数。</param>
      <param name="pVarResult">对用于存储结果的位置的引用。如果 <paramref name="wFlags" /> 指定 DISPATCH_PROPERTYPUT 或 DISPATCH_PROPERTYPUTREF，则忽略 <paramref name="pVarResult" />。如果不需要任何结果，则设置为 null。</param>
      <param name="pExcepInfo">指向异常信息结构的指针，该结构仅在返回 DISP_E_EXCEPTION 时才被填充。</param>
      <param name="puArgErr">如果 Invoke 返回 DISP_E_TYPEMISMATCH，则 <paramref name="puArgErr" /> 指示具有错误类型的参数的索引。如果多个参数返回错误，则 <paramref name="puArgErr" /> 仅指示第一个具有错误的参数。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseFuncDesc(System.IntPtr)">
      <summary>释放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" /> 方法返回的一个 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 结构。</summary>
      <param name="pFuncDesc">对要释放的 FUNCDESC 结构的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseTypeAttr(System.IntPtr)">
      <summary>释放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" /> 方法返回的一个 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 结构。</summary>
      <param name="pTypeAttr">对要释放的 TYPEATTR 结构的引用。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseVarDesc(System.IntPtr)">
      <summary>释放先前由 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" /> 方法返回的一个 VARDESC 结构。</summary>
      <param name="pVarDesc">对要释放的 VARDESC 结构的引用。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib">
      <summary>提供 ITypeLib 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>在类型库中查找类型说明的匹配项。</summary>
      <param name="szNameBuf">要搜索的名称。这是一个输入/输出参数。</param>
      <param name="lHashVal">一个用于加快搜索速度的哈希值，由 LHashValOfNameSys 函数计算。如果 <paramref name="lHashVal" /> 为 0，则计算一个值。</param>
      <param name="ppTInfo">此方法返回时，包含一个指向类型说明的指针数组，这些类型说明中包含 <paramref name="szNameBuf" /> 中指定的名称。该参数未经初始化即被传递。</param>
      <param name="rgMemId">所找到的项的 MEMBERID 数组；<paramref name="rgMemId" />[i] 是由 <paramref name="ppTInfo" />[i] 指定的类型说明中建立索引的 MEMBERID。不能为 null。</param>
      <param name="pcFound">在进入时指示要查找的实例数。例如，可以调用 <paramref name="pcFound" />= 1 以查找第一个匹配项。当找到一个实例时停止搜索。在退出时指示找到的实例数。如果 <paramref name="pcFound" /> 的 in 和 out 值完全相同，则可能存在其他包含此名称的类型说明。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>检索库的文档字符串、完整的帮助文件名和路径以及帮助文件中的库帮助主题的上下文标识符。</summary>
      <param name="index">要返回其文档的类型说明的索引。</param>
      <param name="strName">此方法返回时，包含一个表示指定项的名称的字符串。该参数未经初始化即被传递。</param>
      <param name="strDocString">此方法返回时，包含一个表示指定项的文档字符串的字符串。该参数未经初始化即被传递。</param>
      <param name="dwHelpContext">此方法返回时，包含与指定项关联的帮助上下文标识符。该参数未经初始化即被传递。</param>
      <param name="strHelpFile">此方法返回时，包含一个表示帮助文件的完全限定名的字符串。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>检索包含库的特性的结构。</summary>
      <param name="ppTLibAttr">此方法返回时，包含一个结构，该结构包含库的特性。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>使客户端编译器能够绑定到库的类型、变量、常数和全局函数。</summary>
      <param name="ppTComp">此方法返回时，包含此 ITypeLib 的 ITypeComp 实例的一个实例。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>在库中检索指定的类型说明。</summary>
      <param name="index">要返回的 ITypeInfo 接口的索引。</param>
      <param name="ppTI">此方法返回时，包含一个 ITypeInfo，它描述 <paramref name="index" /> 引用的类型。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoCount">
      <summary>返回类型库中的类型说明的数量。</summary>
      <returns>类型库中的类型说明的数量。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>检索与指定的 GUID 相对应的类型说明。</summary>
      <param name="guid">请求其类型信息的类的接口的 IID 或 CLSID。</param>
      <param name="ppTInfo">此方法返回时，包含请求的 ITypeInfo 接口。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>检索类型说明的类型。</summary>
      <param name="index">类型库中类型说明的索引。</param>
      <param name="pTKind">此方法返回时，包含对用于类型说明的 TYPEKIND 枚举的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.IsName(System.String,System.Int32)">
      <summary>指示传入的字符串是否包含库中描述的类型或成员的名称。</summary>
      <returns>如果在类型库中找到 <paramref name="szNameBuf" />，则为 true；否则为 false。</returns>
      <param name="szNameBuf">要测试的字符串。这是一个输入/输出参数。</param>
      <param name="lHashVal">
        <paramref name="szNameBuf" /> 的哈希值。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>释放最初通过 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" /> 方法获取的 <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> 结构。</summary>
      <param name="pTLibAttr">要释放的 TLIBATTR 结构。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib2">
      <summary>提供 ITypeLib2 接口的托管定义。</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>在类型库中查找类型说明的匹配项。</summary>
      <param name="szNameBuf">要搜索的名称。</param>
      <param name="lHashVal">一个用于加快搜索速度的哈希值，由 LHashValOfNameSys 函数计算。如果 <paramref name="lHashVal" /> 为 0，则计算一个值。</param>
      <param name="ppTInfo">此方法返回时，包含一个指向类型说明的指针数组，这些类型说明中包含 <paramref name="szNameBuf" /> 中指定的名称。该参数未经初始化即被传递。</param>
      <param name="rgMemId">此方法返回时，包含所找到的项的 MEMBERID 数组；<paramref name="rgMemId" /> [i] 是用于通过索引访问 <paramref name="ppTInfo" /> [i] 指定的类型说明的 MEMBERID。此参数不能为 null。该参数未经初始化即被传递。</param>
      <param name="pcFound">输入时由引用传递的一个值，该值指示要查找的实例数。例如，可以调用 <paramref name="pcFound" />= 1 以查找第一个匹配项。当找到一个实例时停止搜索。在退出时指示找到的实例数。如果 <paramref name="pcFound" /> 的 in 和 out 值完全相同，则可能存在其他包含此名称的类型说明。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetAllCustData(System.IntPtr)">
      <summary>获取库的所有自定义数据项。</summary>
      <param name="pCustData">一个指向包含所有自定义数据项的 CUSTDATA 的指针。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetCustData(System.Guid@,System.Object@)">
      <summary>获取自定义数据。</summary>
      <param name="guid">由引用传递的 <see cref="T:System.Guid" />，用于标识数据。</param>
      <param name="pVarVal">此方法返回时，包含一个指定在何处放置检索到的数据的对象。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>检索库的文档字符串、完整的帮助文件名和路径以及帮助文件中的库帮助主题的上下文标识符。</summary>
      <param name="index">要返回其文档的类型说明的索引。</param>
      <param name="strName">该参数在此方法返回时包含一个字符串，该字符串指定了指定项的名称。该参数未经初始化即被传递。</param>
      <param name="strDocString">此方法返回时，包含指定项的文档字符串。该参数未经初始化即被传递。</param>
      <param name="dwHelpContext">此方法返回时，包含与指定项关联的帮助上下文标识符。该参数未经初始化即被传递。</param>
      <param name="strHelpFile">此方法返回时，包含指定帮助文件的完全限定名的字符串。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>检索库的文档字符串、完整的帮助文件名和路径、要使用的本地化上下文以及帮助文件中的库帮助主题的上下文标识符。</summary>
      <param name="index">将返回其文档的类型说明的索引；如果 <paramref name="index" /> 为 -1，则返回库的文档。</param>
      <param name="pbstrHelpString">该参数在此方法返回时包含一个 BSTR，该 BSTR 指定了指定项的名称。如果调用方不需要该项名称，则 <paramref name="pbstrHelpString" /> 可以为 null。该参数未经初始化即被传递。</param>
      <param name="pdwHelpStringContext">此方法返回时，包含帮助本地化上下文。如果调用方不需要该帮助上下文，则 <paramref name="pdwHelpStringContext" /> 可以为 null。该参数未经初始化即被传递。</param>
      <param name="pbstrHelpStringDll">此方法返回时，包含一个指定文件（该文件中包含用于帮助文件的 DLL）的完全限定名的 BSTR。如果调用方不需要该文件名，则 <paramref name="pbstrHelpStringDll" /> 可以为 null。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibAttr(System.IntPtr@)">
      <summary>检索包含库的特性的结构。</summary>
      <param name="ppTLibAttr">此方法返回时，包含一个结构，该结构包含库的特性。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibStatistics(System.IntPtr,System.Int32@)">
      <summary>返回有关类型库的统计信息，该信息是有效调整哈希表的大小所必需的。</summary>
      <param name="pcUniqueNames">指向唯一名称的计数的指针。如果调用方不需要此信息，则将其设置为 null。</param>
      <param name="pcchUniqueNames">此方法返回时，包含一个指向唯一名称计数中的更改的指针。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>使客户端编译器能够绑定到库的类型、变量、常数和全局函数。</summary>
      <param name="ppTComp">此方法返回时，包含一个用于此 ITypeLib 的 ITypeComp 实例。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>在库中检索指定的类型说明。</summary>
      <param name="index">要返回的 ITypeInfo 接口的索引。</param>
      <param name="ppTI">此方法返回时，包含一个 ITypeInfo，它描述 <paramref name="index" /> 引用的类型。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoCount">
      <summary>返回类型库中的类型说明的数量。</summary>
      <returns>类型库中的类型说明的数量。</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>检索与指定的 GUID 相对应的类型说明。</summary>
      <param name="guid">由引用传递的 <see cref="T:System.Guid" />，它表示被请求了类型信息的类的 CLSID 接口的 IID。</param>
      <param name="ppTInfo">此方法返回时，包含请求的 ITypeInfo 接口。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>检索类型说明的类型。</summary>
      <param name="index">类型库中类型说明的索引。</param>
      <param name="pTKind">此方法返回时，包含对用于类型说明的 TYPEKIND 枚举的引用。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.IsName(System.String,System.Int32)">
      <summary>指示传入的字符串是否包含库中描述的类型或成员的名称。</summary>
      <returns>如果在类型库中找到 <paramref name="szNameBuf" />，则为 true；否则为 false。</returns>
      <param name="szNameBuf">要测试的字符串。</param>
      <param name="lHashVal">
        <paramref name="szNameBuf" /> 的哈希值。</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.ReleaseTLibAttr(System.IntPtr)">
      <summary>释放最初通过 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" /> 方法获取的 <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> 结构。</summary>
      <param name="pTLibAttr">要释放的 TLIBATTR 结构。</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.LIBFLAGS">
      <summary>定义应用于类型库的标志。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FCONTROL">
      <summary>类型库描述控件，并且不应在供非可视对象使用的类型浏览器中显示。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHASDISKIMAGE">
      <summary>类型库以一种持久形式存在于磁盘上。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHIDDEN">
      <summary>不应将类型库显示给用户，虽然它的使用并未受到限制。类型库应该由控件使用。宿主应创建用扩展属性包装控件的新类型库。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FRESTRICTED">
      <summary>类型库受到限制且不应显示给用户。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMDESC">
      <summary>包含有关如何在进程之间传输结构元素、参数或函数返回值的信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.lpVarValue">
      <summary>表示指向正在进程之间传递的值的指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.wParamFlags">
      <summary>表示描述结构元素、参数或返回值的位屏蔽值。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMFLAG">
      <summary>描述如何在进程之间传输结构元素、参数或函数返回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASCUSTDATA">
      <summary>该参数具有自定义数据。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASDEFAULT">
      <summary>参数定义了默认行为。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FIN">
      <summary>参数将信息从调用方传递到被调用方。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FLCID">
      <summary>参数是客户端应用程序的本地标识符。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOPT">
      <summary>该参数是可选的。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOUT">
      <summary>参数将信息从被调用方返回到调用方。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FRETVAL">
      <summary>参数是成员的返回值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_NONE">
      <summary>不会指定该参数传递还是接收信息。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATDATA">
      <summary>提供 STATDATA 结构的托管定义。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advf">
      <summary>表示用于确定何时向通知接收器通知数据更改的 <see cref="T:System.Runtime.InteropServices.ComTypes.ADVF" /> 枚举值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advSink">
      <summary>表示将接收更改通知的 <see cref="T:System.Runtime.InteropServices.ComTypes.IAdviseSink" /> 接口。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.connection">
      <summary>表示唯一标识通知连接的标记。此标记由设置通知连接的方法返回。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.formatetc">
      <summary>表示对通知接收器有意义的数据的 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 结构。通知接收器将接收对 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 结构所指定数据的更改通知。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATSTG">
      <summary>包含有关打开的存储、流或字节数组对象的统计信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.atime">
      <summary>指定此存储、流或字节数组的上次访问时间。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.cbSize">
      <summary>指定流或字节数组的大小（以字节为单位）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.clsid">
      <summary>指示存储对象的类标识符。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.ctime">
      <summary>指示此存储、流或字节数组的创建时间。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfLocksSupported">
      <summary>指示受该流或字节数组支持的区域锁定的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfMode">
      <summary>指示打开对象时指定的访问模式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfStateBits">
      <summary>指示存储对象的当前状态位（最近由 IStorage::SetStateBits 方法设置的值）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.mtime">
      <summary>指示此存储、流或字节数组的上次修改日期。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.pwcsName">
      <summary>表示指向以 NULL 结尾的字符串的指针，该字符串包含此结构所描述的对象的名称。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.reserved">
      <summary>保留供将来使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.type">
      <summary>指示存储对象的类型，该类型为 STGTY 枚举值之一。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM">
      <summary>提供 STGMEDIUM 结构的托管定义。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease">
      <summary>表示指向接口实例的指针，该指针允许发送进程控制当接收进程调用 ReleaseStgMedium 函数时释放存储的方式。如果 <see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 为 null，则 ReleaseStgMedium 使用默认过程来释放存储；否则 ReleaseStgMedium 将使用指定的 IUnknown 接口。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.tymed">
      <summary>指定存储介质的类型。封送和取消封送例程使用此值来确定所使用的联合成员。此值必须是 <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" /> 枚举的元素之一。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.unionmember">
      <summary>表示接收进程可用于访问正在传输的数据的句柄、字符串或接口。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.SYSKIND">
      <summary>标识目标操作系统平台。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_MAC">
      <summary>类型库的目标操作系统为 Apple Macintosh。默认情况下，所有数据字段在偶字节边界对齐。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN16">
      <summary>类型库的目标操作系统是 16 位 Windows 系统。默认情况下，数据字段被压缩。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN32">
      <summary>类型库的目标操作系统是 32 位 Windows 系统。默认情况下，数据字段自然对齐（例如，2 字节整数在偶字节边界对齐；4 字节整数在四字边界对齐，依此类推）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN64">
      <summary>类型库的目标操作系统是 64 位 Windows 系统。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYMED">
      <summary>提供 TYMED 结构的托管定义。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ENHMF">
      <summary>该存储介质是增强型图元文件。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成员为 null，目标进程应使用 DeleteEnhMetaFile 删除位图。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_FILE">
      <summary>该存储介质是由路径标识的磁盘文件。如果 STGMEDIUM<see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成员为 null，目标进程应使用 OpenFile 删除文件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_GDI">
      <summary>该存储介质是图形设备接口 (GDI) 组件 (HBITMAP)。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成员为 null，目标进程应使用 DeleteObject 删除位图。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_HGLOBAL">
      <summary>该存储介质是全局内存句柄 (HGLOBAL)。使用 GMEM_SHARE 标志分配全局句柄。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成员为 null，目标进程应使用 GlobalFree 释放内存。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTORAGE">
      <summary>该存储介质是由 IStorage 指针标识的存储组件。数据位于此 IStorage 实例所包含的流和存储中。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成员不为 null，则目标进程应使用 IStorage::Release 释放存储组件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTREAM">
      <summary>该存储介质是由 IStream 指针标识的流对象。使用 ISequentialStream::Read 读取数据。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成员不为 null，则目标进程应使用 IStream::Release 释放流组件。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_MFPICT">
      <summary>该存储介质是图元文件 (HMETAFILE)。使用 Windows 或 WIN32 函数来访问该图元文件的数据。如果 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 成员为 null，目标进程应使用 DeleteMetaFile 删除位图。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_NULL">
      <summary>当前没有传递任何数据。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEATTR">
      <summary>包含 UCOMITypeInfo 的特性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbAlignment">
      <summary>指定此类型实例的字节对齐方式。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeInstance">
      <summary>此类型的实例的大小。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeVft">
      <summary>此类型的虚方法表 (VTBL) 的大小。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cFuncs">
      <summary>指示此结构描述的接口上的函数数目。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cImplTypes">
      <summary>指示在此结构描述的接口上实现的接口数目。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cVars">
      <summary>指示此结构所描述的接口上的变量和数据字段的数目。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.dwReserved">
      <summary>保留供将来使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.guid">
      <summary>类型信息的 GUID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.idldescType">
      <summary>所描述的类型的 IDL 特性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lcid">
      <summary>成员名称和文档字符串的区域设置。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lpstrSchema">
      <summary>保留供将来使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.MEMBER_ID_NIL">
      <summary>与 <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidConstructor" /> 和 <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidDestructor" /> 字段一起使用的常数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidConstructor">
      <summary>构造函数的 ID，如果没有，则为 <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidDestructor">
      <summary>析构函数的 ID，如果没有，则为 <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" />。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.tdescAlias">
      <summary>如果 <see cref="F:System.Runtime.InteropServices.TYPEATTR.typekind" />==<see cref="F:System.Runtime.InteropServices.TYPEKIND.TKIND_ALIAS" />，则指定该类型（此类型为该类型的别名）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.typekind">
      <summary>
        <see cref="T:System.Runtime.InteropServices.TYPEKIND" /> 值，该值描述此信息描述的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMajorVerNum">
      <summary>主要版本号。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMinorVerNum">
      <summary>次要版本号。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wTypeFlags">
      <summary>描述此信息的 <see cref="T:System.Runtime.InteropServices.TYPEFLAGS" /> 值。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEDESC">
      <summary>描述变量的类型、函数的返回类型或函数参数的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.lpValue">
      <summary>如果变量为 VT_SAFEARRAY 或 VT_PTR，则 lpValue 字段包含指向指定元素类型的 TYPEDESC 的指针。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.vt">
      <summary>指示由此 TYPEDESC 描述的项的 Variant 类型。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEFLAGS">
      <summary>定义类型说明的属性和特性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAGGREGATABLE">
      <summary>该类支持聚合。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAPPOBJECT">
      <summary>描述 Application 对象的类型说明。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCANCREATE">
      <summary>可由 ITypeInfo::CreateInstance 创建该类型的实例。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCONTROL">
      <summary>该类型是将派生其他类型的控件，而且不应显示给用户。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDISPATCHABLE">
      <summary>指示该接口直接或间接从 IDispatch 派生。此标志为计算所得，没有此标志的对象描述语言。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDUAL">
      <summary>该接口同时支持 IDispatch 和 VTBL 绑定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FHIDDEN">
      <summary>该类型不应显示在浏览器中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FLICENSED">
      <summary>该类型已授权。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FNONEXTENSIBLE">
      <summary>该接口在运行时无法添加成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FOLEAUTOMATION">
      <summary>该接口中使用的类型与自动化（包括 VTBL 绑定支持）完全兼容。设置双重接口时，将同时设置此标志和 <see cref="F:System.Runtime.InteropServices.TYPEFLAGS.TYPEFLAG_FDUAL" />。不允许在调度接口上设置此标志。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPREDECLID">
      <summary>该类型是预定义的。该客户端应用程序应自动创建具有此特性的对象的单个实例。指向该对象的变量的名称与该对象的类名相同。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPROXY">
      <summary>指示接口将使用 proxy/stub 动态链接库。此标志指定注销类型库时不应注销类型库代理。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREPLACEABLE">
      <summary>该对象支持 IConnectionPointWithDefault，而且具有默认行为。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FRESTRICTED">
      <summary>不应是可从宏语言访问的。此标志用于系统级类型或类型浏览器不应显示的类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREVERSEBIND">
      <summary>指示检查子级前应先检查基接口以进行名称解析，这与默认行为相反。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEKIND">
      <summary>指定各种类型的数据和函数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ALIAS">
      <summary>类型，它是另一个类型的别名。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_COCLASS">
      <summary>已实现的组件接口集。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_DISPATCH">
      <summary>可通过 IDispatch::Invoke 访问的方法和属性集。默认情况下，双重接口返回 TKIND_DISPATCH。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ENUM">
      <summary>枚举数集。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_INTERFACE">
      <summary>类型，它具有虚函数（全部为纯虚函数）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MAX">
      <summary>枚举结尾标记。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MODULE">
      <summary>模块，它只能包含静态函数和数据（例如 DLL）。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_RECORD">
      <summary>没有方法的结构。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_UNION">
      <summary>偏移量为零的所有成员的联合。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPELIBATTR">
      <summary>标识特定类型库并为成员名称提供本地化支持。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.guid">
      <summary>表示类型库的全局唯一的库 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.lcid">
      <summary>表示类型库的区域设置 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.syskind">
      <summary>表示类型库的目标硬件平台。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wLibFlags">
      <summary>表示库标志。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMajorVerNum">
      <summary>表示类型库的主版本号。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMinorVerNum">
      <summary>表示类型库的次版本号。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC">
      <summary>描述变量、常数或数据成员。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.desc">
      <summary>包含有关变量的信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.elemdescVar">
      <summary>包含变量类型。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.lpstrSchema">
      <summary>保留此字段供将来使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.memid">
      <summary>指示变量的成员 ID。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.varkind">
      <summary>定义如何封送变量。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.wVarFlags">
      <summary>定义变量的属性。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION">
      <summary>包含有关变量的信息。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.lpvarValue">
      <summary>描述符号常数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.oInst">
      <summary>指示此变量在该实例中的偏移量。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARFLAGS">
      <summary>标识定义变量属性的常数。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FBINDABLE">
      <summary>该变量支持数据绑定。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTBIND">
      <summary>此变量是最佳地表示此对象的唯一属性。类型信息中仅有一个变量可以具有此特性。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTCOLLELEM">
      <summary>允许进行一种优化，使编译器查找“abc”类型上名为“xyz”的成员。如果找到这样的成员，而且该成员标志为默认集合的某元素的访问函数，则生成对该成员函数的调用。可以对调度接口和接口中的成员使用；不能对模块使用。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDISPLAYBIND">
      <summary>该变量作为可绑定变量显示给用户。还必须设置 <see cref="F:System.Runtime.InteropServices.VARFLAGS.VARFLAG_FBINDABLE" />。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FHIDDEN">
      <summary>尽管该变量存在并且是可绑定的，但不应在浏览器中将它显示给用户。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FIMMEDIATEBIND">
      <summary>该变量作为单独的可绑定属性映射。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FNONBROWSABLE">
      <summary>该变量出现在对象浏览器而非属性浏览器中。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREADONLY">
      <summary>不应允许给该变量赋值。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREPLACEABLE">
      <summary>将该接口标记为具有默认行为。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREQUESTEDIT">
      <summary>设置后，任何直接更改该属性的尝试都会导致对 IPropertyNotifySink::OnRequestEdit 的调用。OnRequestEdit 的实现确定是否接受此更改。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FRESTRICTED">
      <summary>该变量不应该是可以从宏语言访问的。此标志用于系统级变量或不想让类型浏览器显示的变量。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FSOURCE">
      <summary>该变量返回一个对象，该对象为事件的源。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FUIDEFAULT">
      <summary>该变量是用户界面中的默认显示。</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARKIND">
      <summary>定义变量的种类。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_CONST">
      <summary>VARDESC 结构描述符号常量。没有与之关联的内存。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_DISPATCH">
      <summary>该变量只能通过 IDispatch::Invoke 访问。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_PERINSTANCE">
      <summary>该变量是类型的一个字段或成员。它在类型的每个实例中存在于固定的偏移量处。</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_STATIC">
      <summary>该变量只有一个实例。</summary>
    </member>
  </members>
</doc>