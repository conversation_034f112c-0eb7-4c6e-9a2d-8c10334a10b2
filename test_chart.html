<!DOCTYPE html>
<html>

<head>
	<title>圖表功能測試</title>
	<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
</head>

<body>
	<h2>圖表功能測試</h2>
	<div id="piechart" style="width: 100%; height: 500px;"></div>

	<script type="text/javascript">
		google.charts.load('current', { 'packages': ['corechart'] });

		// 模擬後端數據
		var chartData = [
			['科系', '實名制人數'],
			['資訊工程系', 120],
			['電機工程系', 95],
			['機械工程系', 85],
			['化學工程系', 70]
		];
		var chartTitle = '測試圖表 - 實名制人數分布';

		// 只有在有圖表數據時才設置回調
		function initializeChart() {
			if (typeof chartData !== 'undefined' && typeof chartTitle !== 'undefined') {
				google.charts.setOnLoadCallback(drawChart);
			}
		}

		function drawChart() {
			// 再次檢查是否有 chartData 和 chartTitle
			if (typeof chartData === 'undefined' || typeof chartTitle === 'undefined') {
				console.log('圖表資料尚未載入，無法繪製圖表');
				return;
			}

			try {
				var data = google.visualization.arrayToDataTable(chartData);

				var options = {
					title: chartTitle,
					pieHole: 0.4,
					width: 900,
					height: 500,
					titleTextStyle: {
						fontSize: 18,
						fontName: '微軟正黑體',
						bold: true,
						color: '#333'
					},
					legend: {
						position: 'right',
						textStyle: {
							fontSize: 12,
							fontName: '微軟正黑體'
						}
					},
					colors: ['#667eea', '#764ba2', '#4facfe', '#00f2fe']
				};

				var chart = new google.visualization.PieChart(
					document.getElementById("piechart")
				);
				chart.draw(data, options);
				console.log("圖表繪製成功！");

			} catch (error) {
				console.error("繪製圖表時發生錯誤:", error);
				var chartContainer = document.getElementById("piechart");
				if (chartContainer) {
					chartContainer.innerHTML = '<div style="text-align:center; padding:50px; color:#999;">圖表載入失敗: ' + error.message + '</div>';
				}
			}
		}

		// 載入完成後初始化圖表
		window.onload = function () {
			initializeChart();
		};
	</script>
</body>

</html>