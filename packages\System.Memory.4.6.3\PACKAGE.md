## About

Provides types for efficient representation and pooling of managed, stack, and native memory segments and sequences of such segments, along with primitives to parse and format UTF-8 encoded text stored in those memory segments.

## Main Types

The main types provided by this library are:

- System.Span
- System.ReadOnlySpan
- System.Memory
- System.ReadOnlyMemory
- System.Buffers.MemoryPool
- System.Buffers.ReadOnlySequence
- System.Buffers.Text.Utf8Parser
- System.Buffers.Text.Utf8Formatter

## Additional Documentation

- API reference can be found in: https://learn.microsoft.com/en-us/dotnet/api/system.memory
https://learn.microsoft.com/en-us/archive/msdn-magazine/2018/january/csharp-all-about-span-exploring-a-new-net-mainstay

## Related Packages

The types in this assembly are shipped as part of the shared framework starting with .NET Core 3.1.

## License

System.Memory is released as open source under the [MIT license](https://licenses.nuget.org/MIT).
