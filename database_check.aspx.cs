using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class database_check : System.Web.UI.Page
{
	dbconnection db = new dbconnection();

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			litResults.Text = "<div class='result'>點擊上方按鈕開始檢查資料庫狀況</div>";
		}
	}

	protected void CheckConnection_Click(object sender, EventArgs e)
	{
		StringBuilder sb = new StringBuilder();
		sb.Append("<div class='result'>");
		sb.Append("<h3>📡 資料庫連線檢查</h3>");

		try
		{
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			sb.Append("<p class='success'>✅ 資料庫連線成功！</p>");
			sb.Append($"<p>連線字串: {db.conn.ConnectionString}</p>");
			sb.Append($"<p>資料庫: {db.conn.Database}</p>");
			sb.Append($"<p>伺服器: {db.conn.DataSource}</p>");
			sb.Append($"<p>連線狀態: {db.conn.State}</p>");

			db.conn.Close();
		}
		catch (Exception ex)
		{
			sb.Append($"<p class='error'>❌ 連線失敗: {ex.Message}</p>");
		}

		sb.Append("</div>");
		litResults.Text = sb.ToString();
	}

	protected void CheckViews_Click(object sender, EventArgs e)
	{
		StringBuilder sb = new StringBuilder();
		sb.Append("<div class='result'>");
		sb.Append("<h3>👁️ 視圖檢查</h3>");

		try
		{
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			// 檢查視圖是否存在
			string[] views = { "V_recowish1", "V_recowish3", "V_recowish5" };

			foreach (string viewName in views)
			{
				sb.Append($"<h4>📊 {viewName}</h4>");

				// 檢查視圖是否存在
				SqlCommand cmd = new SqlCommand($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = '{viewName}'", db.conn);
				int viewExists = (int)cmd.ExecuteScalar();

				if (viewExists > 0)
				{
					sb.Append("<p class='success'>✅ 視圖存在</p>");

					// 檢查記錄數
					cmd.CommandText = $"SELECT COUNT(*) FROM [school].[dbo].[{viewName}]";
					try
					{
						int recordCount = (int)cmd.ExecuteScalar();
						sb.Append($"<p>📈 記錄數量: <strong>{recordCount}</strong></p>");

						if (recordCount > 0)
						{
							// 顯示欄位結構
							cmd.CommandText = $"SELECT TOP 1 * FROM [school].[dbo].[{viewName}]";
							SqlDataAdapter da = new SqlDataAdapter(cmd);
							DataTable dt = new DataTable();
							da.Fill(dt);

							sb.Append("<p>📋 欄位結構:</p>");
							sb.Append("<table>");
							sb.Append("<tr><th>欄位名稱</th><th>資料類型</th><th>範例數據</th></tr>");

							foreach (DataColumn col in dt.Columns)
							{
								string sampleData = dt.Rows.Count > 0 ? dt.Rows[0][col.ColumnName].ToString() : "無資料";
								sb.Append($"<tr><td>{col.ColumnName}</td><td>{col.DataType.Name}</td><td>{sampleData}</td></tr>");
							}
							sb.Append("</table>");
						}
						else
						{
							sb.Append("<p class='error'>⚠️ 視圖存在但無資料</p>");
						}
					}
					catch (Exception ex)
					{
						sb.Append($"<p class='error'>❌ 查詢錯誤: {ex.Message}</p>");
					}
				}
				else
				{
					sb.Append("<p class='error'>❌ 視圖不存在</p>");
				}

				sb.Append("<hr/>");
			}

			db.conn.Close();
		}
		catch (Exception ex)
		{
			sb.Append($"<p class='error'>❌ 檢查失敗: {ex.Message}</p>");
		}

		sb.Append("</div>");
		litResults.Text = sb.ToString();
	}

	protected void CheckTables_Click(object sender, EventArgs e)
	{
		StringBuilder sb = new StringBuilder();
		sb.Append("<div class='result'>");
		sb.Append("<h3>🗄️ 原始表格檢查</h3>");

		try
		{
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			// 列出所有表格
			SqlCommand cmd = new SqlCommand("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME", db.conn);
			SqlDataAdapter da = new SqlDataAdapter(cmd);
			DataTable tables = new DataTable();
			da.Fill(tables);

			sb.Append($"<p>找到 {tables.Rows.Count} 個表格:</p>");
			sb.Append("<table>");
			sb.Append("<tr><th>表格名稱</th><th>記錄數量</th></tr>");

			foreach (DataRow row in tables.Rows)
			{
				string tableName = row["TABLE_NAME"].ToString();
				try
				{
					cmd.CommandText = $"SELECT COUNT(*) FROM [{tableName}]";
					int count = (int)cmd.ExecuteScalar();
					sb.Append($"<tr><td>{tableName}</td><td>{count}</td></tr>");
				}
				catch
				{
					sb.Append($"<tr><td>{tableName}</td><td>無法查詢</td></tr>");
				}
			}
			sb.Append("</table>");

			db.conn.Close();
		}
		catch (Exception ex)
		{
			sb.Append($"<p class='error'>❌ 檢查失敗: {ex.Message}</p>");
		}

		sb.Append("</div>");
		litResults.Text = sb.ToString();
	}

	protected void TestData_Click(object sender, EventArgs e)
	{
		StringBuilder sb = new StringBuilder();
		sb.Append("<div class='result'>");
		sb.Append("<h3>🧪 測試數據查詢</h3>");

		try
		{
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			// 測試各種可能的查詢
			string[] testQueries = {
				"SELECT TOP 5 * FROM [school].[dbo].[V_recowish1]",
				"SELECT * FROM [school].[dbo].[V_recowish1] WHERE [實名制人數] > 0",
				"SELECT [科系], [實名制人數] FROM [school].[dbo].[V_recowish1]",
				"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'V_recowish1'"
			};

			int queryIndex = 1;
			foreach (string query in testQueries)
			{
				sb.Append($"<h4>測試查詢 {queryIndex}: {query}</h4>");

				try
				{
					SqlCommand cmd = new SqlCommand(query, db.conn);
					SqlDataAdapter da = new SqlDataAdapter(cmd);
					DataTable dt = new DataTable();
					da.Fill(dt);

					sb.Append($"<p>結果: {dt.Rows.Count} 筆記錄</p>");

					if (dt.Rows.Count > 0)
					{
						sb.Append("<table>");
						sb.Append("<tr>");
						foreach (DataColumn col in dt.Columns)
						{
							sb.Append($"<th>{col.ColumnName}</th>");
						}
						sb.Append("</tr>");

						int maxRows = Math.Min(5, dt.Rows.Count);
						for (int i = 0; i < maxRows; i++)
						{
							sb.Append("<tr>");
							foreach (DataColumn col in dt.Columns)
							{
								sb.Append($"<td>{dt.Rows[i][col.ColumnName]}</td>");
							}
							sb.Append("</tr>");
						}
						sb.Append("</table>");
					}
					else
					{
						sb.Append("<p class='error'>無資料返回</p>");
					}
				}
				catch (Exception ex)
				{
					sb.Append($"<p class='error'>查詢失敗: {ex.Message}</p>");
				}

				sb.Append("<hr/>");
				queryIndex++;
			}

			db.conn.Close();
		}
		catch (Exception ex)
		{
			sb.Append($"<p class='error'>❌ 測試失敗: {ex.Message}</p>");
		}

		sb.Append("</div>");
		litResults.Text = sb.ToString();
	}
}
