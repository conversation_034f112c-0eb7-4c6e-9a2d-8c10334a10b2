using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.Script.Serialization;

public partial class debug_analyze : System.Web.UI.Page
{
	private dbconnection db = new dbconnection();

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			lblConnectionResult.Text = "點擊按鈕測試資料庫連線";
			lblQueryResult.Text = "選擇選項後點擊執行查詢";
			lblChartResult.Text = "點擊按鈕測試圖表功能";
		}
	}

	protected void btnTestConnection_Click(object sender, EventArgs e)
	{
		try
		{
			string logMessage = "開始測試資料庫連線...\n";

			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
				logMessage += "✅ 資料庫連線成功!\n";
			}
			else
			{
				logMessage += "ℹ️ 資料庫已經連線\n";
			}

			logMessage += $"📊 資料庫名稱: {db.conn.Database}\n";
			logMessage += $"🖥️ 伺服器: {db.conn.DataSource}\n";
			logMessage += $"📡 版本: {db.conn.ServerVersion}\n";
			logMessage += $"⏱️ 連線逾時: {db.conn.ConnectionTimeout} 秒\n";
			logMessage += $"🔗 連線字串: {db.conn.ConnectionString.Substring(0, 50)}...\n";

			lblConnectionResult.Text = logMessage;
			lblConnectionResult.CssClass = "success";

			// 註冊 JavaScript 調試訊息
			ScriptManager.RegisterStartupScript(this, GetType(), "connTest",
				"updateDebugLog('✅ 資料庫連線測試成功');", true);

			db.conn.Close();
		}
		catch (Exception ex)
		{
			string errorMessage = $"❌ 連線失敗:\n{ex.Message}\n\n詳細錯誤:\n{ex.ToString()}";
			lblConnectionResult.Text = errorMessage;
			lblConnectionResult.CssClass = "error";

			ScriptManager.RegisterStartupScript(this, GetType(), "connError",
				$"updateDebugLog('❌ 資料庫連線失敗: {ex.Message.Replace("'", "\\'")}');", true);
		}
	}

	protected void btnSimulateQuery_Click(object sender, EventArgs e)
	{
		try
		{
			string logMessage = "開始模擬主頁面查詢邏輯...\n";
			logMessage += $"選擇的學制: {RadioButtonList1.SelectedValue} ({RadioButtonList1.SelectedItem.Text})\n";
			logMessage += $"選擇的查詢項目: {RadioButtonList2.SelectedValue} ({RadioButtonList2.SelectedItem.Text})\n\n";

			// 建立資料庫連線
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
				logMessage += "✅ 資料庫連線已建立\n";
			}

			// 設定 SQL 查詢
			string sqlStr = "";
			string dataColumnName = "";
			string chartTitle = "";

			// 根據查詢項目設定欄位名稱
			switch (RadioButtonList2.SelectedValue)
			{
				case "1":
					dataColumnName = "實名制人數";
					break;
				case "2":
					dataColumnName = "報到人數";
					break;
				case "3":
					dataColumnName = "繳交畢業證書人數";
					break;
				case "4":
					dataColumnName = "繳費人數";
					break;
				default:
					dataColumnName = "實名制人數";
					break;
			}

			// 根據學制設定 SQL 查詢
			if (RadioButtonList1.SelectedValue == "1")
			{
				sqlStr = @"SELECT [學制], [科系], [實名制人數], [報到人數], [繳交畢業證書人數], [繳費人數]
                          FROM [school].[dbo].[V_recowish1]";
				chartTitle = "日間部" + RadioButtonList2.SelectedItem.Text + " - " + DateTime.Now.ToString("yyyy/MM/dd");
			}
			else if (RadioButtonList1.SelectedValue == "2")
			{
				sqlStr = @"SELECT [學制], [科系], [實名制人數], [報到人數], [繳交畢業證書人數], [繳費人數]
                          FROM [school].[dbo].[V_recowish3]";
				chartTitle = "碩士班" + RadioButtonList2.SelectedItem.Text + " - " + DateTime.Now.ToString("yyyy/MM/dd");
			}
			else if (RadioButtonList1.SelectedValue == "3")
			{
				sqlStr = @"SELECT [學制], [科系], [實名制人數], [報到人數], [繳交畢業證書人數], [繳費人數]
                          FROM [school].[dbo].[V_recowish5]";
				chartTitle = "進修部" + RadioButtonList2.SelectedItem.Text + " - " + DateTime.Now.ToString("yyyy/MM/dd");
			}

			logMessage += $"📝 SQL 查詢:\n{sqlStr}\n\n";
			logMessage += $"📊 資料欄位: {dataColumnName}\n";
			logMessage += $"📈 圖表標題: {chartTitle}\n\n";

			// 執行查詢
			using (SqlCommand cmd = new SqlCommand(sqlStr, db.conn))
			{
				using (SqlDataAdapter da = new SqlDataAdapter(cmd))
				{
					DataTable dt = new DataTable();
					da.Fill(dt);

					logMessage += $"✅ 查詢執行成功，獲得 {dt.Rows.Count} 筆記錄\n\n";

					if (dt.Rows.Count > 0)
					{
						// 顯示資料
						gvResults.DataSource = dt;
						gvResults.DataBind();

						// 準備圖表資料
						var chartData = new System.Collections.Generic.List<object>();
						chartData.Add(new object[] { "科系", dataColumnName });

						int totalCount = 0;
						foreach (DataRow row in dt.Rows)
						{
							string departmentName = row["科系"].ToString();
							int dataValue = 0;

							if (int.TryParse(row[dataColumnName].ToString(), out dataValue))
							{
								chartData.Add(new object[] { departmentName, dataValue });
								totalCount += dataValue;
							}
						}

						logMessage += $"📊 總計: {totalCount}\n";
						logMessage += $"📈 圖表資料點: {chartData.Count - 1}\n";

						// 產生圖表
						JavaScriptSerializer serializer = new JavaScriptSerializer();
						string jsonData = serializer.Serialize(chartData);

						ScriptManager.RegisterStartupScript(this, GetType(), "chartTest",
							$"testChart({jsonData}, '{chartTitle}');", true);

						logMessage += "✅ 圖表資料已傳送到前端";
					}
					else
					{
						logMessage += "⚠️ 查詢結果為空";
						gvResults.DataSource = null;
						gvResults.DataBind();
					}
				}
			}

			lblQueryResult.Text = logMessage;
			lblQueryResult.CssClass = "success";

			ScriptManager.RegisterStartupScript(this, GetType(), "queryTest",
				$"updateDebugLog('✅ 模擬查詢完成，獲得資料');", true);
		}
		catch (Exception ex)
		{
			string errorMessage = $"❌ 模擬查詢失敗:\n{ex.Message}\n\n詳細錯誤:\n{ex.ToString()}";
			lblQueryResult.Text = errorMessage;
			lblQueryResult.CssClass = "error";

			ScriptManager.RegisterStartupScript(this, GetType(), "queryError",
				$"updateDebugLog('❌ 模擬查詢失敗: {ex.Message.Replace("'", "\\'")}');", true);
		}
		finally
		{
			if (db.conn.State == ConnectionState.Open)
			{
				db.conn.Close();
			}
		}
	}

	protected void btnTestChart_Click(object sender, EventArgs e)
	{
		try
		{
			// 建立測試圖表資料
			var testData = new System.Collections.Generic.List<object>();
			testData.Add(new object[] { "科系", "人數" });
			testData.Add(new object[] { "資訊工程系", 45 });
			testData.Add(new object[] { "電機工程系", 38 });
			testData.Add(new object[] { "機械工程系", 52 });
			testData.Add(new object[] { "化工系", 28 });

			JavaScriptSerializer serializer = new JavaScriptSerializer();
			string jsonData = serializer.Serialize(testData);

			ScriptManager.RegisterStartupScript(this, GetType(), "testChart",
				$"testChart({jsonData}, '測試圖表 - {DateTime.Now.ToString("HH:mm:ss")}');", true);

			lblChartResult.Text = "✅ 測試圖表資料已傳送";
			lblChartResult.CssClass = "success";

			ScriptManager.RegisterStartupScript(this, GetType(), "chartDebug",
				"updateDebugLog('🔧 圖表測試資料已傳送');", true);
		}
		catch (Exception ex)
		{
			lblChartResult.Text = $"❌ 圖表測試失敗: {ex.Message}";
			lblChartResult.CssClass = "error";

			ScriptManager.RegisterStartupScript(this, GetType(), "chartTestError",
				$"updateDebugLog('❌ 圖表測試失敗: {ex.Message.Replace("'", "\\'")}');", true);
		}
	}
}
