<%@ Page Language="C#" AutoEventWireup="true" CodeFile="test_main_functionality.aspx.cs" Inherits="test_main_functionality" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head runat="server">
	<title>測試主頁面功能</title>
	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
	<style>
		.debug-info {
			background: #f0f8ff;
			border: 1px solid #007acc;
			padding: 10px;
			margin: 10px 0;
			border-radius: 5px;
		}

		.error {
			color: red;
		}

		.success {
			color: green;
		}

		.loading-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.7);
			z-index: 9999;
			display: none;
			justify-content: center;
			align-items: center;
			color: white;
			font-size: 20px;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>

		<div class="loading-overlay" id="loadingOverlay">
			<div>正在載入數據，請稍候...</div>
		</div>

		<h1>測試主頁面核心功能</h1>

		<div class="debug-info">
			<h3>🔍 功能測試</h3>
			<asp:Label ID="lblStatus" runat="server" Text="準備測試..."></asp:Label>
		</div>

		<div>
			<h3>選擇分析類型：</h3>
			<asp:RadioButtonList ID="RadioButtonList1" runat="server">
				<asp:ListItem Value="1" Text="日間部學生滿意度分析"></asp:ListItem>
				<asp:ListItem Value="3" Text="碩士班學生滿意度分析"></asp:ListItem>
				<asp:ListItem Value="5" Text="進修部學生滿意度分析"></asp:ListItem>
			</asp:RadioButtonList>
		</div>

		<br />
		<asp:Button ID="btnExecute" runat="server" Text="執行分析" OnClick="btnExecute_Click" OnClientClick="showLoading(); return true;" />

		<div class="debug-info">
			<h3>📊 查詢結果</h3>
			<asp:Label ID="lblResult" runat="server" Text="尚未執行查詢"></asp:Label>
		</div>

		<div>
			<h3>📈 圖表區域</h3>
			<canvas id="myChart" width="400" height="200"></canvas>
		</div>

		<div class="debug-info">
			<h3>🔧 調試信息</h3>
			<div id="debugLog"></div>
		</div>

	</form>

	<script type="text/javascript">
		function showLoading() {
			console.log('顯示載入中遮罩');
			document.getElementById('loadingOverlay').style.display = 'flex';
		}

		function hideLoading() {
			console.log('隱藏載入中遮罩');
			document.getElementById('loadingOverlay').style.display = 'none';
		}

		function updateDebugLog(message) {
			var debugLog = document.getElementById('debugLog');
			var timestamp = new Date().toLocaleTimeString();
			debugLog.innerHTML += '<div>[' + timestamp + '] ' + message + '</div>';
			console.log('[DEBUG] ' + message);
		}

		function drawChart(chartData, chartTitle) {
			try {
				if (!chartData || !chartTitle) {
					updateDebugLog('❌ 圖表數據不完整: chartData=' + (chartData ? '有' : '無') + ', chartTitle=' + (chartTitle ? '有' : '無'));
					return;
				}

				updateDebugLog('📈 開始繪製圖表: ' + chartTitle);

				var ctx = document.getElementById('myChart').getContext('2d');
				var chart = new Chart(ctx, {
					type: 'bar',
					data: chartData,
					options: {
						responsive: true,
						plugins: {
							title: {
								display: true,
								text: chartTitle
							}
						}
					}
				});

				updateDebugLog('✅ 圖表繪製完成');
			} catch (error) {
				updateDebugLog('❌ 圖表繪製錯誤: ' + error.message);
				console.error('Chart error:', error);
			}
		}

		// 頁面載入完成後的初始化
		window.onload = function () {
			updateDebugLog('✅ 頁面載入完成');
		};
	</script>
</body>

</html>