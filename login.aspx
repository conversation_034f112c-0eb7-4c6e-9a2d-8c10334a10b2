﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="login.aspx.cs" Inherits="login" %>
    <!DOCTYPE html>
    <html xmlns="http://www.w3.org/1999/xhtml">

    <head runat="server">
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>招生決策管理系統</title>
        <!-- Bootstrap CSS (保留現有的，看是否能與新設計融合，或後續替換為 Tailwind) -->
        <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
        <!-- 引入 Tailwind CSS CDN -->
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
            body {
                font-family: '微軟正黑體 Light', sans-serif;
            }

            .login-form-container {
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                background-color: #f3f4f6;
                /* 類似 Tailwind bg-gray-100 */
                padding: 2rem;
            }

            .login-card {
                background-color: white;
                padding: 2rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                width: 100%;
                max-width: 28rem;
                /* 類似 Tailwind max-w-md */
            }

            .login-title {
                font-size: 1.5rem;
                /* 類似 Tailwind text-2xl */
                font-weight: bold;
                text-align: center;
                margin-bottom: 0.5rem;
            }

            .login-description {
                text-align: center;
                color: #6b7280;
                /* 類似 Tailwind text-gray-500 */
                margin-bottom: 1.5rem;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 500;
                color: #374151;
                /* 類似 Tailwind text-gray-700 */
            }

            .input-wrapper {
                position: relative;
            }

            .input-icon {
                position: absolute;
                left: 0.75rem;
                top: 50%;
                transform: translateY(-50%);
                color: #9ca3af;
                /* 類似 Tailwind text-gray-400 */
            }

            .form-control-asp {
                width: 100%;
                padding-left: 2.5rem;
                /* 為圖示留出空間 */
                padding-top: 0.5rem;
                padding-bottom: 0.5rem;
                border-radius: 0.375rem;
                border: 1px solid #d1d5db;
                /* 類似 Tailwind border-gray-300 */
            }

            .form-control-asp {
                width: 100%;
                padding-left: 2.5rem;
                /* 為圖示留出空間 */
                padding-top: 0.5rem;
                padding-bottom: 0.5rem;
                border-radius: 0.375rem;
                border: 1px solid #d1d5db;
                /* 類似 Tailwind border-gray-300 */
                transition: border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
            }

            .form-control-asp:focus {
                border-color: #2563eb;
                /* 類似 Tailwind focus:border-blue-500 */
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
                /* 類似 Tailwind focus:ring-blue-500 */
                outline: none;
            }

            .form-control-asp:focus+.input-icon,
            .input-wrapper:focus-within .input-icon {
                /* 當輸入框獲取焦點時，改變圖示顏色 */
                color: #2563eb;
            }


            .radio-group-label {
                margin-bottom: 0.5rem;
                font-weight: 500;
            }

            .radio-item-label {
                margin-left: 0.5rem;
                cursor: pointer;
                transition: color 0.3s ease-in-out;
            }

            /* ASP.NET RadioButtonList 的 ListItem 不容易直接添加動畫，這裡僅為 label 添加 hover 效果 */
            .radio-item-label:hover {
                color: #2563eb;
            }


            .btn-login-submit {
                width: 100%;
                background-color: #2563eb;
                /* 類似 Tailwind bg-blue-600 */
                color: white;
                padding: 0.75rem;
                border-radius: 0.375rem;
                font-weight: 500;
                border: none;
                transition: background-color 0.3s ease-in-out, transform 0.2s ease-in-out;
            }

            .btn-login-submit:hover {
                background-color: #1d4ed8;
                /* 類似 Tailwind hover:bg-blue-700 */
                transform: scale(1.02);
            }

            .btn-login-submit:active {
                transform: scale(0.98);
            }

            .error-message-login {
                color: #dc2626;
                /* 類似 Tailwind text-red-600 */
                font-weight: bold;
                text-align: center;
                margin-bottom: 1rem;
            }
        </style>
    </head>

    <body>
        <form id="form1" runat="server">
            <div class="login-form-container">
                <div class="login-card">
                    <h1 class="login-title">招生決策管理系統</h1>
                    <p class="login-description">請選擇您的身分並輸入帳號密碼登入系統</p>

                    <asp:Label ID="lblMessage" runat="server" CssClass="error-message-login" Font-Bold="True">
                    </asp:Label>

                    <div class="form-group">
                        <label class="form-label radio-group-label">身分選擇</label>
                        <asp:RadioButtonList ID="RadioButtonList1" runat="server" RepeatDirection="Horizontal"
                            Font-Bold="True" Font-Names="微軟正黑體 Light" Font-Size="Medium" CssClass="space-y-2">
                            <%--<asp:ListItem Value="0"><span class="radio-item-label">教師</span></asp:ListItem>
                            <asp:ListItem Value="1"><span class="radio-item-label">職員</span></asp:ListItem>--%>
                            <asp:ListItem Value="3"><span class="radio-item-label">主管</span></asp:ListItem>
                            <%--<asp:ListItem Value="3"><span class="radio-item-label">管理員</span></asp:ListItem>--%>
                        </asp:RadioButtonList>
                    </div>

                    <div class="form-group">
                        <label for="<%=txtUsername.ClientID%>" class="form-label">帳號</label>
                        <div class="input-wrapper">
                            <i class="fas fa-user input-icon"></i>
                            <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control-asp" pattern="^\d+$"
                                onkeyup="this.value=this.value.replace(/\D/g,'')"
                                onafterpaste="this.value=this.value.replace(/\D/g,'')"></asp:TextBox>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="<%=txtPassword.ClientID%>" class="form-label">密碼</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock input-icon"></i>
                            <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" CssClass="form-control-asp"
                                pattern="[^\W'-]+"></asp:TextBox>
                        </div>
                    </div>

                    <asp:Button ID="btnLogin" runat="server" CssClass="btn-login-submit" Text="登入"
                        OnClick="btnLogin_Click" />

                    <%-- 保留被隱藏的 DropDownList 和 Button 以防萬一，但它們在新設計中不可見 --%>
                        <div class="form-group" style="display:none;">
                            <asp:DropDownList ID="Drop_login" runat="server" CssClass="form-control dropdown-large"
                                AppendDataBoundItems="true" Enabled="False" Visible="False">
                            </asp:DropDownList>
                            <asp:Button ID="Button2" runat="server" OnClick="Button_type_Click" Text="確定"
                                CssClass="btn-confirm" Enabled="False" Visible="False" />
                        </div>
                </div>
            </div>
        </form>
        <!-- 原有的 Bootstrap JS 和 dependencies (可考慮移除或替換) -->
        <%-- <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
            <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script> --%>
            <script>
                // 這裡可以放置新的 JavaScript (如果需要的話)
                // 例如，如果需要 txtPassword 的 required 屬性，可以在 Page_Load 中動態添加，
                // 或者在這裡用 JavaScript 添加。
                document.addEventListener('DOMContentLoaded', function () {
                    var txtPassword = document.getElementById('<%=txtPassword.ClientID%>');
                    if (txtPassword) {
                        txtPassword.setAttribute('required', 'true');
                    }
                    var txtUsername = document.getElementById('<%=txtUsername.ClientID%>');
                    if (txtUsername) {
                        txtUsername.setAttribute('required', 'true');
                    }
                });
            </script>
    </body>

    </html>