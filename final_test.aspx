<%@ Page Language="C#" AutoEventWireup="true" CodeFile="final_test.aspx.cs" Inherits="final_test" %>

<!DOCTYPE html>
<html>

<head runat="server">
	<title>最終功能驗證測試</title>
	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
	<style>
		.container {
			max-width: 1200px;
			margin: 20px auto;
			padding: 20px;
		}

		.test-section {
			border: 1px solid #ddd;
			margin: 15px 0;
			padding: 15px;
			border-radius: 8px;
		}

		.success {
			color: green;
			font-weight: bold;
		}

		.error {
			color: red;
			font-weight: bold;
		}

		.warning {
			color: orange;
			font-weight: bold;
		}

		.info {
			background: #e7f3ff;
			padding: 10px;
			margin: 10px 0;
			border-radius: 5px;
		}

		.step {
			margin: 10px 0;
			padding: 10px;
			background: #f9f9f9;
			border-left: 4px solid #007acc;
		}

		.loading-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.7);
			z-index: 9999;
			display: none;
			justify-content: center;
			align-items: center;
			color: white;
			font-size: 20px;
		}

		.chart-container {
			width: 100%;
			height: 400px;
			margin: 20px 0;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>

		<div class="loading-overlay" id="loadingOverlay">
			<div>正在執行測試，請稍候...</div>
		</div>

		<div class="container">
			<h1>🧪 最終功能驗證測試</h1>

			<div class="info">
				<strong>📋 測試項目：</strong><br />
				✅ Session 驗證和權限檢查<br />
				✅ 資料庫連線和查詢功能<br />
				✅ Loading 載入效果<br />
				✅ JavaScript 圖表繪製<br />
				✅ 錯誤處理機制<br />
				✅ 完整的使用者工作流程
			</div>

			<!-- Step 1: Session Setup -->
			<div class="test-section">
				<h3>📋 步驟 1: Session 設定</h3>
				<asp:Button ID="btnSetupSession" runat="server" Text="⚙️ 設定管理者 Session" OnClick="btnSetupSession_Click" />
				<asp:Label ID="lblSessionStatus" runat="server" Text="Session 尚未設定"></asp:Label>
			</div>

			<!-- Step 2: Database Test -->
			<div class="test-section">
				<h3>🗄️ 步驟 2: 資料庫連線測試</h3>
				<asp:Button ID="btnTestDatabase" runat="server" Text="🔍 測試資料庫連線" OnClick="btnTestDatabase_Click" />
				<asp:Label ID="lblDatabaseStatus" runat="server" Text="資料庫尚未測試"></asp:Label>
			</div>

			<!-- Step 3: Loading Effect Test -->
			<div class="test-section">
				<h3>⏳ 步驟 3: Loading 載入效果測試</h3>
				<asp:Button ID="btnTestLoading" runat="server" Text="🔄 測試載入效果" OnClick="btnTestLoading_Click" />
				<asp:Label ID="lblLoadingStatus" runat="server" Text="載入效果尚未測試"></asp:Label>
			</div>

			<!-- Step 4: Full Analysis Test -->
			<div class="test-section">
				<h3>📊 步驟 4: 完整分析功能測試</h3>
				<div class="step">
					<strong>選擇分析項目：</strong><br />
					<asp:RadioButtonList ID="RadioButtonList1" runat="server" RepeatDirection="Horizontal">
						<asp:ListItem Value="1">日間部</asp:ListItem>
						<asp:ListItem Value="3">碩士班</asp:ListItem>
						<asp:ListItem Value="5">進修部</asp:ListItem>
					</asp:RadioButtonList>
				</div>

				<div class="step">
					<strong>選擇查詢類型：</strong><br />
					<asp:RadioButtonList ID="RadioButtonList2" runat="server" RepeatDirection="Horizontal">
						<asp:ListItem Value="1">滿意度</asp:ListItem>
						<asp:ListItem Value="2">參與度</asp:ListItem>
						<asp:ListItem Value="3">完成度</asp:ListItem>
					</asp:RadioButtonList>
				</div>

				<asp:Button ID="btnFullAnalysis" runat="server" Text="🚀 執行完整分析" OnClick="btnFullAnalysis_Click" />
				<asp:Label ID="lblAnalysisResult" runat="server" Text="分析尚未執行"></asp:Label>
			</div>

			<!-- Chart Display -->
			<div class="test-section">
				<h3>📈 圖表顯示區域</h3>
				<div class="chart-container">
					<canvas id="testChart" width="400" height="200"></canvas>
				</div>
			</div>

			<!-- Overall Status -->
			<div class="test-section">
				<h3>🏆 整體測試狀態</h3>
				<asp:Label ID="lblOverallStatus" runat="server" Text="測試尚未開始"></asp:Label>
				<br /><br />
				<asp:Button ID="btnRunAllTests" runat="server" Text="🎯 執行所有測試" OnClick="btnRunAllTests_Click" />
				<asp:Button ID="btnReset" runat="server" Text="🔄 重置測試" OnClick="btnReset_Click" />
			</div>

			<!-- Test Navigation -->
			<div class="test-section">
				<h3>🔗 相關頁面連結</h3>
				<a href="test_session_check.aspx" target="_blank">🔍 Session 檢查器</a> |
				<a href="test_login.aspx" target="_blank">🔐 測試登入</a> |
				<a href="test_main_functionality.aspx" target="_blank">🧪 主功能測試</a> |
				<a href="Analyze_recowish01.aspx" target="_blank">📊 主分析頁面</a> |
				<a href="debug_analyze.aspx" target="_blank">🐛 調試頁面</a>
			</div>
		</div>
	</form>

	<script>
		function showLoading() {
			document.getElementById('loadingOverlay').style.display = 'flex';
		}

		function hideLoading() {
			document.getElementById('loadingOverlay').style.display = 'none';
		}

		function updateTestLog(message) {
			console.log('[TEST] ' + message);
		}

		function drawTestChart(chartData, chartTitle) {
			try {
				if (!chartData || !chartTitle) {
					updateTestLog('圖表數據不完整');
					return;
				}

				updateTestLog('開始繪製測試圖表: ' + chartTitle);

				var ctx = document.getElementById('testChart').getContext('2d');
				var chart = new Chart(ctx, {
					type: 'doughnut',
					data: chartData,
					options: {
						responsive: true,
						plugins: {
							title: {
								display: true,
								text: chartTitle
							}
						}
					}
				});

				updateTestLog('✅ 測試圖表繪製完成');
			} catch (error) {
				updateTestLog('❌ 圖表繪製錯誤: ' + error.message);
				console.error('Chart error:', error);
			}
		}

		// 頁面載入完成後的初始化
		window.addEventListener('load', function () {
			updateTestLog('📋 最終測試頁面載入完成');
		});
	</script>
</body>

</html>