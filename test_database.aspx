<%@ Page Language="C#" AutoEventWireup="true" CodeFile="test_database.aspx.cs" Inherits="test_database" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head runat="server">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>資料庫連接測試</title>
	<style>
		body {
			font-family: '微軟正黑體', Arial, sans-serif;
			margin: 20px;
		}

		.test-section {
			border: 1px solid #ddd;
			margin: 10px 0;
			padding: 15px;
			border-radius: 5px;
		}

		.success {
			background-color: #d4edda;
			border-color: #c3e6cb;
			color: #155724;
		}

		.error {
			background-color: #f8d7da;
			border-color: #f5c6cb;
			color: #721c24;
		}

		.info {
			background-color: #d1ecf1;
			border-color: #bee5eb;
			color: #0c5460;
		}

		.test-btn {
			background: #007bff;
			color: white;
			padding: 10px 20px;
			border: none;
			border-radius: 3px;
			cursor: pointer;
			margin: 5px;
		}

		.test-btn:hover {
			background: #0056b3;
		}

		pre {
			background: #f8f9fa;
			padding: 10px;
			border-radius: 3px;
			overflow-x: auto;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<h1>🔍 資料庫連接和數據診斷工具</h1>

		<div class="test-section">
			<h3>測試選項</h3>
			<asp:Button ID="btnTestConnection" runat="server" Text="🔗 測試資料庫連接" CssClass="test-btn" OnClick="TestConnection_Click" />
			<asp:Button ID="btnTestViews" runat="server" Text="📊 測試視圖存在性" CssClass="test-btn" OnClick="TestViews_Click" />
			<asp:Button ID="btnTestData" runat="server" Text="📋 測試數據查詢" CssClass="test-btn" OnClick="TestData_Click" />
			<asp:Button ID="btnTestAll" runat="server" Text="🧪 執行全部測試" CssClass="test-btn" OnClick="TestAll_Click" />
		</div>

		<div class="test-section">
			<h3>📋 測試結果</h3>
			<asp:Label ID="lblResults" runat="server" Text="點擊上方按鈕開始測試..."></asp:Label>
		</div>

		<div class="test-section">
			<h3>📊 數據預覽</h3>
			<asp:GridView ID="gvPreview" runat="server" CssClass="table table-striped" AutoGenerateColumns="true" EmptyDataText="沒有數據可顯示">
			</asp:GridView>
		</div>
	</form>
</body>

</html>