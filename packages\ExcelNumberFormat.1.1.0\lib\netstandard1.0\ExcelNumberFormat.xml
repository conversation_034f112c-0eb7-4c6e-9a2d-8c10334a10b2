<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ExcelNumberFormat</name>
    </assembly>
    <members>
        <member name="M:ExcelNumberFormat.CompatibleConvert.ToString(System.Object,System.IFormatProvider)">
            <summary>
            A backward-compatible version of <see cref="M:System.Convert.ToString(System.Object,System.IFormatProvider)"/>.
            Starting from .net Core 3.0 the default precision used for formatting floating point number has changed.
            To always format numbers the same way, no matter what version of runtime is used, we specify the precision explicitly.
            </summary>
        </member>
        <member name="T:ExcelNumberFormat.ExcelDateTime">
            <summary>
            Similar to regular .NET DateTime, but also supports 0/1 1900 and 29/2 1900.
            </summary>
        </member>
        <member name="P:ExcelNumberFormat.ExcelDateTime.AdjustedDateTime">
            <summary>
            The closest .NET DateTime to the specified excel date. 
            </summary>
        </member>
        <member name="P:ExcelNumberFormat.ExcelDateTime.AdjustDaysPost">
            <summary>
            Number of days to adjust by in post.
            </summary>
        </member>
        <member name="M:ExcelNumberFormat.ExcelDateTime.#ctor(System.Double,System.Boolean)">
            <summary>
            Constructs a new ExcelDateTime from a numeric value.
            </summary>
        </member>
        <member name="M:ExcelNumberFormat.ExcelDateTime.#ctor(System.DateTime)">
            <summary>
            Wraps a regular .NET datetime.
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:ExcelNumberFormat.Formatter.FormatThousands(System.String,System.Boolean,System.Boolean,System.Collections.Generic.List{System.String},System.Globalization.CultureInfo,System.Text.StringBuilder)">
            <summary>
            Prints right-aligned, left-padded integer before the decimal separator. With optional most-significant zero.
            </summary>
        </member>
        <member name="M:ExcelNumberFormat.Formatter.FormatDecimals(System.String,System.Collections.Generic.List{System.String},System.Text.StringBuilder)">
            <summary>
            Prints left-aligned, right-padded integer after the decimal separator. Does not print significant zero.
            </summary>
        </member>
        <member name="M:ExcelNumberFormat.Formatter.FormatDenominator(System.String,System.Collections.Generic.List{System.String},System.Text.StringBuilder)">
            <summary>
            Prints left-aligned, left-padded fraction integer denominator.
            Assumes tokens contain only placeholders, valueString has fewer or equal number of digits as tokens.
            </summary>
        </member>
        <member name="M:ExcelNumberFormat.Formatter.GetLeftAlignedValueDigit(System.String,System.String,System.Int32,System.Boolean,System.Int32@)">
            <summary>
            Returns the first digit from valueString. If the token is '?' 
            returns the first significant digit from valueString, or '0' if there are no significant digits.
            The out valueIndex parameter contains the offset to the next digit in valueString.
            </summary>
        </member>
        <member name="T:ExcelNumberFormat.NumberFormat">
            <summary>
            Parse ECMA-376 number format strings and format values like Excel and other spreadsheet softwares.
            </summary>
        </member>
        <member name="M:ExcelNumberFormat.NumberFormat.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:ExcelNumberFormat.NumberFormat"/> class.
            </summary>
            <param name="formatString">The number format string.</param>
        </member>
        <member name="P:ExcelNumberFormat.NumberFormat.IsValid">
            <summary>
            Gets a value indicating whether the number format string is valid.
            </summary>
        </member>
        <member name="P:ExcelNumberFormat.NumberFormat.FormatString">
            <summary>
            Gets the number format string.
            </summary>
        </member>
        <member name="P:ExcelNumberFormat.NumberFormat.IsDateTimeFormat">
            <summary>
            Gets a value indicating whether the format represents a DateTime
            </summary>
        </member>
        <member name="P:ExcelNumberFormat.NumberFormat.IsTimeSpanFormat">
            <summary>
            Gets a value indicating whether the format represents a TimeSpan
            </summary>
        </member>
        <member name="M:ExcelNumberFormat.NumberFormat.Format(System.Object,System.Globalization.CultureInfo,System.Boolean)">
            <summary>
            Formats a value with this number format in a specified culture.
            </summary>
            <param name="value">The value to format.</param>
            <param name="culture">The culture to use for formatting.</param>
            <param name="isDate1904">If false, numeric dates start on January 0 1900 and include February 29 1900 - like Excel on PC. If true, numeric dates start on January 1 1904 - like Excel on Mac.</param>
            <returns>The formatted string.</returns>
        </member>
        <member name="M:ExcelNumberFormat.Parser.ParseNumberTokens(System.Collections.Generic.List{System.String},System.Int32,System.Collections.Generic.List{System.String}@,System.Boolean@,System.Collections.Generic.List{System.String}@)">
            <summary>
            Parses as many placeholders and literals needed to format a number with optional decimals. 
            Returns number of tokens parsed, or 0 if the tokens didn't form a number.
            </summary>
        </member>
    </members>
</doc>
