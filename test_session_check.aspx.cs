using System;
using System.Web.UI;

public partial class test_session_check : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			UpdateSessionDisplay();
		}
	}

	protected void btnRefresh_Click(object sender, EventArgs e)
	{
		UpdateSessionDisplay();
	}

	protected void btnSetupSession_Click(object sender, EventArgs e)
	{
		try
		{
			// 設定完整的測試 Session
			Session["tech_no"] = "TEST001";
			Session["user"] = "測試管理者";
			Session["master_single"] = "3";
			Session["office"] = "TEST";
			Session["ysem"] = "113";

			UpdateSessionDisplay();
		}
		catch (Exception ex)
		{
			lblSessionInfo.Text = "❌ 設定 Session 時發生錯誤: " + ex.Message;
		}
	}

	protected void btnClearSession_Click(object sender, EventArgs e)
	{
		try
		{
			Session.Clear();
			UpdateSessionDisplay();
		}
		catch (Exception ex)
		{
			lblSessionInfo.Text = "❌ 清除 Session 時發生錯誤: " + ex.Message;
		}
	}

	private void UpdateSessionDisplay()
	{
		string sessionInfo = "<h3>📊 Session 狀態報告</h3>";

		sessionInfo += "<table border='1' style='border-collapse: collapse; width: 100%;'>";
		sessionInfo += "<tr style='background: #f0f0f0;'><th>Session 變數</th><th>值</th><th>狀態</th></tr>";

		// 檢查每個 Session 變數
		AddSessionRow(ref sessionInfo, "tech_no", Session["tech_no"]);
		AddSessionRow(ref sessionInfo, "user", Session["user"]);
		AddSessionRow(ref sessionInfo, "master_single", Session["master_single"]);
		AddSessionRow(ref sessionInfo, "office", Session["office"]);
		AddSessionRow(ref sessionInfo, "ysem", Session["ysem"]);

		sessionInfo += "</table>";

		// 檢查 CheckUserSession 邏輯
		bool hasValidAuth = Session["tech_no"] != null;
		bool hasAdminAuth = Session["master_single"] != null && Session["master_single"].ToString() == "3";

		sessionInfo += "<h3>🔐 權限檢查結果</h3>";
		sessionInfo += "<div style='background: " + (hasValidAuth ? "#d4edda" : "#f8d7da") + "; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
		sessionInfo += "<strong>CheckUserSession():</strong> " + (hasValidAuth ? "✅ 通過" : "❌ 失敗") + "<br/>";
		sessionInfo += "<strong>管理者權限:</strong> " + (hasAdminAuth ? "✅ 有權限" : "❌ 無權限") + "<br/>";
		sessionInfo += "<strong>可存取主分析頁面:</strong> " + (hasValidAuth && hasAdminAuth ? "✅ 是" : "❌ 否");
		sessionInfo += "</div>";

		// 顯示當前時間
		sessionInfo += "<p><strong>⏰ 檢查時間:</strong> " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "</p>";

		lblSessionInfo.Text = sessionInfo;
	}

	private void AddSessionRow(ref string sessionInfo, string key, object value)
	{
		string valueStr = value != null ? value.ToString() : "";
		string status = value != null ? "✅ 已設定" : "❌ 未設定";
		string rowColor = value != null ? "#d4edda" : "#f8d7da";

		sessionInfo += $"<tr style='background: {rowColor};'>";
		sessionInfo += $"<td><strong>{key}</strong></td>";
		sessionInfo += $"<td>{valueStr}</td>";
		sessionInfo += $"<td>{status}</td>";
		sessionInfo += "</tr>";
	}
}
