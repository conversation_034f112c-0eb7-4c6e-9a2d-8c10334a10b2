﻿using System.Data;
using System.Data.SqlClient;


public class DBSQL
{
    //dbconnection db = new dbconnection();
    //SqlCommand cmd = new SqlCommand();
    //SqlDataAdapter da = new SqlDataAdapter();
    //DataSet ds = new DataSet();

    //Select 使用
    //public DataSet DB_Select(string _sqlStr)
    //{        
    //    if (db.conn.State != ConnectionState.Open)
    //        db.conn.Open();        
    //    cmd.Connection = db.conn; 
    //    cmd.CommandText = _sqlStr;
    //    da.SelectCommand = cmd;
    //    da.Fill(ds, "T01");
    //    return ds;
    //}

    //Select 使用
    public DataSet DB_Select(string _sqlStr)
    {
        //if (db.conn.State != ConnectionState.Open)
        //    db.conn.Open();
        DataSet ds = new DataSet();
        dbconnection db = new dbconnection();

        DataTable dt = new DataTable();
        SqlCommand cmd = new SqlCommand();
        SqlDataAdapter da = new SqlDataAdapter();
        try
        {
            db.conn.Open();
            cmd.Connection = db.conn;
            cmd.CommandText = _sqlStr;
            da.SelectCommand = cmd;
            da.Fill(ds, "T01");
            cmd.Dispose();
            da.Dispose();
        }
        finally
        {
            db.conn.Close();
            db.conn.Dispose();
        }
        return ds;
    }

    //Insert, Update, Delete 使用
    //public int DB_ExecuteNonQuery(string _sqlStr)
    //{
    //    if (db.conn.State != ConnectionState.Open)
    //        db.conn.Open();

    //    SqlCommand cmdUpdate = new SqlCommand(_sqlStr, db.conn);
    //    return cmdUpdate.ExecuteNonQuery();
    //}

    //Insert, Update, Delete 使用
    public int DB_ExecuteNonQuery(string _sqlStr)
    {
        int li_ret = 0;
        //if (db.conn.State != ConnectionState.Open)
        //    db.conn.Open();
        dbconnection db = new dbconnection();
        DataTable dt = new DataTable();
        SqlCommand cmd = new SqlCommand();
        try
        {
            db.conn.Open();
            SqlCommand cmdUpdate = new SqlCommand(_sqlStr, db.conn);
            li_ret = cmdUpdate.ExecuteNonQuery();
            cmdUpdate.Dispose();
        }
        finally
        {
            db.conn.Close();
            db.conn.Dispose();
        }
        return li_ret;
    }

}