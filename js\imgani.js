(function (t) {
  function e(e) {
    for (
      var o, i, s = e[0], c = e[1], l = e[2], u = 0, f = [];
      u < s.length;
      u++
    )
      (i = s[u]),
        Object.prototype.hasOwnProperty.call(r, i) && r[i] && f.push(r[i][0]),
        (r[i] = 0);
    for (o in c) Object.prototype.hasOwnProperty.call(c, o) && (t[o] = c[o]);
    d && d(e);
    while (f.length) f.shift()();
    return n.push.apply(n, l || []), a();
  }
  function a() {
    for (var t, e = 0; e < n.length; e++) {
      for (var a = n[e], o = !0, i = 1; i < a.length; i++) {
        var c = a[i];
        0 !== r[c] && (o = !1);
      }
      o && (n.splice(e--, 1), (t = s((s.s = a[0]))));
    }
    return t;
  }
  var o = {},
    r = { app: 0 },
    n = [];
  function i(t) {
    return (
      s.p +
      "js/" +
      ({ cart: "cart", log_in: "log_in" }[t] || t) +
      "." +
      { cart: "abaa610d", log_in: "be6df251" }[t] +
      ".js"
    );
  }
  function s(e) {
    if (o[e]) return o[e].exports;
    var a = (o[e] = { i: e, l: !1, exports: {} });
    return t[e].call(a.exports, a, a.exports, s), (a.l = !0), a.exports;
  }
  (s.e = function (t) {
    var e = [],
      a = r[t];
    if (0 !== a)
      if (a) e.push(a[2]);
      else {
        var o = new Promise(function (e, o) {
          a = r[t] = [e, o];
        });
        e.push((a[2] = o));
        var n,
          c = document.createElement("script");
        (c.charset = "utf-8"),
          (c.timeout = 120),
          s.nc && c.setAttribute("nonce", s.nc),
          (c.src = i(t));
        var l = new Error();
        n = function (e) {
          (c.onerror = c.onload = null), clearTimeout(u);
          var a = r[t];
          if (0 !== a) {
            if (a) {
              var o = e && ("load" === e.type ? "missing" : e.type),
                n = e && e.target && e.target.src;
              (l.message =
                "Loading chunk " + t + " failed.\n(" + o + ": " + n + ")"),
                (l.name = "ChunkLoadError"),
                (l.type = o),
                (l.request = n),
                a[1](l);
            }
            r[t] = void 0;
          }
        };
        var u = setTimeout(function () {
          n({ type: "timeout", target: c });
        }, 12e4);
        (c.onerror = c.onload = n), document.head.appendChild(c);
      }
    return Promise.all(e);
  }),
    (s.m = t),
    (s.c = o),
    (s.d = function (t, e, a) {
      s.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: a });
    }),
    (s.r = function (t) {
      "undefined" !== typeof Symbol &&
        Symbol.toStringTag &&
        Object.defineProperty(t, Symbol.toStringTag, { value: "Module" }),
        Object.defineProperty(t, "__esModule", { value: !0 });
    }),
    (s.t = function (t, e) {
      if ((1 & e && (t = s(t)), 8 & e)) return t;
      if (4 & e && "object" === typeof t && t && t.__esModule) return t;
      var a = Object.create(null);
      if (
        (s.r(a),
        Object.defineProperty(a, "default", { enumerable: !0, value: t }),
        2 & e && "string" != typeof t)
      )
        for (var o in t)
          s.d(
            a,
            o,
            function (e) {
              return t[e];
            }.bind(null, o)
          );
      return a;
    }),
    (s.n = function (t) {
      var e =
        t && t.__esModule
          ? function () {
              return t["default"];
            }
          : function () {
              return t;
            };
      return s.d(e, "a", e), e;
    }),
    (s.o = function (t, e) {
      return Object.prototype.hasOwnProperty.call(t, e);
    }),
    (s.p = "/Shopping-cart-Vue/"),
    (s.oe = function (t) {
      throw (console.error(t), t);
    });
  var c = (window["webpackJsonp"] = window["webpackJsonp"] || []),
    l = c.push.bind(c);
  (c.push = e), (c = c.slice());
  for (var u = 0; u < c.length; u++) e(c[u]);
  var d = l;
  n.push([0, "chunk-vendors"]), a();
})({
  0: function (t, e, a) {
    t.exports = a("56d7");
  },
  "034f": function (t, e, a) {
    "use strict";
    a("d19c");
  },
  "04e6": function (t, e, a) {
    var o = a("4bad"),
      r = a("90c5");
    (e = o(!1)), e.i(r), e.push([t.i, "", ""]), (t.exports = e);
  },
  "0748": function (t, e, a) {
    var o = a("4bad"),
      r = a("e4ee");
    (e = o(!1)), e.i(r), e.push([t.i, "", ""]), (t.exports = e);
  },
  "087b": function (t, e, a) {
    var o = a("4bad");
    (e = o(!1)),
      e.push([
        t.i,
        ".siderbar{width:260px;min-height:800px;left:0;top:500px;transform:translateX(0);transition:.5s}.bar-handler,.siderbar{position:absolute;background-color:var(--scroll-bar-color)}.bar-handler{display:none;width:30px;height:50px;top:40%;right:-30px;border-top-right-radius:10px;border-bottom-right-radius:10px}.menus li span{position:absolute;right:20px;transform:rotate(0);transition:.3s}.menus li:hover{cursor:pointer}.menus li a{font-size:var(--h3-font-size);color:var(--title-color);line-height:var(--big-font-size);width:100%;padding-left:20px;border-bottom:1px solid var(--first-color-alt)}.menu-open ul,.menus li a{display:block}.menu-close ul{display:none}.menu-close .arrow{transform:rotate(180deg)}.menu-list li{font-size:var(--normal-font-size);color:var(--text-color-light);line-height:2}.menu-list li a{transition:.3s;padding-left:30px}.menu-list li a:hover{border-left:5px solid var(--first-color);padding-left:40px;color:var(--first-color)}@media screen and (max-width:1200px){.siderbar{transform:translateX(-100%)}.siderbar-open{transform:translateX(0)}.bar-handler{display:block}}",
        "",
      ]),
      (t.exports = e);
  },
  "0e36": function (t, e, a) {
    t.exports = a.p + "images/02.jpg";
  },
  "1a4f": function (t, e, a) {
    var o = a("3f20");
    o.__esModule && (o = o.default),
      "string" === typeof o && (o = [[t.i, o, ""]]),
      o.locals && (t.exports = o.locals);
    var r = a("499e").default;
    r("8f7b66ca", o, !0, { sourceMap: !1, shadowMode: !1 });
  },
  2305: function (t, e, a) {
    "use strict";
    a("cf0e");
  },
  "3d15": function (t, e, a) {
    var o = a("4bad");
    (e = o(!1)),
      e.push([
        t.i,
        ".main-footer[data-v-dcb83508]{padding-top:150px;background:linear-gradient(-45deg,#9adcff,#fff89a,#ffb2a6,#ff8aae);background-size:400% 400%;-webkit-animation:gradient-data-v-dcb83508 15s ease infinite;animation:gradient-data-v-dcb83508 15s ease infinite}@media screen and (max-width:576px){.main-footer[data-v-dcb83508]{padding-top:50px}}.main-footer .container[data-v-dcb83508]{max-width:1200px;min-height:unset;margin:auto;display:flex}@media screen and (max-width:576px){.main-footer .container[data-v-dcb83508]{width:100%;flex-direction:column}}.footer-item[data-v-dcb83508]{width:0;flex-grow:1;margin:0 20px}@media screen and (max-width:576px){.footer-item[data-v-dcb83508]{width:50%;flex-grow:unset}}.footer-item h4[data-v-dcb83508]{font-size:24px;color:#9c9c9c;border-bottom:1px solid #fff;margin-bottom:.5em;padding-bottom:.5em}@media screen and (max-width:576px){.footer-item h4[data-v-dcb83508]{font-size:16px;margin-top:.5em}}.footer-item nav[data-v-dcb83508]{display:flex;flex-direction:column}.footer-item nav a[data-v-dcb83508]{text-decoration:none;color:#fff;line-height:1.5;padding-top:10px}.footer-item nav a[data-v-dcb83508]:hover{color:#00008b}.copyright[data-v-dcb83508]{text-align:center;margin-top:150px;padding:10px 0;color:#9b9b9b;background-color:#ffebcd}@media screen and (max-width:576px){.copyright[data-v-dcb83508]{margin-top:30px}}.footer-sub[data-v-dcb83508]{display:flex;flex-direction:column}@media screen and (max-width:576px){.footer-sub[data-v-dcb83508]{width:80%}}.footer-sub form[data-v-dcb83508]{display:flex;width:100%;margin:auto}.footer-sub input[type=text][data-v-dcb83508]{border:none;padding:5px 10px;width:0;flex-grow:1}.footer-sub input[type=submit][data-v-dcb83508]{border:none;padding:5px 10px;color:#9b9b9b;background-color:#ffdd9d}@-webkit-keyframes gradient-data-v-dcb83508{0%{background-position:0 50%}50%{background-position:100% 50%}to{background-position:0 50%}}@keyframes gradient-data-v-dcb83508{0%{background-position:0 50%}50%{background-position:100% 50%}to{background-position:0 50%}}",
        "",
      ]),
      (t.exports = e);
  },
  "3ebd": function (t, e, a) {
    var o = a("3d15");
    o.__esModule && (o = o.default),
      "string" === typeof o && (o = [[t.i, o, ""]]),
      o.locals && (t.exports = o.locals);
    var r = a("499e").default;
    r("644c88a8", o, !0, { sourceMap: !1, shadowMode: !1 });
  },
  "3f20": function (t, e, a) {
    var o = a("4bad");
    (e = o(!1)), e.push([t.i, ".home{position:relative}", ""]), (t.exports = e);
  },
  "415d": function (t, e, a) {
    t.exports = a.p + "images/04.jpg";
  },
  "56d7": function (t, e, a) {
    "use strict";
    a.r(e);
    a("e260"), a("e6cf"), a("cca6"), a("a79d");
    var o = a("2b0e"),
      r = function () {
        var t = this,
          e = t.$createElement,
          a = t._self._c || e;
        return a("div", { attrs: { id: "app" } }, [a("router-view")], 1);
      },
      n = [],
      i = {
        mounted: function () {
          this.$store.commit("updateCartFromLocalStorage");
        },
      },
      s = i,
      c = (a("034f"), a("0c7c")),
      l = Object(c["a"])(s, r, n, !1, null, null, null),
      u = l.exports,
      d = (a("d3b7"), a("3ca3"), a("ddb0"), a("8c4f")),
      f = function () {
        var t = this,
          e = t.$createElement,
          a = t._self._c || e;
        return a(
          "div",
          { staticClass: "home" },
          [
            a("Navbar"),
            a("Carousel"),
            a("router-view"),
            a("Sidebar"),
            a("Footer"),
          ],
          1
        );
      },
      p = [],
      m = function () {
        var t = this,
          e = t.$createElement,
          a = t._self._c || e;
        return a("div", { staticClass: "wrap" }, [
          a(
            "ul",
            { staticClass: "carousel" },
            t._l(t.list, function (e) {
              return a("li", { key: e.id, style: t.img(e) });
            }),
            0
          ),
          a("h1", { staticClass: "type-text" }, [t._v("more cookies...")]),
        ]);
      },
      h = [],
      v = {
        name: "Carousel",
        data: function () {
          return {
            list: [
              {
                id: 1,
                url: "https://images.unsplash.com/photo-1467189386127-c4e5e31ee213?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=870&q=80",
              },
              {
                id: 2,
                url: "https://images.unsplash.com/photo-1568827999250-3f6afff96e66?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=871&q=80",
              },
            ],
            fullWidth: 0,
          };
        },
        mounted: function () {
          var t = this;
          (t.fullWidth = window.innerWidth),
            (window.onresize = function () {
              t.fullWidth = window.innerWidth;
            }),
            t.fullWidth > 576 &&
              window.addEventListener("scroll", function () {
                var t = document.querySelector(".type-text"),
                  e = document.querySelector(".carousel");
                window.scrollY > 0
                  ? (e.classList.add("scroll"), t.classList.add("typing"))
                  : (e.classList.remove("scroll"),
                    t.classList.remove("typing"));
              });
        },
        methods: {
          img: function (t) {
            return {
              backgroundImage: "url(".concat(t.url, ")"),
              animationDelay: "".concat(5 * (t.id - 1), "s"),
              animationDuration: "10s",
            };
          },
        },
      },
      b = v,
      g = (a("e359"), Object(c["a"])(b, m, h, !1, null, "c4a012a4", null)),
      x = g.exports,
      y = function () {
        var t = this,
          e = t.$createElement,
          a = t._self._c || e;
        return a(
          "div",
          { staticClass: "nav" },
          [
            this.$store.state.user.isLogin
              ? a("LoginSuccess", { staticClass: "nav-header" })
              : t._e(),
            t._m(0),
            a(
              "div",
              { staticClass: "nav-links" },
              [
                a(
                  "router-link",
                  { staticClass: "nav-item", attrs: { to: "/" } },
                  [t._v("首頁")]
                ),
                a(
                  "router-link",
                  { staticClass: "nav-item", attrs: { to: "/LogIn" } },
                  [t._v("登入")]
                ),
                a(
                  "router-link",
                  {
                    staticClass: "nav-item",
                    attrs: { to: "/" },
                    nativeOn: {
                      click: function (e) {
                        return t.logout.apply(null, arguments);
                      },
                    },
                  },
                  [t._v(" 登出")]
                ),
                a(
                  "router-link",
                  { staticClass: "nav-item", attrs: { to: "/Cart" } },
                  [t._v("購物車")]
                ),
                a("a", { staticClass: "nav-item", attrs: { href: "/About" } }, [
                  t._v("關於我們"),
                ]),
              ],
              1
            ),
          ],
          1
        );
      },
      w = [
        function () {
          var t = this,
            e = t.$createElement,
            a = t._self._c || e;
          return a("div", { staticClass: "nav-btn" }, [
            a(
              "label",
              { staticClass: "nav-button", attrs: { for: "nav-check" } },
              [t._v(" click ")]
            ),
          ]);
        },
      ],
      k =
        (a("159b"),
        function () {
          var t = this,
            e = t.$createElement,
            a = t._self._c || e;
          return a("p", [
            t._v(" Hi, "),
            a("strong", [t._v(t._s(t.user.account))]),
            t._v(" ! "),
          ]);
        }),
      _ = [],
      C = {
        name: "LoginSuccess",
        computed: {
          user: function () {
            var t = JSON.parse(localStorage.getItem("user"));
            return t || "";
          },
        },
      },
      L = C,
      S = Object(c["a"])(L, k, _, !1, null, null, null),
      z = S.exports,
      O = {
        name: "Navbar",
        components: { LoginSuccess: z },
        data: function () {
          return { scrollNum: 0 };
        },
        mounted: function () {
          var t = this,
            e = document.querySelector(".nav");
          window.addEventListener("scroll", function () {
            var a =
              document.documentElement.scrollTop ||
              document.body.scrollTop ||
              window.pageYOffset;
            (t.scrollNum = a),
              a >= 100 ? e.classList.add("fix") : e.classList.remove("fix");
          });
          var a = document.querySelector(".nav-button"),
            o = document.querySelector(".nav-links");
          a.addEventListener("click", function () {
            var t = document.querySelector(".nav-links.open");
            t && (t.className = "nav-links close"),
              null == t && o.classList.add("open");
          });
          var r = document.querySelectorAll(".nav-item");
          r.forEach(function (t) {
            t.addEventListener("click", function () {
              o.classList.remove("open"), o.classList.add("close");
            });
          });
        },
        methods: {
          logout: function () {
            this.$store.commit({
              type: "removeUserData",
              userData: { account: "", password: "" },
            }),
              this.$router.push("/"),
              this.$store.commit("removeAll");
          },
        },
      },
      j = O,
      M = (a("7416"), Object(c["a"])(j, y, w, !1, null, null, null)),
      E = M.exports,
      q = function () {
        var t = this,
          e = t.$createElement,
          a = t._self._c || e;
        return a("nav", { staticClass: "siderbar" }, [
          a("ul", { staticClass: "menus" }, [
            a(
              "li",
              { staticClass: "menu-list menu-open" },
              [
                a(
                  "router-link",
                  { staticClass: "menu-header", attrs: { to: "/" } },
                  [
                    t._v("餅乾系列"),
                    a("span", { staticClass: "arrow" }, [t._v("▼")]),
                  ]
                ),
                a(
                  "ul",
                  { staticClass: "menu-list-item" },
                  t._l(t.cookieList, function (e) {
                    return a(
                      "li",
                      { key: e.id },
                      [
                        a(
                          "router-link",
                          {
                            attrs: {
                              to: {
                                name: "ProductDetial",
                                query: { Id: e.id },
                              },
                            },
                            nativeOn: {
                              click: function (e) {
                                return t.reload.apply(null, arguments);
                              },
                            },
                          },
                          [t._v(t._s(e.name))]
                        ),
                      ],
                      1
                    );
                  }),
                  0
                ),
              ],
              1
            ),
            a(
              "li",
              { staticClass: "menu-list menu-open" },
              [
                a(
                  "router-link",
                  { staticClass: "menu-header", attrs: { to: "/" } },
                  [
                    t._v("其他點心"),
                    a("span", { staticClass: "arrow" }, [t._v("▼")]),
                  ]
                ),
                a(
                  "ul",
                  { staticClass: "menu-list-item" },
                  t._l(t.otherList, function (e) {
                    return a(
                      "li",
                      { key: e.id },
                      [
                        a(
                          "router-link",
                          {
                            attrs: {
                              to: {
                                name: "ProductDetial",
                                query: { Id: e.id },
                              },
                            },
                            nativeOn: {
                              click: function (e) {
                                return t.reload.apply(null, arguments);
                              },
                            },
                          },
                          [t._v(t._s(e.name))]
                        ),
                      ],
                      1
                    );
                  }),
                  0
                ),
              ],
              1
            ),
          ]),
          a("div", { staticClass: "bar-handler" }, [t._v("X")]),
        ]);
      },
      D = [],
      N = (a("4de4"), a("c7a5")),
      $ = {
        name: "Sidebar",
        data: function () {
          return { itemList: N["a"] };
        },
        mounted: function () {
          var t = this,
            e = document.querySelectorAll(".menu-header");
          e.forEach(function (e) {
            e.addEventListener("click", t.toggleSkills);
          });
          var a = document.querySelector(".bar-handler"),
            o = document.querySelector(".siderbar");
          a.addEventListener("click", function () {
            "siderbar" === o.className
              ? o.classList.add("siderbar-open")
              : o.classList.remove("siderbar-open");
          });
          var r = document.querySelectorAll(".menu-list-item");
          r.forEach(function (t) {
            t.addEventListener("click", function () {
              o.classList.remove("siderbar-open");
            });
          });
        },
        computed: {
          cookieList: function () {
            return this.itemList.filter(function (t) {
              return "cookie" === t.class;
            });
          },
          otherList: function () {
            return this.itemList.filter(function (t) {
              return "other" === t.class;
            });
          },
        },
        methods: {
          toggleSkills: function (t) {
            var e = t.target.parentNode.className;
            "menu-list menu-open" === e &&
              (t.target.parentNode.className = "menu-list menu-close"),
              "menu-list menu-close" === e &&
                (t.target.parentNode.className = "menu-list menu-open");
          },
          reload: function () {
            this.$router.go(0);
          },
        },
      },
      P = $,
      I = (a("2305"), Object(c["a"])(P, q, D, !1, null, null, null)),
      A = I.exports,
      H = function () {
        var t = this,
          e = t.$createElement,
          a = t._self._c || e;
        return a("div", { staticClass: "main-footer" }, [
          t._m(0),
          a("div", { staticClass: "copyright", attrs: { id: "copyright" } }, [
            a("p", [t._v(t._s(t.name) + "公司 copyright" + t._s(t.year))]),
          ]),
        ]);
      },
      T = [
        function () {
          var t = this,
            e = t.$createElement,
            a = t._self._c || e;
          return a("div", { staticClass: "container" }, [
            a("div", { staticClass: "footer-item" }, [
              a("h4", [t._v("聯絡我們")]),
              a("nav", [
                a("a", { attrs: { href: "#" } }, [t._v("聯絡我們")]),
                a("a", { attrs: { href: "#" } }, [t._v("其他問題")]),
                a("a", { attrs: { href: "#" } }, [t._v("隱私政策")]),
                a("a", { attrs: { href: "#" } }, [t._v("法律條款")]),
              ]),
            ]),
            a("div", { staticClass: "footer-item" }, [
              a("h4", [t._v("聯絡我們")]),
              a("nav", [
                a("a", { attrs: { href: "#" } }, [t._v("聯絡我們")]),
                a("a", { attrs: { href: "#" } }, [t._v("其他問題")]),
                a("a", { attrs: { href: "#" } }, [t._v("隱私政策")]),
                a("a", { attrs: { href: "#" } }, [t._v("法律條款")]),
              ]),
            ]),
            a("div", { staticClass: "footer-item footer-sub" }, [
              a("h4", [t._v("訂閱電子報")]),
              a("form", { attrs: { action: "" } }, [
                a("input", { attrs: { type: "text" } }),
                a("input", { attrs: { type: "submit", value: "訂閱" } }),
              ]),
            ]),
          ]);
        },
      ],
      U = {
        name: "Footer",
        data: function () {
          return { name: "HUYUEHHSUAN", year: " " + new Date().getFullYear() };
        },
      },
      W = U,
      F = (a("7b59"), Object(c["a"])(W, H, T, !1, null, "dcb83508", null)),
      J = F.exports,
      Y = {
        name: "Home",
        components: { Navbar: E, Sidebar: A, Footer: J, Carousel: x },
      },
      B = Y,
      G = (a("b76c"), Object(c["a"])(B, f, p, !1, null, null, null)),
      V = G.exports;
    o["a"].use(d["a"]);
    var X = [
        {
          path: "/",
          name: "Home",
          component: V,
          children: [
            {
              path: "/",
              name: "Dashboard",
              component: function () {
                return a.e("cart").then(a.bind(null, "8619"));
              },
            },
            {
              path: "/Cart",
              name: "Cart",
              component: function () {
                return a.e("cart").then(a.bind(null, "b789"));
              },
            },
            {
              path: "/LogIn",
              name: "LogIn",
              component: function () {
                return a.e("log_in").then(a.bind(null, "9ddf"));
              },
            },
            {
              path: "/ProductDetial",
              name: "ProductDetial",
              component: function () {
                return a.e("log_in").then(a.bind(null, "efc6"));
              },
            },
            {
              path: "/About",
              name: "About",
              component: function () {
                return a.e("log_in").then(a.bind(null, "f820"));
              },
            },
          ],
        },
      ],
      R = new d["a"]({
        mode: "history",
        base: "/Shopping-cart-Vue/",
        routes: X,
      }),
      Q = d["a"].prototype.push;
    d["a"].prototype.push = function (t) {
      return Q.call(this, t).catch(function (t) {
        return t;
      });
    };
    var K = R,
      Z = a("f3f3"),
      tt = (a("e9c4"), a("7db0"), a("2f62"));
    function et(t) {
      localStorage.setItem("cart", JSON.stringify(t));
    }
    o["a"].use(tt["a"]);
    var at = function (t) {
        t.subscribe(function (t, e) {
          var a = e.user;
          ("setUserData" !== t.type && "removeUserData" !== t.type) ||
            window.localStorage.setItem("user", JSON.stringify(a));
        });
      },
      ot = new tt["a"].Store({
        plugins: [at],
        state: { cart: [], user: { account: "", password: "", isLogin: !1 } },
        getters: {
          productQuantity: function (t) {
            return function (e) {
              var a = t.cart.find(function (t) {
                return t.id === e.id;
              });
              return a ? a.quantity : 0;
            };
          },
          cartItems: function (t) {
            return t.cart;
          },
          cartTotal: function (t) {
            return t.cart.reduce(function (t, e) {
              return t + e.price * e.quantity;
            }, 0);
          },
        },
        mutations: {
          setUserData: function (t, e) {
            var a = e.userData;
            (t.user.account = a.account),
              (t.user.password = a.password),
              (t.user.isLogin = !0);
          },
          removeUserData: function (t, e) {
            var a = e.userData;
            (t.user.account = a.account),
              (t.user.password = a.password),
              (t.user.isLogin = !1);
          },
          addItemsToCart: function (t, e) {
            var a = t.cart.find(function (t) {
              return t.id === e.id;
            });
            a
              ? (a.quantity = a.quantity + e.quantity)
              : t.cart.push(Object(Z["a"])({}, e)),
              et(t.cart);
          },
          addToCart: function (t, e) {
            var a = t.cart.find(function (t) {
              return t.id === e.id;
            });
            a
              ? a.quantity++
              : t.cart.push(
                  Object(Z["a"])(Object(Z["a"])({}, e), {}, { quantity: 1 })
                ),
              et(t.cart);
          },
          removeFromCart: function (t, e) {
            var a = t.cart.find(function (t) {
              return t.id === e.id;
            });
            a &&
              (a.quantity > 1
                ? a.quantity--
                : (t.cart = t.cart.filter(function (t) {
                    return t.id !== e.id;
                  }))),
              et(t.cart);
          },
          removeOneItem: function (t, e) {
            var a = t.cart.find(function (t) {
              return t.id === e.id;
            });
            a &&
              (t.cart = t.cart.filter(function (t) {
                return t.id !== e.id;
              })),
              et(t.cart);
          },
          removeAll: function (t) {
            (t.cart = []), et(t.cart);
          },
          updateCartFromLocalStorage: function (t) {
            var e = localStorage.getItem("cart");
            e && (t.cart = JSON.parse(e));
          },
        },
      });
    (o["a"].config.productionTip = !1),
      new o["a"]({
        router: K,
        store: ot,
        render: function (t) {
          return t(u);
        },
      }).$mount("#app");
  },
  6701: function (t, e, a) {
    var o = a("4bad");
    (e = o(!1)),
      e.push([
        t.i,
        ".wrap[data-v-c4a012a4]{width:100%;height:500px;background-color:beige;position:relative}.carousel[data-v-c4a012a4]{width:100%;height:100%;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transition:.5s;z-index:2}.scroll[data-v-c4a012a4]{}.carousel>li[data-v-c4a012a4]{width:100%;height:100%;background-position:50%;background-repeat:no-repeat;background-size:150% auto;position:absolute;top:0;left:0;-webkit-animation-name:carouselanimate-data-v-c4a012a4;animation-name:carouselanimate-data-v-c4a012a4;-webkit-animation-timing-function:linear;animation-timing-function:linear;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite}.type-text[data-v-c4a012a4]{position:absolute;left:40%;bottom:0;font-family:monospace;color:#fff;font-size:60px;width:0;overflow:hidden}.typing[data-v-c4a012a4]{-webkit-animation:typing-data-v-c4a012a4 6s steps(16,jump-none) infinite forwards;animation:typing-data-v-c4a012a4 6s steps(16,jump-none) infinite forwards}@-webkit-keyframes carouselanimate-data-v-c4a012a4{0%{opacity:0;background-size:150% auto}25%{opacity:1}50%{opacity:1}75%{opacity:0}to{opacity:0;background-size:110% auto}}@keyframes carouselanimate-data-v-c4a012a4{0%{opacity:0;background-size:150% auto}25%{opacity:1}50%{opacity:1}75%{opacity:0}to{opacity:0;background-size:110% auto}}@-webkit-keyframes typing-data-v-c4a012a4{0%{width:0}to{width:15ch}}@keyframes typing-data-v-c4a012a4{0%{width:0}to{width:15ch}}",
        "",
      ]),
      (t.exports = e);
  },
  7416: function (t, e, a) {
    "use strict";
    a("9a65");
  },
  "7b59": function (t, e, a) {
    "use strict";
    a("3ebd");
  },
  "835c": function (t, e, a) {
    t.exports = a.p + "images/05.jpg";
  },
  "88a8": function (t, e, a) {
    var o = a("4bad"),
      r = a("087b");
    (e = o(!1)), e.i(r), e.push([t.i, "", ""]), (t.exports = e);
  },
  "90c5": function (t, e, a) {
    var o = a("4bad");
    (e = o(!1)),
      e.push([
        t.i,
        ".nav{display:flex;justify-content:flex-end;align-items:center;background-color:#fff;width:100%;position:fixed;z-index:5;transition:.5s;opacity:1;padding:1rem 2rem}.nav>.nav-btn{display:none}.nav-item{padding:.5rem;margin:0 10px;border-bottom:2px solid transparent}.nav-item:hover{color:var(--first-color-lighter);border-bottom:2px solid var(--first-color-lighter)}.fix{opacity:.8}@media (max-width:600px){.fix{opacity:1}.nav-item{padding:.8rem}.nav>.nav-btn{width:100%;display:inline-block;position:absolute;right:0;top:0;background-color:#fff}.nav>.nav-btn>.nav-button{display:block;width:50px;height:50px;line-height:50px;border-top:2px solid #eee;margin-left:auto}.nav>.nav-links{position:absolute;display:block;width:100%;height:0;transition:all .3s ease-in;overflow-y:hidden;top:50px;left:0;background-color:#fff}.nav>.nav-links>.nav-item{display:block}.close{height:0!important}.open{height:calc(100vh - 50px)!important;overflow-y:auto!important}}",
        "",
      ]),
      (t.exports = e);
  },
  9690: function (t, e, a) {
    var o = a("6701");
    o.__esModule && (o = o.default),
      "string" === typeof o && (o = [[t.i, o, ""]]),
      o.locals && (t.exports = o.locals);
    var r = a("499e").default;
    r("73d50321", o, !0, { sourceMap: !1, shadowMode: !1 });
  },
  "9a65": function (t, e, a) {
    var o = a("04e6");
    o.__esModule && (o = o.default),
      "string" === typeof o && (o = [[t.i, o, ""]]),
      o.locals && (t.exports = o.locals);
    var r = a("499e").default;
    r("46f02428", o, !0, { sourceMap: !1, shadowMode: !1 });
  },
  ac7c: function (t, e, a) {
    t.exports = a.p + "images/03.jpg";
  },
  b0c3: function (t, e, a) {
    t.exports = a.p + "images/01.jpg";
  },
  b76c: function (t, e, a) {
    "use strict";
    a("1a4f");
  },
  c7a5: function (t, e, a) {
    "use strict";
    var o = [
      {
        id: 1,
        name: "OREO棉花糖餅乾",
        price: 55,
        description: "好吃的OREO棉花糖餅乾",
        class: "cookie",
        img: a("b0c3"),
      },
      {
        id: 2,
        name: "LOTUS棉花糖餅乾",
        price: 55,
        description: "好吃的LOTUS棉花糖餅乾",
        class: "cookie",
        img: a("0e36"),
      },
      {
        id: 3,
        name: "抹茶巧克力棉花糖餅乾",
        price: 50,
        description: "好吃的抹茶巧克力棉花糖餅乾",
        class: "cookie",
        img: a("ac7c"),
      },
      {
        id: 4,
        name: "蔓越莓餅乾",
        price: 40,
        description: "好吃的蔓越莓餅乾",
        class: "cookie",
        img: a("415d"),
      },
      {
        id: 5,
        name: "巴斯克乳酪蛋糕",
        price: 300,
        description: "好吃的巴斯克乳酪蛋糕",
        class: "other",
        img: a("835c"),
      },
      {
        id: 6,
        name: "蜂蜜燕麥磅蛋糕",
        price: 250,
        description: "好吃的蜂蜜燕麥磅蛋糕",
        class: "other",
        img: a("ee30"),
      },
    ];
    e["a"] = o;
  },
  cf0e: function (t, e, a) {
    var o = a("88a8");
    o.__esModule && (o = o.default),
      "string" === typeof o && (o = [[t.i, o, ""]]),
      o.locals && (t.exports = o.locals);
    var r = a("499e").default;
    r("3a65b562", o, !0, { sourceMap: !1, shadowMode: !1 });
  },
  d19c: function (t, e, a) {
    var o = a("0748");
    o.__esModule && (o = o.default),
      "string" === typeof o && (o = [[t.i, o, ""]]),
      o.locals && (t.exports = o.locals);
    var r = a("499e").default;
    r("acbbb16c", o, !0, { sourceMap: !1, shadowMode: !1 });
  },
  e359: function (t, e, a) {
    "use strict";
    a("9690");
  },
  e4ee: function (t, e, a) {
    var o = a("4bad");
    (e = o(!1)),
      e.push([
        t.i,
        "@import url(https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap);",
      ]),
      e.push([
        t.i,
        ':root{--header-height:3rem;--hue-color:25;--first-color:hsl(var(--hue-color),69%,61%);--first-color-second:hsl(var(--hue-color),69%,61%);--first-color-alt:hsl(var(--hue-color),57%,53%);--first-color-lighter:hsl(var(--hue-color),92%,85%);--title-color:hsl(var(--hue-color),8%,15%);--text-color:hsl(var(--hue-color),8%,45%);--text-color-light:hsl(var(--hue-color),8%,65%);--input-color:hsl(var(--hue-color),70%,96%);--body-color:hsl(var(--hue-color),60%,99%);--container-color:#fff;--scroll-bar-color:hsl(var(--hue-color),12%,90%);--scroll-thumb-color:hsl(var(--hue-color),12%,80%);--body-font:"Poppins",sans-serif;--big-font-size:2rem;--h1-font-size:1.5rem;--h2-font-size:1.25rem;--h3-font-size:1.125rem;--normal-font-size:.938rem;--small-font-size:.813rem;--smaller-font-size:.75rem;--font-medium:500;--font-semi-bold:600;--mb-0-25:.25rem;--mb-0-5:.5rem;--mb-0-75:.75rem;--mb-1:1rem;--mb-1-5:1.5rem;--mb-2:2rem;--mb-2-5:2.5rem;--mb-3:3rem;--z-tooltip:10;--z-fixed:100;--z-modal:1000}@media screen and (min-width:968px){:root{--big-font-size:3rem;--h1-font-size:2.25rem;--h2-font-size:1.5rem;--h3-font-size:1.25rem;--normal-font-size:1rem;--small-font-size:.875rem;--smaller-font-size:.813rem}}*{box-sizing:border-box;padding:0;margin:0}html{scroll-behavior:smooth}body{margin:0 0 var(--header-height) 0;font-family:var(--body-font);font-size:var(--normal-font-size);background-color:var(--body-color)}body,h1,h2,h3,h4{color:var(--text-color)}h1,h2,h3,h4{font-weight:var(--font-semi-bold);text-align:center;margin-bottom:var(--mb-1)}ul{list-style:none}a{text-decoration:none}img{max-width:100%;height:auto}.container{max-width:900px;min-height:800px;margin:auto;padding:2rem 0}@media screen and (max-width:900px){.container{padding:1rem}}.grid{display:grid;grid-template-columns:repeat(3,1fr);gap:1.5rem;justify-items:center}@media screen and (max-width:900px){.grid{grid-template-columns:repeat(2,1fr)}}@media screen and (max-width:700px){.grid{grid-template-columns:repeat(1,1fr)}}.box-border{border:1px solid var(--first-color-lighter);box-shadow:2px 2px 10px 0 rgba(#333,.2);padding:2rem;margin:auto}button{display:block;background-color:#fff;border-radius:.5rem;padding:.5rem;display:inline-block;border:1px solid #999;text-align:center;margin-top:.5rem}button:hover{cursor:pointer}.back{text-align:start;max-width:800px;display:block;margin:auto}.color-button{border:0;border-radius:0;outline:0;color:#fff;font-size:var(--normal-font-size);letter-spacing:2px;background:linear-gradient(45deg,var(--first-color),var(--first-color-lighter));cursor:pointer;line-height:1rem}.color-button:hover{background:linear-gradient(225deg,var(--first-color),var(--first-color-lighter))}',
        "",
      ]),
      (t.exports = e);
  },
  ee30: function (t, e, a) {
    t.exports = a.p + "images/06.jpg";
  },
});
//# sourceMappingURL=app.ea58dc5f.js.map
