using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.Script.Serialization;
using System.IO;
using System.Web.UI;
using iTextSharp.text;
using iTextSharp.text.pdf;
using ClosedXML.Excel;
using System.Web.UI.WebControls;
using System.Web;
using System.Windows.Interop;

public partial class Report_studdata001 : System.Web.UI.Page
{

    App_Func appfun = new App_Func();
    dbconnection db = new dbconnection();
    SqlCommand cmd = new SqlCommand();
    SqlDataAdapter da = new SqlDataAdapter();
    DataSet ds = new DataSet();
    string sqlStr;
    private static DataTable GV1;
    protected void Page_Load(object sender, EventArgs e)
    {
        if (CheckUserSession() == false) { return; }
        if (Session["master_single"].ToString() != "master")
        {
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('權限不足,您無法使用本功能!');location.href='/Home.aspx';", true);
            //Response.Redirect("Home.aspx");
            return;
        }
        if (!IsPostBack)
        {
            cmd.Connection = db.conn;
            sqlStr = @"select DISTINCT a.ysem from (select DISTINCT left(ysem,3) ysem from sgrs_wsdata01 union all select DISTINCT left(ysem,3) ysem from sgrs_hsdata01 union all select DISTINCT left(ysem,3) ysem from sgrs_imdata01) a";
            cmd.CommandText = sqlStr;
            da.SelectCommand = cmd;
            da.Fill(ds, "ysem");
            DataTable tysem = ds.Tables["ysem"];
            if (tysem.Rows.Count > 0)
            {
                ddl_gradeyyy.Items.Add(new System.Web.UI.WebControls.ListItem("==請選擇==", ""));
                ddl_gradeyyy.Items.Add(new System.Web.UI.WebControls.ListItem("==全部==", "0"));
                for (int i = 0; i < tysem.Rows.Count; i++)
                {
                    ddl_gradeyyy.Items.Add(new System.Web.UI.WebControls.ListItem(tysem.Rows[i][0].ToString().Trim(), tysem.Rows[i][0].ToString().Trim()));
                }
            }
            else
            {
                P1.Visible = false;
                Button_search.Enabled = false;
                msg.Text = "訊息:目前無資料，無法查詢。";
                return;
            }
        }
    }

    protected void Button_search_Click(object sender, EventArgs e)
    {
        if (RadioButtonList1.SelectedValue == "")
        {
            msg.Text = "訊息:請選擇獎助學金!";
            return;
        }
        else if (ddl_gradeyyy.SelectedValue.ToString().Trim() == "")
        {
            msg.Text = "訊息:請選擇學年度!";
            return;
        }
        else if (string.IsNullOrWhiteSpace(TextBox_studno.Text))
        {
            msg.Text = "訊息:請輸入學號!";
            return;
        }
        else
        {
            P1.Visible = true;
            GetGV1Data1();
        }

    }

    protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (RadioButtonList1.SelectedValue == "0")
        {
            P1.Visible = false;
            GridView1.AllowPaging = false;
            GridView1.DataSource = "";
            GridView1.DataBind();
            msg.Text = "訊息:";
        }
        else if (RadioButtonList1.SelectedValue == "1")
        {
            P1.Visible = false;
            GridView1.AllowPaging = false;
            GridView1.DataSource = "";
            GridView1.DataBind();
            msg.Text = "訊息:";
        }
        else if (RadioButtonList1.SelectedValue == "2")
        {
            P1.Visible = false;
            GridView1.AllowPaging = false;
            GridView1.DataSource = "";
            GridView1.DataBind();
            msg.Text = "訊息:";
        }
        else if (RadioButtonList1.SelectedValue == "3")
        {
            P1.Visible = false;
            GridView1.AllowPaging = false;
            GridView1.DataSource = "";
            GridView1.DataBind();
            msg.Text = "訊息:";
        }
    }
    private void GetGV1Data1()
    {
        string chartTitle = string.Empty, stud_no; // 初始化
        msg.Text = "訊息:";
        stud_no = appfun.FilterGdName(TextBox_studno.Text.Trim());
        try
        {
            cmd.Connection = db.conn;

            // 查詢最新的數據
            if (RadioButtonList1.SelectedValue == "0")
            {
                sqlStr = @"SELECT a.ysem AS '學年期','工讀金' AS '項目' ,e.ws_name AS '項目名稱',d.fund AS '經費來源',b.class_name AS '班級',b.stud_no AS '學號',b.stud_cname AS '學生姓名',
                           case b.qualifications when 'Y' then '是' when 'N' then '否' else '' end  AS '領取資格', b.tot AS '領取金額',
                           case a.verify when 'Y' then '已核實' when 'N' then '未核實' else '' end AS '核實狀態',trim(f.tech_cname)  AS '承辦人'
                           FROM sgrs_wsdata01 a
                           INNER JOIN sgrs_wsdata011 b ON a.sn = b.sn
                           INNER JOIN sgrs_wslist c ON a.ws_no = c.ws_no
                           INNER JOIN sgrs_fundlist d ON c.fund=d.fund_no 
						   INNER JOIN sgrs_wslist e ON a.ws_no=e.ws_no
						   INNER JOIN s12_teacher f ON a.tech_no=f.tech_no
                           WHERE a.verify<>'V' and b.stud_no=@stud_no and left(a.ysem,3) like @ysem+'%'
                           union all
                           SELECT a.ysem AS '學年期','助學金' AS '項目' ,e.hs_name AS '項目名稱',d.fund AS '經費來源',b.class_name AS '班級',b.stud_no AS '學號',b.stud_cname AS '學生姓名',
                           case b.qualifications when 'Y' then '是' when 'N' then '否' else '' end  AS '領取資格', b.tot AS '領取金額',
                           case a.verify when 'Y' then '已核實' when 'N' then '未核實' else '' end AS '核實狀態',trim(f.tech_cname)  AS '承辦人'
                           FROM sgrs_hsdata01 a
                           INNER JOIN sgrs_hsdata011 b ON a.sn = b.sn
                           INNER JOIN sgrs_hslist c ON a.hs_no = c.hs_no
                           INNER JOIN sgrs_fundlist d ON c.fund=d.fund_no 
						   INNER JOIN sgrs_hslist e ON a.hs_no=e.hs_no
						   INNER JOIN s12_teacher f ON a.tech_no=f.tech_no
                           WHERE a.verify<>'V' and b.stud_no=@stud_no and left(a.ysem,3) like @ysem+'%'
                           union all
                           SELECT a.ysem AS '學年期','獎勵金' AS '項目' ,e.im_name AS '項目名稱',d.fund AS '經費來源',b.class_name AS '班級',b.stud_no AS '學號',b.stud_cname AS '學生姓名',
                           case b.qualifications when 'Y' then '是' when 'N' then '否' else '' end  AS '領取資格', b.tot AS '領取金額',
                           case a.verify when 'Y' then '已核實' when 'N' then '未核實' else '' end AS '核實狀態',trim(f.tech_cname)  AS '承辦人'
                           FROM sgrs_imdata01 a
                           INNER JOIN sgrs_imdata011 b ON a.sn = b.sn
                           INNER JOIN sgrs_imlist c ON a.im_no = c.im_no
                           INNER JOIN sgrs_fundlist d ON c.fund=d.fund_no 
						   INNER JOIN sgrs_imlist e ON a.im_no=e.im_no
						   INNER JOIN s12_teacher f ON a.tech_no=f.tech_no
                           WHERE a.verify<>'V' and b.stud_no=@stud_no and left(a.ysem,3) like @ysem+'%'
						   order by 1,2,10; ";
                chartTitle = "全部請領狀況查詢 - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "全部請領狀況查詢";
            }
            else if (RadioButtonList1.SelectedValue == "1")
            {
                sqlStr = @"SELECT a.ysem AS '學年期','工讀金' AS '項目' ,e.ws_name AS '項目名稱',d.fund AS '經費來源',b.class_name AS '班級',b.stud_no AS '學號',b.stud_cname AS '學生姓名',
                           case b.qualifications when 'Y' then '是' when 'N' then '否' else '' end  AS '領取資格', b.tot AS '領取金額',
                           case a.verify when 'Y' then '已核實' when 'N' then '未核實' else '' end AS '核實狀態',trim(f.tech_cname)  AS '承辦人'
                           FROM sgrs_wsdata01 a
                           INNER JOIN sgrs_wsdata011 b ON a.sn = b.sn
                           INNER JOIN sgrs_wslist c ON a.ws_no = c.ws_no
                           INNER JOIN sgrs_fundlist d ON c.fund=d.fund_no 
						   INNER JOIN sgrs_wslist e ON a.ws_no=e.ws_no
						   INNER JOIN s12_teacher f ON a.tech_no=f.tech_no
                           WHERE a.verify<>'V' and b.stud_no=@stud_no and left(a.ysem,3) like @ysem+'%'; ";
                chartTitle = "工讀金請領狀況查詢 - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "工讀金學生領取資料";
            }
            else if (RadioButtonList1.SelectedValue == "2")
            {
                sqlStr = @"SELECT a.ysem AS '學年期','助學金' AS '項目' ,e.hs_name AS '項目名稱',d.fund AS '經費來源',b.class_name AS '班級',b.stud_no AS '學號',b.stud_cname AS '學生姓名',
                           case b.qualifications when 'Y' then '是' when 'N' then '否' else '' end  AS '領取資格', b.tot AS '領取金額',
                           case a.verify when 'Y' then '已核實' when 'N' then '未核實' else '' end AS '核實狀態',trim(f.tech_cname)  AS '承辦人'
                           FROM sgrs_hsdata01 a
                           INNER JOIN sgrs_hsdata011 b ON a.sn = b.sn
                           INNER JOIN sgrs_hslist c ON a.hs_no = c.hs_no
                           INNER JOIN sgrs_fundlist d ON c.fund=d.fund_no 
						   INNER JOIN sgrs_hslist e ON a.hs_no=e.hs_no
						   INNER JOIN s12_teacher f ON a.tech_no=f.tech_no
                           WHERE a.verify<>'V' and b.stud_no=@stud_no and left(a.ysem,3) like @ysem+'%'; ";
                chartTitle = "助學金請領狀況查詢 - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "工讀金學生領取資料";
            }
            else if (RadioButtonList1.SelectedValue == "3")
            {
                sqlStr = @"SELECT a.ysem AS '學年期','獎勵金' AS '項目' ,e.im_name AS '項目名稱',d.fund AS '經費來源',b.class_name AS '班級',b.stud_no AS '學號',b.stud_cname AS '學生姓名',
                           case b.qualifications when 'Y' then '是' when 'N' then '否' else '' end  AS '領取資格', b.tot AS '領取金額',
                           case a.verify when 'Y' then '已核實' when 'N' then '未核實' else '' end AS '核實狀態',trim(f.tech_cname)  AS '承辦人'
                           FROM sgrs_imdata01 a
                           INNER JOIN sgrs_imdata011 b ON a.sn = b.sn
                           INNER JOIN sgrs_imlist c ON a.im_no = c.im_no
                           INNER JOIN sgrs_fundlist d ON c.fund=d.fund_no 
						   INNER JOIN sgrs_imlist e ON a.im_no=e.im_no
						   INNER JOIN s12_teacher f ON a.tech_no=f.tech_no
                           WHERE a.verify<>'V' and b.stud_no=@stud_no and left(a.ysem,3) like @ysem+'%'; ";
                chartTitle = "工讀金學生領取資料 - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "工讀金學生領取資料";
            }
            else
            {
                msg.Text = "訊息:請選擇獎助學金項目!";
                P1.Visible = false;
                return;
            }

            cmd.CommandText = sqlStr;
            // 使用參數化查詢來避免 SQL Injection
            cmd.Parameters.Clear();
            cmd.Parameters.AddWithValue("@ysem", ddl_gradeyyy.SelectedValue.Trim());
            cmd.Parameters.AddWithValue("@stud_no", stud_no);
            //cmd.Parameters.AddWithValue("@tech_no", Session["tech_no"].ToString().Trim());
            //cmd.Parameters.AddWithValue("@ysem", Session["ysem"].ToString().Trim().Substring(1,3));
            da.SelectCommand = cmd;
            da.Fill(ds, "check");
            //DataTable tcheck = ds.Tables["check"];
            GV1 = ds.Tables["check"];

            // 確認有數據再進行處理       
            if (GV1.Rows.Count > 0)
            {
                //JavaScriptSerializer serializer = new JavaScriptSerializer();
                //var chartData = new System.Collections.Generic.List<object>();
                //if (RadioButtonList1.SelectedValue == "1")
                //{
                //    chartData.Add(new object[] { "經費來源", "金額" });

                //    foreach (DataRow row in GV1.Rows)
                //    {
                //        chartData.Add(new object[] { row["經費來源"].ToString(), Convert.ToInt32(row["金額"]) });
                //    }
                //}
                //else if (RadioButtonList1.SelectedValue == "2")
                //{
                //    chartData.Add(new object[] { "經費來源", "人次" });

                //    foreach (DataRow row in GV1.Rows)
                //    {
                //        chartData.Add(new object[] { row["經費來源"].ToString(), Convert.ToInt32(row["人次"]) });
                //    }
                //}
                //else if (RadioButtonList1.SelectedValue == "3")
                //{
                //    chartData.Add(new object[] { "科系", "人次" });

                //    foreach (DataRow row in GV1.Rows)
                //    {
                //        chartData.Add(new object[] { row["科系"].ToString(), Convert.ToInt32(row["人次"]) });
                //    }
                //// 將標題傳遞到前端
                ////Page.ClientScript.RegisterStartupScript(this.GetType(), "chartTitle", $"var chartTitle = '{chartTitle}';", true); // C# 6.0 以後的寫法
                //Page.ClientScript.RegisterStartupScript(this.GetType(), "chartTitle", "var chartTitle = '" + chartTitle + "';", true);
                //string jsonData = serializer.Serialize(chartData);
                //Page.ClientScript.RegisterStartupScript(this.GetType(), "chartData", "var chartData = " + jsonData + ";", true);
                GridView1.DataSource = GV1;
                GridView1.AllowPaging = true;
                GridView1.PageSize = 10;
                GridView1.DataBind();
            }
            else
            {
                // 處理沒有找到數據的情況
                GridView1.AllowPaging = false;
                GridView1.DataSource = "";
                GridView1.DataBind();
                msg.Text = "訊息:查無資料。";
                P1.Visible = false;
            }
        }
        catch (Exception ex)
        {
            // 處理異常
            msg.Text = "錯誤: " + ex.Message;
            msg.ForeColor = System.Drawing.Color.Red;
        }
        finally
        {
            db.conn.Close();
        }
    }


    public void GridView1_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            // 設定每個欄位的寬度沒設定會套用css的預設寬度
            e.Row.Cells[0].Style.Add("width", "200px");
            e.Row.Cells[1].Style.Add("width", "50px");
            //e.Row.Cells[2].Style.Add("width", "200px");
            //e.Row.Cells[3].Style.Add("width", "50px");
            //e.Row.Cells[4].Style.Add("width", "80px");
            //e.Row.Cells[5].Style.Add("width", "80px");
            //e.Row.Cells[6].Style.Add("width", "80px");
            //e.Row.Cells[7].Style.Add("width", "50px");
            //e.Row.Cells[8].Style.Add("width", "50px");
            //e.Row.Cells[9].Style.Add("width", "60px");
            //e.Row.Cells[10].Style.Add("width", "130px");
            //e.Row.Cells[11].Style.Add("width", "80px");
            //e.Row.Cells[12].Style.Add("width", "80px");
            //e.Row.Cells[13].Style.Add("width", "80px");
            //e.Row.Cells[14].Style.Add("width", "50px");

            // 設定內容置中
            for (int i = 0; i < e.Row.Cells.Count; i++)
            {
                e.Row.Cells[i].HorizontalAlign = HorizontalAlign.Center;
            }

        }
    }
    protected void ExportToExcel(object sender, EventArgs e)
    {
        try
        {
            // 確保有圖表數據
            //if (string.IsNullOrEmpty(hiddenChartData.Value))
            //{
            //    ScriptManager.RegisterStartupScript(this, GetType(), "Msg",
            //        "alert('圖表數據未準備好，請稍後再試');", true);
            //    return;
            //}

            using (XLWorkbook wb = new XLWorkbook())
            {
                var ws = wb.Worksheets.Add("Data");

                // 插入表格
                ws.Cell(1, 1).InsertTable(GV1);

                //// 插入圖表
                //string base64Image = hiddenChartData.Value;
                //byte[] imageBytes = Convert.FromBase64String(base64Image.Split(',')[1]);
                //using (MemoryStream ms = new MemoryStream(imageBytes))
                //{
                //    var picture = ws.AddPicture(ms)
                //        .MoveTo(ws.Cell(GV1.Rows.Count + 3, 1))
                //        .Scale(0.8);
                //}

                // 輸出 Excel
                Response.Clear();
                Response.Buffer = true;
                Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                Response.AddHeader("content-disposition", "attachment;filename=" + ViewState["chartTitle"] + "統計報表" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx");

                using (MemoryStream stream = new MemoryStream())
                {
                    wb.SaveAs(stream);
                    stream.WriteTo(Response.OutputStream);
                    Response.Flush();
                    Response.End();
                }
                ScriptManager.RegisterStartupScript(this, GetType(), "Msg", "alert('資料下載完成。');", true);
            }
        }
        catch (Exception ex)
        {
            //ScriptManager.RegisterStartupScript(this, GetType(), "alert",
            //    $"alert('匯出失敗：{ex.Message}');", true);
            ScriptManager.RegisterStartupScript(this, GetType(), "Msg", "alert('匯出失敗：" + ex.Message + "');", true);
        }
    }

    protected void ExportToPdf(object sender, EventArgs e)
    {
        try
        {
            // 確保有圖表數據
            //if (string.IsNullOrEmpty(hiddenChartData.Value))
            //{
            //    ScriptManager.RegisterStartupScript(this, GetType(), "Msg",
            //        "alert('圖表數據未準備好，請稍後再試');", true);
            //    return;
            //}
            Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 10f, 10f);
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream);
            pdfDoc.Open();

            // 📌 1. 設定中文字型 (標楷體)
            string fontPath = Server.MapPath("~/fonts/kaiu.ttf"); // 確保網站有這個字型
            BaseFont bfChinese = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            Font titleFont = new Font(bfChinese, 16, Font.BOLD);
            Font headerFont = new Font(bfChinese, 12, Font.BOLD); // **標題加粗**
            Font cellFont = new Font(bfChinese, 10, Font.NORMAL);

            // 📌 2. 插入標題
            Paragraph title = new Paragraph("" + ViewState["chartTitle"] + "統計報表\n\n", titleFont);
            title.Alignment = Element.ALIGN_CENTER;
            pdfDoc.Add(title);

            // 📌 3. 插入表格
            PdfPTable pdfTable = new PdfPTable(GV1.Columns.Count);
            pdfTable.WidthPercentage = 100;// 表格佔滿頁面寬度
            pdfTable.SplitLate = false; // 避免表格被切割
            pdfTable.SplitRows = true;  // 允許資料自動分頁

            // 📌 4. 插入標題列
            foreach (DataColumn column in GV1.Columns)
            {
                PdfPCell headerCell = new PdfPCell(new Phrase(column.ColumnName, headerFont));
                headerCell.BackgroundColor = new BaseColor(200, 200, 200); // **灰色背景**
                headerCell.HorizontalAlignment = Element.ALIGN_CENTER;
                pdfTable.AddCell(headerCell);
            }

            // 📌 5. 插入資料列
            foreach (DataRow row in GV1.Rows)
            {
                foreach (var cell in row.ItemArray)
                {
                    PdfPCell pdfCell = new PdfPCell(new Phrase(cell.ToString(), cellFont));
                    pdfCell.HorizontalAlignment = Element.ALIGN_CENTER;
                    pdfTable.AddCell(pdfCell);
                }
            }
            pdfDoc.Add(pdfTable);

            //📌 6.頁尾
            string foot = String.Format(@"Copyright @{0} All rights reserved 中信科技大學 Made with 資訊系統整合中心", DateTime.Now.ToString("yyyy"));
            PdfPTable pdffoot = new PdfPTable(1);
            pdffoot.WidthPercentage = 100;
            //PdfPCell contentTitle = new PdfPCell(new Phrase("總結", fontChinese));
            PdfPCell contentTitle = new PdfPCell(new Phrase(foot, headerFont));
            contentTitle.HorizontalAlignment = Element.ALIGN_CENTER;
            contentTitle.BorderWidth = 0;
            pdffoot.AddCell(contentTitle);
            pdfDoc.Add(pdffoot);

            // 📌 7. 插入圖表
            //string base64Image = hiddenChartData.Value;
            //if (!string.IsNullOrEmpty(base64Image))
            //{
            //    byte[] imageBytes = Convert.FromBase64String(base64Image.Split(',')[1]); // 去掉 "data:image/png;base64,"
            //    iTextSharp.text.Image chartImage = iTextSharp.text.Image.GetInstance(imageBytes);
            //    chartImage.ScaleToFit(500f, 300f);  // 調整大小
            //    chartImage.Alignment = Element.ALIGN_CENTER;
            //    pdfDoc.Add(new Paragraph("\n\n"));  // **加點空間**
            //    pdfDoc.Add(chartImage);
            //}

            pdfDoc.Close();

            Response.ContentType = "application/pdf";
            Response.AddHeader("content-disposition", "attachment;filename=" + ViewState["chartTitle"] + "統計報表" + DateTime.Now.ToString("yyyy-MM-dd") + ".pdf");
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            Response.Write(pdfDoc);
            Response.End();
            ScriptManager.RegisterStartupScript(this, GetType(), "Msg", "alert('資料下載完成。');", true);
        }
        catch (Exception ex)
        {
            //ScriptManager.RegisterStartupScript(this, GetType(), "alert",
            //    $"alert('匯出失敗：{ex.Message}');", true);
            ScriptManager.RegisterStartupScript(this, GetType(), "Msg", "alert('匯出失敗：" + ex.Message + "');", true);
        }
    }


    protected void NewList1(object sender, GridViewPageEventArgs e)
    {
        // 設定 GridView 的新頁索引
        GridView1.PageIndex = e.NewPageIndex;

        // 再次綁定資料到 GridView
        GetGV1Data1();
    }

    //protected void NewList2(object sender, GridViewPageEventArgs e)
    //{
    //    // 設定 GridView 的新頁索引
    //    GridView1.PageIndex = e.NewPageIndex;

    //    // 再次綁定資料到 GridView
    //    GetGV1Data2();
    //}
    public bool CheckUserSession()
    {
        if (Session["tech_no"] == null)
        {
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('作業逾時或尚未登入,請登入後再繼續作業!');location.href='/Logout.aspx';", true);
            return false;
        }
        else
        {
            return true;
        }
    }
}