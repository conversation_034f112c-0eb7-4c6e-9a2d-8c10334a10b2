using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class debug_recowish_clean : System.Web.UI.Page
{
    dbconnection db = new dbconnection();
    StringBuilder results = new StringBuilder();

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            lblResults.Text = "Ready to start diagnosis...";
        }
    }

    protected void TestConnection_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h4>Database Connection Test</h4>");

        try
        {
            results.AppendLine($"<p>Connection String Info: {GetSafeConnectionString()}</p>");

            if (db.conn.State != ConnectionState.Open)
                db.conn.Open();

            results.AppendLine("<p class='success'><span class='status-indicator status-ok'></span>Database connection successful!</p>");
            results.AppendLine($"<p>Database Name: {db.conn.Database}</p>");
            results.AppendLine($"<p>Server: {db.conn.DataSource}</p>");
            results.AppendLine($"<p>Connection Timeout: {db.conn.ConnectionTimeout} seconds</p>");
            results.AppendLine($"<p>Connection State: {db.conn.State}</p>");

            db.conn.Close();
        }
        catch (Exception ex)
        {
            results.AppendLine($"<p class='error'><span class='status-indicator status-error'></span>Connection failed: {ex.Message}</p>");
            results.AppendLine($"<p>Detailed error: {ex.ToString()}</p>");
        }

        lblResults.Text = results.ToString();
    }

    protected void TestViews_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h4>View Existence Check</h4>");

        try
        {
            if (db.conn.State != ConnectionState.Open)
                db.conn.Open();

            string[] viewNames = { "V_recowish1", "V_recowish3", "V_recowish5" };
            string[] descriptions = { "Day Division", "Master Program", "Evening Division" };

            for (int i = 0; i < viewNames.Length; i++)
            {
                results.AppendLine($"<h5>Checking {descriptions[i]} View: {viewNames[i]}</h5>");

                // Check if view exists
                SqlCommand cmd = new SqlCommand($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = '{viewNames[i]}'", db.conn);
                int viewExists = (int)cmd.ExecuteScalar();

                if (viewExists > 0)
                {
                    results.AppendLine($"<p class='success'><span class='status-indicator status-ok'></span>View exists</p>");

                    // Check row count
                    cmd.CommandText = $"SELECT COUNT(*) FROM [school].[dbo].[{viewNames[i]}]";
                    var rowCount = cmd.ExecuteScalar();
                    results.AppendLine($"<p>Row count: {rowCount}</p>");

                    // Check column structure
                    cmd.CommandText = $"SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{viewNames[i]}' ORDER BY ORDINAL_POSITION";
                    using (var reader = cmd.ExecuteReader())
                    {
                        results.AppendLine("<p>Column structure:</p><ul>");
                        while (reader.Read())
                        {
                            results.AppendLine($"<li>{reader["COLUMN_NAME"]} ({reader["DATA_TYPE"]})</li>");
                        }
                        results.AppendLine("</ul>");
                        reader.Close();
                    }

                    // Show first 3 records
                    if (Convert.ToInt32(rowCount) > 0)
                    {
                        cmd.CommandText = $"SELECT TOP 3 * FROM [school].[dbo].[{viewNames[i]}]";
                        using (var reader = cmd.ExecuteReader())
                        {
                            results.AppendLine("<p>First 3 records:</p>");
                            int recordNum = 1;
                            while (reader.Read())
                            {
                                results.AppendLine($"<p><strong>Record {recordNum}:</strong> ");
                                for (int j = 0; j < reader.FieldCount; j++)
                                {
                                    results.AppendLine($"{reader.GetName(j)}={reader[j]} | ");
                                }
                                results.AppendLine("</p>");
                                recordNum++;
                            }
                            reader.Close();
                        }
                    }
                    else
                    {
                        results.AppendLine("<p class='warning'><span class='status-indicator status-warning'></span>View exists but has no data</p>");
                    }
                }
                else
                {
                    results.AppendLine($"<p class='error'><span class='status-indicator status-error'></span>View does not exist</p>");
                }
                results.AppendLine("<hr/>");
            }

            db.conn.Close();
        }
        catch (Exception ex)
        {
            results.AppendLine($"<p class='error'><span class='status-indicator status-error'></span>Check failed: {ex.Message}</p>");
        }

        lblResults.Text = results.ToString();
    }

    protected void TestData_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h4>Data Query Test</h4>");

        try
        {
            if (db.conn.State != ConnectionState.Open)
                db.conn.Open();

            // Test actual query statements - using correct column names
            string[] sqlQueries = {
                @"SELECT [學制],[科系],[實名制人數],[報到人數],[繳交畢業證書人數],[繳費人數]
                  FROM [school].[dbo].[V_recowish1]",
                @"SELECT [學制],[科系],[實名制人數],[報到人數],[繳交畢業證書人數],[繳費人數]
                  FROM [school].[dbo].[V_recowish3]",
                @"SELECT [學制],[科系],[實名制人數],[報到人數],[繳交畢業證書人數],[繳費人數]
                  FROM [school].[dbo].[V_recowish5]"
            };
            string[] queryNames = { "Day Division Query", "Master Program Query", "Evening Division Query" };

            for (int i = 0; i < sqlQueries.Length; i++)
            {
                results.AppendLine($"<h5>{queryNames[i]}</h5>");
                results.AppendLine($"<p>SQL: <code>{sqlQueries[i]}</code></p>");

                try
                {
                    SqlCommand cmd = new SqlCommand(sqlQueries[i], db.conn);
                    SqlDataAdapter da = new SqlDataAdapter(cmd);
                    DataTable dt = new DataTable();
                    da.Fill(dt);

                    results.AppendLine($"<p class='success'><span class='status-indicator status-ok'></span>Query successful, got {dt.Rows.Count} records</p>");

                    if (dt.Rows.Count > 0 && i == 0) // Only show first query result
                    {
                        gvPreview.DataSource = dt;
                        gvPreview.DataBind();
                    }
                }
                catch (Exception queryEx)
                {
                    results.AppendLine($"<p class='error'><span class='status-indicator status-error'></span>Query failed: {queryEx.Message}</p>");
                }
                results.AppendLine("<hr/>");
            }

            db.conn.Close();
        }
        catch (Exception ex)
        {
            results.AppendLine($"<p class='error'><span class='status-indicator status-error'></span>Test failed: {ex.Message}</p>");
        }

        lblResults.Text = results.ToString();
    }

    protected void TestPermissions_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h4>Permission Check</h4>");

        // Check Session state
        results.AppendLine("<h5>Session State Check</h5>");

        if (Session["tech_no"] != null)
        {
            results.AppendLine($"<p class='success'><span class='status-indicator status-ok'></span>tech_no: {Session["tech_no"]}</p>");
        }
        else
        {
            results.AppendLine("<p class='warning'><span class='status-indicator status-warning'></span>tech_no: Not set</p>");
        }

        if (Session["master_single"] != null)
        {
            results.AppendLine($"<p class='success'><span class='status-indicator status-ok'></span>master_single: {Session["master_single"]}</p>");

            if (Session["master_single"].ToString() == "3")
            {
                results.AppendLine("<p class='success'><span class='status-indicator status-ok'></span>Sufficient permissions (master_single = 3)</p>");
            }
            else
            {
                results.AppendLine("<p class='error'><span class='status-indicator status-error'></span>Insufficient permissions (need master_single = 3)</p>");
            }
        }
        else
        {
            results.AppendLine("<p class='warning'><span class='status-indicator status-warning'></span>master_single: Not set</p>");
        }

        lblResults.Text = results.ToString();
    }

    protected void RunAll_Click(object sender, EventArgs e)
    {
        results.Clear();
        results.AppendLine("<h3>Complete Diagnostic Report</h3>");
        results.AppendLine($"<p>Execution time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
        results.AppendLine("<hr/>");

        // Run all tests
        TestConnection_Click(sender, e);
        string connectionResult = results.ToString();

        TestViews_Click(sender, e);
        string viewResult = results.ToString();

        TestData_Click(sender, e);
        string dataResult = results.ToString();

        TestPermissions_Click(sender, e);
        string permissionResult = results.ToString();

        // Combine all results
        results.Clear();
        results.AppendLine("<h3>Complete Diagnostic Report</h3>");
        results.AppendLine($"<p>Execution time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
        results.AppendLine("<hr/>");
        results.AppendLine(connectionResult);
        results.AppendLine(viewResult);
        results.AppendLine(dataResult);
        results.AppendLine(permissionResult);

        // Generate fix suggestions
        GenerateFixSuggestions();

        lblResults.Text = results.ToString();
    }

    private void GenerateFixSuggestions()
    {
        StringBuilder suggestions = new StringBuilder();
        suggestions.AppendLine("<h4>Fix Suggestions</h4>");

        suggestions.AppendLine("<div class='info'>");
        suggestions.AppendLine("<h5>Common Problem Solutions:</h5>");
        suggestions.AppendLine("<ol>");
        suggestions.AppendLine("<li><strong>Views don't exist:</strong> Contact database administrator to create V_recowish1, V_recowish3, V_recowish5 views</li>");
        suggestions.AppendLine("<li><strong>No data:</strong> Check if base tables have data, verify data import is correct</li>");
        suggestions.AppendLine("<li><strong>Permission issues:</strong> Ensure Session['master_single'] = '3'</li>");
        suggestions.AppendLine("<li><strong>Connection issues:</strong> Check network connection and database server status</li>");
        suggestions.AppendLine("</ol>");
        suggestions.AppendLine("</div>");

        fixSuggestions.InnerHtml = suggestions.ToString();
    }

    private string GetSafeConnectionString()
    {
        try
        {
            string connStr = db.conn.ConnectionString;
            // Hide password
            if (connStr.Contains("pwd="))
            {
                int pwdStart = connStr.IndexOf("pwd=") + 4;
                int pwdEnd = connStr.IndexOf(";", pwdStart);
                if (pwdEnd == -1) pwdEnd = connStr.Length;

                string hiddenPassword = new string('*', Math.Min(8, pwdEnd - pwdStart));
                connStr = connStr.Substring(0, pwdStart) + hiddenPassword + connStr.Substring(pwdEnd);
            }
            return connStr;
        }
        catch
        {
            return "Unable to get connection string info";
        }
    }
}
