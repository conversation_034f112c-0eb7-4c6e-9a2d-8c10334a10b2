//------------------------------------------------------------------------------
// <auto-generated>
//     這段程式碼是由工具產生的。
//
//     變更這個檔案可能會導致不正確的行為，而且如果已重新產生
//     程式碼，變更將會遺失。
// </auto-generated>
//------------------------------------------------------------------------------

public partial class Analyze_recowishm01
{
	/// <summary>
	/// Content1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Content Content1;

	/// <summary>
	/// tblselect 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Table tblselect;

	/// <summary>
	/// TableRow1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.TableRow TableRow1;

	/// <summary>
	/// TableCell1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.TableCell TableCell1;

	/// <summary>
	/// label3 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Label label3;

	/// <summary>
	/// RadioButtonList1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.RadioButtonList RadioButtonList1;

	/// <summary>
	/// TableRow2 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.TableRow TableRow2;

	/// <summary>
	/// TableCell2 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.TableCell TableCell2;

	/// <summary>
	/// label1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Label label1;

	/// <summary>
	/// ddl_gradeyyy 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.DropDownList ddl_gradeyyy;

	/// <summary>
	/// Button_search 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Button Button_search;

	/// <summary>
	/// TableRow3 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.TableRow TableRow3;

	/// <summary>
	/// TableCell3 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.TableCell TableCell3;

	/// <summary>
	/// msg 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Label msg;

	/// <summary>
	/// P1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Panel P1;

	/// <summary>
	/// GridView1 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.GridView GridView1;

	/// <summary>
	/// btnExportExcel 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Button btnExportExcel;

	/// <summary>
	/// btnExportPdf 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.WebControls.Button btnExportPdf;

	/// <summary>
	/// hiddenChartData 控制項。
	/// </summary>
	/// <remarks>
	/// 自動產生的欄位。
	/// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
	/// </remarks>
	protected global::System.Web.UI.HtmlControls.HtmlInputHidden hiddenChartData;
}
