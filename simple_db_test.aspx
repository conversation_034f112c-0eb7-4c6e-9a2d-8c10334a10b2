<%@ Page Language="C#" AutoEventWireup="true" CodeFile="simple_db_test.aspx.cs" Inherits="simple_db_test" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">

<head runat="server">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>🔍 簡單資料庫測試</title>
	<style>
		body {
			font-family: 'Microsoft YaHei', Arial, sans-serif;
			margin: 0;
			padding: 20px;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			min-height: 100vh;
		}

		.container {
			max-width: 1000px;
			margin: 0 auto;
			background: white;
			border-radius: 15px;
			box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
			overflow: hidden;
		}

		.header {
			background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
			color: white;
			padding: 25px;
			text-align: center;
		}

		.header h1 {
			margin: 0;
			font-size: 28px;
			font-weight: bold;
		}

		.content {
			padding: 30px;
		}

		.result-box {
			background: #f8f9fa;
			border: 1px solid #dee2e6;
			border-radius: 8px;
			padding: 20px;
			margin: 20px 0;
			font-family: 'Courier New', monospace;
			font-size: 13px;
			line-height: 1.4;
			max-height: 600px;
			overflow-y: auto;
		}

		.button {
			background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
			color: white;
			border: none;
			padding: 12px 30px;
			border-radius: 20px;
			font-size: 14px;
			font-weight: bold;
			cursor: pointer;
			transition: all 0.3s ease;
			margin: 10px 5px;
		}

		.button:hover {
			transform: translateY(-1px);
			box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
		}

		.gridview-container {
			background: white;
			border-radius: 10px;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
			margin: 25px 0;
			overflow: hidden;
		}

		.gridview-container table {
			width: 100%;
			border-collapse: collapse;
		}

		.gridview-container th {
			background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
			color: white;
			padding: 12px;
			text-align: center;
			font-weight: bold;
			font-size: 12px;
		}

		.gridview-container td {
			padding: 8px;
			text-align: center;
			border-bottom: 1px solid #e9ecef;
			font-size: 11px;
		}

		.gridview-container tr:hover {
			background-color: #f8f9fa;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<div class="container">
			<div class="header">
				<h1>🔍 簡單資料庫連接測試</h1>
				<p>用於診斷資料庫連接和數據查詢問題</p>
			</div>

			<div class="content">
				<div style="text-align: center; margin-bottom: 20px;">
					<asp:Button ID="btnRetest" runat="server" Text="🔄 重新測試" OnClick="btnRetest_Click" CssClass="button" />
				</div>

				<div class="result-box">
					<asp:Label ID="lblResult" runat="server"></asp:Label>
				</div>

				<div class="gridview-container">
					<asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="true">
					</asp:GridView>
				</div>
			</div>
		</div>
	</form>
</body>

</html>