<%@ Page Language="C#" AutoEventWireup="true" CodeFile="debug_analyze_v2.aspx.cs" Inherits="debug_analyze_v2" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">

<head runat="server">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>🔍 深度調試模式 - 新生報到統計分析</title>
	<style>
		body {
			font-family: 'Microsoft YaHei', Arial, sans-serif;
			margin: 0;
			padding: 20px;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			min-height: 100vh;
		}

		.container {
			max-width: 1200px;
			margin: 0 auto;
			background: white;
			border-radius: 15px;
			box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
			overflow: hidden;
		}

		.header {
			background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
			color: white;
			padding: 25px;
			text-align: center;
		}

		.header h1 {
			margin: 0;
			font-size: 28px;
			font-weight: bold;
		}

		.content {
			padding: 30px;
		}

		.form-section {
			background: #f8f9fa;
			border-radius: 10px;
			padding: 25px;
			margin-bottom: 25px;
			border-left: 5px solid #007bff;
		}

		.form-section h3 {
			margin-top: 0;
			color: #495057;
			font-size: 18px;
		}

		.radio-group {
			display: flex;
			flex-wrap: wrap;
			gap: 15px;
			margin: 15px 0;
		}

		.radio-item {
			background: white;
			border: 2px solid #e9ecef;
			border-radius: 8px;
			padding: 12px 18px;
			cursor: pointer;
			transition: all 0.3s ease;
			min-width: 150px;
			text-align: center;
		}

		.radio-item:hover {
			border-color: #007bff;
			box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
		}

		.radio-item input[type="radio"] {
			margin-right: 8px;
		}

		.search-button {
			background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
			color: white;
			border: none;
			padding: 15px 40px;
			border-radius: 25px;
			font-size: 16px;
			font-weight: bold;
			cursor: pointer;
			transition: all 0.3s ease;
			display: block;
			margin: 25px auto;
			box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
		}

		.search-button:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
		}

		.message-box {
			background: #fff3cd;
			border: 1px solid #ffeaa7;
			border-radius: 8px;
			padding: 15px;
			margin: 20px 0;
			font-size: 14px;
			line-height: 1.6;
		}

		.debug-info {
			background: #f8f9fa;
			border: 1px solid #dee2e6;
			border-radius: 8px;
			padding: 20px;
			margin: 20px 0;
			font-family: 'Courier New', monospace;
			font-size: 13px;
			line-height: 1.4;
			max-height: 500px;
			overflow-y: auto;
		}

		.gridview-container {
			background: white;
			border-radius: 10px;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
			margin: 25px 0;
			overflow: hidden;
		}

		.gridview-container table {
			width: 100%;
			border-collapse: collapse;
		}

		.gridview-container th {
			background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
			color: white;
			padding: 12px;
			text-align: center;
			font-weight: bold;
		}

		.gridview-container td {
			padding: 10px;
			text-align: center;
			border-bottom: 1px solid #e9ecef;
		}

		.gridview-container tr:hover {
			background-color: #f8f9fa;
		}

		.hidden {
			display: none;
		}
	</style>
</head>

<body>
	<form id="form1" runat="server">
		<asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>

		<div class="container">
			<div class="header">
				<h1>🔍 深度調試模式 - 新生報到統計分析系統</h1>
				<p>此版本包含詳細的調試資訊，用於診斷數據顯示問題</p>
			</div>

			<div class="content">
				<!-- 選擇區域 -->
				<div class="form-section">
					<h3>📊 請選擇人數分析項目</h3>
					<asp:RadioButtonList ID="RadioButtonList1" runat="server" OnSelectedIndexChanged="RadioButtonList1_SelectedIndexChanged" AutoPostBack="true" CssClass="radio-group">
						<asp:ListItem Value="1" Text="🎓 日間部"></asp:ListItem>
						<asp:ListItem Value="2" Text="🎯 碩士班"></asp:ListItem>
						<asp:ListItem Value="3" Text="🌙 進修部"></asp:ListItem>
					</asp:RadioButtonList>
				</div>

				<div class="form-section">
					<h3>📈 請選擇查詢項目類型</h3>
					<asp:RadioButtonList ID="RadioButtonList2" runat="server" OnSelectedIndexChanged="RadioButtonList2_SelectedIndexChanged" AutoPostBack="true" CssClass="radio-group">
						<asp:ListItem Value="1" Text="📝 實名制人數分布"></asp:ListItem>
						<asp:ListItem Value="2" Text="✅ 報到人數分布"></asp:ListItem>
						<asp:ListItem Value="3" Text="📜 繳交畢業證書人數分布"></asp:ListItem>
						<asp:ListItem Value="4" Text="💰 繳費人數分布"></asp:ListItem>
					</asp:RadioButtonList>
				</div>

				<!-- 查詢按鈕 -->
				<asp:Button ID="Button_search" runat="server" Text="🔍 開始深度調試查詢" OnClick="Button_search_Click" CssClass="search-button" />

				<!-- 訊息顯示區域 -->
				<asp:Label ID="msg" runat="server" CssClass="message-box"></asp:Label>

				<!-- 數據顯示區域 -->
				<asp:Panel ID="P1" runat="server" Visible="false">
					<div class="gridview-container">
						<asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="true" OnRowDataBound="GridView1_RowDataBound" OnPageIndexChanging="NewList1" AllowPaging="true" PageSize="20"
							PagerStyle-CssClass="pager" HeaderStyle-CssClass="gridview-header" RowStyle-CssClass="gridview-row">
						</asp:GridView>
					</div>
				</asp:Panel>
			</div>
		</div>
	</form>
</body>

</html>