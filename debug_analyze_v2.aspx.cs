using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.Script.Serialization;
using System.IO;
using System.Web.UI;
using iTextSharp.text;
using iTextSharp.text.pdf;
using ClosedXML.Excel;
using System.Web.UI.WebControls;
using System.Web;
using System.Windows.Interop;

public partial class debug_analyze_v2 : System.Web.UI.Page
{
	App_Func appfun = new App_Func();
	dbconnection db = new dbconnection();
	SqlCommand cmd = new SqlCommand();
	SqlDataAdapter da = new SqlDataAdapter();
	DataSet ds = new DataSet();
	string sqlStr;
	private static DataTable GV1;

	protected void Page_Load(object sender, EventArgs e)
	{
		if (CheckUserSession() == false) { return; }
		if (Session["master_single"].ToString() != "3")
		{
			ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('權限不足,您無法使用本功能!');location.href='/Logout.aspx';", true);
			return;
		}
		if (!IsPostBack)
		{
			RadioButtonList1.SelectedValue = "1";
			RadioButtonList2.SelectedValue = "1";
			msg.Text = "📊 目前選擇：日間部 - 實名制人數分布，請點擊「開始查詢」按鈕進行分析";
			msg.CssClass = "msg-info";
		}
	}

	protected void Button_search_Click(object sender, EventArgs e)
	{
		try
		{
			if (RadioButtonList1.SelectedValue == "")
			{
				msg.Text = "❌ 訊息: 請選擇人數分析項目!";
				return;
			}
			else if (RadioButtonList2.SelectedValue == "")
			{
				msg.Text = "❌ 訊息: 請選擇查詢項目類型!";
				return;
			}
			else
			{
				P1.Visible = true;
				GetGV1Data1_Debug();
			}
		}
		catch (Exception ex)
		{
			msg.Text = "❌ 錯誤: " + ex.Message;
		}
	}

	private void GetGV1Data1_Debug()
	{
		string chartTitle = string.Empty;
		string queryTypeText = string.Empty;

		// 添加全面的調試資訊
		msg.Text = "🔍 開始調試模式...";

		try
		{
			// 1. 測試資料庫連線
			msg.Text += "<br/>📋 步驟1: 測試資料庫連線...";

			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}

			msg.Text += "<br/>✅ 資料庫連線成功 - 資料庫: " + db.conn.Database;
			msg.Text += "<br/>✅ 伺服器: " + db.conn.DataSource;

			// 2. 設定查詢參數
			msg.Text += "<br/>📋 步驟2: 設定查詢參數...";
			msg.Text += "<br/>選擇的學制: " + RadioButtonList1.SelectedValue;
			msg.Text += "<br/>選擇的查詢類型: " + RadioButtonList2.SelectedValue;

			string dataColumnName = string.Empty;

			switch (RadioButtonList2.SelectedValue)
			{
				case "1":
					queryTypeText = "實名制人數分布";
					dataColumnName = "實名制人數";
					break;
				case "2":
					queryTypeText = "報到人數分布";
					dataColumnName = "報到人數";
					break;
				case "3":
					queryTypeText = "繳交畢業證書人數分布";
					dataColumnName = "繳交畢業證書人數";
					break;
				case "4":
					queryTypeText = "繳費人數分布";
					dataColumnName = "繳費人數";
					break;
				default:
					queryTypeText = "實名制人數分布";
					dataColumnName = "實名制人數";
					break;
			}

			msg.Text += "<br/>數據欄位名稱: " + dataColumnName;

			// 3. 建立 SQL 查詢
			msg.Text += "<br/>📋 步驟3: 建立 SQL 查詢...";

			if (RadioButtonList1.SelectedValue == "1")
			{
				sqlStr = @"SELECT [學制]
                                 ,[科系]
                                 ,[實名制人數]
                                 ,[報到人數]
                                 ,[繳交畢業證書人數]
                                 ,[繳費人數]
                          FROM [school].[dbo].[V_recowish1]; ";
				chartTitle = "日間部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
				ViewState["chartTitle"] = "日間部科系" + queryTypeText;
			}
			else if (RadioButtonList1.SelectedValue == "2")
			{
				sqlStr = @"SELECT [學制]
                                 ,[科系]
                                 ,[實名制人數]
                                 ,[報到人數]
                                 ,[繳交畢業證書人數]
                                 ,[繳費人數]
                          FROM [school].[dbo].[V_recowish3]; ";
				chartTitle = "碩士班" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
				ViewState["chartTitle"] = "碩士班系所" + queryTypeText;
			}
			else if (RadioButtonList1.SelectedValue == "3")
			{
				sqlStr = @"SELECT [學制]
                                 ,[科系]
                                 ,[實名制人數]
                                 ,[報到人數]
                                 ,[繳交畢業證書人數]
                                 ,[繳費人數]
                          FROM [school].[dbo].[V_recowish5];";
				chartTitle = "進修部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
				ViewState["chartTitle"] = "進修部科系" + queryTypeText;
			}

			msg.Text += "<br/>SQL查詢語句: " + sqlStr.Replace("\r\n", " ").Replace("  ", " ");

			// 4. 執行查詢前的檢查
			msg.Text += "<br/>📋 步驟4: 執行查詢...";

			cmd.Connection = db.conn;
			cmd.CommandText = sqlStr;
			cmd.Parameters.Clear();

			// 先測試查詢是否能執行
			using (SqlCommand testCmd = new SqlCommand(sqlStr, db.conn))
			{
				try
				{
					int recordCount = (int)testCmd.ExecuteScalar();
					msg.Text += "<br/>✅ SQL 查詢可以執行";
				}
				catch (Exception sqlEx)
				{
					msg.Text += "<br/>❌ SQL 查詢錯誤: " + sqlEx.Message;
					return;
				}
			}

			// 5. 重新初始化 DataSet 和 DataAdapter
			msg.Text += "<br/>📋 步驟5: 重新初始化數據結構...";

			// 創建全新的 DataSet 和 DataAdapter
			DataSet newDs = new DataSet();
			SqlDataAdapter newDa = new SqlDataAdapter();

			newDa.SelectCommand = cmd;

			try
			{
				int fillResult = newDa.Fill(newDs, "check");
				msg.Text += "<br/>✅ DataAdapter.Fill 結果: " + fillResult + " 筆記錄";

				if (newDs.Tables.Contains("check"))
				{
					DataTable dt = newDs.Tables["check"];
					msg.Text += "<br/>✅ DataTable 創建成功";
					msg.Text += "<br/>📊 DataTable.Rows.Count: " + dt.Rows.Count;
					msg.Text += "<br/>📊 DataTable.Columns.Count: " + dt.Columns.Count;

					// 顯示欄位名稱
					string columnNames = "";
					foreach (DataColumn col in dt.Columns)
					{
						columnNames += col.ColumnName + ", ";
					}
					msg.Text += "<br/>📊 欄位名稱: " + columnNames.TrimEnd(',', ' ');

					// 複製到 GV1
					GV1 = dt.Copy();
					msg.Text += "<br/>✅ GV1 複製完成，行數: " + GV1.Rows.Count;

					// 6. 顯示前幾筆資料進行驗證
					if (GV1.Rows.Count > 0)
					{
						msg.Text += "<br/>📋 步驟6: 資料內容驗證...";

						int displayCount = Math.Min(3, GV1.Rows.Count);
						for (int i = 0; i < displayCount; i++)
						{
							DataRow row = GV1.Rows[i];
							msg.Text += "<br/>第" + (i + 1) + "筆: ";
							for (int j = 0; j < row.ItemArray.Length; j++)
							{
								msg.Text += GV1.Columns[j].ColumnName + "=" + row[j].ToString() + " | ";
							}
						}

						// 7. 綁定到 GridView
						msg.Text += "<br/>📋 步驟7: 綁定到 GridView...";

						GridView1.DataSource = GV1;
						GridView1.AllowPaging = true;
						GridView1.PageSize = 20;
						GridView1.DataBind();

						msg.Text += "<br/>✅ GridView 綁定完成，顯示行數: " + GridView1.Rows.Count;

						// 8. 生成圖表數據
						msg.Text += "<br/>📋 步驟8: 生成圖表數據...";

						JavaScriptSerializer serializer = new JavaScriptSerializer();
						var chartData = new System.Collections.Generic.List<object>();

						chartData.Add(new object[] { "科系", dataColumnName });

						int validDataCount = 0;
						foreach (DataRow row in GV1.Rows)
						{
							string departmentName = row["科系"].ToString();
							int dataValue = 0;

							if (int.TryParse(row[dataColumnName].ToString(), out dataValue))
							{
								chartData.Add(new object[] { departmentName, dataValue });
								validDataCount++;
							}
						}

						msg.Text += "<br/>✅ 圖表數據生成完成，有效數據: " + validDataCount + " 筆";

						string jsonData = serializer.Serialize(chartData);
						Page.ClientScript.RegisterStartupScript(this.GetType(), "chartTitle", "var chartTitle = '" + chartTitle + "';", true);
						Page.ClientScript.RegisterStartupScript(this.GetType(), "chartData", "var chartData = " + jsonData + ";", true);

						// 9. 計算統計摘要
						int totalCount = 0;
						int maxValue = 0;
						string maxDepartment = "";

						foreach (DataRow row in GV1.Rows)
						{
							int currentValue = 0;
							if (int.TryParse(row[dataColumnName].ToString(), out currentValue))
							{
								totalCount += currentValue;
								if (currentValue > maxValue)
								{
									maxValue = currentValue;
									maxDepartment = row["科系"].ToString();
								}
							}
						}

						ViewState["TotalCount"] = totalCount;
						ViewState["MaxValue"] = maxValue;
						ViewState["MaxDepartment"] = maxDepartment;
						ViewState["DepartmentCount"] = GV1.Rows.Count;

						msg.Text += "<br/>📊 統計摘要: 共 " + GV1.Rows.Count + " 個科系，總計 " + totalCount + " 人，最高科系：" + maxDepartment + " (" + maxValue + " 人)";

						msg.Text += "<br/>🎉 全部步驟完成！";
					}
					else
					{
						msg.Text += "<br/>⚠️ 警告: 查詢結果為空，沒有找到符合條件的資料";

						// 測試是否表格存在且有資料
						using (SqlCommand countCmd = new SqlCommand("SELECT COUNT(*) FROM [school].[dbo].[V_recowish1]", db.conn))
						{
							try
							{
								int totalRecords = (int)countCmd.ExecuteScalar();
								msg.Text += "<br/>📊 V_recowish1 表格總記錄數: " + totalRecords;
							}
							catch (Exception countEx)
							{
								msg.Text += "<br/>❌ 無法查詢表格記錄數: " + countEx.Message;
							}
						}
					}
				}
				else
				{
					msg.Text += "<br/>❌ 錯誤: DataSet 中沒有找到 'check' 表格";
				}
			}
			catch (Exception fillEx)
			{
				msg.Text += "<br/>❌ 填充 DataSet 時發生錯誤: " + fillEx.Message;
				msg.Text += "<br/>❌ 詳細錯誤資訊: " + fillEx.StackTrace;
			}
			finally
			{
				// 清理資源
				if (newDa != null) newDa.Dispose();
				if (newDs != null) newDs.Dispose();
			}
		}
		catch (Exception ex)
		{
			msg.Text += "<br/>❌ 整體錯誤: " + ex.Message;
			msg.Text += "<br/>❌ 錯誤堆疊: " + ex.StackTrace;
		}
		finally
		{
			if (db.conn.State == ConnectionState.Open)
			{
				db.conn.Close();
				msg.Text += "<br/>✅ 資料庫連接已關閉";
			}
		}
	}

	protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
	{
		P1.Visible = false;
		GridView1.AllowPaging = false;
		GridView1.DataSource = "";
		GridView1.DataBind();
	}

	protected void RadioButtonList2_SelectedIndexChanged(object sender, EventArgs e)
	{
		P1.Visible = false;
		GridView1.AllowPaging = false;
		GridView1.DataSource = "";
		GridView1.DataBind();

		string queryTypeDesc = "";
		switch (RadioButtonList2.SelectedValue)
		{
			case "1": queryTypeDesc = "實名制人數分布"; break;
			case "2": queryTypeDesc = "報到人數分布"; break;
			case "3": queryTypeDesc = "繳交畢業證書人數分布"; break;
			case "4": queryTypeDesc = "繳費人數分布"; break;
		}

		if (RadioButtonList1.SelectedValue != "")
		{
			string analysisTypeDesc = "";
			switch (RadioButtonList1.SelectedValue)
			{
				case "1": analysisTypeDesc = "日間部"; break;
				case "2": analysisTypeDesc = "碩士班"; break;
				case "3": analysisTypeDesc = "進修部"; break;
			}
			msg.Text = string.Format("📊 目前選擇：{0} - {1}，請點擊「開始查詢」按鈕進行分析", analysisTypeDesc, queryTypeDesc);
		}
		else
		{
			msg.Text = string.Format("📈 已選擇查詢類型：{0}，請先選擇人數分析項目", queryTypeDesc);
		}
	}

	public void GridView1_RowDataBound(object sender, GridViewRowEventArgs e)
	{
		if (e.Row.RowType == DataControlRowType.DataRow)
		{
			e.Row.Cells[0].Style.Add("width", "200px");
			e.Row.Cells[1].Style.Add("width", "50px");

			for (int i = 0; i < e.Row.Cells.Count; i++)
			{
				e.Row.Cells[i].HorizontalAlign = HorizontalAlign.Center;
			}
		}
	}

	protected void NewList1(object sender, GridViewPageEventArgs e)
	{
		GridView1.PageIndex = e.NewPageIndex;
		GetGV1Data1_Debug();
	}

	public bool CheckUserSession()
	{
		if (Session["tech_no"] == null)
		{
			ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('作業逾時或尚未登入,請登入後再繼續作業!');location.href='/Logout.aspx';", true);
			return false;
		}
		else
		{
			return true;
		}
	}
}
