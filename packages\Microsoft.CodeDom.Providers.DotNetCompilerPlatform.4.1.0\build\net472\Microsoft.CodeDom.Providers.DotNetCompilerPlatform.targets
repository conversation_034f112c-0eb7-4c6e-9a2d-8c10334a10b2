<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <RoslynCopyToOutDir Condition="$(RoslynCopyToOutDir) == ''">true</RoslynCopyToOutDir>
    <RoslynRegisterInConfig Condition="$(RoslynRegisterInConfig) == ''">true</RoslynRegisterInConfig>
    <RoslynToolPath Condition="'$(RoslynToolPath)' == ''">$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\..\tools\roslyn-4.1.0'))</RoslynToolPath>
  </PropertyGroup>
    
  <Target Name="SetRoslynCompilerFiles" >
    <Message Text="Using Roslyn from '$(RoslynToolPath)' folder" />
    <ItemGroup>
      <RoslynCompilerFiles Include="$(RoslynToolPath)\*">
        <Link>roslyn\%(RecursiveDir)%(Filename)%(Extension)</Link>
      </RoslynCompilerFiles>
    </ItemGroup>
  </Target>

  <Target Name="IncludeRoslynCompilerFilesToFilesForPackagingFromProject" BeforeTargets="PipelineCollectFilesPhase" DependsOnTargets="SetRoslynCompilerFiles" >
    <ItemGroup>
      <FilesForPackagingFromProject Include="@(RoslynCompilerFiles)">
        <DestinationRelativePath>bin\roslyn\%(RecursiveDir)%(Filename)%(Extension)</DestinationRelativePath>
        <DestinationRelativePath Condition=" '$(WebProjectOutputDir)' == '' ">roslyn\%(RecursiveDir)%(Filename)%(Extension)</DestinationRelativePath>
        <FromTarget>IncludeRoslynCompilerFilesToFilesForPackagingFromProject</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
  </Target>

  <Target Name="LocateRoslynToolsDestinationFolder" Condition=" '$(RoslynToolsDestinationFolder)' == '' ">
    <!-- Choose one of WebProjectOutputDir or OutDir or OutputPath (in that order of preference) -->
    <!-- OutDir and OutputPath are confusing... but usually they are the same. When they differ
          it's likely because somebody is mucking with some intermediate details, but the ultimate
          output destination should be OutDir. For non-web projects. So use that if it exists. -->
    <PropertyGroup>
      <RoslynToolsDestinationFolder>$(WebProjectOutputDir)\bin\roslyn</RoslynToolsDestinationFolder>
      <RoslynToolsDestinationFolder Condition=" '$(WebProjectOutputDir)' == '' ">$(OutDir)\roslyn</RoslynToolsDestinationFolder>
      <RoslynToolsDestinationFolder Condition=" '$(WebProjectOutputDir)' == '' AND '$(OutDir)' == '' ">$(OutputPath)\roslyn</RoslynToolsDestinationFolder>
    </PropertyGroup>
  </Target>

  <Target Name="CopyRoslynCompilerFilesToOutputDirectory" AfterTargets="CopyFilesToOutputDirectory" DependsOnTargets="LocateRoslynToolsDestinationFolder;SetRoslynCompilerFiles" Condition="$(RoslynCopyToOutDir) == 'true'">
    <Copy SourceFiles="@(RoslynCompilerFiles)" DestinationFolder="$(RoslynToolsDestinationFolder)" ContinueOnError="true" SkipUnchangedFiles="true" Retries="0" />
    <ItemGroup  Condition="'$(MSBuildLastTaskResult)' == 'True'" >
      <FileWrites Include="$(RoslynToolsDestinationFolder)\*" />
    </ItemGroup>
  </Target>

  <Target Name="UpdateRoslynCompilersInConfigFile"  BeforeTargets="ResolveAssemblyReferences" Condition="$(RoslynRegisterInConfig) == 'true'">
    <UpdateCompilerConfigRecord ConfigFile="web.config" Extension=".cs" Language="c#;cs;csharp" WarningLevel="4" Options="/langversion:default /nowarn:1659;1699;1701;612;618"
                                CompilerType="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
                                Condition="Exists('web.config')" />
    <UpdateCompilerConfigRecord ConfigFile="web.config" Extension=".vb" Language="vb;vbs;visualbasic;vbscript" WarningLevel="4" Options="/langversion:default /nowarn:41008,40000,40008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+"
                                CompilerType="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
                                Condition="Exists('web.config')" />
    <UpdateCompilerConfigRecord ConfigFile="App.config" Extension=".cs" Language="c#;cs;csharp" WarningLevel="4" Options="/langversion:default /nowarn:1659;1699;1701"
                                CompilerType="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
                                Condition="Exists('App.config')" />
    <UpdateCompilerConfigRecord ConfigFile="App.config" Extension=".vb" Language="vb;vbs;visualbasic;vbscript" WarningLevel="4" Options="/langversion:default /nowarn:41008 /optionInfer+"
                                CompilerType="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
                                Condition="Exists('App.config')" />
  </Target>

  <Target Name="CheckIfShouldKillVBCSCompiler" DependsOnTargets="LocateRoslynToolsDestinationFolder">
    <CheckIfVBCSCompilerWillOverride src="$(RoslynToolPath)\VBCSCompiler.exe" dest="$(RoslynToolsDestinationFolder)\VBCSCompiler.exe">
      <Output TaskParameter="WillOverride" PropertyName="ShouldKillVBCSCompiler" />
    </CheckIfVBCSCompilerWillOverride>
  </Target>

  <Target Name="KillVBCSCompilerBeforeCopy" BeforeTargets="CopyRoslynCompilerFilesToOutputDirectory" DependsOnTargets="LocateRoslynToolsDestinationFolder;CheckIfShouldKillVBCSCompiler" >
    <KillProcess ProcessName="VBCSCompiler" ImagePath="$(RoslynToolsDestinationFolder)" Condition="'$(ShouldKillVBCSCompiler)' == 'True'" />
  </Target>

  <Target Name="KillVBCSCompilerBeforeClean" AfterTargets="BeforeClean" DependsOnTargets="LocateRoslynToolsDestinationFolder">
    <KillProcess ProcessName="VBCSCompiler" ImagePath="$(RoslynToolsDestinationFolder)" />
  </Target>

  <UsingTask TaskName="KillProcess" AssemblyFile="..\..\tasks\DotNetCompilerPlatformTasks.dll" />
  <UsingTask TaskName="CheckIfVBCSCompilerWillOverride" AssemblyFile="..\..\tasks\DotNetCompilerPlatformTasks.dll" />
  <UsingTask TaskName="UpdateCompilerConfigRecord" AssemblyFile="..\..\tasks\DotNetCompilerPlatformTasks.dll" />

</Project>
