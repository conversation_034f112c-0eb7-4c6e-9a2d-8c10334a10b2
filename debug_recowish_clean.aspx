<%@ Page Language="C#" AutoEventWireup="true" CodeFile="debug_recowish_clean.aspx.cs" Inherits="debug_recowish_clean" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Recowish Database Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            border: 1px solid #ddd;
            margin: 15px 0;
            padding: 20px;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .result-box {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 500px;
            overflow-y: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background-color: #667eea;
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9ff;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>Recowish Database Debug Tool</h1>
            <p>This tool helps diagnose issues with Analyze_recowish01.aspx data query problems</p>
            
            <div class="test-section">
                <h3>Diagnostic Tests</h3>
                <asp:Button ID="btnTestConnection" runat="server" Text="Test Database Connection" CssClass="test-btn" OnClick="TestConnection_Click" />
                <asp:Button ID="btnTestViews" runat="server" Text="Check Views Existence" CssClass="test-btn" OnClick="TestViews_Click" />
                <asp:Button ID="btnTestData" runat="server" Text="Test Data Query" CssClass="test-btn" OnClick="TestData_Click" />
                <asp:Button ID="btnTestPermissions" runat="server" Text="Check Permissions" CssClass="test-btn" OnClick="TestPermissions_Click" />
                <asp:Button ID="btnRunAll" runat="server" Text="Run All Tests" CssClass="test-btn" OnClick="RunAll_Click" />
            </div>
            
            <div class="test-section">
                <h3>Test Results</h3>
                <div class="result-box">
                    <asp:Label ID="lblResults" runat="server" Text="Click buttons above to start diagnosis..."></asp:Label>
                </div>
            </div>
            
            <div class="test-section">
                <h3>Data Preview</h3>
                <div class="grid-container">
                    <asp:GridView ID="gvPreview" runat="server" AutoGenerateColumns="true" 
                        EmptyDataText="No data to display" CssClass="table">
                        <HeaderStyle BackColor="#667eea" ForeColor="White" Font-Bold="true" />
                        <AlternatingRowStyle BackColor="#f8f9ff" />
                    </asp:GridView>
                </div>
            </div>
            
            <div class="test-section">
                <h3>Fix Suggestions</h3>
                <div id="fixSuggestions" runat="server">
                    <p>Fix suggestions will appear after running diagnostics...</p>
                </div>
            </div>
        </div>
    </form>
</body>
</html>
