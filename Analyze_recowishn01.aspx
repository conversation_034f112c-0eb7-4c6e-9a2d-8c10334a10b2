﻿<%@ Page Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeFile="Analyze_recowishn01.aspx.cs" Inherits="Analyze_recowishn01" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

    <html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>日間部分析</title>
        <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
        <script type="text/javascript">
            google.charts.load('current', { 'packages': ['corechart'] });
            google.charts.setOnLoadCallback(drawChart);

            function drawChart() {
                var data = google.visualization.arrayToDataTable(chartData);

                var options = {
                    title: chartTitle,
                    pieHole: 0.4,
                    width: 900,
                    height: 500,
                };

                var chart = new google.visualization.PieChart(
                    document.getElementById("piechart")
                );
                chart.draw(data, options);

                setTimeout(function () {
                    try {
                        var chartDiv = document.getElementById("piechart");
                        var svg = chartDiv.getElementsByTagName("svg")[0];
                        var canvas = document.createElement("canvas");
                        canvas.width = svg.width.baseVal.value;
                        canvas.height = svg.height.baseVal.value;

                        var xml = new XMLSerializer().serializeToString(svg);
                        var img = new Image();
                        img.src =
                            "data:image/svg+xml;base64," +
                            btoa(unescape(encodeURIComponent(xml)));

                        img.onload = function () {
                            var ctx = canvas.getContext("2d");
                            ctx.drawImage(img, 0, 0);
                            var pngData = canvas.toDataURL("image/png");
                            document.getElementById(
                    "<%= hiddenChartData.ClientID %>"
                            ).value = pngData;
                            console.log("圖表已轉換為 Base64");
                        };
                    } catch (error) {
                        console.error("轉換圖表時發生錯誤:", error);
                    }
                }, 1000);
            }

            //google.charts.setOnLoadCallback(drawChart1);
            //function drawChart1() {
            //    var data = google.visualization.arrayToDataTable(chartData);

            //    var options = {
            //        title: '中信科技大學工讀金發放科系數據分析',
            //        pieHole: 0.4
            //    };

            //    var chart = new google.visualization.PieChart(document.getElementById('piechart2'));
            //    chart.draw(data, options);
        </script>
        <style>
            .gridview-table {
                border-collapse: collapse; /* 移除儲存格之間的間隙 */
            }

                .gridview-table td {
                    width: 100px; /* 預設寬度 */
                    height: auto;
                    text-align: center; /* 置中 */
                    border: 1px solid #000; /* 設置邊框樣式、顏色和粗細 */
                    padding: 8px;
                    font-size: 14px;
                }

                .gridview-table th {
                    border: 1px solid #000; /* 設置邊框樣式、顏色和粗細 */
                    padding: 8px;
                }
        </style>
    </head>
    <body>
        <asp:Table ID="tblselect" runat="server" BorderWidth="2px" Width="100%" Style="border-top-style: ridge; border-right-style: ridge; border-left-style: ridge; border-bottom-style: ridge;"
            ForeColor="Black" GridLines="Both" BorderColor="Black" BorderStyle="Groove" BackColor="LightGray">
            <asp:TableRow ID="TableRow1" runat="server" BorderWidth="1px" BorderColor="Black" BorderStyle="Outset"
                BackColor="LightGray">
                <asp:TableCell ID="TableCell1" runat="server" BorderStyle="Inset" BorderWidth="1px" BackColor="LightGray"
                    ForeColor="Black" HorizontalAlign="Left" Wrap="False" BorderColor="WhiteSmoke"
                    Width="100%">
                    <asp:Label ID="label3" Font-Bold="true" Height="26px" runat="server" Font-Names="微軟正黑體 Light" Font-Size="14" Text="獎助學金項目:"></asp:Label><br />
                    <asp:RadioButtonList ID="RadioButtonList1" runat="server" AutoPostBack="True" OnSelectedIndexChanged="RadioButtonList1_SelectedIndexChanged" RepeatLayout="Flow" RepeatDirection="Horizontal">
                        <asp:ListItem Value="1">助學金經費來源金額分佈</asp:ListItem>
                        <asp:ListItem Value="2">助學金經費來源人次分佈</asp:ListItem>
                        <asp:ListItem Value="3">助學金發放科系人次分佈</asp:ListItem>
                    </asp:RadioButtonList>
                </asp:TableCell>

            </asp:TableRow>
            <asp:TableRow ID="TableRow2" runat="server" BorderWidth="1px" BorderColor="Black" BorderStyle="Outset"
                BackColor="LightGray">
                <asp:TableCell ID="TableCell2" runat="server" BorderStyle="Inset" BorderWidth="1px" BackColor="LightGray"
                    ForeColor="Black" HorizontalAlign="Left" Wrap="False" BorderColor="WhiteSmoke"
                    Width="100%">
                    <asp:Label ID="label1" Font-Bold="true" Height="26px" runat="server" Font-Names="微軟正黑體 Light" Font-Size="14" Text="學年度: "></asp:Label>
                    <asp:DropDownList ID="ddl_gradeyyy" runat="server" BackColor="White" Style="margin-right: 5px">
                    </asp:DropDownList>
                    <%--<asp:Button ID="Button_add" runat="server" Width="90px" Font-Names="微軟正黑體 Light"
                Text="新增" Enabled="False" TabIndex="50" OnClick="Button_add_Click" Style="margin-right: 5px" CssClass="btn"></asp:Button>--%>
                    <asp:Button ID="Button_search" runat="server" Width="90px" Font-Names="微軟正黑體 Light"
                        Text="查詢" Enabled="True" TabIndex="50" OnClick="Button_search_Click" Style="margin-right: 5px" CssClass="btn btn-outline-info" ></asp:Button>
                    <%--<asp:Button ID="Button_uload" runat="server" Width="90px" Height="28px" Font-Size="12" Font-Names="微軟正黑體 Light"
                Text="上傳" TabIndex="50" Enabled="False" OnClick="Button_uload_Click"></asp:Button>--%>
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow ID="TableRow3" runat="server" BorderWidth="1px" BorderColor="Black" BorderStyle="Outset"
                BackColor="LightGray">
                <asp:TableCell ID="TableCell3" runat="server" BorderStyle="Inset" BorderWidth="1px" BackColor="LightGray"
                    ForeColor="Black" HorizontalAlign="Left" Wrap="False" BorderColor="WhiteSmoke"
                    Width="100%">
                    <asp:Label ID="msg" Font-Bold="true" Height="26px" runat="server" ForeColor="blue"></asp:Label>
                </asp:TableCell>
            </asp:TableRow>
        </asp:Table>

        <asp:Panel ID="P1" runat="server" Visible="False">
            <div>
                <asp:GridView ID="GridView1" runat="server" CellPadding="2" Font-Names="微軟正黑體 Light" ForeColor="Black" HorizontalAlign="Justify" OnRowDataBound="GridView1_RowDataBound" Width="100%" Font-Bold="True" BackColor="#CCCCCC" CellSpacing="2" AutoGenerateColumns="true"
                    OnPageIndexChanging="NewList1" CssClass="gridview-table">
                    <AlternatingRowStyle Font-Names="微軟正黑體 Light" HorizontalAlign="Center" />
                    <EditRowStyle Font-Names="微軟正黑體 Light" Width="100px" />
                    <EmptyDataRowStyle Font-Names="微軟正黑體 Light" Width="100px" />
                    <FooterStyle BackColor="#CCCCCC" Font-Names="微軟正黑體 Light" Width="100px" />
                    <HeaderStyle BackColor="#007c7c" Font-Bold="True" Font-Names="微軟正黑體 Light" ForeColor="White" HorizontalAlign="Center" />
                    <PagerStyle BackColor="#CCCCCC" Font-Names="微軟正黑體 Light" ForeColor="Blue" HorizontalAlign="Center" />
                    <RowStyle BackColor="White" Font-Names="微軟正黑體 Light" />
                    <SelectedRowStyle BackColor="#000099" Font-Bold="True" Font-Names="微軟正黑體 Light" ForeColor="White" Width="100px" />
                    <SortedAscendingCellStyle BackColor="#F1F1F1" />
                    <SortedAscendingHeaderStyle BackColor="#808080" />
                    <SortedDescendingCellStyle BackColor="#CAC9C9" />
                    <SortedDescendingHeaderStyle BackColor="#383838" />
                </asp:GridView>
                <asp:Button ID="btnExportExcel" runat="server" CssClass="btn btn-success" Text="匯出 Excel" OnClick="ExportToExcel" />
                <asp:Button ID="btnExportPdf" runat="server" CssClass="btn btn-danger" Text="匯出 PDF" OnClick="ExportToPdf" />
            </div>
            <div id="piechart" style="width: 900px; height: 500px;"></div>
            <!-- 📌隱藏欄位用來傳遞 Base64 圖片 -->
            <input type="hidden" id="hiddenChartData" runat="server" />
            <%--<button type="button" onclick="alert(document.getElementById('<%= hiddenChartData.ClientID %>').value)">檢查圖表資料</button>--%>
        </asp:Panel>

    </body>
    </html>
</asp:Content>
