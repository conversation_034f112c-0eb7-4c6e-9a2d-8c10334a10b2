using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.Script.Serialization;
using System.IO;
using System.Web.UI;
using iTextSharp.text;
using iTextSharp.text.pdf;
using ClosedXML.Excel;
using System.Web.UI.WebControls;
using System.Web;
using System.Windows.Interop;

public partial class Analyze_recowish01 : System.Web.UI.Page
{    // 控件宣告 - 已移動到設計器檔案
	App_Func appfun = new App_Func();
	dbconnection db = new dbconnection();
	SqlCommand cmd = new SqlCommand();
	SqlDataAdapter da = new SqlDataAdapter();
	DataSet ds = new DataSet();
	string sqlStr;
	private static DataTable GV1;

	protected void Page_Load(object sender, EventArgs e)
	{
		if (CheckUserSession() == false) { return; }
		if (Session["master_single"].ToString() != "3")
		{
			ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('權限不足,您無法使用本功能!');location.href='/Logout.aspx';", true);
			//Response.Redirect("Home.aspx");
			return;
		}
		if (!IsPostBack)
		{
			// 設定預設選項
			RadioButtonList1.SelectedValue = "1"; // 預設選擇日間部
			RadioButtonList2.SelectedValue = "1"; // 預設選擇實名制人數分布

			// 更新初始提示訊息
			msg.Text = "📊 目前選擇：日間部 - 實名制人數分布，請點擊「開始查詢」按鈕進行分析";
			msg.CssClass = "msg-info";
		}
	}
	protected void Button_search_Click(object sender, EventArgs e)
	{
		try
		{
			if (RadioButtonList1.SelectedValue == "")
			{
				msg.Text = "❌ 訊息: 請選擇人數分析項目!";
				// 添加 JavaScript 確保移除載入遮罩
				ScriptManager.RegisterStartupScript(this, GetType(), "resetButton",
					@"if(typeof hideLoading === 'function') {
                        hideLoading();
                    } else {
                        // 備用方案：手動移除載入遮罩
                        var overlay = document.getElementById('loadingOverlay');
                        if (overlay && overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                        }
                        // 恢復按鈕狀態
                        var btn = document.getElementById('" + Button_search.ClientID + @"');
                        if(btn) {
                            btn.innerHTML = '🔍 開始查詢';
                            btn.disabled = false;
                            btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                        }
                    }", true);
				return;
			}
			else if (RadioButtonList2.SelectedValue == "")
			{
				msg.Text = "❌ 訊息: 請選擇查詢項目類型!";
				// 添加 JavaScript 確保移除載入遮罩
				ScriptManager.RegisterStartupScript(this, GetType(), "resetButton",
					@"if(typeof hideLoading === 'function') {
                        hideLoading();
                    } else {
                        // 備用方案：手動移除載入遮罩
                        var overlay = document.getElementById('loadingOverlay');
                        if (overlay && overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                        }
                        // 恢復按鈕狀態
                        var btn = document.getElementById('" + Button_search.ClientID + @"');
                        if(btn) {
                            btn.innerHTML = '🔍 開始查詢';
                            btn.disabled = false;
                            btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                        }
                    }", true);
				return;
			}
			else
			{
				P1.Visible = true;
				GetGV1Data1();

				// 添加 JavaScript 恢復按鈕狀態，並添加成功提示動畫
				// 使用 setTimeout 確保腳本一定會執行，解決載入覆蓋層停留問題
				ScriptManager.RegisterStartupScript(this, GetType(), "successSearch",
					@"setTimeout(function() {
                        if(typeof hideLoading === 'function') {
                            hideLoading();
                            var btn = document.getElementById('" + Button_search.ClientID + @"');
                            if(btn) {
                                btn.innerHTML = '✓ 查詢成功';
                                btn.style.background = 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)';
                                setTimeout(function() {
                                    btn.innerHTML = '🔍 開始查詢';
                                    btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                                }, 2000);
                            }
                        } else {
                            console.log('hideLoading function not found');
                            // 嘗試手動移除載入覆蓋層
                            var overlay = document.getElementById('loadingOverlay');
                            if (overlay && overlay.parentNode) {
                                overlay.parentNode.removeChild(overlay);
                            }
                            // 恢復按鈕狀態
                            var btn = document.getElementById('" + Button_search.ClientID + @"');
                            if(btn) {
                                btn.innerHTML = '🔍 開始查詢';
                                btn.disabled = false;
                                btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                            }
                        }
                    }, 500);", true);
			}
		}
		catch (Exception ex)
		{
			// 處理異常，並確保移除載入遮罩
			msg.Text = "❌ 錯誤: " + ex.Message;
			ScriptManager.RegisterStartupScript(this, GetType(), "errorHandler",
				@"setTimeout(function() {
                    if(typeof hideLoading === 'function') {
                        hideLoading();
                    } else {
                        // 備用方案：手動移除載入遮罩
                        var overlay = document.getElementById('loadingOverlay');
                        if (overlay && overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                        }
                        // 恢復按鈕狀態
                        var btn = document.getElementById('" + Button_search.ClientID + @"');
                        if(btn) {
                            btn.innerHTML = '🔍 開始查詢';
                            btn.disabled = false;
                            btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                        }
                    }
                }, 500);", true);
		}
	}
	protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
	{
		if (RadioButtonList1.SelectedValue == "1")
		{
			P1.Visible = false;
			GridView1.AllowPaging = false;
			GridView1.DataSource = "";
			GridView1.DataBind();
			//msg.Text = "訊息:";
		}
		else if (RadioButtonList1.SelectedValue == "2")
		{
			P1.Visible = false;
			GridView1.AllowPaging = false;
			GridView1.DataSource = "";
			GridView1.DataBind();
			//msg.Text = "訊息:";
		}
		else if (RadioButtonList1.SelectedValue == "3")
		{
			P1.Visible = false;
			GridView1.AllowPaging = false;
			GridView1.DataSource = "";
			GridView1.DataBind();
			//msg.Text = "訊息:";
		}
	}

	protected void RadioButtonList2_SelectedIndexChanged(object sender, EventArgs e)
	{
		// 清除先前的結果
		P1.Visible = false;
		GridView1.AllowPaging = false;
		GridView1.DataSource = "";
		GridView1.DataBind();

		// 更新提示訊息
		string queryTypeDesc = "";
		switch (RadioButtonList2.SelectedValue)
		{
			case "1":
				queryTypeDesc = "實名制人數分布";
				break;
			case "2":
				queryTypeDesc = "報到人數分布";
				break;
			case "3":
				queryTypeDesc = "繳交畢業證書人數分布";
				break;
			case "4":
				queryTypeDesc = "繳費人數分布";
				break;
		}

		if (RadioButtonList1.SelectedValue != "")
		{
			string analysisTypeDesc = "";
			switch (RadioButtonList1.SelectedValue)
			{
				case "1":
					analysisTypeDesc = "日間部";
					break;
				case "2":
					analysisTypeDesc = "碩士班";
					break;
				case "3":
					analysisTypeDesc = "進修部";
					break;
			}
			msg.Text = string.Format("📊 目前選擇：{0} - {1}，請點擊「開始查詢」按鈕進行分析", analysisTypeDesc, queryTypeDesc);
		}
		else
		{
			msg.Text = string.Format("📈 已選擇查詢類型：{0}，請先選擇人數分析項目", queryTypeDesc);
		}
	}
	private void GetGV1Data1()
	{
		string chartTitle = string.Empty; // 初始化
		string queryTypeText = string.Empty; // 查詢類型描述

		// 添加詳細的調試資訊
		ScriptManager.RegisterStartupScript(this, GetType(), "methodStart",
			"console.log('=== GetGV1Data1 方法開始 ==='); " +
			"console.log('RadioButtonList1.SelectedValue: \"" + RadioButtonList1.SelectedValue + "\"'); " +
			"console.log('RadioButtonList2.SelectedValue: \"" + RadioButtonList2.SelectedValue + "\"');", true);

		try
		{
			// 測試資料庫連線
			ScriptManager.RegisterStartupScript(this, GetType(), "dbConnTest",
				"console.log('嘗試建立資料庫連線...');", true);

			// 檢查資料庫連接狀態並開啟連線
			if (db.conn.State != ConnectionState.Open)
			{
				db.conn.Open();
			}
			ScriptManager.RegisterStartupScript(this, GetType(), "dbConnSuccess",
				"console.log('✅ 資料庫連線成功'); " +
				"console.log('資料庫: " + db.conn.Database + "'); " +
				"console.log('伺服器: " + db.conn.DataSource + "');", true);

			msg.Text = "🔍 開始執行查詢...";
			cmd.Connection = db.conn;

			string dataColumnName = string.Empty; // 數據欄位名稱

			// 根據查詢項目類型設定數據欄位和描述
			switch (RadioButtonList2.SelectedValue)
			{
				case "1":
					queryTypeText = "實名制人數分布";
					dataColumnName = "實名制人數";
					break;
				case "2":
					queryTypeText = "報到人數分布";
					dataColumnName = "報到人數";
					break;
				case "3":
					queryTypeText = "繳交畢業證書人數分布";
					dataColumnName = "繳交畢業證書人數";
					break;
				case "4":
					queryTypeText = "繳費人數分布";
					dataColumnName = "繳費人數";
					break;
				default:
					queryTypeText = "實名制人數分布";
					dataColumnName = "實名制人數";
					break;
			}

			// 查詢最新的數據
			if (RadioButtonList1.SelectedValue == "1")
			{
				sqlStr = @"SELECT [學制]
                                 ,[科系]
                                 ,[實名制人數]
                                 ,[報到人數]
                                 ,[繳交畢業證書人數]
                                 ,[繳費人數]
                          FROM [school].[dbo].[V_recowish1]; ";
				chartTitle = "日間部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
				ViewState["chartTitle"] = "日間部科系" + queryTypeText;
			}
			else if (RadioButtonList1.SelectedValue == "2")
			{
				sqlStr = @"SELECT [學制]
                                 ,[科系]
                                 ,[實名制人數]
                                 ,[報到人數]
                                 ,[繳交畢業證書人數]
                                 ,[繳費人數]
                          FROM [school].[dbo].[V_recowish3]; ";
				chartTitle = "碩士班" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
				ViewState["chartTitle"] = "碩士班系所" + queryTypeText;
			}
			else if (RadioButtonList1.SelectedValue == "3")
			{
				sqlStr = @"SELECT [學制]
                                 ,[科系]
                                 ,[實名制人數]
                                 ,[報到人數]
                                 ,[繳交畢業證書人數]
                                 ,[繳費人數]
                          FROM [school].[dbo].[V_recowish5];";
				chartTitle = "進修部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
				ViewState["chartTitle"] = "進修部科系" + queryTypeText;
			}
			else
			{
				msg.Text = "❌ 錯誤: 請選擇人數分析項目!";
				P1.Visible = false;

				// 添加調試資訊
				ScriptManager.RegisterStartupScript(this, GetType(), "debugInfo",
					"console.log('調試資訊: 未選擇分析項目，RadioButtonList1.SelectedValue = \"" +
					RadioButtonList1.SelectedValue + "\"');", true);
				return;
			}

			// 添加 SQL 查詢調試資訊
			msg.Text = "📋 執行 SQL 查詢: " + sqlStr.Substring(0, Math.Min(50, sqlStr.Length)) + "...";

			cmd.CommandText = sqlStr;
			// 使用參數化查詢來避免 SQL Injection
			cmd.Parameters.Clear();

			// 添加調試日誌
			ScriptManager.RegisterStartupScript(this, GetType(), "sqlDebug",
				"console.log('SQL查詢: " + sqlStr.Replace("'", "\\'").Replace("\r", "").Replace("\n", " ") + "');", true);

			// 🔧 修復關鍵問題：避免 DataSet 重複使用造成的數據衝突
			// 清除之前的數據表
			ds.Clear();

			// 每次創建新的 DataTable 來避免重複使用
			da.SelectCommand = cmd;
			da.Fill(ds, "check");
			GV1 = ds.Tables["check"].Copy(); // 使用 Copy() 創建獨立副本

			// 添加數據行數調試資訊
			msg.Text = "📊 查詢完成，獲得 " + GV1.Rows.Count + " 筆記錄...";
			ScriptManager.RegisterStartupScript(this, GetType(), "dataDebug",
				"console.log('查詢結果: " + GV1.Rows.Count + " 筆記錄');", true);

			// 確認有數據再進行處理
			if (GV1.Rows.Count > 0)
			{
				// 添加成功獲取數據的調試資訊
				msg.Text = "📈 正在處理數據並生成圖表...";
				ScriptManager.RegisterStartupScript(this, GetType(), "dataProcessDebug",
					"console.log('開始處理 " + GV1.Rows.Count + " 筆數據');", true);

				JavaScriptSerializer serializer = new JavaScriptSerializer();
				var chartData = new System.Collections.Generic.List<object>();

				// 根據選擇的查詢項目類型生成圖表數據
				chartData.Add(new object[] { "科系", dataColumnName });

				foreach (DataRow row in GV1.Rows)
				{
					string departmentName = row["科系"].ToString();
					int dataValue = 0;

					// 根據選擇的數據類型獲取對應數值
					if (int.TryParse(row[dataColumnName].ToString(), out dataValue))
					{
						chartData.Add(new object[] { departmentName, dataValue });
					}
				}

				// 將標題傳遞到前端
				Page.ClientScript.RegisterStartupScript(this.GetType(), "chartTitle", "var chartTitle = '" + chartTitle + "';", true);
				string jsonData = serializer.Serialize(chartData);
				Page.ClientScript.RegisterStartupScript(this.GetType(), "chartData", "var chartData = " + jsonData + ";", true);

				// 添加圖表數據調試資訊
				ScriptManager.RegisterStartupScript(this, GetType(), "chartDebug",
					"console.log('圖表數據:', " + jsonData + "); console.log('圖表標題:', '" + chartTitle + "');", true);

				// 計算統計摘要
				int totalCount = 0;
				int maxValue = 0;
				string maxDepartment = "";

				foreach (DataRow row in GV1.Rows)
				{
					int currentValue = 0;
					if (int.TryParse(row[dataColumnName].ToString(), out currentValue))
					{
						totalCount += currentValue;
						if (currentValue > maxValue)
						{
							maxValue = currentValue;
							maxDepartment = row["科系"].ToString();
						}
					}
				}

				// 將統計資訊存到 ViewState 以供前端使用
				ViewState["TotalCount"] = totalCount;
				ViewState["MaxValue"] = maxValue;
				ViewState["MaxDepartment"] = maxDepartment;
				ViewState["DepartmentCount"] = GV1.Rows.Count;

				GridView1.DataSource = GV1;
				GridView1.AllowPaging = true;
				GridView1.PageSize = 20;
				GridView1.DataBind();

				// 更新訊息顯示統計摘要
				msg.Text = string.Format("✅ 查詢完成！共 {0} 個科系，總計 {1} 人，最高科系：{2} ({3} 人)",
					GV1.Rows.Count, totalCount, maxDepartment, maxValue);
				msg.CssClass = "msg-info";

				// 註冊腳本來觸發統計卡和表格的動畫效果，以及初始化圖表
				ScriptManager.RegisterStartupScript(this, GetType(), "animationEffects",
					"setTimeout(function() { " +
					"   if(typeof animateStatCards === 'function') { animateStatCards(); } " +
					"   if(typeof animateTableRows === 'function') { animateTableRows(); } " +
					"   if(typeof initializeChart === 'function') { initializeChart(); } " +
					"}, 500);", true);
			}
			else
			{
				// 處理沒有找到數據的情況
				GridView1.AllowPaging = false;
				GridView1.DataSource = "";
				GridView1.DataBind();
				msg.Text = "⚠️ 訊息: 查無資料，請調整查詢條件。";
				P1.Visible = false;

				// 確保移除載入遮罩
				ScriptManager.RegisterStartupScript(this, GetType(), "noDataHandler",
					@"setTimeout(function() {
                        if(typeof hideLoading === 'function') {
                            hideLoading();
                        } else {
                            // 備用方案：手動移除載入遮罩
                            var overlay = document.getElementById('loadingOverlay');
                            if (overlay && overlay.parentNode) {
                                overlay.parentNode.removeChild(overlay);
                            }
                            // 恢復按鈕狀態，添加警告樣式
                            var btn = document.getElementById('" + Button_search.ClientID + @"');
                            if(btn) {
                                btn.innerHTML = '⚠️ 查無資料';
                                btn.disabled = false;
                                btn.style.background = 'linear-gradient(135deg, #faad14 0%, #ffc53d 100%)';
                                setTimeout(function() {
                                    btn.innerHTML = '🔍 開始查詢';
                                    btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                                }, 2000);
                            }
                        }
                    }, 500);", true);
			}
		}
		catch (Exception ex)
		{
			// 處理異常
			msg.Text = "❌ 錯誤: " + ex.Message;
			msg.ForeColor = System.Drawing.Color.Red;

			// 確保移除載入遮罩
			ScriptManager.RegisterStartupScript(this, GetType(), "errorDataHandler",
				@"setTimeout(function() {
                    if(typeof hideLoading === 'function') {
                        hideLoading();
                    } else {
                        // 備用方案：手動移除載入遮罩
                        var overlay = document.getElementById('loadingOverlay');
                        if (overlay && overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                        }
                        // 恢復按鈕狀態
                        var btn = document.getElementById('" + Button_search.ClientID + @"');
                        if(btn) {
                            btn.innerHTML = '🔍 開始查詢';
                            btn.disabled = false;
                            btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                        }
                    }
                }, 500);", true);
		}
		finally
		{
			if (db.conn.State == ConnectionState.Open)
			{
				db.conn.Close();
			}
		}
	}

	public void GridView1_RowDataBound(object sender, GridViewRowEventArgs e)
	{
		if (e.Row.RowType == DataControlRowType.DataRow)
		{
			// 設定每個欄位的寬度沒設定會套用css的預設寬度
			e.Row.Cells[0].Style.Add("width", "200px");
			e.Row.Cells[1].Style.Add("width", "50px");

			// 設定內容置中
			for (int i = 0; i < e.Row.Cells.Count; i++)
			{
				e.Row.Cells[i].HorizontalAlign = HorizontalAlign.Center;
			}
		}
	}
	protected void ExportToExcel(object sender, EventArgs e)
	{
		try
		{
			// 確保有圖表數據
			if (string.IsNullOrEmpty(hiddenChartData.Value))
			{
				ScriptManager.RegisterStartupScript(this, GetType(), "Msg",
					"alert('圖表數據未準備好，請稍後再試');", true);
				return;
			}

			using (XLWorkbook wb = new XLWorkbook())
			{
				var ws = wb.Worksheets.Add("Data");

				// 插入表格
				ws.Cell(1, 1).InsertTable(GV1);

				// 插入圖表
				string base64Image = hiddenChartData.Value;
				byte[] imageBytes = Convert.FromBase64String(base64Image.Split(',')[1]);
				using (MemoryStream ms = new MemoryStream(imageBytes))
				{
					var picture = ws.AddPicture(ms)
						.MoveTo(ws.Cell(GV1.Rows.Count + 3, 1))
						.Scale(0.8);
				}

				// 輸出 Excel
				Response.Clear();
				Response.Buffer = true;
				Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
				Response.AddHeader("content-disposition", "attachment;filename=" + ViewState["chartTitle"] + "統計報表" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx");

				using (MemoryStream stream = new MemoryStream())
				{
					wb.SaveAs(stream);
					stream.WriteTo(Response.OutputStream);
					Response.Flush();
					Response.End();
				}
			}
		}
		catch (Exception ex)
		{
			ScriptManager.RegisterStartupScript(this, GetType(), "Msg", "alert('匯出失敗：" + ex.Message + "');", true);
		}
	}

	protected void ExportToPdf(object sender, EventArgs e)
	{
		try
		{
			// 確保有圖表數據
			if (string.IsNullOrEmpty(hiddenChartData.Value))
			{
				ScriptManager.RegisterStartupScript(this, GetType(), "Msg",
					"alert('圖表數據未準備好，請稍後再試');", true);
				return;
			}
			Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 10f, 10f);
			PdfWriter.GetInstance(pdfDoc, Response.OutputStream);
			pdfDoc.Open();

			// 📌 1. 設定中文字型 (標楷體)
			string fontPath = Server.MapPath("~/fonts/kaiu.ttf"); // 確保網站有這個字型
			BaseFont bfChinese = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
			Font titleFont = new Font(bfChinese, 16, Font.BOLD);
			Font headerFont = new Font(bfChinese, 12, Font.BOLD); // **標題加粗**
			Font cellFont = new Font(bfChinese, 10, Font.NORMAL);

			// 📌 2. 插入標題
			Paragraph title = new Paragraph("" + ViewState["chartTitle"] + "統計報表\n\n", titleFont);
			title.Alignment = Element.ALIGN_CENTER;
			pdfDoc.Add(title);

			// 📌 3. 插入表格
			PdfPTable pdfTable = new PdfPTable(GV1.Columns.Count);
			pdfTable.WidthPercentage = 100;// 表格佔滿頁面寬度
			pdfTable.SplitLate = false; // 避免表格被切割
			pdfTable.SplitRows = true;  // 允許資料自動分頁

			// 📌 4. 插入標題列
			foreach (DataColumn column in GV1.Columns)
			{
				PdfPCell headerCell = new PdfPCell(new Phrase(column.ColumnName, headerFont));
				headerCell.BackgroundColor = new BaseColor(200, 200, 200); // **灰色背景**
				headerCell.HorizontalAlignment = Element.ALIGN_CENTER;
				pdfTable.AddCell(headerCell);
			}

			// 📌 5. 插入資料列
			foreach (DataRow row in GV1.Rows)
			{
				foreach (var cell in row.ItemArray)
				{
					PdfPCell pdfCell = new PdfPCell(new Phrase(cell.ToString(), cellFont));
					pdfCell.HorizontalAlignment = Element.ALIGN_CENTER;
					pdfTable.AddCell(pdfCell);
				}
			}
			pdfDoc.Add(pdfTable);

			//📌 6.頁尾
			string foot = String.Format(@"Copyright @{0} All rights reserved 中信科技大學 Made with 資訊系統整合中心", DateTime.Now.ToString("yyyy"));
			PdfPTable pdffoot = new PdfPTable(1);
			pdffoot.WidthPercentage = 100;
			//PdfPCell contentTitle = new PdfPCell(new Phrase("總結", fontChinese));
			PdfPCell contentTitle = new PdfPCell(new Phrase(foot, headerFont));
			contentTitle.HorizontalAlignment = Element.ALIGN_CENTER;
			contentTitle.BorderWidth = 0;
			pdffoot.AddCell(contentTitle);
			pdfDoc.Add(pdffoot);

			// 📌 7. 插入圖表
			string base64Image = hiddenChartData.Value;
			if (!string.IsNullOrEmpty(base64Image))
			{
				byte[] imageBytes = Convert.FromBase64String(base64Image.Split(',')[1]); // 去掉 "data:image/png;base64,"
				iTextSharp.text.Image chartImage = iTextSharp.text.Image.GetInstance(imageBytes);
				chartImage.ScaleToFit(500f, 300f);  // 調整大小
				chartImage.Alignment = Element.ALIGN_CENTER;
				pdfDoc.Add(new Paragraph("\n\n"));  // **加點空間**
				pdfDoc.Add(chartImage);
			}

			pdfDoc.Close();

			Response.ContentType = "application/pdf";
			Response.AddHeader("content-disposition", "attachment;filename=" + ViewState["chartTitle"] + "統計報表" + DateTime.Now.ToString("yyyy-MM-dd") + ".pdf");
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Write(pdfDoc);
			Response.End();
		}
		catch (Exception ex)
		{
			ScriptManager.RegisterStartupScript(this, GetType(), "Msg", "alert('匯出失敗：" + ex.Message + "');", true);
		}
	}

	protected void NewList1(object sender, GridViewPageEventArgs e)
	{
		// 設定 GridView 的新頁索引
		GridView1.PageIndex = e.NewPageIndex;

		// 再次綁定資料到 GridView
		GetGV1Data1();
	}

	public bool CheckUserSession()
	{
		if (Session["tech_no"] == null)
		{
			ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('作業逾時或尚未登入,請登入後再繼續作業!');location.href='/Logout.aspx';", true);
			return false;
		}
		else
		{
			return true;
		}
	}
}
