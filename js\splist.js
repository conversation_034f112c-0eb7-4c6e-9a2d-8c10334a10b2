$(document).ready(function () {
    // Initialize jqGrid
    $("#scholarshipGrid").jqGrid({
        url: 'splistData.ashx',
        datatype: "json",
        mtype: "POST",
        colNames: ['特殊身分別', '身分名稱', '負責單位1', '填報人1', '負責單位2', '填報人2', '負責單位3', '填報人3', '異動人員', '異動時間', '操作'],
        colModel: [
            { name: 'sp_type', index: 'sp_type', width: 100, editable: true },
            { name: 'sp_name', index: 'sp_name', width: 300, editable: true },
            { name: 'dep1', index: 'dep1', width: 200, editable: true },
            { name: 'tech_no1', index: 'tech_no1', width: 200, editable: true },
            { name: 'dep2', index: 'dep2', width: 200, editable: true },
            { name: 'tech_no2', index: 'tech_no2', width: 200, editable: true },
            { name: 'dep3', index: 'dep3', width: 200, editable: true },
            { name: 'tech_no3', index: 'tech_no3', width: 200, editable: true },
            { name: 'update_tech', index: 'update_tech', width: 200, editable: true },
            { name: 'update_time', index: 'update_time', width: 200, align: "center", editable: true, formatter: 'date', formatoptions: { newformat: 'Y-m-d' } },
            { name: 'oper', index: 'oper', width: 100, sortable: false, formatter: 'actions', formatoptions: { keys: true } }
        ],
        pager: "#scholarshipPager",
        rowNum: 20,
        rowList: [10, 20, 30],
        sortname: '特殊身分別',
        sortorder: "asc",
        viewrecords: true,
        height: 'auto',
        autowidth: true,
        caption: "特殊身分列表維護",
        editurl: "splistCRUD.ashx",
        loadonce: true
    }).navGrid("#scholarshipPager", { edit: true, add: true, del: true, search: false, refresh: true });

    // Handle search button click event
    $("#btnSearch").click(function () {
        var sp_name = $("#txtsp_name").val();
        var tech_no = $("#txttech_no").val();

        // Reload grid with new parameters
        $("#scholarshipGrid").jqGrid('setGridParam', {
            url: 'splistData.ashx',
            datatype: 'json',
            postData: {
                sp_name: sp_name,
                tech_no: tech_no
            },
            page: 1
        }).trigger('reloadGrid'); // Reload the grid with the new parameters
    });
});