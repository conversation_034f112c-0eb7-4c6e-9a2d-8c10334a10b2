using System.Data.SqlClient;

/// <summary>
/// dbconnection 的摘要描述
/// </summary>
public class dbconnection
{
    public SqlConnection conn;
    private static readonly string connectionString = "Server=192.168.2.61;uid=feuisic2;pwd=**************************;Database=school;Connection Timeout=30;"; //宣告SQL的連線(校務資料庫)    

    public dbconnection()
    {
        if (string.IsNullOrEmpty(connectionString))
        {
            // 這不應該發生，因為它是硬編碼的，但作為一個檢查點
            throw new System.InvalidOperationException("Static connectionString variable is null or empty in dbconnection constructor.");
        }
        conn = new SqlConnection(connectionString);
        if (string.IsNullOrEmpty(conn.ConnectionString))
        {
            // 如果 SqlConnection 在賦值後 ConnectionString 仍然是空的，這是一個更深層的問題
            throw new System.InvalidOperationException("SqlConnection.ConnectionString is null or empty after assignment in dbconnection constructor. Original static string was: " + connectionString);
        }
    }
}
