## About

Provides hardware-accelerated numeric types, suitable for high-performance processing and graphics applications.

## Main Types

The main types provided by this library are:

- System.Numerics.Matrix3x2
- System.Numerics.Matrix4x4
- System.Numerics.Plane
- System.Numerics.Quaternion
- System.Numerics.Vector2
- System.Numerics.Vector3
- System.Numerics.Vector4
- System.Numerics.Vector
- System.Numerics.Vector<T>

## Additional Documentation

- API reference can be found in: https://learn.microsoft.com/en-us/dotnet/api/system.numerics.vectors

## License

System.Numerics.Vectors is released as open source under the [MIT license](https://licenses.nuget.org/MIT).
