﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;
using System.Text;
using System.Drawing;


public partial class login : System.Web.UI.Page
{
    // 確保引用正確的命名空間或組件  
    // 如果 App_Func 是自定義類別，請確認它是否在專案中，並添加正確的命名空間引用  
    // 替換為 App_Func 所在的命名空間
    App_Func appfun = new App_Func();
    dbconnection db = new dbconnection();
    SqlCommand cmd = new SqlCommand();
    SqlDataAdapter da = new SqlDataAdapter();
    DataSet ds = new DataSet();
    string sqlStr = string.Empty;
    private static DataTable myTable;
    protected void Page_Load(object sender, EventArgs e)
    {

        if (!IsPostBack)  // 這裡的代碼只會在頁面第一次加載時執行
        {
            // 清空訊息
            lblMessage.Text = string.Empty;
            //Drop_login.SelectedValue = "";
            // 動態新增 required 屬性
            txtPassword.Attributes.Add("required", "true");

            // 設定下拉選單的寬度屬性
            //Drop_login.Attributes.Add("style", "width: 100%; max-width: 100%; text-overflow: ellipsis;");
        }

    }


    protected void Button_type_Click(object sender, EventArgs e)
    {
 

    }

    protected void btnLogin_Click(object sender, EventArgs e)
    {
        
        if (txtUsername.Text.Trim().IndexOf("'") >= 0 || txtUsername.Text.Trim().IndexOf("-") >= 0 || txtUsername.Text.Trim().IndexOf("--") >= 0 || txtPassword.Text.Trim().IndexOf("'") >= 0 || txtPassword.Text.Trim().IndexOf("-") >= 0 || txtPassword.Text.Trim().IndexOf("--") >= 0)
        {
            Response.Write("<script language='JavaScript'>window.alert(\"訊息: 請勿輸入'或-等字元!!!\");window.open('login.aspx','_parent');</script>");
            Response.End();
            return;
        }
        //驗證空白 如果 txtUsername.Text 不是空的、不是 null，也不是只有空白字符，就執行 if 內的程式碼。
        if (!string.IsNullOrWhiteSpace(txtUsername.Text) && !string.IsNullOrWhiteSpace(txtPassword.Text))
        {
            dbconnection db = new dbconnection();
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataSet ds = new DataSet();
            DataSet ds1 = new DataSet();
            cmd.Connection = db.conn;
            string username = string.Empty;
            string password = string.Empty;

            if (txtPassword.Text.Length > 10)
            {
                password = appfun.FilterGdName(txtPassword.Text.Substring(0, 10));
                username = appfun.FilterGdName(txtUsername.Text.Trim());
            }
            else
            {
                password = appfun.FilterGdName(txtPassword.Text.Trim());
                username = appfun.FilterGdName(txtUsername.Text.Trim());
            }

            string sqlcheck = @"select s.tech_cname,s.tech_no from  S12_teacher s  where s.tech_no = @username and s.tech_passw = @password  and s.tech_duty='1' ";
            cmd.CommandText = sqlcheck;
            //定義parameter型別
            //cmd.Parameters.Add("@tech", SqlDbType.VarBinary);
            //cmd.Parameters["@tech"].Value = tech;
            //讓ADO.NET自行判斷型別轉換
            cmd.Parameters.Clear();
            cmd.Parameters.AddWithValue("@username", username);
            cmd.Parameters.AddWithValue("@password", password);
            da.SelectCommand = cmd;
            da.Fill(ds, "master_check");
            DataTable tMas = ds.Tables["master_check"];
            if (ds.Tables["master_check"].Rows.Count > 0) //登入成功
            {

                //if (RadioButtonList1.SelectedValue == "0") //教師
                //{
                //        sqlStr = @"select t2.year, t2.class_name, t2.tech_no, t1.tech_cname from S12_teacher t1 inner join graduate_authority t2 on t1.tech_no = t2.tech_no where  t1.tech_no = @username and t1.tech_passw =@password  ";
                //        cmd.CommandText = sqlStr;
                //        da.SelectCommand = cmd;
                //        da.Fill(ds, "login_teacher");
                //        myTable = ds.Tables["login_teacher"];
                //        if (ds.Tables["login_teacher"].Rows.Count > 0)
                //        {

                //            Session["tech_no"] = myTable.Rows[0][2].ToString().Trim(); //取得登入教師代號
                //            Session["user"] = myTable.Rows[0][3].ToString().Trim();    //取得登入教師姓名
                //            Session["master_single"] = "0";
                //            Response.Redirect("PIS.aspx");
                //        }
                //        else
                //        {
                //            Response.Write("<script>alert('系助理尚未加入維護班級！')</script>");
                //            return;
                //        }
                    
                //}
                //else if (RadioButtonList1.SelectedValue == "1") //科系職員
                //{

                //    sqlStr = @"select a.tech_no,a.tech_cname,isnull(a.tech_office1,'') tech_office1,isnull(a.Expr1,'') Expr1,isnull(a.tech_office2,'') tech_office2,isnull(a.Expr2,'') Expr2,isnull(a.tech_office3,'') tech_office3,isnull(a.Expr3,'') Expr3,isnull(a.tech_office4,'') tech_office4,isnull(a.Expr4,'') Expr4,isnull(a.tech_office5,'') tech_office5,isnull(a.Expr5,'') Expr5,isnull(a.tech_office6,'') tech_office6,isnull(a.Expr6,'') Expr6,isnull(a.tech_office7,'') tech_office7,isnull(a.Expr7,'') Expr7,isnull(a.tech_office8,'') tech_office8,isnull(a.Expr8,'') Expr8,isnull(a.tech_office9,'') tech_office9,isnull(a.Expr9,'') Expr9 FROM GT_sub_login a join s12_teacher b on a.tech_no=b.tech_no where a.tech_no = @username and b.tech_passw = @password and tech_duty='1' ";
                //    cmd.CommandText = sqlStr;
                //    da.SelectCommand = cmd;
                //    da.Fill(ds, "login_manager");
                //    myTable = ds.Tables["login_manager"];
                //    if (ds.Tables["login_manager"].Rows.Count > 0)
                //    {
                //        //指定系統所需Session變數
                //        Session["tech_no"] = myTable.Rows[0][0].ToString().Trim(); //取得登入者代號
                //        Session["user"] = myTable.Rows[0][1].ToString().Trim();    //取得登入者姓名 
                //        Session["office"] = myTable.Rows[0][2].ToString().Trim();
                //        Session["officeall"] = "( '" + myTable.Rows[0][2].ToString().Trim() + "' ," + "'" + myTable.Rows[0][4].ToString().Trim() + "'," + "'" + myTable.Rows[0][6].ToString().Trim() + "'," + "'" + myTable.Rows[0][8].ToString().Trim() + "'," + "'" + myTable.Rows[0][10].ToString().Trim() + "'," + "'" + myTable.Rows[0][12].ToString().Trim() + "'," + "'" + myTable.Rows[0][14].ToString().Trim() + "'," + "'" + myTable.Rows[0][16].ToString().Trim() + "'," + "'" + myTable.Rows[0][18].ToString().Trim() + "' )"; //取得登入者單位代碼
                //        Session["office_name"] = myTable.Rows[0][3].ToString().Trim();  //取得登入者科系單位名稱
                //        Session["office_name2"] = myTable.Rows[0][5].ToString().Trim();
                //        Session["office_name3"] = myTable.Rows[0][7].ToString().Trim();
                //        Session["office_name4"] = myTable.Rows[0][9].ToString().Trim();
                //        Session["office_name5"] = myTable.Rows[0][11].ToString().Trim();
                //        Session["office_name6"] = myTable.Rows[0][13].ToString().Trim();
                //        Session["office_name7"] = myTable.Rows[0][15].ToString().Trim();
                //        Session["office_name8"] = myTable.Rows[0][17].ToString().Trim();
                //        Session["office_name9"] = myTable.Rows[0][19].ToString().Trim();
                //        Session["master_single"] = "1";
                //        Response.Redirect("PIS.aspx");
                //    }
                //    else
                //    {
                //        Response.Write("<script>alert('帳號或密碼不正確！')</script>");
                //    }
                //}
                //else if (RadioButtonList1.SelectedValue == "2") //學院職員
                //{
                //    sqlStr = @"select t1.tech_no,t1.tech_cname, t1.tech_office, left(t2.detail,5) 
                //           from S12_teacher t1, psn_administrative t2
                //           where (t1.tech_office = t2.adm_code or t1.tech_sub = t2.adm_code) and t1.tech_no = @username and t1.tech_passw = @password ";
                //    cmd.CommandText = sqlStr;
                //    da.SelectCommand = cmd;
                //    da.Fill(ds, "login_manager");
                //    myTable = ds.Tables["login_manager"];
                //    if (ds.Tables["login_manager"].Rows.Count > 0)
                //    {
                //        //指定系統所需Session變數
                //        Session["tech_no"] = myTable.Rows[0][0].ToString().Trim(); //取得登入者代號
                //        Session["user"] = myTable.Rows[0][1].ToString().Trim();    //取得登入者姓名   
                //        Session["office"] = myTable.Rows[0][2].ToString().Trim(); //取得登入者單位代碼
                //        Session["office_name"] = myTable.Rows[0][3].ToString().Trim();  //取得登入者單位名稱

                //        if (txtUsername.Text == "02222")
                //        {
                //            Session["master_single"] = "2";
                //            Response.Redirect("PIS.aspx");
                //        }
                //        else
                //        {
                //            //判斷學院職員
                //            string sqlStrCollege = @"select * from school.dbo.psn_administrative t 
                //                                    where t.bel_dep = 'T' and t.degree = '1' and t.dep_status = '1' and LEFT(t.detail,6)<> '通識教育中心' and t.adm_code = '" + Session["office"].ToString() + "'";
                //            cmd.CommandText = sqlStrCollege;
                //            da.SelectCommand = cmd;
                //            da.Fill(ds, "college_check");
                //            if (ds.Tables["college_check"].Rows.Count > 0)
                //            {
                //                Session["master_single"] = "2";
                //                Response.Redirect("PIS.aspx");
                //            }
                //            else
                //            {
                //                Response.Write("<script>alert('您沒有院助理權限！')</script>");
                //            }
                //        }
                //    }
                //    else
                //    {
                //        Response.Write("<script>alert('帳號或密碼不正確！')</script>");
                //    }
                //}
                //else if (RadioButtonList1.SelectedValue == "3") //職涯管理員
                //{

                    sqlStr = @"select t1.tech_no,t1.tech_cname,trim(t2.detail) from S12_teacher t1 left join  psn_administrative t2 on t1.tech_office = t2.adm_code
                           where t1.tech_no = '02222' and t1.tech_passw = @password " +
                     "union " +
                        "select t1.tech_no,t1.tech_cname,trim(t2.detail) from S12_teacher t1 left join  psn_administrative t2 on t1.tech_office = t2.adm_code " +
                          " where t1.tech_no = '01602' and t1.tech_passw = @password ";
                    cmd.CommandText = sqlStr;
                    da.SelectCommand = cmd;
                    da.Fill(ds, "login_manager");
                    myTable = ds.Tables["login_manager"];
                    if (ds.Tables["login_manager"].Rows.Count > 0)
                    {
                        //指定系統所需Session變數
                        Session["tech_no"] = myTable.Rows[0][0].ToString().Trim(); //取得登入者代號
                        Session["user"] = myTable.Rows[0][1].ToString().Trim();    //取得登入者姓名   
                        Session["office"] = myTable.Rows[0][2].ToString().Trim(); //取得登入者單位代碼
                        //Session["office_name"] = myTable.Rows[0][3].ToString().Trim();  //取得登入者單位名稱
                        Session["master_single"] = "3";
                        Response.Redirect("PIS.aspx");
                        //Response.Redirect("Home.aspx");
                    }
                    else
                    {
                        Response.Write("<script>alert('帳號或密碼不正確！')</script>");
                        return;
                    }
                //}
                //else
                //{
                //    Response.Write("<script>alert('請選擇身分別！')</script>");
                //    return;
                //}
            }
            else
            {
                // 顯示錯誤訊息
                lblMessage.Text = "使用者名稱或密碼無效!";
                return;
            }


        }
        else
        {
            // 顯示錯誤訊息
            lblMessage.Text = "請輸入帳密!";
            return;
        }
    }
    private void Verification_Click(object sender, EventArgs e)
    {
        //圖形驗證碼
        //Vaild vn = new Vaild();
        ////Vaild.VaildNumAnswerVaildNum vn = new Vaild.VaildNumAnswerVaildNum(10);
        //Bitmap mm = vn.VaildNumImage; //取得對應的圖
        //gg.DrawImage(mm, 50, 50);

        //MessageBox.Show(vn.VaildNumAnswer); //取得對應的答案並秀出
    }
}