using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.Script.Serialization;
using System.IO;
using System.Web.UI;
using iTextSharp.text;
using iTextSharp.text.pdf;
using ClosedXML.Excel;
using System.Web.UI.WebControls;
using System.Web;

public partial class Analyze_recowish01 : System.Web.UI.Page
{
    App_Func appfun = new App_Func();
    dbconnection db = new dbconnection();
    SqlCommand cmd = new SqlCommand();
    SqlDataAdapter da = new SqlDataAdapter();
    DataSet ds = new DataSet();
    string sqlStr;
    private static DataTable GV1;

    protected void Page_Load(object sender, EventArgs e)
    {
        if (CheckUserSession() == false) { return; }
        if (Session["master_single"].ToString() != "3")
        {
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('權限不足,您無法使用本功能!');location.href='/Logout.aspx';", true);
            return;
        }
        if (!IsPostBack)
        {
            RadioButtonList1.SelectedValue = "1";
            RadioButtonList2.SelectedValue = "1";
            msg.Text = "目前選擇：日間部 - 實名制人數分布，請點擊開始查詢按鈕進行分析";
            msg.CssClass = "msg-info";
        }
    }

    protected void Button_search_Click(object sender, EventArgs e)
    {
        try
        {
            if (RadioButtonList1.SelectedValue == "")
            {
                msg.Text = "訊息: 請選擇人數分析項目!";
                ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
                return;
            }
            else if (RadioButtonList2.SelectedValue == "")
            {
                msg.Text = "訊息: 請選擇查詢項目類型!";
                ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
                return;
            }
            else
            {
                P1.Visible = true;
                GetGV1Data1();
            }
        }
        catch (Exception ex)
        {
            msg.Text = "錯誤: " + ex.Message;
            ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingException", "hideLoading();", true);
        }
    }

    protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
    {
        P1.Visible = false;
        GridView1.AllowPaging = false;
        GridView1.DataSource = "";
        GridView1.DataBind();
    }

    protected void RadioButtonList2_SelectedIndexChanged(object sender, EventArgs e)
    {
        P1.Visible = false;
        GridView1.AllowPaging = false;
        GridView1.DataSource = "";
        GridView1.DataBind();

        string queryTypeDesc = "";
        switch (RadioButtonList2.SelectedValue)
        {
            case "1":
                queryTypeDesc = "實名制人數分布";
                break;
            case "2":
                queryTypeDesc = "報到人數分布";
                break;
            case "3":
                queryTypeDesc = "繳交畢業證書人數分布";
                break;
            case "4":
                queryTypeDesc = "繳費人數分布";
                break;
        }

        if (RadioButtonList1.SelectedValue != "")
        {
            string analysisTypeDesc = "";
            switch (RadioButtonList1.SelectedValue)
            {
                case "1":
                    analysisTypeDesc = "日間部";
                    break;
                case "2":
                    analysisTypeDesc = "碩士班";
                    break;
                case "3":
                    analysisTypeDesc = "進修部";
                    break;
            }
            msg.Text = string.Format("目前選擇：{0} - {1}，請點擊開始查詢按鈕進行分析", analysisTypeDesc, queryTypeDesc);
        }
        else
        {
            msg.Text = string.Format("已選擇查詢類型：{0}，請先選擇人數分析項目", queryTypeDesc);
        }
    }

    private void GetGV1Data1()
    {
        string chartTitle = string.Empty;
        string queryTypeText = string.Empty;
        string debugInfo = "";

        try
        {
            debugInfo += "🔍 開始執行查詢...<br/>";
            debugInfo += "📊 選擇的分析項目: " + RadioButtonList1.SelectedValue + "<br/>";
            debugInfo += "📈 選擇的查詢類型: " + RadioButtonList2.SelectedValue + "<br/>";

            // 檢查資料庫連線
            debugInfo += "🔗 檢查資料庫連線狀態: " + db.conn.State.ToString() + "<br/>";

            if (db.conn.State != ConnectionState.Open)
            {
                debugInfo += "🔄 正在開啟資料庫連線...<br/>";
                db.conn.Open();
                debugInfo += "✅ 資料庫連線已開啟<br/>";
            }
            else
            {
                debugInfo += "✅ 資料庫連線已存在<br/>";
            }

            debugInfo += "🏢 資料庫名稱: " + db.conn.Database + "<br/>";
            debugInfo += "🖥️ 伺服器: " + db.conn.DataSource + "<br/>";

            cmd.Connection = db.conn;
            string dataColumnName = string.Empty;

            switch (RadioButtonList2.SelectedValue)
            {
                case "1":
                    queryTypeText = "實名制人數分布";
                    dataColumnName = "實名制人數";
                    break;
                case "2":
                    queryTypeText = "報到人數分布";
                    dataColumnName = "報到人數";
                    break;
                case "3":
                    queryTypeText = "繳交畢業證書人數分布";
                    dataColumnName = "繳交畢業證書人數";
                    break;
                case "4":
                    queryTypeText = "繳費人數分布";
                    dataColumnName = "繳費人數";
                    break;
                default:
                    queryTypeText = "實名制人數分布";
                    dataColumnName = "實名制人數";
                    break;
            }

            debugInfo += "查詢類型: " + queryTypeText + "<br/>";
            debugInfo += "數據欄位: " + dataColumnName + "<br/>";

            // 根據選擇的分析項目設定 SQL 查詢
            if (RadioButtonList1.SelectedValue == "1")
            {
                sqlStr = "SELECT [學制],[科系],[實名制人數],[報到人數],[繳交畢業證書人數],[繳費人數] FROM [school].[dbo].[V_recowish1]";
                chartTitle = "日間部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "日間部科系" + queryTypeText;
                debugInfo += "使用視圖: V_recowish1<br/>";
            }
            else if (RadioButtonList1.SelectedValue == "2")
            {
                sqlStr = "SELECT [學制],[科系],[實名制人數],[報到人數],[繳交畢業證書人數],[繳費人數] FROM [school].[dbo].[V_recowish3]";
                chartTitle = "碩士班" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "碩士班系所" + queryTypeText;
                debugInfo += "使用視圖: V_recowish3<br/>";
            }
            else if (RadioButtonList1.SelectedValue == "3")
            {
                sqlStr = "SELECT [學制],[科系],[實名制人數],[報到人數],[繳交畢業證書人數],[繳費人數] FROM [school].[dbo].[V_recowish5]";
                chartTitle = "進修部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "進修部科系" + queryTypeText;
                debugInfo += "使用視圖: V_recowish5<br/>";
            }
            else
            {
                msg.Text = "錯誤: 請選擇人數分析項目!";
                P1.Visible = false;
                ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
                return;
            }

            debugInfo += "SQL查詢: " + sqlStr + "<br/>";

            cmd.CommandText = sqlStr;
            cmd.Parameters.Clear();

            // 清除之前的數據
            ds.Clear();

            // 先測試資料庫連接和視圖存在性
            try
            {
                debugInfo += "🔍 檢查視圖存在性...<br/>";

                // 檢查視圖是否存在
                string[] viewNames = { "V_recowish1", "V_recowish3", "V_recowish5" };
                string[] viewDescriptions = { "日間部", "碩士班", "進修部" };

                for (int i = 0; i < viewNames.Length; i++)
                {
                    cmd.CommandText = $"SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = '{viewNames[i]}'";
                    var viewExists = cmd.ExecuteScalar();

                    if (Convert.ToInt32(viewExists) > 0)
                    {
                        debugInfo += $"✅ {viewDescriptions[i]}視圖 {viewNames[i]} 存在<br/>";

                        // 檢查視圖資料筆數
                        try
                        {
                            cmd.CommandText = $"SELECT COUNT(*) FROM [school].[dbo].[{viewNames[i]}]";
                            var testCount = cmd.ExecuteScalar();
                            debugInfo += $"📊 {viewNames[i]} 總筆數: {testCount}<br/>";

                            // 檢查視圖結構
                            cmd.CommandText = $"SELECT TOP 1 * FROM [school].[dbo].[{viewNames[i]}]";
                            using (var reader = cmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    debugInfo += $"✅ {viewNames[i]} 有資料<br/>";

                                    // 顯示欄位名稱
                                    string columns = "欄位: ";
                                    for (int j = 0; j < reader.FieldCount; j++)
                                    {
                                        columns += reader.GetName(j) + ", ";
                                    }
                                    debugInfo += columns.TrimEnd(',', ' ') + "<br/>";
                                }
                                else
                                {
                                    debugInfo += $"⚠️ {viewNames[i]} 沒有資料<br/>";
                                }
                                reader.Close();
                            }
                        }
                        catch (Exception dataEx)
                        {
                            debugInfo += $"❌ 查詢 {viewNames[i]} 資料時發生錯誤: {dataEx.Message}<br/>";
                        }
                    }
                    else
                    {
                        debugInfo += $"❌ {viewDescriptions[i]}視圖 {viewNames[i]} 不存在<br/>";
                    }
                }
            }
            catch (Exception testEx)
            {
                debugInfo += "❌ 視圖檢查失敗: " + testEx.Message + "<br/>";
            }

            // 執行實際查詢
            cmd.CommandText = sqlStr;
            da.SelectCommand = cmd;

            try
            {
                da.Fill(ds, "check");
                debugInfo += "DataAdapter Fill 完成<br/>";
                debugInfo += "DataSet Tables 數量: " + ds.Tables.Count + "<br/>";

                if (ds.Tables.Contains("check"))
                {
                    debugInfo += "check 表格存在，行數: " + ds.Tables["check"].Rows.Count + "<br/>";
                    debugInfo += "check 表格欄數: " + ds.Tables["check"].Columns.Count + "<br/>";

                    // 顯示欄位名稱
                    string columnNames = "欄位名稱: ";
                    foreach (DataColumn col in ds.Tables["check"].Columns)
                    {
                        columnNames += col.ColumnName + ", ";
                    }
                    debugInfo += columnNames + "<br/>";

                    GV1 = ds.Tables["check"].Copy();
                }
                else
                {
                    debugInfo += "check 表格不存在<br/>";
                    GV1 = new DataTable();
                }
            }
            catch (Exception fillEx)
            {
                debugInfo += "Fill 錯誤: " + fillEx.Message + "<br/>";
                GV1 = new DataTable();
            }

            debugInfo += "最終 GV1 行數: " + GV1.Rows.Count + "<br/>";

            if (GV1.Rows.Count > 0)
            {
                debugInfo += "🎯 開始處理數據和生成圖表...<br/>";
                debugInfo += $"📋 資料表欄位數: {GV1.Columns.Count}<br/>";

                // 顯示欄位名稱
                string columnInfo = "📊 欄位名稱: ";
                foreach (DataColumn col in GV1.Columns)
                {
                    columnInfo += col.ColumnName + " (" + col.DataType.Name + "), ";
                }
                debugInfo += columnInfo.TrimEnd(',', ' ') + "<br/>";

                // 顯示前幾筆資料
                debugInfo += "📝 前3筆數據:<br/>";
                for (int i = 0; i < Math.Min(3, GV1.Rows.Count); i++)
                {
                    string rowData = "";
                    for (int j = 0; j < GV1.Columns.Count; j++)
                    {
                        string cellValue = GV1.Rows[i][j]?.ToString() ?? "NULL";
                        rowData += $"{GV1.Columns[j].ColumnName}={cellValue} | ";
                    }
                    debugInfo += $"第{i + 1}筆: {rowData}<br/>";
                }

                // 檢查必要欄位是否存在
                bool hasRequiredColumns = GV1.Columns.Contains("科系") && GV1.Columns.Contains(dataColumnName);
                debugInfo += $"🔍 必要欄位檢查 - 科系欄位: {GV1.Columns.Contains("科系")}, {dataColumnName}欄位: {GV1.Columns.Contains(dataColumnName)}<br/>";

                if (!hasRequiredColumns)
                {
                    debugInfo += "❌ 缺少必要欄位，無法繼續處理<br/>";
                    msg.Text = "錯誤: 資料表缺少必要欄位<br/><br/>調試資訊:<br/>" + debugInfo;
                    P1.Visible = false;
                    ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
                    return;
                }

                JavaScriptSerializer serializer = new JavaScriptSerializer();
                var chartData = new System.Collections.Generic.List<object>();

                chartData.Add(new object[] { "科系", dataColumnName });

                int validDataCount = 0;
                foreach (DataRow row in GV1.Rows)
                {
                    string departmentName = row["科系"].ToString();
                    int dataValue = 0;

                    if (int.TryParse(row[dataColumnName].ToString(), out dataValue))
                    {
                        chartData.Add(new object[] { departmentName, dataValue });
                        validDataCount++;
                    }
                }

                debugInfo += "有效圖表數據筆數: " + validDataCount + "<br/>";

                // 註冊圖表變數
                ScriptManager.RegisterStartupScript(this, GetType(), "chartTitle", "var chartTitle = '" + chartTitle + "';", true);
                string jsonData = serializer.Serialize(chartData);
                ScriptManager.RegisterStartupScript(this, GetType(), "chartData", "var chartData = " + jsonData + ";", true);

                // 計算統計摘要
                int totalCount = 0;
                int maxValue = 0;
                string maxDepartment = "";

                foreach (DataRow row in GV1.Rows)
                {
                    int currentValue = 0;
                    if (int.TryParse(row[dataColumnName].ToString(), out currentValue))
                    {
                        totalCount += currentValue;
                        if (currentValue > maxValue)
                        {
                            maxValue = currentValue;
                            maxDepartment = row["科系"].ToString();
                        }
                    }
                }

                ViewState["TotalCount"] = totalCount;
                ViewState["MaxValue"] = maxValue;
                ViewState["MaxDepartment"] = maxDepartment;
                ViewState["DepartmentCount"] = GV1.Rows.Count;

                debugInfo += "統計摘要 - 總計: " + totalCount + ", 最高: " + maxValue + ", 科系數: " + GV1.Rows.Count + "<br/>";

                GridView1.DataSource = GV1;
                GridView1.AllowPaging = true;
                GridView1.PageSize = 20;
                GridView1.DataBind();

                msg.Text = string.Format("查詢完成！共 {0} 個科系，總計 {1} 人，最高科系：{2} ({3} 人)<br/><br/>調試資訊:<br/>{4}",
                GV1.Rows.Count, totalCount, maxDepartment, maxValue, debugInfo);
                msg.CssClass = "msg-info";

                // 成功完成後，初始化圖表並隱藏載入遮罩
                ScriptManager.RegisterStartupScript(this, GetType(), "initAndHideLoading",
                "setTimeout(function() { " +
                "try { " +
                "if (typeof initializeChart === 'function') { initializeChart(); } " +
                "} catch(e) { console.log('圖表初始化錯誤:', e); } " +
                "hideLoading(); " +
                "}, 500);", true);
            }
            else
            {
                GridView1.AllowPaging = false;
                GridView1.DataSource = "";
                GridView1.DataBind();

                // 提供更詳細的無資料原因分析
                string noDataReason = "⚠️ 查無資料！可能的原因：<br/>";
                noDataReason += "1. 選擇的視圖中沒有資料<br/>";
                noDataReason += "2. 資料庫視圖不存在<br/>";
                noDataReason += "3. 資料庫連線問題<br/>";
                noDataReason += "4. SQL 查詢語法錯誤<br/><br/>";

                msg.Text = noDataReason + "🔍 詳細調試資訊:<br/>" + debugInfo;
                msg.CssClass = "msg-info";
                P1.Visible = false;

                // 沒有資料時也要隱藏載入遮罩
                ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingNoData", "hideLoading();", true);
            }
        }
        catch (Exception ex)
        {
            msg.Text = "錯誤: " + ex.Message + "<br/><br/>調試資訊:<br/>" + debugInfo + "<br/>詳細錯誤: " + ex.StackTrace;

            // 發生錯誤時隱藏載入遮罩
            ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingException", "hideLoading();", true);
        }
        finally
        {
            if (db.conn.State == ConnectionState.Open)
            {
                db.conn.Close();
            }
        }
    }

    public void GridView1_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            e.Row.Cells[0].Style.Add("width", "200px");
            e.Row.Cells[1].Style.Add("width", "50px");

            for (int i = 0; i < e.Row.Cells.Count; i++)
            {
                e.Row.Cells[i].HorizontalAlign = HorizontalAlign.Center;
            }
        }
    }

    protected void ExportToExcel(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(hiddenChartData.Value))
            {
                ScriptManager.RegisterStartupScript(this, GetType(), "Msg",
                "alert('圖表數據未準備好，請稍後再試');", true);
                return;
            }

            using (XLWorkbook wb = new XLWorkbook())
            {
                var ws = wb.Worksheets.Add("Data");
                ws.Cell(1, 1).InsertTable(GV1);

                string base64Image = hiddenChartData.Value;
                byte[] imageBytes = Convert.FromBase64String(base64Image.Split(',')[1]);
                using (MemoryStream ms = new MemoryStream(imageBytes))
                {
                    var picture = ws.AddPicture(ms)
                    .MoveTo(ws.Cell(GV1.Rows.Count + 3, 1))
                    .Scale(0.8);
                }

                Response.Clear();
                Response.Buffer = true;
                Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                Response.AddHeader("content-disposition", "attachment;filename=" + ViewState["chartTitle"] + "統計報表" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx");

                using (MemoryStream stream = new MemoryStream())
                {
                    wb.SaveAs(stream);
                    stream.WriteTo(Response.OutputStream);
                    Response.Flush();
                    Response.End();
                }
            }
        }
        catch (Exception ex)
        {
            ScriptManager.RegisterStartupScript(this, GetType(), "Msg", "alert('匯出失敗：" + ex.Message + "');", true);
        }
    }

    protected void ExportToPdf(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(hiddenChartData.Value))
            {
                ScriptManager.RegisterStartupScript(this, GetType(), "Msg",
                "alert('圖表數據未準備好，請稍後再試');", true);
                return;
            }

            Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 10f, 10f);
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream);
            pdfDoc.Open();

            string fontPath = Server.MapPath("~/fonts/kaiu.ttf");
            BaseFont bfChinese = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            Font titleFont = new Font(bfChinese, 16, Font.BOLD);
            Font headerFont = new Font(bfChinese, 12, Font.BOLD);
            Font cellFont = new Font(bfChinese, 10, Font.NORMAL);

            Paragraph title = new Paragraph("" + ViewState["chartTitle"] + "統計報表\n\n", titleFont);
            title.Alignment = Element.ALIGN_CENTER;
            pdfDoc.Add(title);

            PdfPTable pdfTable = new PdfPTable(GV1.Columns.Count);
            pdfTable.WidthPercentage = 100;
            pdfTable.SplitLate = false;
            pdfTable.SplitRows = true;

            foreach (DataColumn column in GV1.Columns)
            {
                PdfPCell headerCell = new PdfPCell(new Phrase(column.ColumnName, headerFont));
                headerCell.BackgroundColor = new BaseColor(200, 200, 200);
                headerCell.HorizontalAlignment = Element.ALIGN_CENTER;
                pdfTable.AddCell(headerCell);
            }

            foreach (DataRow row in GV1.Rows)
            {
                foreach (var cell in row.ItemArray)
                {
                    PdfPCell pdfCell = new PdfPCell(new Phrase(cell.ToString(), cellFont));
                    pdfCell.HorizontalAlignment = Element.ALIGN_CENTER;
                    pdfTable.AddCell(pdfCell);
                }
            }
            pdfDoc.Add(pdfTable);

            string foot = String.Format(@"Copyright @{0} All rights reserved 中信科技大學 Made with 資訊系統整合中心", DateTime.Now.ToString("yyyy"));
            PdfPTable pdffoot = new PdfPTable(1);
            pdffoot.WidthPercentage = 100;
            PdfPCell contentTitle = new PdfPCell(new Phrase(foot, headerFont));
            contentTitle.HorizontalAlignment = Element.ALIGN_CENTER;
            contentTitle.BorderWidth = 0;
            pdffoot.AddCell(contentTitle);
            pdfDoc.Add(pdffoot);

            string base64Image = hiddenChartData.Value;
            if (!string.IsNullOrEmpty(base64Image))
            {
                byte[] imageBytes = Convert.FromBase64String(base64Image.Split(',')[1]);
                iTextSharp.text.Image chartImage = iTextSharp.text.Image.GetInstance(imageBytes);
                chartImage.ScaleToFit(500f, 300f);
                chartImage.Alignment = Element.ALIGN_CENTER;
                pdfDoc.Add(new Paragraph("\n\n"));
                pdfDoc.Add(chartImage);
            }

            pdfDoc.Close();

            Response.ContentType = "application/pdf";
            Response.AddHeader("content-disposition", "attachment;filename=" + ViewState["chartTitle"] + "統計報表" + DateTime.Now.ToString("yyyy-MM-dd") + ".pdf");
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            Response.Write(pdfDoc);
            Response.End();
        }
        catch (Exception ex)
        {
            ScriptManager.RegisterStartupScript(this, GetType(), "Msg", "alert('匯出失敗：" + ex.Message + "');", true);
        }
    }

    protected void NewList1(object sender, GridViewPageEventArgs e)
    {
        GridView1.PageIndex = e.NewPageIndex;
        GetGV1Data1();
    }

    public bool CheckUserSession()
    {
        if (Session["tech_no"] == null)
        {
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('作業逾時或尚未登入,請登入後再繼續作業!');location.href='/Logout.aspx';", true);
            return false;
        }
        else
        {
            return true;
        }
    }
}
