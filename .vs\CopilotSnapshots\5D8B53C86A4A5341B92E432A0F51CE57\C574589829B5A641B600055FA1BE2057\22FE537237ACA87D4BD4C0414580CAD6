﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text.RegularExpressions;

public partial class Site : System.Web.UI.MasterPage
{
    protected System.Web.UI.HtmlControls.HtmlGenericControl adminMenu;
    protected System.Web.UI.HtmlControls.HtmlGenericControl departmentAssistantMenu;
    protected System.Web.UI.HtmlControls.HtmlGenericControl teacherMenu;
    
    App_Func appfun = new App_Func();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["tech_no"] != null)
        {
            string masterSingle = Session["master_single"] != null ? Session["master_single"].ToString() : "";

            // 控制選單顯示的邏輯需要在每次頁面載入時都執行，以確保 PostBack 後狀態正確
            if (!string.IsNullOrEmpty(masterSingle))
            {
                // 根據 master_single 的值顯示對應的選單
                switch (masterSingle)
                {
                    case "3": // 管理者
                        adminMenu.Visible = true;
                        break;
                    case "1": // 系助理
                        departmentAssistantMenu.Visible = true;
                        break;
                    case "0": // 教師
                        teacherMenu.Visible = true;
                        break;
                    default:
                        break;
                }
            }
        }
        else
        {
            // 若使用者尚未登入，重定向到登入頁面
            Session.Clear();
            Response.Redirect("Login.aspx");
        }
    }

    //判斷Session["TempUser"]是否有值，沒有則回到登入頁
    public bool CheckUserSession()
    {
        if (Session["tech_no"] == null)
        {
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('作業逾時或尚未登入,請登入後再繼續作業!');location.href='/Logout.aspx';", true);
            return false;
        }
        else
        {
            return true;
        }
    }
    public string FilterGdName(string orgString)
    {
        string newString = string.Empty;
        MatchCollection matches = Regex.Matches(orgString, @"[^\W']+", RegexOptions.IgnoreCase);
        foreach (Match match in matches)
        {
            newString += match.Value;
        }
        return newString;
    }
}
